// lib/api/medusa-admin.ts
import axios from 'axios';
import { handleUnauthorizedWithSmartRedirect } from '../utils/authUtils';

const baseURL = process.env.NEXT_PUBLIC_MEDUSA_BASE_URL || 'http://localhost:9000';

// Create a separate axios instance for Medusa admin API
const medusaAdminApi = axios.create({
  baseURL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor for authentication
medusaAdminApi.interceptors.request.use((config) => {
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('ondc_auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
  }
  return config;
});

// Add response interceptor for 401 handling
medusaAdminApi.interceptors.response.use(
  (response) => {
    // Return successful responses as-is
    return response;
  },
  (error) => {
    // Handle 401 Unauthorized responses
    if (error.response?.status === 401) {
      console.log('🚨 Medusa Admin API returned 401 Unauthorized:', error.config?.url);
      
      // Extract store handle from headers if available
      const storeHandle = error.config?.headers?.['x-tenant-id'];
      
      // Clear auth and redirect
      handleUnauthorizedWithSmartRedirect(storeHandle);
    }
    
    // Re-throw the error for the calling code to handle
    return Promise.reject(error);
  }
);

// Types for Medusa API responses
export interface MedusaCategory {
  id: string;
  name: string;
  handle?: string;
  description?: string;
  parent_category_id?: string | null;
  category_children?: MedusaCategory[];
  created_at: string;
  updated_at: string;
}

export interface MedusaCollection {
  id: string;
  title: string;
  handle?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface MedusaTag {
  id: string;
  value: string;
  created_at: string;
  updated_at: string;
}

export interface MedusaProduct {
  id: string;
  title: string;
  handle?: string;
  description?: string;
  status: 'draft' | 'published' | 'proposed' | 'rejected';
  thumbnail?: string;
  categories?: MedusaCategory[];
  collection_id?: string;
  collection?: MedusaCollection;
  tags?: MedusaTag[];
  variants?: MedusaProductVariant[];
  options?: MedusaProductOption[];
  images?: MedusaProductImage[];
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface MedusaProductVariant {
  id: string;
  title: string;
  sku?: string;
  barcode?: string;
  ean?: string;
  upc?: string;
  inventory_quantity: number;
  allow_backorder: boolean;
  manage_inventory: boolean;
  hs_code?: string;
  origin_country?: string;
  mid_code?: string;
  material?: string;
  weight?: number;
  length?: number;
  height?: number;
  width?: number;
  prices?: MedusaProductPrice[];
  options?: MedusaProductOptionValue[];
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface MedusaProductOption {
  id: string;
  title: string;
  values?: MedusaProductOptionValue[];
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface MedusaProductOptionValue {
  id: string;
  value: string;
  option_id: string;
  variant_id: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface MedusaProductPrice {
  id: string;
  currency_code: string;
  amount: number;
  min_quantity?: number;
  max_quantity?: number;
  price_list_id?: string;
  variant_id: string;
  region_id?: string;
  created_at: string;
  updated_at: string;
}

export interface MedusaProductImage {
  id: string;
  url: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface MedusaStore {
  id: string;
  name: string;
  handle: string;
  description?: string;
  logo?: string;
  banner?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };
  contact?: {
    email: string;
    phone: string;
    website?: string;
  };
  social_media?: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
  };
  business_hours?: {
    monday?: string;
    tuesday?: string;
    wednesday?: string;
    thursday?: string;
    friday?: string;
    saturday?: string;
    sunday?: string;
  };
  theme?: {
    primary_color?: string;
    secondary_color?: string;
    accent_color?: string;
    background_color?: string;
    text_color?: string;
  };
  settings?: {
    currency: string;
    timezone: string;
    language: string;
    tax_inclusive: boolean;
    shipping_enabled: boolean;
  };
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface MedusaDashboardAnalytics {
  total_products: number;
  total_orders: number;
  total_revenue: number;
  total_customers: number;
  recent_orders: {
    id: string;
    display_id: string;
    customer_email: string;
    total: number;
    status: string;
    created_at: string;
  }[];
  top_products: {
    id: string;
    title: string;
    sales_count: number;
    revenue: number;
    price: string;
    stock: string;
    product_handle: string;
    status: string;
  }[];
  revenue_chart_data?: {
    month: string;
    revenue: number;
    orders: number;
    customers: number;
  }[];
  sales_chart_data?: {
    day: string;
    sales: number;
    profit: number;
  }[];
}

// Interface for the actual API response structure
export interface MedusaDashboardApiResponse {
  stats: {
    totalRevenue: number;
    totalOrders: number;
    totalCustomers: number;
    totalProducts: number;
    averageOrderValue: number;
    conversionRate: number;
    revenueGrowth: number;
    orderGrowth: number;
    customerGrowth: number;
  };
  revenueTrend: {
    date: string;
    revenue: number;
    orders: number;
    customers: number;
  }[];
  topProducts: any[];
  topOrders: {
    order_id: string;
    order_display_id: string;
    customer_name: string;
    customer_email: string;
    total_order_amount: number;
    order_status: string;
    created_at: string;
  }[];
  refundRate: {
    name: string;
    value: number;
    color: string;
  }[];
  customerSplit: {
    segment: string;
    count: number;
    percentage: number;
  }[];
}

export interface MedusaCategoriesResponse {
  product_categories: MedusaCategory[];
  count: number;
  offset: number;
  limit: number;
}

export interface MedusaCollectionsResponse {
  collections: MedusaCollection[];
  count: number;
  offset: number;
  limit: number;
}

export interface MedusaTagsResponse {
  product_tags: MedusaTag[];
  count: number;
  offset: number;
  limit: number;
}

// API functions for fetching data for product forms
export const medusaAdminService = {
  // Fetch categories from Medusa admin API
  getCategories: async (storeHandle: string): Promise<MedusaCategory[]> => {
    try {
      console.log('Fetching categories from Medusa admin API...');
      console.log('Store handle (x-tenant-id):', storeHandle);
      
      const response = await medusaAdminApi.get('/admin/product-categories', {
        headers: {
          'x-tenant-id': storeHandle,
        },
      });
      
      console.log('Categories response:', response.data);
      
      // Handle different response structures
      if (response.data?.product_categories) {
        return response.data.product_categories;
      } else if (Array.isArray(response.data)) {
        return response.data;
      } else {
        console.warn('Unexpected categories response structure:', response.data);
        return [];
      }
    } catch (error: any) {
      console.error('Error fetching categories:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Fetch collections from Medusa admin API
  getCollections: async (storeHandle: string): Promise<MedusaCollection[]> => {
    try {
      console.log('Fetching collections from Medusa admin API...');
      console.log('Store handle (x-tenant-id):', storeHandle);
      
      const response = await medusaAdminApi.get('/admin/collections', {
        headers: {
          'x-tenant-id': storeHandle,
        },
      });
      
      console.log('Collections response:', response.data);
      
      // Handle different response structures
      if (response.data?.collections) {
        return response.data.collections;
      } else if (Array.isArray(response.data)) {
        return response.data;
      } else {
        console.warn('Unexpected collections response structure:', response.data);
        return [];
      }
    } catch (error: any) {
      console.error('Error fetching collections:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Fetch tags from Medusa admin API
  getTags: async (storeHandle: string): Promise<MedusaTag[]> => {
    try {
      console.log('Fetching tags from Medusa admin API...');
      console.log('Store handle (x-tenant-id):', storeHandle);
      
      const response = await medusaAdminApi.get('/admin/product-tags', {
        headers: {
          'x-tenant-id': storeHandle,
        },
      });
      
      console.log('Tags response:', response.data);
      
      // Handle different response structures
      if (response.data?.product_tags) {
        return response.data.product_tags;
      } else if (Array.isArray(response.data)) {
        return response.data;
      } else {
        console.warn('Unexpected tags response structure:', response.data);
        return [];
      }
    } catch (error: any) {
      console.error('Error fetching tags:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Get a single tag by ID
  getTag: async (storeHandle: string, tagId: string): Promise<MedusaTag> => {
    try {
      console.log('Fetching tag from Medusa admin API...');
      console.log('Store handle (x-tenant-id):', storeHandle);
      console.log('Tag ID:', tagId);
      
      const response = await medusaAdminApi.get(`/admin/product-tags/${tagId}`, {
        headers: {
          'x-tenant-id': storeHandle,
        },
      });
      
      console.log('Tag response:', response.data);
      
      // Handle different response structures
      if (response.data?.product_tag) {
        return response.data.product_tag;
      } else if (response.data?.id) {
        return response.data;
      } else {
        throw new Error('Invalid tag response structure');
      }
    } catch (error: any) {
      console.error('Error fetching tag:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Create a new tag
  createTag: async (storeHandle: string, value: string): Promise<MedusaTag> => {
    try {
      console.log('Creating tag via Medusa admin API...');
      console.log('Store handle (x-tenant-id):', storeHandle);
      console.log('Tag value:', value);
      
      const response = await medusaAdminApi.post('/admin/product-tags', 
        { value },
        {
          headers: {
            'x-tenant-id': storeHandle,
          },
        }
      );
      
      console.log('Create tag response:', response.data);
      
      // Handle different response structures
      if (response.data?.product_tag) {
        return response.data.product_tag;
      } else if (response.data?.id) {
        return response.data;
      } else {
        throw new Error('Invalid create tag response structure');
      }
    } catch (error: any) {
      console.error('Error creating tag:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Update an existing tag
  updateTag: async (storeHandle: string, tagId: string, value: string): Promise<MedusaTag> => {
    try {
      console.log('Updating tag via Medusa admin API...');
      console.log('Store handle (x-tenant-id):', storeHandle);
      console.log('Tag ID:', tagId);
      console.log('Tag value:', value);
      
      const response = await medusaAdminApi.post(`/admin/product-tags/${tagId}`, 
        { value },
        {
          headers: {
            'x-tenant-id': storeHandle,
          },
        }
      );
      
      console.log('Update tag response:', response.data);
      
      // Handle different response structures
      if (response.data?.product_tag) {
        return response.data.product_tag;
      } else if (response.data?.id) {
        return response.data;
      } else {
        throw new Error('Invalid update tag response structure');
      }
    } catch (error: any) {
      console.error('Error updating tag:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Delete a tag
  deleteTag: async (storeHandle: string, tagId: string): Promise<void> => {
    try {
      console.log('Deleting tag via Medusa admin API...');
      console.log('Store handle (x-tenant-id):', storeHandle);
      console.log('Tag ID:', tagId);
      
      await medusaAdminApi.delete(`/admin/product-tags/${tagId}`, {
        headers: {
          'x-tenant-id': storeHandle,
        },
      });
      
      console.log('Tag deleted successfully');
    } catch (error: any) {
      console.error('Error deleting tag:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Get a single collection by ID
  getCollection: async (storeHandle: string, collectionId: string): Promise<MedusaCollection> => {
    try {
      console.log('Fetching collection from Medusa admin API...');
      console.log('Store handle (x-tenant-id):', storeHandle);
      console.log('Collection ID:', collectionId);
      
      const response = await medusaAdminApi.get(`/admin/collections/${collectionId}`, {
        headers: {
          'x-tenant-id': storeHandle,
        },
      });
      
      console.log('Collection response:', response.data);
      
      // Handle different response structures
      if (response.data?.collection) {
        return response.data.collection;
      } else if (response.data?.id) {
        return response.data;
      } else {
        throw new Error('Invalid collection response structure');
      }
    } catch (error: any) {
      console.error('Error fetching collection:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Create a new collection
  createCollection: async (storeHandle: string, title: string, handle?: string): Promise<MedusaCollection> => {
    try {
      console.log('Creating collection via Medusa admin API...');
      console.log('Store handle (x-tenant-id):', storeHandle);
      console.log('Collection title:', title);
      
      // Generate handle from title if not provided
      const collectionHandle = handle || title.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single
        .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
      
      console.log('Generated handle:', collectionHandle);
      
      const payload = {
        title,
        handle: collectionHandle
      };
      
      const response = await medusaAdminApi.post('/admin/collections', 
        payload,
        {
          headers: {
            'x-tenant-id': storeHandle,
          },
        }
      );
      
      console.log('Create collection response:', response.data);
      
      // Handle different response structures
      if (response.data?.collection) {
        return response.data.collection;
      } else if (response.data?.id) {
        return response.data;
      } else {
        throw new Error('Invalid create collection response structure');
      }
    } catch (error: any) {
      console.error('Error creating collection:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Update an existing collection
  updateCollection: async (storeHandle: string, collectionId: string, title: string, handle?: string): Promise<MedusaCollection> => {
    try {
      console.log('Updating collection via Medusa admin API...');
      console.log('Store handle (x-tenant-id):', storeHandle);
      console.log('Collection ID:', collectionId);
      console.log('Collection title:', title);
      
      // Generate handle from title if not provided
      const collectionHandle = handle || title.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single
        .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
      
      console.log('Generated handle:', collectionHandle);
      
      const payload = {
        title,
        handle: collectionHandle
      };
      
      const response = await medusaAdminApi.post(`/admin/collections/${collectionId}`, 
        payload,
        {
          headers: {
            'x-tenant-id': storeHandle,
          },
        }
      );
      
      console.log('Update collection response:', response.data);
      
      // Handle different response structures
      if (response.data?.collection) {
        return response.data.collection;
      } else if (response.data?.id) {
        return response.data;
      } else {
        throw new Error('Invalid update collection response structure');
      }
    } catch (error: any) {
      console.error('Error updating collection:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Delete a collection
  deleteCollection: async (storeHandle: string, collectionId: string): Promise<void> => {
    try {
      console.log('Deleting collection via Medusa admin API...');
      console.log('Store handle (x-tenant-id):', storeHandle);
      console.log('Collection ID:', collectionId);
      
      await medusaAdminApi.delete(`/admin/collections/${collectionId}`, {
        headers: {
          'x-tenant-id': storeHandle,
        },
      });
      
      console.log('Collection deleted successfully');
    } catch (error: any) {
      console.error('Error deleting collection:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Get a single category by ID
  getCategory: async (storeHandle: string, categoryId: string): Promise<MedusaCategory> => {
    try {
      console.log('Fetching category from Medusa admin API...');
      console.log('Store handle (x-tenant-id):', storeHandle);
      console.log('Category ID:', categoryId);
      
      const response = await medusaAdminApi.get(`/admin/product-categories/${categoryId}`, {
        headers: {
          'x-tenant-id': storeHandle,
        },
      });
      
      console.log('Category response:', response.data);
      
      // Handle different response structures
      if (response.data?.product_category) {
        return response.data.product_category;
      } else if (response.data?.id) {
        return response.data;
      } else {
        throw new Error('Invalid category response structure');
      }
    } catch (error: any) {
      console.error('Error fetching category:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Create a new category
  createCategory: async (
    storeHandle: string, 
    name: string, 
    description?: string, 
    parentCategoryId?: string,
    handle?: string
  ): Promise<MedusaCategory> => {
    try {
      console.log('Creating category via Medusa admin API...');
      console.log('Store handle (x-tenant-id):', storeHandle);
      console.log('Category name:', name);
      console.log('Parent category ID:', parentCategoryId);
      
      // Generate handle from name if not provided
      const categoryHandle = handle || name.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single
        .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
      
      console.log('Generated handle:', categoryHandle);
      
      const payload: any = {
        name,
        handle: categoryHandle
      };
      
      if (description) {
        payload.description = description;
      }
      
      if (parentCategoryId) {
        payload.parent_category_id = parentCategoryId;
      }
      
      const response = await medusaAdminApi.post('/admin/product-categories', 
        payload,
        {
          headers: {
            'x-tenant-id': storeHandle,
          },
        }
      );
      
      console.log('Create category response:', response.data);
      
      // Handle different response structures
      if (response.data?.product_category) {
        return response.data.product_category;
      } else if (response.data?.id) {
        return response.data;
      } else {
        throw new Error('Invalid create category response structure');
      }
    } catch (error: any) {
      console.error('Error creating category:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Update an existing category
  updateCategory: async (
    storeHandle: string, 
    categoryId: string, 
    name: string, 
    description?: string, 
    parentCategoryId?: string,
    handle?: string
  ): Promise<MedusaCategory> => {
    try {
      console.log('Updating category via Medusa admin API...');
      console.log('Store handle (x-tenant-id):', storeHandle);
      console.log('Category ID:', categoryId);
      console.log('Category name:', name);
      console.log('Parent category ID:', parentCategoryId);
      
      // Generate handle from name if not provided
      const categoryHandle = handle || name.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single
        .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
      
      console.log('Generated handle:', categoryHandle);
      
      const payload: any = {
        name,
        handle: categoryHandle
      };
      
      if (description) {
        payload.description = description;
      }
      
      if (parentCategoryId) {
        payload.parent_category_id = parentCategoryId;
      }
      
      const response = await medusaAdminApi.post(`/admin/product-categories/${categoryId}`, 
        payload,
        {
          headers: {
            'x-tenant-id': storeHandle,
          },
        }
      );
      
      console.log('Update category response:', response.data);
      
      // Handle different response structures
      if (response.data?.product_category) {
        return response.data.product_category;
      } else if (response.data?.id) {
        return response.data;
      } else {
        throw new Error('Invalid update category response structure');
      }
    } catch (error: any) {
      console.error('Error updating category:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Delete a category
  deleteCategory: async (storeHandle: string, categoryId: string): Promise<void> => {
    try {
      console.log('Deleting category via Medusa admin API...');
      console.log('Store handle (x-tenant-id):', storeHandle);
      console.log('Category ID:', categoryId);
      
      await medusaAdminApi.delete(`/admin/product-categories/${categoryId}`, {
        headers: {
          'x-tenant-id': storeHandle,
        },
      });
      
      console.log('Category deleted successfully');
    } catch (error: any) {
      console.error('Error deleting category:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Get all products
  getProducts: async (storeHandle: string): Promise<MedusaProduct[]> => {
    try {
      console.log('Fetching products from Medusa admin API...');
      console.log('Store handle (x-tenant-id):', storeHandle);
      
      const response = await medusaAdminApi.get('/admin/products', {
        headers: {
          'x-tenant-id': storeHandle,
        },
      });
      
      console.log('Products response:', response.data);
      
      // Handle different response structures
      if (response.data?.products) {
        return response.data.products;
      } else if (Array.isArray(response.data)) {
        return response.data;
      } else {
        console.warn('Unexpected products response structure:', response.data);
        return [];
      }
    } catch (error: any) {
      console.error('Error fetching products:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Get a single product by ID
  getProduct: async (storeHandle: string, productId: string): Promise<MedusaProduct> => {
    try {
      console.log('Fetching product from Medusa admin API...');
      console.log('Store handle (x-tenant-id):', storeHandle);
      console.log('Product ID:', productId);
      
      const response = await medusaAdminApi.get(`/admin/products/${productId}`, {
        headers: {
          'x-tenant-id': storeHandle,
        },
      });
      
      console.log('Product response:', response.data);
      
      // Handle different response structures
      if (response.data?.product) {
        return response.data.product;
      } else if (response.data?.id) {
        return response.data;
      } else {
        throw new Error('Invalid product response structure');
      }
    } catch (error: any) {
      console.error('Error fetching product:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Create a new product
  createProduct: async (storeHandle: string, productData: any): Promise<MedusaProduct> => {
    try {
      console.log('Creating product via Medusa admin API...');
      console.log('Store handle (x-tenant-id):', storeHandle);
      console.log('Product data:', productData);
      
      const response = await medusaAdminApi.post('/admin/products', 
        productData,
        {
          headers: {
            'x-tenant-id': storeHandle,
          },
        }
      );
      
      console.log('Create product response:', response.data);
      
      // Handle different response structures
      if (response.data?.product) {
        return response.data.product;
      } else if (response.data?.id) {
        return response.data;
      } else {
        throw new Error('Invalid create product response structure');
      }
    } catch (error: any) {
      console.error('Error creating product:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Update an existing product
  updateProduct: async (storeHandle: string, productId: string, productData: any): Promise<MedusaProduct> => {
    try {
      console.log('Updating product via Medusa admin API...');
      console.log('Store handle (x-tenant-id):', storeHandle);
      console.log('Product ID:', productId);
      console.log('Product data:', productData);
      
      const response = await medusaAdminApi.post(`/admin/products/${productId}`, 
        productData,
        {
          headers: {
            'x-tenant-id': storeHandle,
          },
        }
      );
      
      console.log('Update product response:', response.data);
      
      // Handle different response structures
      if (response.data?.product) {
        return response.data.product;
      } else if (response.data?.id) {
        return response.data;
      } else {
        throw new Error('Invalid update product response structure');
      }
    } catch (error: any) {
      console.error('Error updating product:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Delete a product
  deleteProduct: async (storeHandle: string, productId: string): Promise<void> => {
    try {
      console.log('Deleting product via Medusa admin API...');
      console.log('Store handle (x-tenant-id):', storeHandle);
      console.log('Product ID:', productId);
      
      await medusaAdminApi.delete(`/admin/products/${productId}`, {
        headers: {
          'x-tenant-id': storeHandle,
        },
      });
      
      console.log('Product deleted successfully');
    } catch (error: any) {
      console.error('Error deleting product:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Get store configuration
  getStoreConfig: async (storeHandle: string): Promise<MedusaStore> => {
    try {
      console.log('Fetching store config from Medusa API...');
      console.log('Store handle (x-tenant-id):', storeHandle);
      
      const response = await medusaAdminApi.get('/admin/store', {
        headers: {
          'x-tenant-id': storeHandle,
        },
      });
      
      console.log('Store config response:', response.data);
      
      // Handle different response structures
      if (response.data?.store) {
        return response.data.store;
      } else if (response.data?.id) {
        return response.data;
      } else {
        throw new Error('Invalid store config response structure');
      }
    } catch (error: any) {
      console.error('Error fetching store config:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Get dashboard analytics
  getDashboardAnalytics: async (storeHandle: string, timePeriod: string = 'monthly'): Promise<MedusaDashboardAnalytics> => {
    try {
      const response = await medusaAdminApi.get('/admin/analytics/dashboard', {
        headers: {
          'x-tenant-id': storeHandle,
        },
        params: {
          period: timePeriod,
        },
      });

      const apiData: MedusaDashboardApiResponse = response.data;

      // Transform the API response to match our expected structure
      const normalizedData: MedusaDashboardAnalytics = {
        total_products: apiData.stats?.totalProducts || 0,
        total_orders: apiData.stats?.totalOrders || 0,
        total_revenue: apiData.stats?.totalRevenue || 0,
        total_customers: apiData.stats?.totalCustomers || 0,
        
        // Transform top orders
        recent_orders: apiData.topOrders?.map(order => ({
          id: order.order_id,
          display_id: order.order_display_id,
          customer_email: order.customer_email || order.customer_name || 'Guest Customer',
          total: order.total_order_amount,
          status: order.order_status,
          created_at: order.created_at,
        })) || [],
        
        // Transform top products (empty in current response)
        top_products: apiData.topProducts?.map(product => ({
          id: product.productId || 'N/A',
          title: product.title || product.name || 'Unknown Product',
          product_handle: product.sku,
          stock: product.stock,
          price: product.price,
          sales_count: product.sales_count || product.sales || 0,
          revenue: product.total_order_amount || 0,
          status: product.order_status || 'published',
        })) || [],
        
        // Transform revenue trend data for charts based on time period
        revenue_chart_data: (apiData.revenueTrend && Array.isArray(apiData.revenueTrend)) ? apiData.revenueTrend.map((item, index) => {
          let label = '';

          try {
            if (!item || !item.date) {
              throw new Error('Invalid item or missing date');
            }

            switch (timePeriod) {
              case 'weekly': {
                const date = new Date(item.date);
                if (isNaN(date.getTime())) {
                  throw new Error('Invalid date format for weekly');
                }
                label = date.toLocaleDateString('en-US', { weekday: 'short' });
                break;
              }
              case 'monthly': {
                const date = new Date(item.date);
                if (isNaN(date.getTime())) {
                  throw new Error('Invalid date format for monthly');
                }
                label = date.toLocaleDateString('en-US', { month: 'short' });
                break;
              }
              case 'quarterly': {
                // Backend returns format like "2024-Q1", "2024-Q2", etc.
                if (item.date.includes('-Q')) {
                  const [year, quarter] = item.date.split('-Q');
                  label = `Q${quarter}`;
                } else {
                  // Fallback: try to parse as date
                  const date = new Date(item.date);
                  if (!isNaN(date.getTime())) {
                    const quarter = Math.floor(date.getMonth() / 3) + 1;
                    label = `Q${quarter}`;
                  } else {
                    throw new Error('Invalid quarterly date format');
                  }
                }
                break;
              }
              case 'yearly': {
                // Backend returns simple year strings like "2023", "2024"
                if (/^\d{4}$/.test(item.date)) {
                  label = item.date;
                } else {
                  // Fallback: try to parse as date
                  const date = new Date(item.date);
                  if (!isNaN(date.getTime())) {
                    label = date.getFullYear().toString();
                  } else {
                    throw new Error('Invalid yearly date format');
                  }
                }
                break;
              }
              default: {
                const date = new Date(item.date);
                if (!isNaN(date.getTime())) {
                  label = date.toLocaleDateString('en-US', { month: 'short' });
                } else {
                  throw new Error('Invalid default date format');
                }
              }
            }
          } catch (error) {
            console.error('Error parsing date:', item?.date, error);
            // Fallback label
            label = `Point ${index + 1}`;
          }

          return {
            month: label,
            revenue: item.revenue || 0,
            orders: item.orders || 0,
            customers: item.customers || 0,
          };
        }) : undefined,
        
        // Use revenue trend for sales chart as well (can be customized later)
        sales_chart_data: (apiData.revenueTrend && Array.isArray(apiData.revenueTrend)) ? apiData.revenueTrend.map((item, index) => {
          let label = '';

          try {
            if (!item || !item.date) {
              throw new Error('Invalid item or missing date');
            }

            switch (timePeriod) {
              case 'weekly': {
                const date = new Date(item.date);
                if (isNaN(date.getTime())) {
                  throw new Error('Invalid date format for weekly');
                }
                label = date.toLocaleDateString('en-US', { weekday: 'short' });
                break;
              }
              case 'monthly': {
                const date = new Date(item.date);
                if (isNaN(date.getTime())) {
                  throw new Error('Invalid date format for monthly');
                }
                label = date.toLocaleDateString('en-US', { day: 'numeric' });
                break;
              }
              case 'quarterly': {
                // Backend returns format like "2024-Q1", "2024-Q2", etc.
                if (item.date.includes('-Q')) {
                  const [year, quarter] = item.date.split('-Q');
                  label = `Q${quarter}`;
                } else {
                  // Fallback: try to parse as date
                  const date = new Date(item.date);
                  if (!isNaN(date.getTime())) {
                    const quarter = Math.floor(date.getMonth() / 3) + 1;
                    label = `Q${quarter}`;
                  } else {
                    throw new Error('Invalid quarterly date format');
                  }
                }
                break;
              }
              case 'yearly': {
                // Backend returns simple year strings like "2023", "2024"
                // For sales chart, we'll use months of the year instead of just the year
                if (/^\d{4}$/.test(item.date)) {
                  // Use the year as label for yearly view
                  label = item.date;
                } else {
                  // Fallback: try to parse as date
                  const date = new Date(item.date);
                  if (!isNaN(date.getTime())) {
                    label = date.getFullYear().toString();
                  } else {
                    throw new Error('Invalid yearly date format');
                  }
                }
                break;
              }
              default: {
                label = `Point ${index + 1}`;
              }
            }
          } catch (error) {
            console.error('Error parsing date for sales chart:', item?.date, error);
            // Fallback label
            label = `Point ${index + 1}`;
          }

          return {
            day: label,
            sales: item.revenue || 0,
            profit: Math.floor((item.revenue || 0) * 0.3), // Assume 30% profit margin
          };
        }) : undefined,
      };
      

      
      return normalizedData;
    } catch (error: any) {
      console.error('Error fetching dashboard analytics:', error);
      if (error.response) {
        console.error('Error response status:', error.response.status);
        console.error('Error response data:', error.response.data);
        console.error('Error response headers:', error.response.headers);
      }
      throw error;
    }
  },

  // Update store configuration
  updateStoreConfig: async (storeHandle: string, storeData: Partial<MedusaStore>): Promise<MedusaStore> => {
    try {
      console.log('Updating store config via Medusa API...');
      console.log('Store handle (x-tenant-id):', storeHandle);
      console.log('Store data:', storeData);
      
      const response = await medusaAdminApi.post('/admin/store', 
        storeData,
        {
          headers: {
            'x-tenant-id': storeHandle,
          },
        }
      );
      
      console.log('Update store config response:', response.data);
      
      // Handle different response structures
      if (response.data?.store) {
        return response.data.store;
      } else if (response.data?.id) {
        return response.data;
      } else {
        throw new Error('Invalid update store config response structure');
      }
    } catch (error: any) {
      console.error('Error updating store config:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

    // Fetch orders from Medusa admin API
    getOrders: async (storeHandle: string): Promise<any[]> => {
      try {
        console.log('Fetching orders from Medusa admin API...');
        console.log('Store handle (x-tenant-id):', storeHandle);
        
        const response = await medusaAdminApi.get('/admin/orders', {
          headers: {
            'x-tenant-id': storeHandle,
          },
        });
        
        console.log('Orders response:', response.data);
        
        // Handle different response structures
        if (response.data?.orders) {
          return response.data.orders;
        } else if (Array.isArray(response.data)) {
          return response.data;
        } else {
          console.warn('Unexpected orders response structure:', response.data);
          return [];
        }
      } catch (error: any) {
        console.error('Error fetching orders:', error);
        if (error.response) {
          console.error('Error response:', error.response.data);
        }
        throw error;
      }
    },

    // Get a single order by ID
    getOrder: async (storeHandle: string, orderId: string): Promise<any> => {
      try {
        console.log('Fetching order from Medusa admin API...');
        console.log('Store handle (x-tenant-id):', storeHandle);
        console.log('Order ID:', orderId);
        
        const response = await medusaAdminApi.get(`/admin/orders/${orderId}`, {
          headers: {
            'x-tenant-id': storeHandle,
          },
        });
        
        console.log('Order response:', response.data);
        
        // Handle different response structures
        if (response.data?.order) {
          return response.data.order;
        } else if (response.data?.id) {
          return response.data;
        } else {
          throw new Error('Invalid order response structure');
        }
      } catch (error: any) {
        console.error('Error fetching order:', error);
        if (error.response) {
          console.error('Error response:', error.response.data);
        }
        throw error;
      }
    },
};

export default medusaAdminService;