'use client';

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export interface LoadingState {
  isLoading: boolean;
  loadingType: 'button' | 'page' | 'navigation' | 'backdrop' | null;
  message: string;
  subMessage?: string;
  progress?: number;
  actionId?: string;
}

interface GlobalLoadingContextType {
  loadingState: LoadingState;
  startLoading: (type: LoadingState['loadingType'], message: string, options?: {
    subMessage?: string;
    actionId?: string;
    progress?: number;
  }) => void;
  stopLoading: (actionId?: string) => void;
  updateProgress: (progress: number) => void;
  updateMessage: (message: string, subMessage?: string) => void;
  isActionLoading: (actionId: string) => boolean;
}

const GlobalLoadingContext = createContext<GlobalLoadingContextType | undefined>(undefined);

interface GlobalLoadingProviderProps {
  children: React.ReactNode;
}

export const GlobalLoadingProvider: React.FC<GlobalLoadingProviderProps> = ({ children }) => {
  const router = useRouter();
  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: false,
    loadingType: null,
    message: '',
    subMessage: '',
    progress: 0,
    actionId: undefined,
  });

  const startLoading = useCallback((
    type: LoadingState['loadingType'], 
    message: string, 
    options?: {
      subMessage?: string;
      actionId?: string;
      progress?: number;
    }
  ) => {
    console.log(`DEBUG Starting loading: ${type} - ${message}`, options);
    setLoadingState(prev => {
      // Prevent duplicate navigation loading
      if (type === 'navigation' && prev.isLoading && prev.loadingType === 'navigation') {
        console.log('WARNING Preventing duplicate navigation loading');
        return prev;
      }
      
      return {
        isLoading: true,
        loadingType: type,
        message,
        subMessage: options?.subMessage || '',
        progress: options?.progress || 0,
        actionId: options?.actionId,
      };
    });
  }, []);

  const stopLoading = useCallback((actionId?: string) => {
    setLoadingState(prev => {
      // Only stop loading if no actionId is specified or if it matches the current actionId
      if (!actionId || prev.actionId === actionId) {
        console.log(`SUCCESS Stopping loading for action: ${actionId || 'global'}`);
        return {
          isLoading: false,
          loadingType: null,
          message: '',
          subMessage: '',
          progress: 0,
          actionId: undefined,
        };
      }
      return prev;
    });
  }, []);

  const updateProgress = useCallback((progress: number) => {
    setLoadingState(prev => ({
      ...prev,
      progress: Math.max(0, Math.min(100, progress)),
    }));
  }, []);

  const updateMessage = useCallback((message: string, subMessage?: string) => {
    setLoadingState(prev => ({
      ...prev,
      message,
      subMessage: subMessage || prev.subMessage,
    }));
  }, []);

  const isActionLoading = useCallback((actionId: string) => {
    return loadingState.isLoading && loadingState.actionId === actionId;
  }, [loadingState]);

  // Handle navigation loading
  useEffect(() => {
    const handleRouteChangeStart = (url: string) => {
      console.log(`DEBUG Navigation started to: ${url}`);
      startLoading('navigation', 'Navigating...', {
        subMessage: 'Loading page content',
        actionId: 'navigation'
      });
    };

    const handleRouteChangeComplete = () => {
      console.log(`SUCCESS Navigation completed`);
      // Add a small delay to ensure the page is fully rendered
      setTimeout(() => {
        stopLoading('navigation');
      }, 300); // Reduced from 500ms to 300ms
    };

    const handleRouteChangeError = () => {
      console.log(`ERROR Navigation error`);
      stopLoading('navigation');
    };

    // Note: Next.js 13+ with app router doesn't have router events
    // We'll handle this differently in the components that trigger navigation
    
    return () => {
      // Cleanup if needed
    };
  }, [startLoading, stopLoading]);

  // Auto-stop loading after a maximum timeout to prevent stuck states
  useEffect(() => {
    if (loadingState.isLoading) {
      // Don't auto-timeout login loading - let the page handle it
      if (loadingState.actionId === 'user-login') {
        console.log('DEBUG Skipping auto-timeout for login loading');
        return;
      }
      
      // Different timeouts for different loading types
      const timeoutDuration = loadingState.loadingType === 'navigation' ? 5000 : 30000; // 5s for navigation, 30s for others
      
      const timeout = setTimeout(() => {
        console.warn(`WARNING Loading timeout reached for ${loadingState.loadingType}, auto-stopping loading`);
        stopLoading();
      }, timeoutDuration);

      return () => clearTimeout(timeout);
    }
  }, [loadingState.isLoading, loadingState.loadingType, loadingState.actionId, stopLoading]);

  const value: GlobalLoadingContextType = {
    loadingState,
    startLoading,
    stopLoading,
    updateProgress,
    updateMessage,
    isActionLoading,
  };

  return (
    <GlobalLoadingContext.Provider value={value}>
      {children}
    </GlobalLoadingContext.Provider>
  );
};

export const useGlobalLoading = (): GlobalLoadingContextType => {
  const context = useContext(GlobalLoadingContext);
  if (context === undefined) {
    throw new Error('useGlobalLoading must be used within a GlobalLoadingProvider');
  }
  return context;
};