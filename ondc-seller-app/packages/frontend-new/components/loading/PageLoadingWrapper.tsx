'use client';

import React, { useEffect, useState } from 'react';
import { useGlobalLoading } from './GlobalLoadingProvider';

interface PageLoadingWrapperProps {
  children: React.ReactNode;
  loadingMessage?: string;
  loadingSubMessage?: string;
  minLoadingTime?: number; // Minimum time to show loading (in ms)
  pageId?: string;
}

export const PageLoadingWrapper: React.FC<PageLoadingWrapperProps> = ({
  children,
  loadingMessage = 'Loading page...',
  loadingSubMessage = 'Please wait while we prepare the content',
  minLoadingTime = 0,
  pageId,
}) => {
  const { startLoading, stopLoading, loadingState, updateMessage } = useGlobalLoading();
  const [isReady, setIsReady] = useState(false);
  const [startTime] = useState(Date.now());

  useEffect(() => {
    const currentPageId = pageId || `page-${Date.now()}`;
    
    // Check if there's an ongoing login loading
    const isLoginLoading = loadingState.isLoading && loadingState.actionId === 'user-login';
    
    console.log('🔄 PageLoadingWrapper: Effect triggered', {
      pageId: currentPageId,
      isLoginLoading,
      loadingState: {
        isLoading: loadingState.isLoading,
        actionId: loadingState.actionId,
        loadingType: loadingState.loadingType,
        message: loadingState.message
      }
    });
    
    if (isLoginLoading) {
      // Continue the login loading with updated message
      console.log('🔄 PageLoadingWrapper: Continuing login loading with page-specific message');
      updateMessage(loadingMessage, loadingSubMessage);
    } else {
      // Start new page loading
      console.log('🔄 PageLoadingWrapper: Starting new page loading');
      startLoading('page', loadingMessage, {
        subMessage: loadingSubMessage,
        actionId: currentPageId
      });
    }

    // Simulate page loading and ensure minimum loading time
    const loadPage = async () => {
      console.log('🔄 PageLoadingWrapper: Starting loadPage function');
      
      // Wait for next tick to ensure component is mounted
      await new Promise(resolve => setTimeout(resolve, 100)); // Increased from 0 to 100ms
      
      // Calculate remaining time to meet minimum loading time
      const elapsed = Date.now() - startTime;
      const remainingTime = Math.max(0, minLoadingTime - elapsed);
      
      console.log('🔄 PageLoadingWrapper: Timing info', {
        elapsed,
        minLoadingTime,
        remainingTime
      });
      
      if (remainingTime > 0) {
        console.log(`🔄 PageLoadingWrapper: Waiting additional ${remainingTime}ms`);
        await new Promise(resolve => setTimeout(resolve, remainingTime));
      }
      
      console.log('🔄 PageLoadingWrapper: Setting isReady to true');
      setIsReady(true);
      
      // Stop the appropriate loading (login or page)
      // Re-check login loading status as it might have changed
      const currentIsLoginLoading = loadingState.isLoading && loadingState.actionId === 'user-login';
      
      if (currentIsLoginLoading || isLoginLoading) {
        console.log('✅ PageLoadingWrapper: Stopping login loading');
        stopLoading('user-login');
      } else {
        console.log('✅ PageLoadingWrapper: Stopping page loading');
        stopLoading(currentPageId);
      }
    };

    loadPage();

    // Cleanup function
    return () => {
      console.log('🧹 PageLoadingWrapper: Cleanup function called');
      // Don't stop loading in cleanup unless component is unmounting unexpectedly
      // The loadPage function should handle stopping the loading
    };
  }, [startLoading, stopLoading, updateMessage, loadingMessage, loadingSubMessage, minLoadingTime, pageId, startTime]);

  // Don't render children until ready
  if (!isReady) {
    return null;
  }

  return <>{children}</>;
};