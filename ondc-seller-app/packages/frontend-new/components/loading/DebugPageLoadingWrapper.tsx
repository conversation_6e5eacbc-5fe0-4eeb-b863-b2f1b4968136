'use client';

import React, { useEffect, useState } from 'react';
import { useGlobalLoading } from './GlobalLoadingProvider';

interface DebugPageLoadingWrapperProps {
  children: React.ReactNode;
  loadingMessage?: string;
  loadingSubMessage?: string;
  minLoadingTime?: number;
  pageId?: string;
}

export const DebugPageLoadingWrapper: React.FC<DebugPageLoadingWrapperProps> = ({
  children,
  loadingMessage = 'Loading page...',
  loadingSubMessage = 'Please wait while we prepare the content',
  minLoadingTime = 0,
  pageId,
}) => {
  const { startLoading, stopLoading, loadingState, updateMessage } = useGlobalLoading();
  const [isReady, setIsReady] = useState(false);
  const [startTime] = useState(Date.now());
  const [debugLog, setDebugLog] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const logMessage = `[${timestamp}] ${message}`;
    console.log(`DEBUG: ${logMessage}`);
    setDebugLog(prev => [...prev, logMessage]);
  };

  useEffect(() => {
    const currentPageId = pageId || `page-${Date.now()}`;
    
    addLog(`DebugPageLoadingWrapper: Effect triggered for pageId: ${currentPageId}`);
    addLog(`Current loading state: ${JSON.stringify(loadingState)}`);
    
    // Check if there's an ongoing login loading
    const isLoginLoading = loadingState.isLoading && loadingState.actionId === 'user-login';
    
    addLog(`Is login loading: ${isLoginLoading}`);
    
    if (isLoginLoading) {
      addLog('Continuing login loading with page-specific message');
      updateMessage(loadingMessage, loadingSubMessage);
    } else {
      addLog('Starting new page loading');
      startLoading('page', loadingMessage, {
        subMessage: loadingSubMessage,
        actionId: currentPageId
      });
    }

    // Simulate page loading and ensure minimum loading time
    const loadPage = async () => {
      addLog('Starting loadPage function');
      
      // Wait for next tick to ensure component is mounted
      await new Promise(resolve => setTimeout(resolve, 100));
      addLog('Component mount delay completed');
      
      // Calculate remaining time to meet minimum loading time
      const elapsed = Date.now() - startTime;
      const remainingTime = Math.max(0, minLoadingTime - elapsed);
      
      addLog(`Timing: elapsed=${elapsed}ms, minLoadingTime=${minLoadingTime}ms, remainingTime=${remainingTime}ms`);
      
      if (remainingTime > 0) {
        addLog(`Waiting additional ${remainingTime}ms`);
        await new Promise(resolve => setTimeout(resolve, remainingTime));
      }
      
      addLog('Setting isReady to true');
      setIsReady(true);
      
      // Stop the appropriate loading (login or page)
      const currentIsLoginLoading = loadingState.isLoading && loadingState.actionId === 'user-login';
      addLog(`Current login loading status: ${currentIsLoginLoading}`);
      
      if (currentIsLoginLoading || isLoginLoading) {
        addLog('Stopping login loading');
        stopLoading('user-login');
      } else {
        addLog('Stopping page loading');
        stopLoading(currentPageId);
      }
      
      addLog('loadPage function completed');
    };

    loadPage();

    // Cleanup function
    return () => {
      addLog('Cleanup function called');
    };
  }, [startLoading, stopLoading, updateMessage, loadingMessage, loadingSubMessage, minLoadingTime, pageId, startTime]);

  // Show debug info in development
  if (process.env.NODE_ENV === 'development' && !isReady) {
    return (
      <div className="fixed inset-0 bg-white z-50 p-4 overflow-auto">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold mb-4">Debug Page Loading Wrapper</h1>
          <div className="mb-4">
            <h2 className="text-lg font-semibold mb-2">Current State:</h2>
            <div className="bg-gray-100 p-3 rounded">
              <p><strong>isReady:</strong> {isReady.toString()}</p>
              <p><strong>pageId:</strong> {pageId}</p>
              <p><strong>loadingMessage:</strong> {loadingMessage}</p>
              <p><strong>minLoadingTime:</strong> {minLoadingTime}ms</p>
            </div>
          </div>
          
          <div className="mb-4">
            <h2 className="text-lg font-semibold mb-2">Loading State:</h2>
            <div className="bg-gray-100 p-3 rounded">
              <pre>{JSON.stringify(loadingState, null, 2)}</pre>
            </div>
          </div>
          
          <div>
            <h2 className="text-lg font-semibold mb-2">Debug Log:</h2>
            <div className="bg-black text-green-400 p-3 rounded font-mono text-sm max-h-96 overflow-auto">
              {debugLog.map((log, index) => (
                <div key={index}>{log}</div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Don't render children until ready
  if (!isReady) {
    return null;
  }

  return <>{children}</>;
};