'use client';\n\nimport React, { useEffect, useState } from 'react';\nimport { useGlobalLoading } from './GlobalLoadingProvider';\n\ninterface DebugPageLoadingWrapperProps {\n  children: React.ReactNode;\n  loadingMessage?: string;\n  loadingSubMessage?: string;\n  minLoadingTime?: number;\n  pageId?: string;\n}\n\nexport const DebugPageLoadingWrapper: React.FC<DebugPageLoadingWrapperProps> = ({\n  children,\n  loadingMessage = 'Loading page...',\n  loadingSubMessage = 'Please wait while we prepare the content',\n  minLoadingTime = 0,\n  pageId,\n}) => {\n  const { startLoading, stopLoading, loadingState, updateMessage } = useGlobalLoading();\n  const [isReady, setIsReady] = useState(false);\n  const [startTime] = useState(Date.now());\n  const [debugLog, setDebugLog] = useState<string[]>([]);\n\n  const addLog = (message: string) => {\n    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];\n    const logMessage = `[${timestamp}] ${message}`;\n    console.log(`🐛 ${logMessage}`);\n    setDebugLog(prev => [...prev, logMessage]);\n  };\n\n  useEffect(() => {\n    const currentPageId = pageId || `page-${Date.now()}`;\n    \n    addLog(`DebugPageLoadingWrapper: Effect triggered for pageId: ${currentPageId}`);\n    addLog(`Current loading state: ${JSON.stringify(loadingState)}`);\n    \n    // Check if there's an ongoing login loading\n    const isLoginLoading = loadingState.isLoading && loadingState.actionId === 'user-login';\n    \n    addLog(`Is login loading: ${isLoginLoading}`);\n    \n    if (isLoginLoading) {\n      addLog('Continuing login loading with page-specific message');\n      updateMessage(loadingMessage, loadingSubMessage);\n    } else {\n      addLog('Starting new page loading');\n      startLoading('page', loadingMessage, {\n        subMessage: loadingSubMessage,\n        actionId: currentPageId\n      });\n    }\n\n    // Simulate page loading and ensure minimum loading time\n    const loadPage = async () => {\n      addLog('Starting loadPage function');\n      \n      // Wait for next tick to ensure component is mounted\n      await new Promise(resolve => setTimeout(resolve, 100));\n      addLog('Component mount delay completed');\n      \n      // Calculate remaining time to meet minimum loading time\n      const elapsed = Date.now() - startTime;\n      const remainingTime = Math.max(0, minLoadingTime - elapsed);\n      \n      addLog(`Timing: elapsed=${elapsed}ms, minLoadingTime=${minLoadingTime}ms, remainingTime=${remainingTime}ms`);\n      \n      if (remainingTime > 0) {\n        addLog(`Waiting additional ${remainingTime}ms`);\n        await new Promise(resolve => setTimeout(resolve, remainingTime));\n      }\n      \n      addLog('Setting isReady to true');\n      setIsReady(true);\n      \n      // Stop the appropriate loading (login or page)\n      const currentIsLoginLoading = loadingState.isLoading && loadingState.actionId === 'user-login';\n      addLog(`Current login loading status: ${currentIsLoginLoading}`);\n      \n      if (currentIsLoginLoading || isLoginLoading) {\n        addLog('Stopping login loading');\n        stopLoading('user-login');\n      } else {\n        addLog('Stopping page loading');\n        stopLoading(currentPageId);\n      }\n      \n      addLog('loadPage function completed');\n    };\n\n    loadPage();\n\n    // Cleanup function\n    return () => {\n      addLog('Cleanup function called');\n    };\n  }, [startLoading, stopLoading, updateMessage, loadingMessage, loadingSubMessage, minLoadingTime, pageId, startTime]);\n\n  // Show debug info in development\n  if (process.env.NODE_ENV === 'development' && !isReady) {\n    return (\n      <div className=\"fixed inset-0 bg-white z-50 p-4 overflow-auto\">\n        <div className=\"max-w-4xl mx-auto\">\n          <h1 className=\"text-2xl font-bold mb-4\">Debug Page Loading Wrapper</h1>\n          <div className=\"mb-4\">\n            <h2 className=\"text-lg font-semibold mb-2\">Current State:</h2>\n            <div className=\"bg-gray-100 p-3 rounded\">\n              <p><strong>isReady:</strong> {isReady.toString()}</p>\n              <p><strong>pageId:</strong> {pageId}</p>\n              <p><strong>loadingMessage:</strong> {loadingMessage}</p>\n              <p><strong>minLoadingTime:</strong> {minLoadingTime}ms</p>\n            </div>\n          </div>\n          \n          <div className=\"mb-4\">\n            <h2 className=\"text-lg font-semibold mb-2\">Loading State:</h2>\n            <div className=\"bg-gray-100 p-3 rounded\">\n              <pre>{JSON.stringify(loadingState, null, 2)}</pre>\n            </div>\n          </div>\n          \n          <div>\n            <h2 className=\"text-lg font-semibold mb-2\">Debug Log:</h2>\n            <div className=\"bg-black text-green-400 p-3 rounded font-mono text-sm max-h-96 overflow-auto\">\n              {debugLog.map((log, index) => (\n                <div key={index}>{log}</div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Don't render children until ready\n  if (!isReady) {\n    return null;\n  }\n\n  return <>{children}</>;\n};"