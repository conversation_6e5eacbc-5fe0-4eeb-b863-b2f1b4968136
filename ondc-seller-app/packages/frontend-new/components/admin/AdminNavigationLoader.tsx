'use client';

import React, { useState, useEffect } from 'react';
import { useGlobalLoading } from '../loading/GlobalLoadingProvider';
import { LoadingSpinner } from '../ui/LoadingSpinner';

export const AdminNavigationLoader: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { loadingState } = useGlobalLoading();

  useEffect(() => {
    const handleNavigationStart = () => {
      console.log('Navigation started - showing loader');
      setIsLoading(true);
    };

    const handleNavigationEnd = () => {
      console.log('Navigation ended - hiding loader');
      setIsLoading(false);
    };

    // Listen for custom navigation events
    window.addEventListener('admin-navigation-start', handleNavigationStart);
    window.addEventListener('admin-navigation-end', handleNavigationEnd);

    // Also listen for Next.js router events if available
    const handleRouteChangeStart = () => {
      console.log('Route change started');
      setIsLoading(true);
    };

    const handleRouteChangeComplete = () => {
      console.log('Route change completed');
      setIsLoading(false);
    };

    const handleRouteChangeError = () => {
      console.log('Route change error');
      setIsLoading(false);
    };

    // Add event listeners for Next.js router events
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', handleNavigationStart);
    }

    return () => {
      window.removeEventListener('admin-navigation-start', handleNavigationStart);
      window.removeEventListener('admin-navigation-end', handleNavigationEnd);
      window.removeEventListener('beforeunload', handleNavigationStart);
    };
  }, []);

  // Show loading if either local state or global loading state indicates loading
  // But only show for admin-specific loading (not login/logout loading, which is handled by LoadingOverlay)
  const shouldShowLoading = isLoading || (loadingState.isLoading && loadingState.loadingType === 'navigation' && loadingState.actionId !== 'user-login' && loadingState.actionId !== 'user-logout');

  if (!shouldShowLoading) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-[9999] flex items-center justify-center">
      <div className="bg-white rounded-lg p-6 shadow-xl flex items-center space-x-4">
        {/* Use consistent loading spinner */}
        <LoadingSpinner size="md" text="" />
        
        {/* Loading text */}
        <div>
          <p className="text-lg font-medium text-gray-900">
            {loadingState.message || 'Loading...'}
          </p>
          <p className="text-sm text-gray-500">
            {loadingState.subMessage || 'Please wait while we load the page'}
          </p>
        </div>
      </div>
    </div>
  );
};