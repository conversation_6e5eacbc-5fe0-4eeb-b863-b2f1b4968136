'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Tab,
  Tabs,
  Grid,
  InputAdornment,
  IconButton,
  Alert,
  CircularP<PERSON>ress,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from '@mui/material';
// Custom icon components to avoid @mui/icons-material dependency
const VisibilityIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
  </svg>
);

const VisibilityOffIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7zM2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3 2 4.27zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2zm4.31-.78l3.15 3.15.02-.16c0-1.66-1.34-3-3-3l-.17.01z"/>
  </svg>
);

const EmailIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
  </svg>
);

const LockIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z"/>
  </svg>
);

const PersonIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
  </svg>
);

const StoreIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z"/>
  </svg>
);

const PhoneIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
  </svg>
);

const BusinessIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 7V3H2v18h20V7H12zM6 19H4v-2h2v2zm0-4H4v-2h2v2zm0-4H4V9h2v2zm0-4H4V5h2v2zm4 12H8v-2h2v2zm0-4H8v-2h2v2zm0-4H8V9h2v2zm0-4H8V5h2v2zm10 12h-8v-2h2v-2h-2v-2h2v-2h-2V9h8v10zm-2-8h-2v2h2v-2zm0 4h-2v2h2v-2z"/>
  </svg>
);
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { loginSchema, registerSchema, LoginFormData, RegisterFormData } from '@/validators/auth';
import { generateStoreHandle, checkStoreHandleAvailability } from '@/lib/utils/storeHandle';
import { useToast } from '@/app/providers/toast-provider';
import { authApi } from '@/lib/api/auth';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';
import { useGlobalLoading } from '../loading/GlobalLoadingProvider';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`auth-tabpanel-${index}`}
      aria-labelledby={`auth-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3, width: '100%', overflow: 'hidden' }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface AuthScreenProps {
  defaultTab?: 0 | 1; // 0 for login, 1 for register
}

export default function AuthScreen({ defaultTab = 0 }: AuthScreenProps) {
  const searchParams = useSearchParams();
  const tabFromUrl = searchParams.get('tab');
  
  // Determine initial tab value: URL param > defaultTab prop
  const initialTab = tabFromUrl === 'login' ? 0 : tabFromUrl === 'signup' ? 1 : defaultTab;
  const [tabValue, setTabValue] = useState(initialTab);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [storeHandleLoading, setStoreHandleLoading] = useState(false);
  const [storeHandleAvailable, setStoreHandleAvailable] = useState<boolean | null>(null);
  const { showToast } = useToast();
  const router = useRouter();
  const { setAuth, getStoreHandle } = useAuthStore();
  const { startLoading, stopLoading, updateMessage, loadingState } = useGlobalLoading();

  // Login form
  const loginForm = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  // Register form
  const registerForm = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      storeName: '',
      storeHandle: '',
      contactNumber: '',
      password: '',
      confirmPassword: '',
    },
  });

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    
    // Update URL to reflect the current tab
    const tabParam = newValue === 0 ? 'login' : 'signup';
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set('tab', tabParam);
    window.history.replaceState({}, '', currentUrl.toString());
    
    // Clear forms when switching tabs
    loginForm.reset();
    registerForm.reset();
    setStoreHandleAvailable(null);
  };

  // Update tab when URL parameters change (but not when triggered by manual tab change)
  useEffect(() => {
    const tabFromUrl = searchParams.get('tab');
    const newTab = tabFromUrl === 'login' ? 0 : tabFromUrl === 'signup' ? 1 : defaultTab;
    setTabValue(newTab);
  }, [searchParams, defaultTab]);

  // Auto-generate store handle when store name changes
  const watchStoreName = registerForm.watch('storeName');
  useEffect(() => {
    if (watchStoreName) {
      const generatedHandle = generateStoreHandle(watchStoreName);
      registerForm.setValue('storeHandle', generatedHandle);
      
      // Check availability if handle is valid
      if (generatedHandle.length >= 3) {
        checkHandleAvailability(generatedHandle);
      }
    }
  }, [watchStoreName, registerForm]);

  const checkHandleAvailability = async (handle: string) => {
    if (handle.length < 3) return;
    
    setStoreHandleLoading(true);
    try {
      const isAvailable = await checkStoreHandleAvailability(handle);
      setStoreHandleAvailable(isAvailable);
    } catch (error) {
      console.error('Error checking handle availability:', error);
    } finally {
      setStoreHandleLoading(false);
    }
  };


  const onLoginSubmit = async (data: LoginFormData) => {
    setIsLoading(true);
    
    // Start global loading for the entire login process
    startLoading('navigation', 'Signing you in...', {
      subMessage: 'Please wait while we authenticate your credentials',
      actionId: 'user-login'
    });
        
    try {
      
      updateMessage('Authenticating...', 'Verifying your credentials');
      // Step 1: Login API call
      const loginResponse = await authApi.login({
        email: data.email,
        password: data.password,
      });
      
      console.log('=== LOGIN RESPONSE DEBUG ===');
      console.log('Full login response:', loginResponse);
      console.log('Response type:', typeof loginResponse);
      console.log('Response keys:', loginResponse ? Object.keys(loginResponse) : 'No response');
      console.log('Response JSON:', JSON.stringify(loginResponse, null, 2));
      
      // Check if response exists
      if (!loginResponse) {
        throw new Error('No response received from login API');
      }
      
      // Extract token from different possible field names and nested objects
      let token = null;
      
      // Direct token fields
      token = loginResponse.access_token || 
              loginResponse.token || 
              loginResponse.accessToken || 
              loginResponse.authToken ||
              loginResponse.jwt;
      
      // Check nested objects for token
      if (!token && loginResponse.data) {
        token = loginResponse.data.access_token || 
                loginResponse.data.token || 
                loginResponse.data.accessToken || 
                loginResponse.data.authToken ||
                loginResponse.data.jwt;
      }
      
      // Check if response has user object with token
      if (!token && loginResponse.user) {
        token = loginResponse.user.access_token || 
                loginResponse.user.token || 
                loginResponse.user.accessToken || 
                loginResponse.user.authToken ||
                loginResponse.user.jwt;
      }
      
      // Check for auth object
      if (!token && loginResponse.auth) {
        token = loginResponse.auth.access_token || 
                loginResponse.auth.token || 
                loginResponse.auth.accessToken || 
                loginResponse.auth.authToken ||
                loginResponse.auth.jwt;
      }
      
      console.log('=== TOKEN EXTRACTION ===');
      console.log('Extracted token:', token);
      console.log('Token type:', typeof token);
      console.log('Token length:', token ? token.length : 'No token');
      
      if (!token) {
        console.error('=== TOKEN NOT FOUND ===');
        console.error('Available fields in response:', Object.keys(loginResponse));
        console.error('Full response structure:', JSON.stringify(loginResponse, null, 2));
        
        // Try to find any field that looks like a token (long string)
        const possibleTokens = Object.entries(loginResponse)
          .filter(([key, value]) => 
            typeof value === 'string' && 
            value.length > 20 && 
            (key.toLowerCase().includes('token') || key.toLowerCase().includes('auth'))
          );
        
        console.error('Possible token fields found:', possibleTokens);
        
        throw new Error(`No access token received from login. Available fields: ${Object.keys(loginResponse).join(', ')}`);
      }
      
      console.log('=== FETCHING USER DETAILS ===');
      updateMessage('Loading profile...', 'Fetching your account details');
      // Step 2: Get user details with the token
      const userDetails = await authApi.getUserDetails(token);
      
      console.log('User details fetched:', userDetails);
   
      // Step 3: Store auth data in Zustand (which will persist to localStorage)
      console.log('=== STORING AUTH DATA IN ZUSTAND ===');
      console.log('Token to store:', token);
      console.log('User details to store:', userDetails);
      setAuth(token, userDetails);
      
      // Step 4: Also store token in localStorage for axios interceptor
      localStorage.setItem('ondc_auth_token', token);
      
      // Step 5: Verify auth was stored correctly
      // setTimeout(() => {
      //   const authState = useAuthStore.getState();
      //   console.log('=== VERIFYING AUTH STORAGE ===');
      //   console.log('Auth state after setAuth:', {
      //     token: authState.token ? 'exists' : 'null',
      //     user: authState.user ? 'exists' : 'null',
      //     isAuthenticated: authState.isAuthenticated,
      //     hasHydrated: authState.hasHydrated
      //   });
      //   console.log('localStorage auth-storage:', localStorage.getItem('auth-storage'));
      // }, 100);
      
      // Step 5: Check onboarding status
      console.log('=== CHECKING ONBOARDING STATUS ===');
      updateMessage('Checking setup status...', 'Determining your next steps');
      console.log('Full userDetails structure:', JSON.stringify(userDetails, null, 2));
      
      // Extract onboarding status from the nested user object
      const onboardingStatus = 
        userDetails.user?.metadata?.onboarding_status || 
        userDetails.metadata?.onboarding_status || 
        userDetails.onboarding_status;
      
      console.log('Onboarding status paths checked:');
      console.log('  userDetails.user?.metadata?.onboarding_status:', userDetails.user?.metadata?.onboarding_status);
      console.log('  userDetails.metadata?.onboarding_status:', userDetails.metadata?.onboarding_status);
      console.log('  userDetails.onboarding_status:', userDetails.onboarding_status);
      console.log('Final onboarding status:', onboardingStatus);
      
      // Step 6: Show success toast
      showToast('Successfully logged in!', 'success');
      
      // Step 7: Redirect based on onboarding status
      if (onboardingStatus === 'pending') {
        console.log('=== REDIRECTING TO ONBOARDING ===');
        console.log('User has pending onboarding, redirecting to onboarding screen');
        
        updateMessage('Redirecting to setup...', 'Taking you to complete your store setup');
        
        // Wait a moment for the toast to show, then redirect
        // Note: Don't stop loading here - let the onboarding page handle it
        setTimeout(() => {
          router.push('/onboarding');
          
          // Safety timeout to stop loading if page doesn't handle it
          setTimeout(() => {
            console.log('⚠️ AuthScreen: Safety timeout - stopping login loading');
            stopLoading('user-login');
          }, 10000); // 10 second safety timeout
        }, 800);

        return; // Exit early to prevent admin redirect
      }
      
      console.log('=== USER DETAILS FETCH SUCCESS - ONBOARDING COMPLETED - REDIRECTING TO ADMIN ===');
      console.log('User details to be stored:', userDetails);
      console.log('Onboarding status is completed, proceeding to admin dashboard');
      
      // Wait a moment for Zustand to update, then get store handle
      // await new Promise(resolve => setTimeout(resolve, 100));
      
      // Get store handle from Zustand store (which now has the user data)
      const storeHandleFromStore = getStoreHandle();
      
      console.log('=== STORE HANDLE EXTRACTION DEBUG ===');
      console.log('Store handle from getStoreHandle():', storeHandleFromStore);
      console.log('Direct from userDetails.store_handle:', userDetails.store_handle);
      console.log('Direct from userDetails.metadata?.store_handle:', userDetails.metadata?.store_handle);
      console.log('Direct from userDetails.user?.metadata?.store_handle:', userDetails.user?.metadata?.store_handle);
      console.log('Direct from userDetails.user?.store_handle:', userDetails.user?.store_handle);
      console.log('Store handle from Zustand:', storeHandleFromStore);
      console.log('Available user data in store:', {
        store_handle: userDetails.store_handle,
        metadata: userDetails.metadata,
        user: userDetails.user
      });
      
      // Determine which store handle to use (prioritize nested user data)
      const directStoreHandle = 
        userDetails.user?.metadata?.store_handle ||
        userDetails.user?.store_handle ||
        userDetails.store_handle || 
        userDetails.metadata?.store_handle;
      const finalStoreHandle = directStoreHandle || storeHandleFromStore;
      
      console.log('=== FINAL STORE HANDLE DECISION ===');
      console.log('Direct store handle:', directStoreHandle);
      console.log('Zustand store handle:', storeHandleFromStore);
      console.log('Final store handle to use:', finalStoreHandle);
      
      // Make Strapi API call to fetch store configuration
      // if (finalStoreHandle) {
      //   try {
      //     console.log('=== MAKING STRAPI API CALL ===');
      //     console.log('Using store handle:', finalStoreHandle);
      //     const storeConfig = await authApi.getStoreConfiguration(finalStoreHandle);
      //     console.log('Store configuration fetched successfully:', storeConfig);
      //     return;

      //   } catch (error) {
      //     console.error('Error fetching store configuration from Strapi:', error);
      //     // Continue with redirect even if Strapi call fails
      //   }
        
      //   // Redirect to admin dashboard with store handle
      //   updateMessage('Redirecting to dashboard...', 'Taking you to your store admin panel');
      //   console.log(`Redirecting to: /${finalStoreHandle}/admin`);
        
      //   // Add a small delay to ensure auth state is fully updated
      //   // setTimeout(() => {
      //   //   console.log('=== FINAL AUTH STATE BEFORE REDIRECT ===');
      //   //   const finalAuthState = useAuthStore.getState();
      //   //   console.log('Final auth state:', {
      //   //     token: finalAuthState.token ? 'exists' : 'null',
      //   //     user: finalAuthState.user ? 'exists' : 'null',
      //   //     isAuthenticated: finalAuthState.isAuthenticated,
      //   //     hasHydrated: finalAuthState.hasHydrated
      //   //   });
          
      //   // }, 200);
      //   router.push(`/${finalStoreHandle}/admin`);
      //   // setTimeout(() => {
      //     // Note: Loading will be stopped by the admin page when it's ready
      //   // }, 500);
      // } 
        updateMessage('Redirecting to dashboard...', 'Taking you to your store admin panel');
        
        // Note: Don't stop loading here - let the admin page handle it
        setTimeout(() => {
          router.push(`/${finalStoreHandle}/admin`);
          
          // Safety timeout to stop loading if page doesn't handle it
          setTimeout(() => {
            console.log('⚠️ AuthScreen: Safety timeout - stopping login loading');
            stopLoading('user-login');
          }, 10000); // 10 second safety timeout
        }, 800);
      
    } catch (error: any) {
      console.error('Login error:', error);
      
      // Handle different types of errors
      let errorMessage = 'Login failed. Please try again.';
      
      if (error.response) {
        // Server responded with error status
        const status = error.response.status;
        const data = error.response.data;
        
        if (status === 401) {
          errorMessage = 'Invalid email or password. Please check your credentials.';
        } else if (status === 403) {
          errorMessage = 'Access denied. You may not have the required permissions.';
        } else if (status === 400) {
          errorMessage = data.message || 'Invalid request. Please check your input.';
        } else if (status === 500) {
          errorMessage = 'Server error. Please try again later.';
        } else {
          errorMessage = data.message || `Error ${status}: ${error.response.statusText}`;
        }
      } else if (error.request) {
        // Network error
        errorMessage = 'Network error. Please check your connection and try again.';
      } else if (error.message) {
        // Custom error messages
        errorMessage = error.message;
      }
      
      // Show error toast
      showToast(errorMessage, 'error');
      
      // Clear any partial auth state on error
      localStorage.removeItem('ondc_auth_token');
      
      // Stop loading on error
      stopLoading('user-login');
    } finally {
      setIsLoading(false);
    }
  };

  const onRegisterSubmit = async (data: RegisterFormData) => {
    setIsLoading(true);
    try {
      const response = await authApi.signup({
        email: data.email,
        password: data.password,
        firstName: data.firstName,
        lastName: data.lastName,
        storeName: data.storeName,
        storeHandle: data.storeHandle,
      });
      
      // Show success toast
      showToast('Account created successfully! Please login to continue.', 'success');
      
      // Log the response for debugging
      console.log('Registration successful:', response);
      
      // Clear the form
      registerForm.reset();
      
      // Redirect to login screen with login tab selected after a short delay to show the toast
      setTimeout(() => {
        router.push('/login?tab=login');
      }, 1500);
      
    } catch (error: any) {
      console.error('Registration error:', error);
      
      // Handle different types of errors
      let errorMessage = 'Registration failed. Please try again.';
      
      if (error.response) {
        // Server responded with error status
        const status = error.response.status;
        const data = error.response.data;
        
        if (status === 400) {
          if (data.message) {
            errorMessage = data.message;
          } else if (data.errors) {
            // Handle validation errors
            const errors = Array.isArray(data.errors) ? data.errors : [data.errors];
            errorMessage = errors.join(', ');
          } else {
            errorMessage = 'Invalid registration data. Please check your input.';
          }
        } else if (status === 409) {
          errorMessage = 'Email or store handle already exists. Please choose different ones.';
        } else if (status === 422) {
          errorMessage = 'Validation failed. Please check your input and try again.';
        } else if (status === 500) {
          errorMessage = 'Server error. Please try again later.';
        } else {
          errorMessage = data.message || `Error ${status}: ${error.response.statusText}`;
        }
      } else if (error.request) {
        // Network error
        errorMessage = 'Network error. Please check your connection and try again.';
      }
      
      // Show error toast
      showToast(errorMessage, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        p: 2,
        position: 'relative',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'radial-gradient(circle at 30% 20%, rgba(102, 126, 234, 0.3) 0%, transparent 50%), radial-gradient(circle at 70% 80%, rgba(118, 75, 162, 0.3) 0%, transparent 50%)',
          zIndex: 0,
        },
      }}
    >
      <Card
        sx={{
          maxWidth: { xs: '90vw', sm: 500 },
          width: '100%',
          minWidth: { xs: 320, sm: 400 },
          position: 'relative',
          zIndex: 1,
          backdropFilter: 'blur(10px)',
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
        }}
      >
        <CardContent sx={{ p: { xs: 3, sm: 4 }, width: '100%', boxSizing: 'border-box' }}>
          {/* Logo/Brand */}
          <Box sx={{ textAlign: 'center', mb: 3 }}>
            <Box
              sx={{
                width: 60,
                height: 60,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mx: 'auto',
                mb: 2,
              }}
            >
              <StoreIcon />
            </Box>
            <Typography variant="h4" component="h1" fontWeight="bold" color="black" className='!text-black'>
              OneStore
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Create your online store in minutes
            </Typography>
          </Box>

          {/* Tabs */}
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            variant="fullWidth"
            sx={{ mb: 2 }}
          >
            <Tab label="Login" />
            <Tab label="Sign Up" />
          </Tabs>

          {/* Login Tab */}
          <TabPanel value={tabValue} index={0}>
            <form onSubmit={loginForm.handleSubmit(onLoginSubmit)}>
              <Grid container spacing={3} sx={{ width: '100%' }}>
                <Grid item size={12}>
                  <Controller
                    name="email"
                    control={loginForm.control}
                    render={({ field, fieldState }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Email Address"
                        type="email"
                        error={!!fieldState.error}
                        helperText={fieldState.error?.message}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <EmailIcon />
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </Grid>

                <Grid item size={12}>
                  <Controller
                    name="password"
                    control={loginForm.control}
                    render={({ field, fieldState }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Password"
                        type={showPassword ? 'text' : 'password'}
                        error={!!fieldState.error}
                        helperText={fieldState.error?.message}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <LockIcon />
                            </InputAdornment>
                          ),
                          endAdornment: (
                            <InputAdornment position="end">
                              <IconButton
                                onClick={() => setShowPassword(!showPassword)}
                                edge="end"
                              >
                                {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                              </IconButton>
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </Grid>

                <Grid item size={6}>
                  <Box sx={{ textAlign: 'right', mb: 2 , display:'flex',justifyContent:"end",height:"100%",alignItems:"center"}}>
                    <Link
                      href="#"
                      variant="body2"
                      color="primary"
                      sx={{ textDecoration: 'none', '&:hover': { textDecoration: 'underline' } }}
                    >
                      Forgot Password?
                    </Link>
                  </Box>
                </Grid>

                <Grid item size={6}>
                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    size="large"
                    disabled={isLoading}
                    sx={{
                      py: 1.5,
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      '&:hover': {
                        background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                      },
                    }}
                  >
                    {isLoading ? <CircularProgress size={24} color="inherit" /> : 'Login'}
                  </Button>
                </Grid>
              </Grid>
            </form>
          </TabPanel>

          {/* Register Tab */}
          <TabPanel value={tabValue} index={1}>
            <form onSubmit={registerForm.handleSubmit(onRegisterSubmit)}>
              <Grid container spacing={3} sx={{ width: '100%' }}>
                <Grid item size={12}>
                  <Controller
                    name="firstName"
                    control={registerForm.control}
                    render={({ field, fieldState }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="First Name"
                        error={!!fieldState.error}
                        helperText={fieldState.error?.message}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <PersonIcon />
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </Grid>

                <Grid item size={12}>
                  <Controller
                    name="lastName"
                    control={registerForm.control}
                    render={({ field, fieldState }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Last Name"
                        error={!!fieldState.error}
                        helperText={fieldState.error?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid item size={12}>
                  <Controller
                    name="email"
                    control={registerForm.control}
                    render={({ field, fieldState }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Email Address"
                        type="email"
                        error={!!fieldState.error}
                        helperText={fieldState.error?.message}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <EmailIcon />
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </Grid>

                <Grid item size={12}>
                  <Controller
                    name="storeName"
                    control={registerForm.control}
                    render={({ field, fieldState }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Store Name"
                        error={!!fieldState.error}
                        helperText={fieldState.error?.message}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <BusinessIcon />
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </Grid>

                <Grid item size={12}>
                  <Controller
                    name="storeHandle"
                    control={registerForm.control}
                    render={({ field, fieldState }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Store Handle"
                        error={!!fieldState.error}
                        helperText={
                          fieldState.error?.message ||
                          (storeHandleAvailable === true && 'Handle is available!') ||
                          (storeHandleAvailable === false && 'Handle is already taken') ||
                          'This will be your store URL: yourstore.com/store-handle'
                        }
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <Typography variant="body2" color="text.secondary">
                                @
                              </Typography>
                            </InputAdornment>
                          ),
                          endAdornment: storeHandleLoading && (
                            <InputAdornment position="end">
                              <CircularProgress size={20} />
                            </InputAdornment>
                          ),
                        }}
                        color={
                          storeHandleAvailable === true
                            ? 'success'
                            : storeHandleAvailable === false
                            ? 'error'
                            : 'primary'
                        }
                      />
                    )}
                  />
                </Grid>

                <Grid item size={12}>
                  <Controller
                    name="contactNumber"
                    control={registerForm.control}
                    render={({ field, fieldState }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Contact Number"
                        error={!!fieldState.error}
                        helperText={fieldState.error?.message}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <PhoneIcon />
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </Grid>

                <Grid item size={12}>
                  <Controller
                    name="password"
                    control={registerForm.control}
                    render={({ field, fieldState }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Password"
                        type={showPassword ? 'text' : 'password'}
                        error={!!fieldState.error}
                        helperText={fieldState.error?.message}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <LockIcon />
                            </InputAdornment>
                          ),
                          endAdornment: (
                            <InputAdornment position="end">
                              <IconButton
                                onClick={() => setShowPassword(!showPassword)}
                                edge="end"
                              >
                                {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                              </IconButton>
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </Grid>

                <Grid item size={12}>
                  <Controller
                    name="confirmPassword"
                    control={registerForm.control}
                    render={({ field, fieldState }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Confirm Password"
                        type={showConfirmPassword ? 'text' : 'password'}
                        error={!!fieldState.error}
                        helperText={fieldState.error?.message}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <LockIcon />
                            </InputAdornment>
                          ),
                          endAdornment: (
                            <InputAdornment position="end">
                              <IconButton
                                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                edge="end"
                              >
                                {showConfirmPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                              </IconButton>
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </Grid>

                <Grid item size={12}>
                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    size="large"
                    disabled={isLoading || storeHandleAvailable === false}
                    sx={{
                      py: 1.5,
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      '&:hover': {
                        background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                      },
                    }}
                  >
                    {isLoading ? <CircularProgress size={24} color="inherit" /> : 'Create Account'}
                  </Button>
                </Grid>
              </Grid>
            </form>
          </TabPanel>

          <Divider sx={{ my: 3 }} />

          <Typography variant="body2" color="text.secondary" textAlign="center">
            By continuing, you agree to our Terms of Service and Privacy Policy
          </Typography>
          
          {/* Debug info in development */}
          {process.env.NODE_ENV === 'development' && (
            <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
              <Typography variant="caption" component="div" sx={{ mb: 1, fontWeight: 'bold' }}>
                Debug - Loading State:
              </Typography>
              <Typography variant="caption" component="pre" sx={{ fontSize: '10px', whiteSpace: 'pre-wrap' }}>
                {JSON.stringify({
                  isLoading: loadingState.isLoading,
                  loadingType: loadingState.loadingType,
                  actionId: loadingState.actionId,
                  message: loadingState.message,
                  subMessage: loadingState.subMessage
                }, null, 2)}
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );
}