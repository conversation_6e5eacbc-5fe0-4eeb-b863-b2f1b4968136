'use client';

import React from 'react';
import { AdminDashboard } from '@/components/admin/AdminDashboard';
import { DebugPageLoadingWrapper } from '@/components/loading/DebugPageLoadingWrapper';

export default function AdminPage() {
  return (
    <DebugPageLoadingWrapper 
      loadingMessage="Loading admin dashboard..."
      loadingSubMessage="Setting up your store management panel"
      pageId="store-admin-page"
      minLoadingTime={1000}
    >
      <AdminDashboard />
    </DebugPageLoadingWrapper>
  );
}