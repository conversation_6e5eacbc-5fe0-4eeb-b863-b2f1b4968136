'use client';

import AuthScreen from '@/components/auth/AuthScreen';
import { Suspense, useEffect } from 'react';
import { CircularProgress, Box } from '@mui/material';
import { useGlobalLoading } from '@/components/loading/GlobalLoadingProvider';

function LoginPageContent() {
  const { loadingState, stopLoading } = useGlobalLoading();
  
  // Immediate check for logout loading
  const isLogoutLoading = loadingState.isLoading && loadingState.actionId === 'user-logout';
  
  useEffect(() => {
    console.log('DEBUG LoginPage: Component mounted', {
      loadingState: {
        isLoading: loadingState.isLoading,
        actionId: loadingState.actionId,
        loadingType: loadingState.loadingType,
        message: loadingState.message
      },
      isLogoutLoading
    });
    
    // Stop logout loading immediately when component mounts
    if (loadingState.isLoading && loadingState.actionId === 'user-logout') {
      console.log('DEBUG LoginPage: Detected logout loading on mount, stopping it immediately');
      stopLoading('user-logout');
    }
  }, [loadingState.isLoading, loadingState.actionId, stopLoading]); // Proper dependencies
  
  // Also watch for loading state changes
  useEffect(() => {
    console.log('DEBUG LoginPage: Loading state changed', {
      isLoading: loadingState.isLoading,
      actionId: loadingState.actionId,
      message: loadingState.message
    });
    
    // Stop logout loading if it appears after mount
    if (loadingState.isLoading && loadingState.actionId === 'user-logout') {
      console.log('DEBUG LoginPage: Detected logout loading after mount, stopping it');
      setTimeout(() => {
        stopLoading('user-logout');
      }, 100);
    }
  }, [loadingState.isLoading, loadingState.actionId, stopLoading]);
  
  console.log('DEBUG LoginPage: Rendering AuthScreen');
  return (
    <div>
      {/* Debug info in development */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{ 
          position: 'fixed', 
          top: 10, 
          right: 10, 
          background: 'rgba(0,0,0,0.8)', 
          color: 'white', 
          padding: '8px', 
          borderRadius: '4px', 
          fontSize: '12px',
          zIndex: 100000
        }}>
          Login Page Loaded | Loading: {loadingState.isLoading ? 'Yes' : 'No'} | Action: {loadingState.actionId || 'None'}
        </div>
      )}
      <AuthScreen defaultTab={0} />
    </div>
  );
}

export default function LoginPage() {
  return (
    <Suspense fallback={
      <Box sx={{ 
        minHeight: '100vh', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}>
        <CircularProgress size={60} sx={{ color: 'white' }} />
      </Box>
    }>
      <LoginPageContent />
    </Suspense>
  );
}