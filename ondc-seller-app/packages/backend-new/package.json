{"name": "@ondc-seller/backend-new", "version": "0.0.1", "description": "Fresh Medusa backend for ONDC Seller App", "author": "ONDC Team", "license": "MIT", "keywords": ["medusa", "ecommerce", "ondc"], "scripts": {"clean": "cross-env rim<PERSON>f dist", "build": "cd medusa-store && npm run build", "build:server": "cd medusa-store && npm run build:server", "build:admin": "cd medusa-store && npm run build:admin", "watch": "cd medusa-store && npm run watch", "test": "cd medusa-store && npm run test", "seed": "cd medusa-store && npm run seed", "start": "cd medusa-store && npm run start", "start:custom": "cd medusa-store && npm run start:custom", "dev": "cd medusa-store && npm run dev", "migrations": "cd medusa-store && npm run migrations", "migrations:generate": "cd medusa-store && npm run migrations:generate"}, "dependencies": {"@medusajs/admin": "^7.1.14", "@medusajs/cache-inmemory": "^1.8.10", "@medusajs/cache-redis": "^1.9.0", "@medusajs/event-bus-local": "^1.9.7", "@medusajs/event-bus-redis": "^1.8.13", "@medusajs/file-local": "^1.0.3", "@medusajs/medusa": "^1.20.6", "@medusajs/medusa-cli": "^1.3.22", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "16.4.5", "express": "^4.18.2", "medusa-fulfillment-manual": "^1.1.40", "medusa-interfaces": "^1.3.9", "medusa-payment-manual": "^1.0.24", "medusa-payment-stripe": "^6.0.6", "pg": "^8.11.3", "prism-react-renderer": "^2.0.4", "typeorm": "^0.3.16"}, "devDependencies": {"@babel/cli": "^7.23.4", "@babel/core": "^7.23.6", "@babel/preset-typescript": "^7.23.3", "@medusajs/medusa-cli": "^1.3.22", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/node": "^20.10.4", "@types/pg": "^8.10.9", "babel-preset-medusa-package": "^1.1.19", "cross-env": "^7.0.3", "eslint": "^8.55.0", "jest": "^29.7.0", "rimraf": "^5.0.10", "ts-jest": "^29.1.1", "ts-loader": "^9.5.1", "typescript": "^5.3.3"}, "jest": {"globals": {"ts-jest": {"tsconfig": "tsconfig.json"}}, "moduleFileExtensions": ["js", "json", "ts"], "testPathIgnorePatterns": ["/node_modules/", "<rootDir>/node_modules/"], "rootDir": "src", "testRegex": "(/__tests__/.*|\\.(test|spec))\\.(ts|js)$", "transform": {".ts": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node"}}