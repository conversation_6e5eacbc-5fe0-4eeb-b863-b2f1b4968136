{"name": "@ondc-seller/ondc-adapter", "version": "0.0.1", "private": true, "description": "ONDC Protocol Adapter Service for translating between Medusa Commerce and ONDC network", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:unit": "jest --testPathPattern=src/.*\\.test\\.ts$", "test:integration": "jest --testPathPattern=tests/integration/.*\\.test\\.ts$", "test:coverage": "jest --coverage", "test:ci": "jest --coverage --ci --watchAll=false --passWithNoTests", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "rimraf dist coverage", "api:docs": "swagger-jsdoc -d swaggerDef.js src/**/*.ts -o docs/openapi.yaml", "docker:build": "docker build -t ondc-adapter .", "docker:run": "docker run -p 3001:3001 ondc-adapter"}, "dependencies": {"@medusajs/medusa-js": "^6.1.10", "axios": "^1.9.0", "bull": "^4.12.2", "compression": "^1.7.4", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "fast-xml-parser": "^4.3.2", "helmet": "^7.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "morgan": "^1.10.0", "node-cron": "^3.0.3", "redis": "^4.6.10", "tsconfig-paths": "^4.2.0", "uuid": "^9.0.1", "winston": "^3.11.0", "xml2js": "^0.6.2"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/joi": "^17.2.3", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.202", "@types/morgan": "^1.9.9", "@types/node": "^20.10.5", "@types/node-cron": "^3.0.11", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "@types/xml2js": "^0.4.14", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.56.0", "jest": "^29.7.0", "jest-watch-typeahead": "^2.2.2", "rimraf": "^5.0.5", "supertest": "^6.3.4", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "ts-jest": "^29.3.4", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}, "keywords": ["ondc", "e-commerce", "protocol-adapter", "medusa", "typescript"], "author": "ONDC Seller Team", "license": "MIT"}