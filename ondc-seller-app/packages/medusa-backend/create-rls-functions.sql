-- PostgreSQL RLS Helper Functions for Multi-Tenancy
-- Task 1.1: Create PostgreSQL RLS Helper Functions
-- This script implements the core RLS helper functions required for tenant isolation

-- 1. Create the main helper function for getting current tenant ID
-- This function is used in RLS policies to get the tenant context
CREATE OR REPLACE FUNCTION current_app_tenant()
RETURNS VARCHAR(255) AS $$
BEGIN
  -- Get tenant_id from PostgreSQL session variable
  -- This is set by the tenant middleware for each request
  RETURN current_setting('app.current_tenant_id', TRUE);
EXCEPTION
  WHEN OTHERS THEN
    -- If no tenant is set, return NULL (will block access)
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Create function to safely set tenant context
-- Used by middleware to set tenant context for the session
CREATE OR REPLACE FUNCTION set_tenant_context(tenant_id VARCHAR(255))
RETURNS VOID AS $$
BEGIN
  -- Validate tenant_id is not empty
  IF tenant_id IS NULL OR trim(tenant_id) = '' THEN
    RAISE EXCEPTION 'Tenant ID cannot be null or empty';
  END IF;
  
  -- Set the session variable for RLS policies
  PERFORM set_config('app.current_tenant_id', tenant_id, TRUE);
  
  -- Log tenant context setting for debugging
  RAISE NOTICE 'Tenant context set to: %', tenant_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Create function to clear tenant context
-- Used for cleanup and testing purposes
CREATE OR REPLACE FUNCTION clear_tenant_context()
RETURNS VOID AS $$
BEGIN
  -- Clear the session variable
  PERFORM set_config('app.current_tenant_id', '', TRUE);
  RAISE NOTICE 'Tenant context cleared';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Create function to get current tenant context (for debugging)
CREATE OR REPLACE FUNCTION get_current_tenant_context()
RETURNS VARCHAR(255) AS $$
BEGIN
  RETURN current_setting('app.current_tenant_id', TRUE);
EXCEPTION
  WHEN OTHERS THEN
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Create function to validate tenant access
-- Used in RLS policies to ensure proper tenant isolation
CREATE OR REPLACE FUNCTION validate_tenant_access(record_tenant_id VARCHAR(255))
RETURNS BOOLEAN AS $$
DECLARE
  current_tenant VARCHAR(255);
BEGIN
  -- Get current tenant from session
  current_tenant := current_app_tenant();
  
  -- If no tenant context is set, deny access
  IF current_tenant IS NULL OR trim(current_tenant) = '' THEN
    RETURN FALSE;
  END IF;
  
  -- If record has no tenant_id, allow access (global data)
  IF record_tenant_id IS NULL THEN
    RETURN TRUE;
  END IF;
  
  -- Check if current tenant matches record tenant
  RETURN current_tenant = record_tenant_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Create function for RLS policy enforcement logging
-- Helps with debugging and monitoring RLS policy effectiveness
CREATE OR REPLACE FUNCTION log_rls_access(
  table_name VARCHAR(255),
  operation VARCHAR(10),
  record_tenant_id VARCHAR(255),
  allowed BOOLEAN
)
RETURNS VOID AS $$
DECLARE
  current_tenant VARCHAR(255);
BEGIN
  current_tenant := current_app_tenant();
  
  -- Log access attempt (in development mode)
  IF current_setting('app.environment', TRUE) = 'development' THEN
    RAISE NOTICE 'RLS Access: table=%, op=%, current_tenant=%, record_tenant=%, allowed=%',
      table_name, operation, current_tenant, record_tenant_id, allowed;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Create function to check if current user is system/admin
-- Used for admin operations that might need to bypass tenant restrictions
CREATE OR REPLACE FUNCTION is_system_user()
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if current tenant is 'system' or 'admin'
  RETURN current_app_tenant() IN ('system', 'admin');
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Grant necessary permissions to the application database user
-- Ensure the functions can be executed by the application
GRANT EXECUTE ON FUNCTION current_app_tenant() TO PUBLIC;
GRANT EXECUTE ON FUNCTION set_tenant_context(VARCHAR) TO PUBLIC;
GRANT EXECUTE ON FUNCTION clear_tenant_context() TO PUBLIC;
GRANT EXECUTE ON FUNCTION get_current_tenant_context() TO PUBLIC;
GRANT EXECUTE ON FUNCTION validate_tenant_access(VARCHAR) TO PUBLIC;
GRANT EXECUTE ON FUNCTION log_rls_access(VARCHAR, VARCHAR, VARCHAR, BOOLEAN) TO PUBLIC;
GRANT EXECUTE ON FUNCTION is_system_user() TO PUBLIC;

-- Test the functions
SELECT 'RLS Helper Functions created successfully' as status;
