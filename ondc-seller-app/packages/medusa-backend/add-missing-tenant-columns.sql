-- Task 1.5: Add Missing tenant_id Columns
-- Add tenant_id columns to remaining tables that need multi-tenant support

-- ============================================================================
-- CORE BUSINESS TABLES
-- ============================================================================

-- Cart table (main cart entity)
ALTER TABLE cart 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(255) NOT NULL DEFAULT 'default';

-- Cart related tables
ALTER TABLE cart_address 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(255) NOT NULL DEFAULT 'default';

ALTER TABLE cart_shipping_method 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(255) NOT NULL DEFAULT 'default';

-- ============================================================================
-- INVENTORY AND PRICING TABLES
-- ============================================================================

-- Inventory item table
ALTER TABLE inventory_item 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(255) NOT NULL DEFAULT 'default';

-- Price list table
ALTER TABLE price_list 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(255) NOT NULL DEFAULT 'default';

-- Price set table
ALTER TABLE price_set 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(255) NOT NULL DEFAULT 'default';

-- Product variant price set (junction table)
ALTER TABLE product_variant_price_set 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(255) NOT NULL DEFAULT 'default';

-- ============================================================================
-- PRODUCT OPTION TABLES
-- ============================================================================

-- Product option table
ALTER TABLE product_option 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(255) NOT NULL DEFAULT 'default';

-- Product option value table
ALTER TABLE product_option_value 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(255) NOT NULL DEFAULT 'default';

-- ============================================================================
-- ORDER RELATED TABLES
-- ============================================================================

-- Order address table
ALTER TABLE order_address 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(255) NOT NULL DEFAULT 'default';

-- Order shipping method table
ALTER TABLE order_shipping_method 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(255) NOT NULL DEFAULT 'default';

-- ============================================================================
-- PROMOTION AND MARKETING TABLES
-- ============================================================================

-- Promotion table
ALTER TABLE promotion 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(255) NOT NULL DEFAULT 'default';

-- ============================================================================
-- LOGISTICS AND FULFILLMENT TABLES
-- ============================================================================

-- Stock location table
ALTER TABLE stock_location 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(255) NOT NULL DEFAULT 'default';

-- Shipping option table
ALTER TABLE shipping_option 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(255) NOT NULL DEFAULT 'default';

-- Shipping profile table
ALTER TABLE shipping_profile 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(255) NOT NULL DEFAULT 'default';

-- ============================================================================
-- TAX TABLES
-- ============================================================================

-- Tax rate table
ALTER TABLE tax_rate 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(255) NOT NULL DEFAULT 'default';

-- Tax region table
ALTER TABLE tax_region 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(255) NOT NULL DEFAULT 'default';

-- ============================================================================
-- RETURN AND REFUND TABLES
-- ============================================================================

-- Return table
ALTER TABLE "return" 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(255) NOT NULL DEFAULT 'default';

-- Return item table
ALTER TABLE return_item 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(255) NOT NULL DEFAULT 'default';

-- Refund table
ALTER TABLE refund 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(255) NOT NULL DEFAULT 'default';

-- ============================================================================
-- NOTIFICATION TABLE
-- ============================================================================

-- Notification table
ALTER TABLE notification 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(255) NOT NULL DEFAULT 'default';

SELECT 'Added tenant_id columns to all missing tables' as result;
