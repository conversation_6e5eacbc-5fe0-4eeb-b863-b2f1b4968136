#!/usr/bin/env node

/**
 * Fix Existing Products Shipping Profile Script
 *
 * This script assigns shipping profiles to existing products that don't have them,
 * ensuring they can be used in checkout without shipping profile errors.
 */

const { Client } = require('pg');
require('dotenv').config();

const DEFAULT_SHIPPING_PROFILE_ID =
  process.env.DEFAULT_SHIPPING_PROFILE_ID || 'sp_01JZ4VNZYVNWS3976TD64VQ423';

async function fixExistingProductsShipping() {
  console.log('🚚 Starting shipping profile fix for existing products...');
  console.log(`📦 Default Shipping Profile: ${DEFAULT_SHIPPING_PROFILE_ID}`);

  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    await client.connect();
    console.log('✅ Database connected');

    // Step 1: Verify default shipping profile exists
    console.log('\n📋 Step 1: Verifying default shipping profile...');
    const profileResult = await client.query(
      'SELECT id, name, type FROM shipping_profile WHERE id = $1;',
      [DEFAULT_SHIPPING_PROFILE_ID]
    );

    if (profileResult.rows.length === 0) {
      console.error(`❌ Default shipping profile ${DEFAULT_SHIPPING_PROFILE_ID} not found!`);
      process.exit(1);
    }

    console.log(
      `✅ Default shipping profile found: ${profileResult.rows[0].name} (${profileResult.rows[0].type})`
    );

    // Step 2: Find products without shipping profiles
    console.log('\n📋 Step 2: Finding products without shipping profiles...');
    const productsWithoutProfilesQuery = `
      SELECT p.id, p.title
      FROM product p
      LEFT JOIN product_shipping_profile psp ON p.id = psp.product_id
      WHERE psp.product_id IS NULL 
        AND p.deleted_at IS NULL
      ORDER BY p.created_at DESC;
    `;

    const productsResult = await client.query(productsWithoutProfilesQuery);
    console.log(`Found ${productsResult.rows.length} products without shipping profiles`);

    if (productsResult.rows.length === 0) {
      console.log('✅ All products already have shipping profiles assigned!');
      await client.end();
      return;
    }

    let fixedCount = 0;
    let errorCount = 0;

    // Step 3: Assign shipping profiles to products
    console.log('\n🔧 Step 3: Assigning shipping profiles...');

    for (const product of productsResult.rows) {
      try {
        console.log(`\n🔍 Processing product: ${product.title} (${product.id})`);

        // Create shipping profile association
        const associationId = `psp_${product.id.slice(5)}_${DEFAULT_SHIPPING_PROFILE_ID.slice(3)}`;

        await client.query(
          `
          INSERT INTO product_shipping_profile (id, product_id, shipping_profile_id, created_at, updated_at)
          VALUES ($1, $2, $3, NOW(), NOW());
        `,
          [associationId, product.id, DEFAULT_SHIPPING_PROFILE_ID]
        );

        console.log(`  ✅ Shipping profile assigned successfully`);
        fixedCount++;
      } catch (error) {
        console.error(`  ❌ Error processing product ${product.id}:`, error.message);
        errorCount++;
      }
    }

    // Step 4: Verify shipping options exist
    console.log('\n🚚 Step 4: Verifying shipping options...');
    const shippingOptionsQuery = `
      SELECT so.id, so.name, so.shipping_profile_id
      FROM shipping_option so
      WHERE so.shipping_profile_id = $1;
    `;

    const optionsResult = await client.query(shippingOptionsQuery, [DEFAULT_SHIPPING_PROFILE_ID]);

    if (optionsResult.rows.length > 0) {
      console.log(
        `✅ Found ${optionsResult.rows.length} shipping options for the default profile:`
      );
      optionsResult.rows.forEach(option => {
        console.log(`  - ${option.name}`);
      });
    } else {
      console.log('⚠️  No shipping options found for the default profile');
      console.log('   This may cause checkout issues. Consider creating shipping options.');
    }

    await client.end();

    // Summary
    console.log('\n🎉 Shipping profile fix completed!');
    console.log(`📊 Summary:`);
    console.log(`  - Products processed: ${productsResult.rows.length}`);
    console.log(`  - Successfully fixed: ${fixedCount}`);
    console.log(`  - Errors: ${errorCount}`);

    if (fixedCount > 0) {
      console.log(`\n✅ ${fixedCount} products can now be used in checkout!`);
    }
  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  }
}

// Test function to verify a specific product can be checked out
async function testProductCheckout(productId) {
  console.log(`\n🧪 Testing checkout capability for product: ${productId}`);

  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    await client.connect();

    // Check if product has shipping profile
    const profileResult = await client.query(
      `
      SELECT psp.shipping_profile_id, sp.name as profile_name
      FROM product_shipping_profile psp
      LEFT JOIN shipping_profile sp ON psp.shipping_profile_id = sp.id
      WHERE psp.product_id = $1;
    `,
      [productId]
    );

    if (profileResult.rows.length > 0) {
      console.log(`✅ Product has shipping profile: ${profileResult.rows[0].profile_name}`);

      // Check if shipping options exist for this profile
      const optionsResult = await client.query(
        `
        SELECT so.name, r.name as region_name
        FROM shipping_option so
        LEFT JOIN region r ON so.region_id = r.id
        WHERE so.shipping_profile_id = $1;
      `,
        [profileResult.rows[0].shipping_profile_id]
      );

      if (optionsResult.rows.length > 0) {
        console.log(`✅ Shipping options available:`);
        optionsResult.rows.forEach(option => {
          console.log(`  - ${option.name} (${option.region_name})`);
        });
        console.log(`✅ Product should be able to checkout successfully!`);
      } else {
        console.log(`❌ No shipping options found for this profile`);
      }
    } else {
      console.log(`❌ Product has no shipping profile assigned`);
    }

    await client.end();
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the script
if (require.main === module) {
  const args = process.argv.slice(2);

  if (args[0] === 'test' && args[1]) {
    // Test mode for specific product
    testProductCheckout(args[1])
      .then(() => {
        console.log('\n✅ Test completed');
        process.exit(0);
      })
      .catch(error => {
        console.error('\n❌ Test failed:', error);
        process.exit(1);
      });
  } else {
    // Normal fix mode
    fixExistingProductsShipping()
      .then(() => {
        console.log('\n✅ Script completed successfully');
        process.exit(0);
      })
      .catch(error => {
        console.error('\n❌ Script failed:', error);
        process.exit(1);
      });
  }
}

module.exports = { fixExistingProductsShipping, testProductCheckout };
