#!/usr/bin/env node

const { Client } = require('pg');

async function debugProducts() {
  const client = new Client({
    connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
  });

  try {
    await client.connect();
    console.log('🔍 Connected to database, checking products...\n');

    // Check total products
    const totalResult = await client.query('SELECT COUNT(*) as total FROM product');
    console.log(`📊 Total products in database: ${totalResult.rows[0].total}`);

    // Check products by tenant_id
    const tenantResult = await client.query(`
      SELECT 
        tenant_id, 
        COUNT(*) as count 
      FROM product 
      GROUP BY tenant_id 
      ORDER BY count DESC
    `);
    
    console.log('\n🏢 Products by tenant_id:');
    tenantResult.rows.forEach(row => {
      console.log(`  - ${row.tenant_id || 'NULL'}: ${row.count} products`);
    });

    // Check first few products with details
    const sampleResult = await client.query(`
      SELECT id, title, tenant_id, created_at 
      FROM product 
      ORDER BY created_at DESC 
      LIMIT 5
    `);
    
    console.log('\n📋 Sample products:');
    sampleResult.rows.forEach(row => {
      console.log(`  - ID: ${row.id}`);
      console.log(`    Title: ${row.title}`);
      console.log(`    Tenant: ${row.tenant_id || 'NULL'}`);
      console.log(`    Created: ${row.created_at}`);
      console.log('');
    });

    // Check if there are products with tenant_id = 'default'
    const defaultResult = await client.query(`
      SELECT COUNT(*) as count 
      FROM product 
      WHERE tenant_id = 'default'
    `);
    console.log(`🎯 Products with tenant_id = 'default': ${defaultResult.rows[0].count}`);

    // Check if there are products with NULL tenant_id
    const nullResult = await client.query(`
      SELECT COUNT(*) as count 
      FROM product 
      WHERE tenant_id IS NULL
    `);
    console.log(`❓ Products with NULL tenant_id: ${nullResult.rows[0].count}`);

    // Check specifically for my-kirana-store tenant
    const kiranaResult = await client.query(`
      SELECT COUNT(*) as count 
      FROM product 
      WHERE tenant_id = 'my-kirana-store'
    `);
    console.log(`🏪 Products with tenant_id = 'my-kirana-store': ${kiranaResult.rows[0].count}`);

    // Show all products for my-kirana-store tenant
    const kiranaProductsResult = await client.query(`
      SELECT id, title, handle, tenant_id, created_at 
      FROM product 
      WHERE tenant_id = 'my-kirana-store'
      ORDER BY created_at DESC
    `);
    
    console.log('\n🏪 Products for my-kirana-store tenant:');
    if (kiranaProductsResult.rows.length === 0) {
      console.log('  ❌ No products found for my-kirana-store tenant');
    } else {
      kiranaProductsResult.rows.forEach(row => {
        console.log(`  - ID: ${row.id}`);
        console.log(`    Title: ${row.title}`);
        console.log(`    Handle: ${row.handle}`);
        console.log(`    Tenant: ${row.tenant_id}`);
        console.log(`    Created: ${row.created_at}`);
        console.log('');
      });
    }

  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await client.end();
  }
}

debugProducts().catch(console.error);