#!/usr/bin/env node

/**
 * Test Authenticated Cart Completion Workflow
 * 
 * This script tests the complete authenticated cart completion workflow:
 * 1. Create cart with authentication
 * 2. Add products to cart
 * 3. Complete cart with authentication (CRITICAL!)
 * 4. Retrieve order with authentication
 * 5. Verify customer can access their own order
 */

const BASE_URL = 'http://localhost:9000';
const TENANT_ID = 'kisan-connect';
const PUBLISHABLE_KEY = 'pk_3d67561dece2d466dc798c18c1f80523f84f3b2f01316e0bf915e51f3a59b98b';

// Use the existing JWT token from the failing request
const AUTH_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY3Rvcl9pZCI6ImN1c18wMUszNVYzWlBIWE42R041VEdSTlRHWlJXRyIsImFjdG9yX3R5cGUiOiJjdXN0b21lciIsImF1dGhfaWRlbnRpdHlfaWQiOiJhdXRoaWRfMDFLMzVWMjk3WUIyUVdQVFlQR1JRQjg3Q1ciLCJhcHBfbWV0YWRhdGEiOnsiY3VzdG9tZXJfaWQiOiJjdXNfMDFLMzVWM1pQSFhONkdONVRHUk5UR1pSV0cifSwiaWF0IjoxNzU2MTE5ODk0LCJleHAiOjE3NTYyMDYyOTR9.RqT6ggoU35ZhFiINiaW4iNV8Wk_uBVM6mHpWG-DE0Q0';
const EXPECTED_CUSTOMER_ID = 'cus_01K35V3ZPHXN6GN5TGRNTGZRWG';

async function makeRequest(url, options = {}) {
  const fetch = (await import('node-fetch')).default;
  
  const defaultHeaders = {
    'Content-Type': 'application/json',
    'x-tenant-id': TENANT_ID,
    'x-publishable-api-key': PUBLISHABLE_KEY,
  };

  const response = await fetch(url, {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  });

  const data = await response.json();
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${JSON.stringify(data)}`);
  }

  return { data, status: response.status };
}

async function testAuthenticatedCartCompletion() {
  console.log('🧪 Testing Authenticated Cart Completion Workflow...');
  console.log(`🏢 Tenant: ${TENANT_ID}`);
  console.log(`👤 Customer: ${EXPECTED_CUSTOMER_ID}`);
  console.log(`🔑 Using existing JWT token`);

  try {
    // Step 1: Create a cart with authentication
    console.log('\n📋 Step 1: Creating authenticated cart...');
    const cartResponse = await makeRequest(`${BASE_URL}/store/carts`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`, // ✅ WITH AUTHENTICATION
      },
      body: JSON.stringify({
        region_id: 'reg_01JZWRHZMZXE395C69Q60FXNT4',
        sales_channel_id: 'sc_01K33ENQEWWEMQNT0WDXAHMCWD',
      }),
    });

    const cartId = cartResponse.data.cart.id;
    console.log(`✅ Authenticated cart created: ${cartId}`);

    // Step 2: Add products to cart
    console.log('\n📋 Step 2: Adding products to cart...');
    
    // Add first product
    await makeRequest(`${BASE_URL}/store/carts/${cartId}/line-items`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
      },
      body: JSON.stringify({
        variant_id: 'variant_1754662247227_9s6bvmf', // Apple
        quantity: 1,
      }),
    });

    // Add second product
    await makeRequest(`${BASE_URL}/store/carts/${cartId}/line-items`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
      },
      body: JSON.stringify({
        variant_id: 'variant_1754978391564_2tuwhk7', // Mango
        quantity: 2,
      }),
    });

    console.log('✅ Products added to cart');

    // Step 3: Complete cart with authentication (CRITICAL!)
    console.log('\n📋 Step 3: Completing cart WITH AUTHENTICATION...');
    console.log('🔑 This is the critical step - cart completion MUST include Authorization header');
    
    const orderResponse = await makeRequest(`${BASE_URL}/store/carts/${cartId}/complete`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`, // ✅ CRITICAL: WITH AUTHENTICATION
      },
    });

    const orderId = orderResponse.data.order.id;
    const orderCustomerId = orderResponse.data.order.customer_id;
    const orderTenantId = orderResponse.data.order.tenant_id;

    console.log(`✅ Order created: ${orderId}`);
    console.log(`📊 Order details:`);
    console.log(`  - Customer ID: ${orderCustomerId}`);
    console.log(`  - Tenant ID: ${orderTenantId}`);
    console.log(`  - Total: ${orderResponse.data.order.total}`);

    // Verify associations
    if (orderCustomerId === EXPECTED_CUSTOMER_ID) {
      console.log('✅ Customer association CORRECT');
    } else {
      console.log(`❌ Customer association WRONG: expected ${EXPECTED_CUSTOMER_ID}, got ${orderCustomerId}`);
    }

    if (orderTenantId === TENANT_ID) {
      console.log('✅ Tenant association CORRECT');
    } else {
      console.log(`❌ Tenant association WRONG: expected ${TENANT_ID}, got ${orderTenantId}`);
    }

    // Step 4: Retrieve order with authentication
    console.log('\n📋 Step 4: Retrieving order with authentication...');
    const retrievedOrderResponse = await makeRequest(`${BASE_URL}/store/orders/${orderId}`, {
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
      },
    });

    console.log('🎉 SUCCESS! Order retrieved successfully!');
    console.log(`📊 Retrieved order:`, {
      id: retrievedOrderResponse.data.order.id,
      customer_id: retrievedOrderResponse.data.order.customer_id,
      tenant_id: retrievedOrderResponse.data.order.tenant_id,
      status: retrievedOrderResponse.data.order.status,
      total: retrievedOrderResponse.data.order.total,
      items_count: retrievedOrderResponse.data.order.items.length,
    });

    // Step 5: Verify database state
    console.log('\n📋 Step 5: Verifying database state...');
    const { Client } = require('pg');
    require('dotenv').config();
    
    const client = new Client({ 
      connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
    });
    await client.connect();

    const dbResult = await client.query(
      'SELECT id, customer_id, tenant_id, email, status FROM "order" WHERE id = $1;',
      [orderId]
    );

    if (dbResult.rows.length > 0) {
      const dbOrder = dbResult.rows[0];
      console.log('✅ Database verification successful:');
      console.log(`  - Order ID: ${dbOrder.id}`);
      console.log(`  - Customer ID: ${dbOrder.customer_id}`);
      console.log(`  - Tenant ID: ${dbOrder.tenant_id}`);
      console.log(`  - Email: ${dbOrder.email}`);
      console.log(`  - Status: ${dbOrder.status}`);

      if (dbOrder.customer_id === EXPECTED_CUSTOMER_ID && dbOrder.tenant_id === TENANT_ID) {
        console.log('🎉 PERFECT! Order has proper customer and tenant association!');
      } else {
        console.log('⚠️  Order missing proper associations');
      }
    }

    await client.end();

    console.log('\n🎉 AUTHENTICATED CART COMPLETION TEST COMPLETED SUCCESSFULLY!');
    console.log('✅ The workflow now works correctly when authentication is included!');
    console.log('');
    console.log('🔑 KEY LESSON:');
    console.log('   Cart completion requests MUST include the Authorization header');
    console.log('   when the customer is authenticated to ensure proper order associations.');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
if (require.main === module) {
  testAuthenticatedCartCompletion()
    .then(() => {
      console.log('\n✅ Test script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test script failed:', error);
      process.exit(1);
    });
}

module.exports = { testAuthenticatedCartCompletion };
