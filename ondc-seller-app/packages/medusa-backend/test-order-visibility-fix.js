#!/usr/bin/env node

/**
 * Test Order Visibility Fix
 * 
 * This script tests the complete order visibility fix by:
 * 1. Creating a new cart with authentication
 * 2. Adding a product to the cart
 * 3. Completing the cart with COD (using updated endpoint)
 * 4. Retrieving the order using the store API
 * 5. Verifying customer can access their own order
 */

const { Client } = require('pg');
require('dotenv').config();

const BASE_URL = 'http://localhost:9000';
const TENANT_ID = 'my-kirana-store';
const PUBLISHABLE_KEY = 'pk_3d67561dece2d466dc798c18c1f80523f84f3b2f01316e0bf915e51f3a59b98b';

// Test customer credentials (you may need to update these)
const TEST_CUSTOMER = {
  email: '<EMAIL>',
  password: 'password123'
};

async function makeRequest(url, options = {}) {
  const fetch = (await import('node-fetch')).default;
  
  const defaultHeaders = {
    'Content-Type': 'application/json',
    'x-tenant-id': TENANT_ID,
    'x-publishable-api-key': PUBLISHABLE_KEY,
  };

  const response = await fetch(url, {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  });

  const data = await response.json();
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${JSON.stringify(data)}`);
  }

  return { data, status: response.status };
}

async function testOrderVisibilityFix() {
  console.log('🧪 Testing Order Visibility Fix...');
  console.log(`🏢 Tenant: ${TENANT_ID}`);
  console.log(`🔑 Publishable Key: ${PUBLISHABLE_KEY}`);

  try {
    // Step 1: Create or authenticate customer
    console.log('\n📋 Step 1: Customer Authentication...');
    
    let authToken;
    try {
      // Try to login first
      const loginResponse = await makeRequest(`${BASE_URL}/store/auth`, {
        method: 'POST',
        body: JSON.stringify({
          email: TEST_CUSTOMER.email,
          password: TEST_CUSTOMER.password,
        }),
      });
      authToken = loginResponse.data.token;
      console.log('✅ Customer logged in successfully');
    } catch (error) {
      console.log('⚠️  Login failed, trying to register customer...');
      
      try {
        // Register new customer
        const registerResponse = await makeRequest(`${BASE_URL}/store/customers`, {
          method: 'POST',
          body: JSON.stringify({
            email: TEST_CUSTOMER.email,
            password: TEST_CUSTOMER.password,
            first_name: 'Test',
            last_name: 'Customer',
          }),
        });
        
        // Now login
        const loginResponse = await makeRequest(`${BASE_URL}/store/auth`, {
          method: 'POST',
          body: JSON.stringify({
            email: TEST_CUSTOMER.email,
            password: TEST_CUSTOMER.password,
          }),
        });
        authToken = loginResponse.data.token;
        console.log('✅ Customer registered and logged in successfully');
      } catch (regError) {
        console.error('❌ Customer registration/login failed:', regError.message);
        return;
      }
    }

    // Step 2: Create a cart
    console.log('\n📋 Step 2: Creating cart...');
    const cartResponse = await makeRequest(`${BASE_URL}/store/carts`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify({
        region_id: 'reg_01JZ4VN8WNQHQHQHQHQHQHQHQH', // You may need to update this
        sales_channel_id: 'sc_01K33ENQEWWEMQNT0WDXAHMCWD',
      }),
    });

    const cartId = cartResponse.data.cart.id;
    console.log(`✅ Cart created: ${cartId}`);

    // Step 3: Add a product to cart
    console.log('\n📋 Step 3: Adding product to cart...');
    
    // First, get a product to add
    const productsResponse = await makeRequest(`${BASE_URL}/store/products?limit=1`);
    
    if (!productsResponse.data.products || productsResponse.data.products.length === 0) {
      console.error('❌ No products found to add to cart');
      return;
    }

    const product = productsResponse.data.products[0];
    const variant = product.variants[0];
    
    console.log(`📦 Adding product: ${product.title} (${variant.id})`);

    await makeRequest(`${BASE_URL}/store/carts/${cartId}/line-items`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify({
        variant_id: variant.id,
        quantity: 1,
      }),
    });

    console.log('✅ Product added to cart');

    // Step 4: Complete cart with COD (using updated endpoint)
    console.log('\n📋 Step 4: Completing cart with COD...');
    const orderResponse = await makeRequest(`${BASE_URL}/store/carts/${cartId}/complete-cod`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    const orderId = orderResponse.data.order.id;
    console.log(`✅ Order created: ${orderId}`);
    console.log(`📊 Order details:`, {
      id: orderId,
      customer_id: orderResponse.data.order.customer_id,
      tenant_id: orderResponse.data.order.tenant_id,
      total: orderResponse.data.order.total,
    });

    // Step 5: Retrieve order using store API
    console.log('\n📋 Step 5: Retrieving order via store API...');
    const retrievedOrderResponse = await makeRequest(`${BASE_URL}/store/orders/${orderId}`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    console.log('🎉 SUCCESS! Order retrieved successfully!');
    console.log(`📊 Retrieved order:`, {
      id: retrievedOrderResponse.data.order.id,
      customer_id: retrievedOrderResponse.data.order.customer_id,
      tenant_id: retrievedOrderResponse.data.order.tenant_id,
      status: retrievedOrderResponse.data.order.status,
      total: retrievedOrderResponse.data.order.total,
    });

    // Step 6: Verify database state
    console.log('\n📋 Step 6: Verifying database state...');
    const client = new Client({ connectionString: process.env.DATABASE_URL });
    await client.connect();

    const dbResult = await client.query(
      'SELECT id, customer_id, tenant_id, email, status FROM "order" WHERE id = $1;',
      [orderId]
    );

    if (dbResult.rows.length > 0) {
      const dbOrder = dbResult.rows[0];
      console.log('✅ Database verification successful:');
      console.log(`  - Order ID: ${dbOrder.id}`);
      console.log(`  - Customer ID: ${dbOrder.customer_id}`);
      console.log(`  - Tenant ID: ${dbOrder.tenant_id}`);
      console.log(`  - Email: ${dbOrder.email}`);
      console.log(`  - Status: ${dbOrder.status}`);

      if (dbOrder.customer_id && dbOrder.tenant_id) {
        console.log('🎉 PERFECT! Order has proper customer and tenant association!');
      } else {
        console.log('⚠️  Order missing customer or tenant association');
      }
    }

    await client.end();

    console.log('\n🎉 Order Visibility Fix Test COMPLETED SUCCESSFULLY!');
    console.log('✅ Customers can now retrieve their own orders!');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
if (require.main === module) {
  testOrderVisibilityFix()
    .then(() => {
      console.log('\n✅ Test script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test script failed:', error);
      process.exit(1);
    });
}

module.exports = { testOrderVisibilityFix };
