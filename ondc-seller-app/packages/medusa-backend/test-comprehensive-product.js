#!/usr/bin/env node

const axios = require('axios');

const BASE_URL = 'http://localhost:9000';
const ADMIN_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY3Rvcl9pZCI6InVzZXJfMDFKWjRWVkdFSlg2S1RRM1JaTUI3OFk1TTEiLCJhY3Rvcl90eXBlIjoidXNlciIsImF1dGhfaWRlbnRpdHlfaWQiOiJhdXRoaWRfMDFKWjRWVkdKSlRLVzNSWUdNTUUwRVg0NDMiLCJhcHBfbWV0YWRhdGEiOnsidXNlcl9pZCI6InVzZXJfMDFKWjRWVkdFSlg2S1RRM1JaTUI3OFk1TTEifSwiaWF0IjoxNzU0NDYzNTcxLCJleHAiOjE3NTQ1NDk5NzF9.8QuHH1MajzPppiHgz0yHRamCtzsgC0GIqc9sWcmlryo';

async function testComprehensiveProduct() {
  console.log('🧪 Testing Comprehensive Product Creation');
  console.log('========================================\n');

  // Your exact payload from the curl command
  const productPayload = {
    "title": "kirana product 3",
    "handle": "kirana-product-3",
    "description": "test description for product 3",
    "status": "draft",
    "categories": [],
    "collection_id": "",
    "tags": [],
    "options": [
      {
        "title": "default",
        "values": ["default"]
      }
    ],
    "metadata": {
      "additional_data": {
        "product_prices": [
          {
            "sale_price": 799,
            "original_price": 899
          }
        ],
        "product_quantity": 8,
        "product_inventory_status": "in_stock",
        "product_overview": "Product Overview for kirana product 3",
        "product_features": "Product Features for kirana product 3",
        "product_specifications": "Product Specification for kirana product 3"
      }
    },
    "variants": [
      {
        "title": "default",
        "sku": "default-kirana-prd-001",
        "material": null,
        "weight": null,
        "width": null,
        "length": null,
        "height": null,
        "metadata": {
          "sale_price": 799,
          "original_price": 899,
          "product_quantity": 10,
          "product_inventory_status": "in_stock"
        },
        "prices": [
          {
            "currency_code": "inr",
            "amount": 899
          }
        ]
      }
    ],
    "images": [
      {
        "url": "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?q=80&w=1170&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
      }
    ],
    "thumbnail": "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?q=80&w=1170&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
  };

  try {
    console.log('📦 Creating comprehensive product with your exact payload...');
    console.log('🏷️  Tenant: my-kirana-store');
    console.log('📋 Payload includes:');
    console.log(`   - Title: ${productPayload.title}`);
    console.log(`   - Variants: ${productPayload.variants.length}`);
    console.log(`   - Images: ${productPayload.images.length}`);
    console.log(`   - Options: ${productPayload.options.length}`);
    console.log(`   - Thumbnail: ${productPayload.thumbnail ? 'Yes' : 'No'}`);
    console.log(`   - Metadata: ${Object.keys(productPayload.metadata).length} keys`);

    const response = await axios.post(`${BASE_URL}/admin/products`, productPayload, {
      headers: {
        'Authorization': `Bearer ${ADMIN_TOKEN}`,
        'Content-Type': 'application/json',
        'x-tenant-id': 'my-kirana-store'
      }
    });

    const createdProduct = response.data.product;
    const tenantInfo = response.data._tenant;

    console.log('\n✅ Product created successfully!');
    console.log('================================');
    console.log(`🆔 Product ID: ${createdProduct.id}`);
    console.log(`🏷️  Title: ${createdProduct.title}`);
    console.log(`🔗 Handle: ${createdProduct.handle}`);
    console.log(`📝 Description: ${createdProduct.description}`);
    console.log(`📊 Status: ${createdProduct.status}`);
    console.log(`🏢 Tenant ID: ${createdProduct.tenant_id}`);
    console.log(`🖼️  Thumbnail: ${createdProduct.thumbnail ? 'Set' : 'Not Set'}`);

    console.log('\n📊 Entities Created:');
    console.log('===================');
    console.log(`   - Products: ${tenantInfo.entities_created.product}`);
    console.log(`   - Variants: ${tenantInfo.entities_created.variants}`);
    console.log(`   - Images: ${tenantInfo.entities_created.images}`);
    console.log(`   - Options: ${tenantInfo.entities_created.options}`);
    console.log(`   - Categories: ${tenantInfo.entities_created.categories}`);
    console.log(`   - Tags: ${tenantInfo.entities_created.tags}`);

    console.log('\n🔍 Variant Details:');
    console.log('==================');
    if (createdProduct.variants && createdProduct.variants.length > 0) {
      createdProduct.variants.forEach((variant, index) => {
        console.log(`   Variant ${index + 1}:`);
        console.log(`     - ID: ${variant.id}`);
        console.log(`     - Title: ${variant.title}`);
        console.log(`     - SKU: ${variant.sku}`);
        console.log(`     - Tenant ID: ${variant.tenant_id}`);
        if (variant.metadata) {
          const metadata = typeof variant.metadata === 'string' ? JSON.parse(variant.metadata) : variant.metadata;
          console.log(`     - Sale Price: ${metadata.sale_price}`);
          console.log(`     - Original Price: ${metadata.original_price}`);
          console.log(`     - Quantity: ${metadata.product_quantity}`);
          console.log(`     - Status: ${metadata.product_inventory_status}`);
        }
      });
    }

    console.log('\n🖼️  Image Details:');
    console.log('=================');
    if (createdProduct.images && createdProduct.images.length > 0) {
      createdProduct.images.forEach((image, index) => {
        console.log(`   Image ${index + 1}:`);
        console.log(`     - ID: ${image.id}`);
        console.log(`     - URL: ${image.url.substring(0, 50)}...`);
      });
    }

    console.log('\n⚙️  Option Details:');
    console.log('==================');
    if (createdProduct.options && createdProduct.options.length > 0) {
      createdProduct.options.forEach((option, index) => {
        console.log(`   Option ${index + 1}:`);
        console.log(`     - ID: ${option.id}`);
        console.log(`     - Title: ${option.title}`);
        console.log(`     - Tenant ID: ${option.tenant_id}`);
      });
    }

    // Test retrieval to verify all data is saved
    console.log('\n🔍 Testing product retrieval...');
    const getResponse = await axios.get(`${BASE_URL}/admin/products?limit=1`, {
      headers: {
        'Authorization': `Bearer ${ADMIN_TOKEN}`,
        'x-tenant-id': 'my-kirana-store'
      }
    });

    const retrievedProducts = getResponse.data.products;
    if (retrievedProducts.length > 0) {
      const retrievedProduct = retrievedProducts[0];
      console.log('✅ Product retrieval successful!');
      console.log(`   - Retrieved: ${retrievedProduct.title}`);
      console.log(`   - Variants: ${retrievedProduct.variants ? retrievedProduct.variants.length : 0}`);
      console.log(`   - Images: ${retrievedProduct.images ? retrievedProduct.images.length : 0}`);
      console.log(`   - Options: ${retrievedProduct.options ? retrievedProduct.options.length : 0}`);
      console.log(`   - Thumbnail: ${retrievedProduct.thumbnail ? 'Present' : 'Missing'}`);
    }

    console.log('\n🎉 Comprehensive Product Test: SUCCESS!');
    console.log('======================================');
    console.log('✅ All fields from your curl payload have been processed');
    console.log('✅ Tenant ID automatically injected into all entities');
    console.log('✅ Product variants with prices created');
    console.log('✅ Product images saved');
    console.log('✅ Product options with values created');
    console.log('✅ Thumbnail image saved');
    console.log('✅ Metadata preserved with tenant information');

  } catch (error) {
    console.error('\n❌ Error creating comprehensive product:');
    console.error('=====================================');
    console.error(`Status: ${error.response?.status || 'Network Error'}`);
    console.error(`Message: ${error.response?.data?.message || error.message}`);
    
    if (error.response?.data?._debug) {
      console.error('Debug Info:', error.response.data._debug);
    }
    
    if (error.response?.data?.stack) {
      console.error('Stack Trace:', error.response.data.stack);
    }
  }
}

testComprehensiveProduct().catch(console.error);