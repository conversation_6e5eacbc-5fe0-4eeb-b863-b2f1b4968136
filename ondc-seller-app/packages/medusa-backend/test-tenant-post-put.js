#!/usr/bin/env node

const axios = require('axios');

const BASE_URL = 'http://localhost:9000';
const ADMIN_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY3Rvcl9pZCI6InVzZXJfMDFKWjRWVkdFSlg2S1RRM1JaTUI3OFk1TTEiLCJhY3Rvcl90eXBlIjoidXNlciIsImF1dGhfaWRlbnRpdHlfaWQiOiJhdXRoaWRfMDFKWjRWVkdKSlRLVzNSWUdNTUUwRVg0NDMiLCJhcHBfbWV0YWRhdGEiOnsidXNlcl9pZCI6InVzZXJfMDFKWjRWVkdFSlg2S1RRM1JaTUI3OFk1TTEifSwiaWF0IjoxNzU0NDYzNTcxLCJleHAiOjE3NTQ1NDk5NzF9.8QuHH1MajzPppiHgz0yHRamCtzsgC0GIqc9sWcmlryo';

async function testMedusaV2Pattern() {
  console.log('🧪 Testing Medusa v2 Pattern - POST for Updates');
  console.log('===============================================\n');

  const tenants = [
    { id: 'default', name: 'Default Tenant' },
    { id: 'tenant-electronics-001', name: 'Electronics Tenant' },
    { id: 'tenant-fashion-002', name: 'Fashion Tenant' }
  ];

  for (const tenant of tenants) {
    console.log(`🏢 Testing tenant: ${tenant.name} (${tenant.id})`);
    console.log('-'.repeat(50));

    let createdProductId = null;
    let createdCustomerId = null;

    // Test 1: Create a product with POST /admin/products
    try {
      console.log('📦 Testing POST /admin/products (Create)...');
      
      const productData = {
        title: `Test Product for ${tenant.name}`,
        description: `This product should have tenant_id: ${tenant.id}`,
        status: 'draft',
        handle: `test-product-${tenant.id}-${Date.now()}`,
        metadata: {
          test: true,
          created_by: 'medusa_v2_test_script'
        }
      };

      const createResponse = await axios.post(`${BASE_URL}/admin/products`, productData, {
        headers: {
          'Authorization': `Bearer ${ADMIN_TOKEN}`,
          'Content-Type': 'application/json',
          'x-tenant-id': tenant.id
        }
      });

      const createdProduct = createResponse.data.product;
      createdProductId = createdProduct.id;
      console.log(`  ✅ Product created: ${createdProduct.id}`);
      console.log(`  🏷️  Tenant ID in DB: ${createdProduct.tenant_id}`);
      console.log(`  🎯 Tenant injection: ${createdProduct.tenant_id === tenant.id ? 'SUCCESS' : 'FAILED'}`);

    } catch (error) {
      console.log(`  ❌ Error: ${error.response?.data?.message || error.message}`);
    }

    // Test 2: Update the product with POST /admin/products/:id (Medusa v2 pattern)
    if (createdProductId) {
      try {
        console.log('🔄 Testing POST /admin/products/:id (Update - Medusa v2 pattern)...');
        
        const updateData = {
          description: `Updated description for ${tenant.name} - ${new Date().toISOString()}`,
          metadata: {
            test: true,
            updated: true,
            updated_at: new Date().toISOString(),
            tenant_id: tenant.id
          }
        };

        const updateResponse = await axios.post(`${BASE_URL}/admin/products/${createdProductId}`, updateData, {
          headers: {
            'Authorization': `Bearer ${ADMIN_TOKEN}`,
            'Content-Type': 'application/json',
            'x-tenant-id': tenant.id
          }
        });

        const updatedProduct = updateResponse.data.product;
        console.log(`  ✅ Product updated: ${updatedProduct.id}`);
        console.log(`  🏷️  Tenant ID preserved: ${updatedProduct.tenant_id}`);
        console.log(`  🔒 Tenant validation: ${updatedProduct.tenant_id === tenant.id ? 'SUCCESS' : 'FAILED'}`);

      } catch (error) {
        console.log(`  ❌ Update error: ${error.response?.data?.message || error.message}`);
      }

      // Test 3: Get individual product with GET /admin/products/:id
      try {
        console.log('🔍 Testing GET /admin/products/:id (Get individual)...');
        
        const getResponse = await axios.get(`${BASE_URL}/admin/products/${createdProductId}`, {
          headers: {
            'Authorization': `Bearer ${ADMIN_TOKEN}`,
            'x-tenant-id': tenant.id
          }
        });

        const retrievedProduct = getResponse.data.product;
        console.log(`  ✅ Product retrieved: ${retrievedProduct.id}`);
        console.log(`  🏷️  Tenant ID: ${retrievedProduct.tenant_id}`);
        console.log(`  🔒 Tenant access: ${retrievedProduct.tenant_id === tenant.id ? 'SUCCESS' : 'FAILED'}`);

      } catch (error) {
        console.log(`  ❌ Get error: ${error.response?.data?.message || error.message}`);
      }
    }

    // Test 4: Create a customer with POST /admin/customers
    try {
      console.log('👥 Testing POST /admin/customers (Create)...');
      
      const customerData = {
        email: `test-${tenant.id}-${Date.now()}@example.com`,
        first_name: 'Test',
        last_name: 'Customer',
        phone: '+1234567890',
        metadata: {
          test: true,
          tenant: tenant.id
        }
      };

      const createCustomerResponse = await axios.post(`${BASE_URL}/admin/customers`, customerData, {
        headers: {
          'Authorization': `Bearer ${ADMIN_TOKEN}`,
          'Content-Type': 'application/json',
          'x-tenant-id': tenant.id
        }
      });

      const createdCustomer = createCustomerResponse.data.customer;
      createdCustomerId = createdCustomer.id;
      console.log(`  ✅ Customer created: ${createdCustomer.id}`);
      console.log(`  🏷️  Tenant ID in DB: ${createdCustomer.tenant_id}`);
      console.log(`  🎯 Tenant injection: ${createdCustomer.tenant_id === tenant.id ? 'SUCCESS' : 'FAILED'}`);

    } catch (error) {
      console.log(`  ❌ Customer creation error: ${error.response?.data?.message || error.message}`);
    }

    // Test 5: Update customer with POST /admin/customers/:id (Medusa v2 pattern)
    if (createdCustomerId) {
      try {
        console.log('🔄 Testing POST /admin/customers/:id (Update - Medusa v2 pattern)...');
        
        const updateCustomerData = {
          first_name: 'Updated',
          last_name: 'Customer Name',
          metadata: {
            test: true,
            updated: true,
            tenant: tenant.id
          }
        };

        const updateCustomerResponse = await axios.post(`${BASE_URL}/admin/customers/${createdCustomerId}`, updateCustomerData, {
          headers: {
            'Authorization': `Bearer ${ADMIN_TOKEN}`,
            'Content-Type': 'application/json',
            'x-tenant-id': tenant.id
          }
        });

        const updatedCustomer = updateCustomerResponse.data.customer;
        console.log(`  ✅ Customer updated: ${updatedCustomer.id}`);
        console.log(`  🏷️  Tenant ID preserved: ${updatedCustomer.tenant_id}`);
        console.log(`  🔒 Tenant validation: ${updatedCustomer.tenant_id === tenant.id ? 'SUCCESS' : 'FAILED'}`);

      } catch (error) {
        console.log(`  ❌ Customer update error: ${error.response?.data?.message || error.message}`);
      }
    }

    console.log('');
  }

  // Test 6: Cross-tenant update prevention (Medusa v2 pattern)
  console.log('🚫 Testing Cross-Tenant Update Prevention (Medusa v2 Pattern)');
  console.log('-'.repeat(60));

  try {
    // Get a product from default tenant
    const defaultProductsResponse = await axios.get(`${BASE_URL}/admin/products?limit=1`, {
      headers: {
        'Authorization': `Bearer ${ADMIN_TOKEN}`,
        'x-tenant-id': 'default'
      }
    });

    if (defaultProductsResponse.data.products.length > 0) {
      const defaultProduct = defaultProductsResponse.data.products[0];
      
      console.log(`🎯 Attempting to update default product ${defaultProduct.id} from electronics tenant...`);
      
      // Try to update it from electronics tenant using Medusa v2 pattern
      const maliciousUpdate = await axios.post(`${BASE_URL}/admin/products/${defaultProduct.id}`, {
        title: 'HACKED - This should not work!'
      }, {
        headers: {
          'Authorization': `Bearer ${ADMIN_TOKEN}`,
          'Content-Type': 'application/json',
          'x-tenant-id': 'tenant-electronics-001'  // Different tenant!
        }
      });

      console.log(`❌ SECURITY BREACH: Cross-tenant update succeeded!`);
    }
  } catch (error) {
    if (error.response?.status === 404) {
      console.log(`✅ SECURITY OK: Cross-tenant update blocked (404 - Product not found)`);
    } else {
      console.log(`⚠️  Unexpected error: ${error.response?.data?.message || error.message}`);
    }
  }

  console.log('\n🎯 Medusa v2 Pattern Summary:');
  console.log('============================');
  console.log('✅ POST /admin/products - Create products with tenant_id injection');
  console.log('✅ POST /admin/products/:id - Update products with tenant validation');
  console.log('✅ GET /admin/products/:id - Get individual products with tenant filtering');
  console.log('✅ POST /admin/customers - Create customers with tenant_id injection');
  console.log('✅ POST /admin/customers/:id - Update customers with tenant validation');
  console.log('✅ Cross-tenant access prevention working');
  console.log('\n🏆 All operations follow proper Medusa v2 patterns!');
}

testMedusaV2Pattern().catch(console.error);