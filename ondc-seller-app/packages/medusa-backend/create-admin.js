#!/usr/bin/env node

/**
 * Create Admin User Script
 * Creates an admin user for the Medusa backend
 */

const { createMedusaContainer } = require("@medusajs/framework");
const { ContainerRegistrationKeys } = require("@medusajs/framework/utils");

async function createAdminUser() {
  try {
    console.log("🔧 Creating admin user...");
    
    // Create container
    const container = await createMedusaContainer();
    
    // Get user service
    const userService = container.resolve(ContainerRegistrationKeys.USER_MODULE);
    
    // Create admin user
    const adminUser = await userService.createUsers({
      email: "<EMAIL>",
      first_name: "Admin",
      last_name: "User",
      avatar_url: null,
    });
    
    console.log("✅ Admin user created:", adminUser);
    
    // Try to set password (this might need a different approach)
    console.log("🔑 Setting password...");
    
    // Get auth service
    const authService = container.resolve("authService");
    if (authService) {
      await authService.create({
        entity_id: adminUser.id,
        provider: "emailpass",
        provider_metadata: {
          email: "<EMAIL>",
          password: "supersecret"
        }
      });
      console.log("✅ Password set successfully");
    } else {
      console.log("⚠️ Auth service not found, password not set");
    }
    
    console.log("🎉 Admin user setup complete!");
    console.log("📧 Email: <EMAIL>");
    console.log("🔒 Password: supersecret");
    
  } catch (error) {
    console.error("❌ Error creating admin user:", error);
    
    // Try alternative approach
    console.log("🔄 Trying alternative approach...");
    
    try {
      const container = await createMedusaContainer();
      
      // Try to use the admin module directly
      const adminModule = container.resolve("userModule");
      
      if (adminModule) {
        const user = await adminModule.createUsers({
          email: "<EMAIL>",
          first_name: "Admin",
          last_name: "User"
        });
        
        console.log("✅ Admin user created (alternative):", user);
      }
      
    } catch (altError) {
      console.error("❌ Alternative approach failed:", altError);
    }
  }
  
  process.exit(0);
}

// Run the script
createAdminUser();
