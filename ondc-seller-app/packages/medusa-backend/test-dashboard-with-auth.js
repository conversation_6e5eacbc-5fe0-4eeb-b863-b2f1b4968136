#!/usr/bin/env node

const axios = require('axios');

const BASE_URL = 'http://localhost:9000';

// Test configuration
const config = {
  adminCredentials: {
    email: '<EMAIL>',
    password: 'supersecret'
  },
  testEndpoints: [
    {
      name: 'Test Dashboard API (No Auth)',
      url: '/test/dashboard?period=30d',
      method: 'GET',
      requiresAuth: false
    },
    {
      name: 'Admin Dashboard API',
      url: '/admin/analytics/dashboard?period=30d',
      method: 'GET',
      requiresAuth: true
    },
    {
      name: 'Admin KPI API',
      url: '/admin/analytics/kpi?period=30d',
      method: 'GET',
      requiresAuth: true
    },
    {
      name: 'Admin Sales API',
      url: '/admin/analytics/sales?period=30d',
      method: 'GET',
      requiresAuth: true
    },
    {
      name: 'Admin Products API',
      url: '/admin/analytics/products?period=30d',
      method: 'GET',
      requiresAuth: true
    },
    {
      name: 'Admin Inventory API',
      url: '/admin/analytics/inventory?period=30d',
      method: 'GET',
      requiresAuth: true
    }
  ]
};

async function getAdminToken() {
  try {
    console.log('🔐 Getting admin authentication token...');
    
    const response = await axios.post(`${BASE_URL}/auth/user/emailpass`, config.adminCredentials, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.data && response.data.token) {
      console.log('✅ Admin token obtained successfully');
      return response.data.token;
    } else {
      console.log('❌ No token in response:', response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ Failed to get admin token:', error.response?.data || error.message);
    return null;
  }
}

async function testEndpoint(endpoint, token = null) {
  try {
    const headers = {
      'Content-Type': 'application/json'
    };

    if (token && endpoint.requiresAuth) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    console.log(`\n🧪 Testing: ${endpoint.name}`);
    console.log(`   URL: ${endpoint.url}`);
    console.log(`   Auth: ${endpoint.requiresAuth ? 'Required' : 'Not Required'}`);

    const response = await axios({
      method: endpoint.method,
      url: `${BASE_URL}${endpoint.url}`,
      headers
    });

    console.log(`   ✅ SUCCESS (${response.status})`);
    
    // Log key response data
    if (response.data) {
      if (response.data.success !== undefined) {
        console.log(`   📊 Success: ${response.data.success}`);
      }
      if (response.data.message) {
        console.log(`   💬 Message: ${response.data.message}`);
      }
      if (response.data.data) {
        const data = response.data.data;
        if (data.stats) {
          console.log(`   📈 Stats: Orders=${data.stats.totalOrders}, Revenue=${data.stats.totalRevenue}, Customers=${data.stats.totalCustomers}`);
        }
      }
      if (response.data.kpis) {
        const kpis = response.data.kpis;
        console.log(`   📊 KPIs: Orders=${kpis.totalOrders}, Revenue=${kpis.totalRevenue}, Products=${kpis.totalProducts}`);
      }
      if (response.data.summary) {
        const summary = response.data.summary;
        console.log(`   📈 Summary: Revenue=${summary.total_revenue || summary.totalRevenue}, Orders=${summary.total_orders || summary.totalOrders}`);
      }
      if (response.data.metrics && Array.isArray(response.data.metrics)) {
        console.log(`   📊 Metrics: ${response.data.metrics.length} KPI metrics returned`);
      }
    }

    return { success: true, status: response.status, data: response.data };
  } catch (error) {
    if (error.response) {
      console.log(`   ❌ FAILED (${error.response.status})`);
      console.log(`   💬 Error: ${error.response.data?.error || error.response.data?.message || 'Unknown error'}`);
      
      if (error.response.status === 401 && endpoint.requiresAuth) {
        console.log(`   🔒 Authentication required (expected for admin endpoints)`);
      }
    } else {
      console.log(`   ❌ NETWORK ERROR: ${error.message}`);
    }
    
    return { success: false, status: error.response?.status, error: error.message };
  }
}

async function main() {
  console.log('🚀 Starting Dashboard Analytics API Tests with Authentication');
  console.log('==================================================');

  // Get admin token
  const adminToken = await getAdminToken();
  
  if (!adminToken) {
    console.log('\n❌ Could not obtain admin token. Testing only public endpoints...\n');
  }

  // Test all endpoints
  const results = [];
  
  for (const endpoint of config.testEndpoints) {
    const result = await testEndpoint(endpoint, adminToken);
    results.push({
      endpoint: endpoint.name,
      ...result
    });
  }

  // Summary
  console.log('\n==================================================');
  console.log('✨ Test Summary');
  console.log('==================================================');

  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);

  console.log(`✅ Successful: ${successful.length}`);
  console.log(`❌ Failed: ${failed.length}`);

  if (successful.length > 0) {
    console.log('\n✅ Successful endpoints:');
    successful.forEach(r => console.log(`   - ${r.endpoint} (${r.status})`));
  }

  if (failed.length > 0) {
    console.log('\n❌ Failed endpoints:');
    failed.forEach(r => console.log(`   - ${r.endpoint} (${r.status || 'Network Error'})`));
  }

  console.log('\n🎉 Dashboard API testing completed!');
  
  // Check if core functionality is working
  const coreEndpoints = successful.filter(r => 
    r.endpoint.includes('Dashboard') || r.endpoint.includes('KPI')
  );
  
  if (coreEndpoints.length > 0) {
    console.log('\n✅ Core dashboard functionality is working correctly!');
    console.log('📊 Dashboard APIs are returning proper responses with real data.');
  } else {
    console.log('\n⚠️  Core dashboard functionality needs attention.');
    console.log('🔧 Please check authentication and API implementations.');
  }
}

// Run the tests
main().catch(console.error);
