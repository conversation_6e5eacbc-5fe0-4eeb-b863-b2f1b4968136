# Performance Optimization Guide

## Overview

This guide identifies performance bottlenecks in the multi-tenant Medusa backend and provides specific optimization strategies with implementation examples.

## Current Performance Analysis

### Identified Bottlenecks

#### 1. Database Query Performance Issues

**Problem:** Inefficient query patterns with application-level filtering
```typescript
// CURRENT INEFFICIENT PATTERN
const { data: allProducts } = await query.graph({
  pagination: { skip: 0, take: 200 }, // Fetches 200 to filter later
});
const tenantFilteredProducts = tenantConfig.productFilter(allProducts);
```

**Impact:**
- Unnecessary data transfer from database
- High memory usage
- Slow response times for large datasets

**Solution:**
```typescript
// OPTIMIZED DATABASE-LEVEL FILTERING
const { data: products } = await query.graph({
  entity: 'product',
  filters: {
    tenant_id: tenantId,
    status: 'published'
  },
  pagination: { skip: offset, take: limit }
});
```

#### 2. Missing Composite Indexes

**Problem:** Single-column indexes on `tenant_id` only
```sql
-- CURRENT INDEXES
CREATE INDEX IF NOT EXISTS "idx_product_tenant_id" ON "product" ("tenant_id");
```

**Impact:**
- Slow queries for common filtering patterns
- Full table scans for complex queries
- Poor performance with large datasets

**Solution:**
```sql
-- OPTIMIZED COMPOSITE INDEXES
CREATE INDEX CONCURRENTLY idx_product_tenant_status_created 
ON product (tenant_id, status, created_at DESC);

CREATE INDEX CONCURRENTLY idx_product_tenant_category 
ON product (tenant_id, category_id) 
WHERE status = 'published';

CREATE INDEX CONCURRENTLY idx_order_tenant_customer_date 
ON "order" (tenant_id, customer_id, created_at DESC);

CREATE INDEX CONCURRENTLY idx_customer_tenant_email 
ON customer (tenant_id, email) 
WHERE deleted_at IS NULL;
```

#### 3. N+1 Query Problems

**Problem:** Service wrapper causing multiple database calls
```typescript
// PROBLEMATIC PATTERN
async retrieve(id: string, config: any = {}) {
  const result = await this.service.retrieve(id, config); // Query 1
  // Validation might trigger additional queries for each item
  if (result && result.tenant_id && result.tenant_id !== this.tenantId) {
    throw new Error(`Access denied`);
  }
  return result;
}
```

**Solution:**
```typescript
// OPTIMIZED WITH BATCH VALIDATION
class OptimizedTenantService {
  private validatedTenants = new Set<string>();
  
  async batchValidateAccess(items: any[], tenantId: string) {
    const invalidItems = items.filter(item => 
      item.tenant_id && item.tenant_id !== tenantId
    );
    
    if (invalidItems.length > 0) {
      throw new Error(`Access denied to ${invalidItems.length} items`);
    }
  }
  
  async listWithValidation(query: any) {
    const results = await this.service.list({
      ...query,
      tenant_id: this.tenantId // Database-level filtering
    });
    
    return results; // No additional validation needed
  }
}
```

## Caching Optimization Strategies

### 1. Tenant-Aware Caching

**Problem:** Unbounded cache growth without eviction
```typescript
// CURRENT PROBLEMATIC CACHING
private tenantCache: Map<string, TenantConfig> = new Map()
```

**Solution:**
```typescript
// OPTIMIZED TENANT-AWARE CACHING
import LRU from 'lru-cache';

class OptimizedTenantCache {
  private cache = new LRU<string, TenantConfig>({
    max: 1000, // Maximum 1000 tenant configs
    ttl: 5 * 60 * 1000, // 5-minute TTL
    updateAgeOnGet: true
  });
  
  private productCache = new LRU<string, any[]>({
    max: 500,
    ttl: 2 * 60 * 1000, // 2-minute TTL for products
  });
  
  getTenantConfig(tenantId: string): TenantConfig | undefined {
    return this.cache.get(tenantId);
  }
  
  setTenantConfig(tenantId: string, config: TenantConfig): void {
    this.cache.set(tenantId, config);
  }
  
  getCachedProducts(tenantId: string, cacheKey: string): any[] | undefined {
    return this.productCache.get(`${tenantId}:${cacheKey}`);
  }
  
  setCachedProducts(tenantId: string, cacheKey: string, products: any[]): void {
    this.productCache.set(`${tenantId}:${cacheKey}`, products);
  }
}
```

### 2. Redis-Based Distributed Caching

```typescript
// REDIS IMPLEMENTATION FOR MULTI-INSTANCE DEPLOYMENTS
import Redis from 'ioredis';

class DistributedTenantCache {
  private redis: Redis;
  
  constructor() {
    this.redis = new Redis({
      host: process.env.REDIS_HOST,
      port: parseInt(process.env.REDIS_PORT || '6379'),
      keyPrefix: 'tenant:',
      retryDelayOnFailover: 100
    });
  }
  
  async getTenantConfig(tenantId: string): Promise<TenantConfig | null> {
    const cached = await this.redis.get(`config:${tenantId}`);
    return cached ? JSON.parse(cached) : null;
  }
  
  async setTenantConfig(tenantId: string, config: TenantConfig): Promise<void> {
    await this.redis.setex(
      `config:${tenantId}`, 
      300, // 5-minute expiration
      JSON.stringify(config)
    );
  }
  
  async invalidateTenantCache(tenantId: string): Promise<void> {
    const pattern = `*:${tenantId}:*`;
    const keys = await this.redis.keys(pattern);
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
  }
}
```

## Database Optimization Strategies

### 1. Connection Pooling per Tenant

```typescript
// TENANT-SPECIFIC CONNECTION POOLS
class TenantConnectionManager {
  private pools = new Map<string, Pool>();
  
  getPool(tenantId: string): Pool {
    if (!this.pools.has(tenantId)) {
      const pool = new Pool({
        host: process.env.DB_HOST,
        database: process.env.DB_NAME,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        max: 10, // Maximum 10 connections per tenant
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000,
        application_name: `tenant-${tenantId}`
      });
      
      this.pools.set(tenantId, pool);
    }
    
    return this.pools.get(tenantId)!;
  }
  
  async executeQuery(tenantId: string, query: string, params: any[]) {
    const pool = this.getPool(tenantId);
    const client = await pool.connect();
    
    try {
      // Set tenant context for row-level security
      await client.query('SET app.current_tenant_id = $1', [tenantId]);
      return await client.query(query, params);
    } finally {
      client.release();
    }
  }
}
```

### 2. Query Optimization Patterns

```typescript
// OPTIMIZED QUERY PATTERNS
class OptimizedProductService {
  async getProductsByTenant(
    tenantId: string, 
    filters: ProductFilters,
    pagination: PaginationOptions
  ) {
    // Use prepared statements for better performance
    const query = `
      SELECT p.*, pv.*, pi.*
      FROM product p
      LEFT JOIN product_variant pv ON p.id = pv.product_id
      LEFT JOIN product_image pi ON p.id = pi.product_id
      WHERE p.tenant_id = $1
        AND p.status = $2
        AND ($3::text IS NULL OR p.title ILIKE $3)
        AND ($4::uuid IS NULL OR p.category_id = $4)
      ORDER BY p.created_at DESC
      LIMIT $5 OFFSET $6
    `;
    
    const params = [
      tenantId,
      filters.status || 'published',
      filters.search ? `%${filters.search}%` : null,
      filters.categoryId || null,
      pagination.limit,
      pagination.offset
    ];
    
    return await this.db.query(query, params);
  }
}
```

### 3. Database Partitioning Strategy

```sql
-- PARTITION LARGE TABLES BY TENANT
CREATE TABLE product_partitioned (
  LIKE product INCLUDING ALL
) PARTITION BY HASH (tenant_id);

-- Create partitions for better performance
CREATE TABLE product_partition_0 PARTITION OF product_partitioned
FOR VALUES WITH (MODULUS 4, REMAINDER 0);

CREATE TABLE product_partition_1 PARTITION OF product_partitioned
FOR VALUES WITH (MODULUS 4, REMAINDER 1);

CREATE TABLE product_partition_2 PARTITION OF product_partitioned
FOR VALUES WITH (MODULUS 4, REMAINDER 2);

CREATE TABLE product_partition_3 PARTITION OF product_partitioned
FOR VALUES WITH (MODULUS 4, REMAINDER 3);
```

## Application-Level Optimizations

### 1. Bulk Operations Optimization

```typescript
// OPTIMIZED BULK OPERATIONS
class BulkOperationService {
  async bulkCreateProducts(
    products: ProductData[], 
    tenantId: string
  ): Promise<Product[]> {
    // Prepare data in batches
    const batchSize = 100;
    const batches = this.chunkArray(products, batchSize);
    const results: Product[] = [];
    
    for (const batch of batches) {
      const tenantProducts = batch.map(p => ({
        ...p,
        tenant_id: tenantId,
        created_at: new Date(),
        updated_at: new Date()
      }));
      
      // Use bulk insert with RETURNING clause
      const query = `
        INSERT INTO product (title, description, tenant_id, created_at, updated_at)
        VALUES ${tenantProducts.map((_, i) => 
          `($${i * 5 + 1}, $${i * 5 + 2}, $${i * 5 + 3}, $${i * 5 + 4}, $${i * 5 + 5})`
        ).join(', ')}
        RETURNING *
      `;
      
      const params = tenantProducts.flatMap(p => 
        [p.title, p.description, p.tenant_id, p.created_at, p.updated_at]
      );
      
      const batchResults = await this.db.query(query, params);
      results.push(...batchResults.rows);
    }
    
    return results;
  }
  
  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }
}
```

### 2. Asynchronous Processing

```typescript
// ASYNC PROCESSING FOR HEAVY OPERATIONS
import Bull from 'bull';

class AsyncTenantOperations {
  private queue = new Bull('tenant operations', {
    redis: { host: process.env.REDIS_HOST }
  });
  
  constructor() {
    this.queue.process('bulk-import', this.processBulkImport.bind(this));
    this.queue.process('tenant-analytics', this.processTenantAnalytics.bind(this));
  }
  
  async queueBulkImport(tenantId: string, data: any[]) {
    return await this.queue.add('bulk-import', {
      tenantId,
      data,
      timestamp: new Date()
    }, {
      attempts: 3,
      backoff: 'exponential',
      delay: 2000
    });
  }
  
  private async processBulkImport(job: Bull.Job) {
    const { tenantId, data } = job.data;
    
    // Process in chunks to avoid memory issues
    const chunkSize = 50;
    for (let i = 0; i < data.length; i += chunkSize) {
      const chunk = data.slice(i, i + chunkSize);
      await this.bulkOperationService.bulkCreateProducts(chunk, tenantId);
      
      // Update job progress
      const progress = Math.round(((i + chunkSize) / data.length) * 100);
      job.progress(progress);
    }
  }
}
```

## Performance Monitoring

### 1. Metrics Collection

```typescript
// PERFORMANCE METRICS
class PerformanceMonitor {
  private metrics = new Map<string, number[]>();
  
  recordQueryTime(tenantId: string, operation: string, duration: number) {
    const key = `${tenantId}:${operation}`;
    if (!this.metrics.has(key)) {
      this.metrics.set(key, []);
    }
    
    this.metrics.get(key)!.push(duration);
    
    // Keep only last 100 measurements
    if (this.metrics.get(key)!.length > 100) {
      this.metrics.get(key)!.shift();
    }
  }
  
  getAverageQueryTime(tenantId: string, operation: string): number {
    const key = `${tenantId}:${operation}`;
    const times = this.metrics.get(key) || [];
    
    if (times.length === 0) return 0;
    
    return times.reduce((sum, time) => sum + time, 0) / times.length;
  }
  
  getSlowQueries(threshold: number = 1000): Array<{tenant: string, operation: string, avgTime: number}> {
    const slowQueries: Array<{tenant: string, operation: string, avgTime: number}> = [];
    
    for (const [key, times] of this.metrics.entries()) {
      const [tenant, operation] = key.split(':');
      const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
      
      if (avgTime > threshold) {
        slowQueries.push({ tenant, operation, avgTime });
      }
    }
    
    return slowQueries.sort((a, b) => b.avgTime - a.avgTime);
  }
}
```

## Performance Benchmarks

### Target Performance Metrics
- **API Response Time:** < 200ms for 95th percentile
- **Database Query Time:** < 100ms for complex queries
- **Cache Hit Rate:** > 80% for frequently accessed data
- **Concurrent Users:** Support 1000+ concurrent users per tenant
- **Memory Usage:** < 512MB per tenant in production

### Load Testing Recommendations
```bash
# Use Artillery.js for load testing
npm install -g artillery

# Test tenant-specific endpoints
artillery run --target http://localhost:9000 \
  --header "x-tenant-id: tenant-electronics-001" \
  load-test-config.yml
```

---

*Implement these optimizations incrementally and measure performance improvements at each step.*
