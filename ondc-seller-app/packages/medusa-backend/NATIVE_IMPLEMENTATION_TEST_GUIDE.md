# Native Implementation Test Guide

## Overview

This guide provides comprehensive testing instructions for the refactored Medusa native multi-tenancy implementation.

## 🎯 **Testing Prerequisites**

### **1. Setup Requirements (When Node.js Available)**
```bash
# Run these commands when Node.js is installed:
cd ondc-seller-app/packages/backend-new/medusa-backend

# Setup tenant sales channels
npx ts-node src/scripts/setup-tenant-sales-channels.ts

# Migrate existing data
npx ts-node src/scripts/migrate-tenant-data.ts
```

### **2. Environment Variables**
Copy generated keys to frontend `.env.local`:
```bash
NEXT_PUBLIC_MEDUSA_API_URL=http://localhost:9000
NEXT_PUBLIC_TENANT_ELECTRONICS_001_API_KEY=pk_electronics_generated_key
NEXT_PUBLIC_TENANT_FASHION_002_API_KEY=pk_fashion_generated_key
NEXT_PUBLIC_DEFAULT_API_KEY=pk_default_generated_key
```

## 🧪 **API Testing**

### **1. Store API Testing (Customer-facing)**

#### **Test Product Isolation**
```bash
# Electronics tenant products
curl -H "x-publishable-api-key: $ELECTRONICS_API_KEY" \
     http://localhost:9000/store/products

# Fashion tenant products  
curl -H "x-publishable-api-key: $FASHION_API_KEY" \
     http://localhost:9000/store/products

# Verify different products returned for each tenant
```

#### **Test Cart Operations**
```bash
# Create cart for electronics tenant
CART_RESPONSE=$(curl -X POST \
  -H "x-publishable-api-key: $ELECTRONICS_API_KEY" \
  -H "Content-Type: application/json" \
  http://localhost:9000/store/carts \
  -d '{}')

CART_ID=$(echo $CART_RESPONSE | jq -r '.cart.id')

# Add product to cart
curl -X POST \
  -H "x-publishable-api-key: $ELECTRONICS_API_KEY" \
  -H "Content-Type: application/json" \
  "http://localhost:9000/store/carts/$CART_ID/line-items" \
  -d '{"variant_id":"variant_123","quantity":1}'

# Get cart
curl -H "x-publishable-api-key: $ELECTRONICS_API_KEY" \
     "http://localhost:9000/store/carts/$CART_ID"
```

#### **Test ONDC Cash on Delivery**
```bash
# Complete cart with COD (ONDC-specific endpoint)
curl -X POST \
  -H "x-publishable-api-key: $ELECTRONICS_API_KEY" \
  -H "Content-Type: application/json" \
  "http://localhost:9000/store/carts/$CART_ID/complete-cod"

# Expected response:
# {
#   "success": true,
#   "order": {
#     "id": "order_123",
#     "payment_method": "cash_on_delivery",
#     "payment_status": "awaiting",
#     "total": 1000
#   }
# }
```

### **2. Admin API Testing (Management)**

#### **Test Sales Channels (Tenants)**
```bash
# Get admin token
ADMIN_TOKEN=$(curl -X POST http://localhost:9000/admin/auth/token \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"supersecret"}' | jq -r '.access_token')

# List all sales channels (tenants)
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     http://localhost:9000/admin/sales-channels

# Get specific sales channel
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     "http://localhost:9000/admin/sales-channels/$ELECTRONICS_CHANNEL_ID"
```

#### **Test Admin Product Filtering**
```bash
# Get all products (admin)
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     http://localhost:9000/admin/products

# Get products for specific sales channel (tenant)
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     "http://localhost:9000/admin/products?sales_channel_id=$ELECTRONICS_CHANNEL_ID"

# Verify only electronics products returned
```

#### **Test Admin Customer/Order Filtering**
```bash
# Get customers for specific sales channel
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     "http://localhost:9000/admin/customers?sales_channel_id=$ELECTRONICS_CHANNEL_ID"

# Get orders for specific sales channel
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     "http://localhost:9000/admin/orders?sales_channel_id=$ELECTRONICS_CHANNEL_ID"
```

## 🖥️ **Frontend Testing**

### **1. Native API Client Testing**
```typescript
// Test native API client
import { MedusaNativeAPI } from './lib/medusa-native-api'

// Electronics tenant
const electronicsAPI = new MedusaNativeAPI('tenant-electronics-001')
const electronicsProducts = await electronicsAPI.getProducts({ limit: 10 })
console.log('Electronics products:', electronicsProducts)

// Fashion tenant
const fashionAPI = new MedusaNativeAPI('tenant-fashion-002')
const fashionProducts = await fashionAPI.getProducts({ limit: 10 })
console.log('Fashion products:', fashionProducts)

// Verify different products returned
```

### **2. Cart and Order Flow Testing**
```typescript
// Test complete e-commerce flow
const api = new MedusaNativeAPI('tenant-electronics-001')

// 1. Create cart
const cart = await api.createCart()
console.log('Cart created:', cart.cart.id)

// 2. Add product to cart
await api.addToCart(cart.cart.id, {
  variant_id: 'variant_electronics_123',
  quantity: 1
})

// 3. Complete with COD (ONDC-specific)
const order = await api.completeCartWithCOD(cart.cart.id)
console.log('COD Order created:', order.order.id)
```

### **3. Admin Dashboard Testing**
```typescript
// Test admin operations
const api = new MedusaNativeAPI('tenant-electronics-001')
api.setAdminToken(adminToken)

// Get tenant configuration
const salesChannel = await api.getSalesChannel()
console.log('Tenant config:', salesChannel.sales_channel.metadata)

// Get tenant-specific products
const products = await api.getAdminProducts(salesChannelId)
console.log('Admin products:', products)
```

## ✅ **Verification Checklist**

### **Store API Verification**
- [ ] Products are filtered by tenant (different results for different API keys)
- [ ] Cart creation works with publishable keys
- [ ] Cart operations (add/update/remove items) work correctly
- [ ] COD order creation works and returns proper order structure
- [ ] Product details load correctly for each tenant
- [ ] Search and filtering work within tenant scope

### **Admin API Verification**
- [ ] Sales channels list shows all tenants with metadata
- [ ] Products can be filtered by sales channel ID
- [ ] Customers can be filtered by sales channel ID  
- [ ] Orders can be filtered by sales channel ID
- [ ] Sales channel metadata contains ONDC configuration
- [ ] Admin operations respect tenant boundaries

### **Frontend Integration Verification**
- [ ] Native API client initializes correctly for each tenant
- [ ] Tenant switching works without errors
- [ ] Product listings show tenant-specific products
- [ ] Cart operations work for each tenant
- [ ] COD order creation completes successfully
- [ ] Admin panel shows tenant-filtered data
- [ ] No console errors during operations

### **ONDC Configuration Verification**
- [ ] Sales channel metadata contains ONDC configs
- [ ] Tenant-specific ONDC settings are preserved
- [ ] Currency and timezone settings are maintained
- [ ] COD payment method works correctly
- [ ] Order metadata includes ONDC-specific information

## 🚨 **Common Issues & Solutions**

### **Issue: "No publishable API key found for tenant"**
**Solution:** Run the setup script to generate API keys:
```bash
npx ts-node src/scripts/setup-tenant-sales-channels.ts
```

### **Issue: "Products not filtered by tenant"**
**Solution:** Verify migration completed and products are linked to sales channels:
```bash
npx ts-node src/scripts/migrate-tenant-data.ts --verify
```

### **Issue: "Sales channel not found"**
**Solution:** Check that sales channels were created:
```bash
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     http://localhost:9000/admin/sales-channels
```

### **Issue: "COD endpoint not working"**
**Solution:** Verify the COD endpoint exists and cart has items:
```bash
curl -X POST \
  -H "x-publishable-api-key: $API_KEY" \
  "http://localhost:9000/store/carts/$CART_ID/complete-cod"
```

## 📊 **Success Metrics**

### **Performance Metrics**
- [ ] API response times < 500ms for product listings
- [ ] Cart operations complete < 200ms
- [ ] Order creation completes < 1000ms
- [ ] Admin operations complete < 800ms

### **Functionality Metrics**
- [ ] 100% tenant isolation (no cross-tenant data leakage)
- [ ] All CRUD operations working correctly
- [ ] COD payment flow working end-to-end
- [ ] Admin filtering working correctly
- [ ] Frontend integration seamless

### **Code Quality Metrics**
- [ ] ~2000+ lines of custom code removed
- [ ] All endpoints use native Medusa patterns
- [ ] No custom middleware dependencies
- [ ] Standard Medusa configuration only
- [ ] Comprehensive error handling

## 🎉 **Test Completion**

Once all verification items pass, the native implementation is successfully working and ready for production use. The refactored system provides:

- ✅ **Complete tenant isolation** through sales channels
- ✅ **ONDC-specific functionality** preserved (COD payments)
- ✅ **Native Medusa patterns** for maintainability
- ✅ **Future-proof architecture** compatible with Medusa updates
- ✅ **Reduced complexity** with ~2000+ lines of custom code removed

The implementation successfully transforms the custom multi-tenancy approach into a standard, scalable solution using Medusa's proven architecture.
