import { loadEnv, defineConfig } from '@medusajs/framework/utils';

loadEnv(process.env.NODE_ENV || 'development', process.cwd());

export default defineConfig({
  projectConfig: {
    databaseUrl: process.env.DATABASE_URL,
    http: {
      storeCors: process.env.STORE_CORS!,
      adminCors: process.env.ADMIN_CORS!,
      authCors: process.env.AUTH_CORS!,
      jwtSecret: process.env.JWT_SECRET || 'supersecret',
      cookieSecret: process.env.COOKIE_SECRET || 'supersecret',
    },
  },

  // ✅ MINIMAL MODULES CONFIGURATION (Only working modules)
  modules: {
    // Disable problematic modules temporarily
    // auth: {
    //   resolve: '@medusajs/medusa/auth',
    //   options: {
    //     providers: [
    //       {
    //         resolve: '@medusajs/medusa/auth-emailpass',
    //         id: 'emailpass',
    //         options: {},
    //       },
    //     ],
    //   },
    // },
    // user: {
    //   resolve: '@medusajs/medusa/user',
    //   options: {
    //     jwt_secret: process.env.JWT_SECRET || 'supersecret',
    //   },
    // },
    // cart: {
    //   resolve: '@medusajs/cart',
    //   options: {},
    // },
    // order: {
    //   resolve: '@medusajs/order',
    //   options: {},
    // },
    // customer: {
    //   resolve: '@medusajs/customer',
    //   options: {},
    // },
    // payment: {
    //   resolve: '@medusajs/payment',
    //   options: {},
    // },
    // inventory: {
    //   resolve: '@medusajs/inventory',
    //   options: {},
    // },
    // promotion: { resolve: '@medusajs/medusa/promotion' },
  },

  // ✅ MINIMAL PLUGINS CONFIGURATION
  plugins: [
    // Temporarily disable all plugins to get basic server running
    // {
    //   resolve: 'medusa-payment-manual',
    //   options: {
    //     // Cash on Delivery configuration
    //     name: 'Cash on Delivery',
    //     description: 'Pay with cash when your order is delivered',
    //     id: 'manual',
    //   },
    // },
  ],
});
