{"message": "Product import completed", "transaction_id": "e02a57a3-aa77-47aa-9491-0277da893008", "success": false, "total_rows": 2, "successful_imports": 0, "failed_imports": 2, "errors": [{"row": 2, "field": "general", "message": "Failed to create product: Product with handle: sample-product-1, already exists.", "value": "Sample Product 1"}, {"row": 3, "field": "general", "message": "Failed to create product: Product with handle: sample-product-2, already exists.", "value": "Sample Product 2"}], "created_products": []}