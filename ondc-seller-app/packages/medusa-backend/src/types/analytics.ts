/**
 * Analytics API Response Types
 * 
 * Standardized response formats for all analytics endpoints
 * Ensures consistency across the dashboard API system
 */

// Base response interface
export interface BaseAnalyticsResponse {
  success: boolean;
  timestamp: string;
  period?: string;
  tenant_id?: string;
  sales_channel_id?: string;
}

// Error response interface
export interface AnalyticsErrorResponse extends BaseAnalyticsResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
}

// Chart data interfaces
export interface ChartDataPoint {
  date: string;
  value: number;
  label?: string;
  color?: string;
}

export interface PieChartData {
  name: string;
  value: number;
  percentage: number;
  color: string;
}

export interface BarChartData {
  category: string;
  value: number;
  growth?: number;
  color?: string;
}

// KPI Metric interface
export interface KPIMetric {
  id: string;
  title: string;
  value: number;
  formatted_value: string;
  previous_value?: number;
  change?: {
    value: number;
    percentage: number;
    type: 'increase' | 'decrease' | 'neutral';
  };
  trend?: ChartDataPoint[];
  target?: number;
  unit: string;
  category: 'revenue' | 'orders' | 'customers' | 'products' | 'operations';
  priority: 'high' | 'medium' | 'low';
  description: string;
}

// Dashboard Analytics Response
export interface DashboardAnalyticsResponse extends BaseAnalyticsResponse {
  success: true;
  data: {
    stats: {
      totalRevenue: number;
      totalOrders: number;
      totalCustomers: number;
      totalProducts: number;
      averageOrderValue: number;
      conversionRate: number;
      revenueGrowth: number;
      orderGrowth: number;
      customerGrowth: number;
    };
    revenueTrend: ChartDataPoint[];
    topProducts: Array<{
      productId: string;
      title: string;
      sku: string;
      revenue: number;
      units: number;
      stock: number;
      gross: number;
    }>;
    topOrders: Array<{
      order_id: string;
      order_display_id: string;
      customer_name: string;
      customer_email: string;
      total_order_amount: number;
      order_status: string;
      created_at: string;
    }>;
    refundRate: PieChartData[];
    customerSplit: Array<{
      segment: string;
      count: number;
      percentage: number;
    }>;
  };
  metadata: {
    generated_at: string;
    cache_expires_at?: string;
    data_freshness: 'real_time' | 'cached' | 'stale';
  };
}

// Sales Analytics Response
export interface SalesAnalyticsResponse extends BaseAnalyticsResponse {
  success: true;
  data: {
    summary: {
      total_revenue: number;
      total_orders: number;
      total_units_sold: number;
      average_order_value: number;
      growth_rate: number;
    };
    trends: Array<{
      period: string;
      revenue: number;
      orders: number;
      units_sold: number;
      average_order_value: number;
      growth_rate: number;
    }>;
    by_category: Array<{
      category_id: string;
      category_name: string;
      revenue: number;
      orders: number;
      products: number;
      growth_rate: number;
    }>;
    by_channel: Array<{
      channel_id: string;
      channel_name: string;
      revenue: number;
      orders: number;
      conversion_rate: number;
    }>;
    top_performing_days: Array<{
      date: string;
      revenue: number;
      orders: number;
    }>;
  };
}

// Customer Analytics Response
export interface CustomerAnalyticsResponse extends BaseAnalyticsResponse {
  success: true;
  data: {
    summary: {
      total_customers: number;
      new_customers: number;
      repeat_customers: number;
      customer_retention_rate: number;
      average_customer_lifetime_value: number;
      churn_rate: number;
    };
    segments: Array<{
      segment: string;
      count: number;
      revenue: number;
      average_order_value: number;
      percentage: number;
      orders_per_customer: number;
    }>;
    acquisition_trends: Array<{
      date: string;
      new_customers: number;
      repeat_customers: number;
      total_customers: number;
    }>;
    lifetime_value: Array<{
      segment: string;
      average_ltv: number;
      median_ltv: number;
      total_customers: number;
    }>;
    top_customers: Array<{
      customer_id: string;
      name: string;
      email: string;
      total_orders: number;
      total_spent: number;
      last_order_date: string;
    }>;
    geographic_distribution: Array<{
      country: string;
      customers: number;
      revenue: number;
      percentage: number;
    }>;
  };
}

// Product Analytics Response
export interface ProductAnalyticsResponse extends BaseAnalyticsResponse {
  success: true;
  data: {
    summary: {
      total_products: number;
      active_products: number;
      total_revenue: number;
      total_units_sold: number;
      average_conversion_rate: number;
      low_stock_alerts: number;
    };
    top_products: Array<{
      product_id: string;
      title: string;
      sku: string;
      thumbnail: string | null;
      revenue: number;
      units_sold: number;
      orders: number;
      views: number;
      conversion_rate: number;
      average_rating: number;
      stock_level: number;
      category: string;
      growth_rate: number;
    }>;
    category_performance: Array<{
      category_id: string;
      category_name: string;
      revenue: number;
      units_sold: number;
      products_count: number;
      orders: number;
      average_order_value: number;
      growth_rate: number;
    }>;
    trends: Array<{
      date: string;
      revenue: number;
      units_sold: number;
      orders: number;
      views: number;
    }>;
    inventory_alerts: Array<{
      product_id: string;
      title: string;
      sku: string;
      current_stock: number;
      reorder_level: number;
      status: 'low_stock' | 'out_of_stock' | 'overstock';
    }>;
    new_products: Array<{
      product_id: string;
      title: string;
      created_at: string;
      initial_sales: number;
    }>;
  };
}

// Inventory Analytics Response
export interface InventoryAnalyticsResponse extends BaseAnalyticsResponse {
  success: true;
  data: {
    summary: {
      total_items: number;
      total_value: number;
      in_stock_items: number;
      low_stock_items: number;
      out_of_stock_items: number;
      average_turnover_rate: number;
      total_locations: number;
    };
    inventory_items: Array<{
      product_id: string;
      variant_id: string;
      title: string;
      sku: string;
      current_stock: number;
      reserved_stock: number;
      available_stock: number;
      reorder_level: number;
      status: 'in_stock' | 'low_stock' | 'out_of_stock' | 'overstock';
      location: string;
      category: string;
      last_restocked: string | null;
      turnover_rate: number;
    }>;
    stock_movements: Array<{
      date: string;
      product_id: string;
      variant_id: string;
      title: string;
      sku: string;
      movement_type: 'inbound' | 'outbound' | 'adjustment';
      quantity: number;
      reason: string;
      location: string;
    }>;
    alerts: Array<{
      alert_type: 'low_stock' | 'out_of_stock' | 'overstock' | 'slow_moving';
      product_id: string;
      variant_id: string;
      title: string;
      sku: string;
      current_stock: number;
      threshold: number;
      priority: 'high' | 'medium' | 'low';
      days_since_last_sale: number;
    }>;
    location_summary: Array<{
      location_id: string;
      location_name: string;
      total_items: number;
      total_value: number;
      low_stock_items: number;
      out_of_stock_items: number;
      utilization_rate: number;
    }>;
    turnover_analysis: Array<{
      category: string;
      turnover_rate: number;
      total_items: number;
      slow_moving_items: number;
    }>;
    stock_trends: Array<{
      date: string;
      total_stock: number;
      stock_value: number;
      movements_in: number;
      movements_out: number;
    }>;
  };
}

// KPI Dashboard Response
export interface KPIDashboardResponse extends BaseAnalyticsResponse {
  success: true;
  data: {
    summary: {
      period: string;
      generated_at: string;
      total_metrics: number;
      metrics_with_targets: number;
      metrics_meeting_targets: number;
    };
    metrics: KPIMetric[];
    performance_score: {
      overall_score: number;
      category_scores: {
        revenue: number;
        orders: number;
        customers: number;
        products: number;
        operations: number;
      };
    };
    alerts: Array<{
      metric_id: string;
      alert_type: 'target_missed' | 'significant_change' | 'trend_concern';
      severity: 'high' | 'medium' | 'low';
      message: string;
    }>;
  };
}

// Union type for all analytics responses
export type AnalyticsResponse = 
  | DashboardAnalyticsResponse
  | SalesAnalyticsResponse
  | CustomerAnalyticsResponse
  | ProductAnalyticsResponse
  | InventoryAnalyticsResponse
  | KPIDashboardResponse
  | AnalyticsErrorResponse;

// Chart configuration interfaces for frontend
export interface ChartConfig {
  type: 'line' | 'bar' | 'pie' | 'area' | 'donut';
  title: string;
  description?: string;
  colors: string[];
  responsive: boolean;
  animation: boolean;
  legend: {
    show: boolean;
    position: 'top' | 'bottom' | 'left' | 'right';
  };
  tooltip: {
    enabled: boolean;
    format?: string;
  };
}

// Export utility types
export type AnalyticsTimeRange = '7d' | '30d' | '90d' | '1y';
export type AnalyticsMetricCategory = 'revenue' | 'orders' | 'customers' | 'products' | 'operations';
export type ChartType = 'line' | 'bar' | 'pie' | 'area' | 'donut';
export type AlertSeverity = 'high' | 'medium' | 'low';
export type ChangeType = 'increase' | 'decrease' | 'neutral';
