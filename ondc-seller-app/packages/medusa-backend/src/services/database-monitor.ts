/**
 * Database Connection Monitor Service
 *
 * Monitors database connection health, tracks metrics, and provides
 * automatic recovery mechanisms for connection pool issues.
 */

import { dbPool } from '../utils/database-pool';

export interface ConnectionHealthMetrics {
  timestamp: Date;
  totalConnections: number;
  idleConnections: number;
  activeConnections: number;
  waitingRequests: number;
  poolUtilization: number; // Percentage of pool being used
  avgResponseTime: number;
  errorRate: number;
  isHealthy: boolean;
}

export interface AlertThresholds {
  maxPoolUtilization: number; // Alert if pool utilization exceeds this %
  maxResponseTime: number; // Alert if avg response time exceeds this ms
  maxErrorRate: number; // Alert if error rate exceeds this %
  minHealthyConnections: number; // Alert if healthy connections drop below this
}

export class DatabaseMonitorService {
  private static instance: DatabaseMonitorService;
  private isMonitoring = false;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private metricsHistory: ConnectionHealthMetrics[] = [];
  private maxHistorySize = 100; // Keep last 100 metrics snapshots

  // Default alert thresholds
  private alertThresholds: AlertThresholds = {
    maxPoolUtilization: 80, // 80%
    maxResponseTime: 5000, // 5 seconds
    maxErrorRate: 10, // 10%
    minHealthyConnections: 2,
  };

  // Tracking variables
  private requestCount = 0;
  private errorCount = 0;
  private totalResponseTime = 0;
  private lastResetTime = Date.now();

  private constructor() {}

  public static getInstance(): DatabaseMonitorService {
    if (!DatabaseMonitorService.instance) {
      DatabaseMonitorService.instance = new DatabaseMonitorService();
    }
    return DatabaseMonitorService.instance;
  }

  /**
   * Start monitoring database connections
   */
  public startMonitoring(intervalMs: number = 30000): void {
    if (this.isMonitoring) {
      console.log('⚠️ [DB-MONITOR] Monitoring is already running');
      return;
    }

    console.log(
      `🔍 [DB-MONITOR] Starting database connection monitoring (interval: ${intervalMs}ms)`
    );

    this.isMonitoring = true;
    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
    }, intervalMs);

    // Initial metrics collection
    this.collectMetrics();
  }

  /**
   * Stop monitoring database connections
   */
  public stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    console.log('🛑 [DB-MONITOR] Stopping database connection monitoring');

    this.isMonitoring = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
  }

  /**
   * Collect current connection metrics
   */
  private async collectMetrics(): Promise<void> {
    try {
      const poolMetrics = dbPool.getConnectionMetrics();
      const currentTime = Date.now();
      const timeSinceReset = currentTime - this.lastResetTime;

      // Calculate rates (per minute)
      const avgResponseTime =
        this.requestCount > 0 ? this.totalResponseTime / this.requestCount : 0;
      const errorRate = this.requestCount > 0 ? (this.errorCount / this.requestCount) * 100 : 0;
      const poolUtilization =
        poolMetrics.totalConnections > 0
          ? (poolMetrics.activeConnections / poolMetrics.totalConnections) * 100
          : 0;

      const metrics: ConnectionHealthMetrics = {
        timestamp: new Date(),
        totalConnections: poolMetrics.totalConnections,
        idleConnections: poolMetrics.idleConnections,
        activeConnections: poolMetrics.activeConnections,
        waitingRequests: poolMetrics.waitingRequests,
        poolUtilization,
        avgResponseTime,
        errorRate,
        isHealthy: this.assessHealth(poolMetrics, poolUtilization, avgResponseTime, errorRate),
      };

      // Add to history
      this.metricsHistory.push(metrics);
      if (this.metricsHistory.length > this.maxHistorySize) {
        this.metricsHistory.shift();
      }

      // Log metrics
      this.logMetrics(metrics);

      // Check for alerts
      this.checkAlerts(metrics);

      // Reset counters every 5 minutes
      if (timeSinceReset > 300000) {
        // 5 minutes
        this.resetCounters();
      }
    } catch (error) {
      console.error('❌ [DB-MONITOR] Error collecting metrics:', error);
    }
  }

  /**
   * Assess overall database health
   */
  private assessHealth(
    poolMetrics: any,
    poolUtilization: number,
    avgResponseTime: number,
    errorRate: number
  ): boolean {
    // Check various health indicators
    const checks = [
      poolMetrics.totalConnections >= this.alertThresholds.minHealthyConnections,
      poolUtilization < this.alertThresholds.maxPoolUtilization,
      avgResponseTime < this.alertThresholds.maxResponseTime,
      errorRate < this.alertThresholds.maxErrorRate,
      poolMetrics.waitingRequests < 10, // No more than 10 waiting requests
    ];

    return checks.every(check => check);
  }

  /**
   * Log current metrics
   */
  private logMetrics(metrics: ConnectionHealthMetrics): void {
    const healthIcon = metrics.isHealthy ? '✅' : '❌';

    console.log(`${healthIcon} [DB-MONITOR] Connection Health:`, {
      total: metrics.totalConnections,
      active: metrics.activeConnections,
      idle: metrics.idleConnections,
      waiting: metrics.waitingRequests,
      utilization: `${metrics.poolUtilization.toFixed(1)}%`,
      avgResponseTime: `${metrics.avgResponseTime.toFixed(0)}ms`,
      errorRate: `${metrics.errorRate.toFixed(1)}%`,
    });
  }

  /**
   * Check for alert conditions
   */
  private checkAlerts(metrics: ConnectionHealthMetrics): void {
    const alerts: string[] = [];

    if (metrics.poolUtilization > this.alertThresholds.maxPoolUtilization) {
      alerts.push(`High pool utilization: ${metrics.poolUtilization.toFixed(1)}%`);
    }

    if (metrics.avgResponseTime > this.alertThresholds.maxResponseTime) {
      alerts.push(`High response time: ${metrics.avgResponseTime.toFixed(0)}ms`);
    }

    if (metrics.errorRate > this.alertThresholds.maxErrorRate) {
      alerts.push(`High error rate: ${metrics.errorRate.toFixed(1)}%`);
    }

    if (metrics.totalConnections < this.alertThresholds.minHealthyConnections) {
      alerts.push(`Low connection count: ${metrics.totalConnections}`);
    }

    if (metrics.waitingRequests > 5) {
      alerts.push(`High waiting requests: ${metrics.waitingRequests}`);
    }

    if (alerts.length > 0) {
      console.warn('🚨 [DB-MONITOR] ALERTS:', alerts);

      // Trigger recovery actions if needed
      this.triggerRecoveryActions(metrics, alerts);
    }
  }

  /**
   * Trigger automatic recovery actions
   */
  private async triggerRecoveryActions(
    metrics: ConnectionHealthMetrics,
    alerts: string[]
  ): Promise<void> {
    console.log('🔧 [DB-MONITOR] Triggering recovery actions...');

    // Action 1: Force garbage collection if memory might be an issue
    if (global.gc && (metrics.poolUtilization > 90 || metrics.errorRate > 20)) {
      console.log('🧹 [DB-MONITOR] Forcing garbage collection');
      global.gc();
    }

    // Action 2: Health check the database connection
    try {
      const isHealthy = await dbPool.healthCheck();
      if (!isHealthy) {
        console.error('❌ [DB-MONITOR] Database health check failed');
      }
    } catch (error) {
      console.error('❌ [DB-MONITOR] Health check error:', error);
    }

    // Action 3: Log detailed pool statistics for debugging
    const stats = dbPool.getStats();
    console.log('📊 [DB-MONITOR] Detailed pool stats:', stats);
  }

  /**
   * Reset tracking counters
   */
  private resetCounters(): void {
    console.log('🔄 [DB-MONITOR] Resetting performance counters');
    this.requestCount = 0;
    this.errorCount = 0;
    this.totalResponseTime = 0;
    this.lastResetTime = Date.now();
  }

  /**
   * Record a database request for metrics
   */
  public recordRequest(responseTime: number, isError: boolean = false): void {
    this.requestCount++;
    this.totalResponseTime += responseTime;

    if (isError) {
      this.errorCount++;
    }
  }

  /**
   * Get current health status
   */
  public getCurrentHealth(): ConnectionHealthMetrics | null {
    return this.metricsHistory.length > 0
      ? this.metricsHistory[this.metricsHistory.length - 1]
      : null;
  }

  /**
   * Get metrics history
   */
  public getMetricsHistory(): ConnectionHealthMetrics[] {
    return [...this.metricsHistory];
  }

  /**
   * Update alert thresholds
   */
  public updateAlertThresholds(thresholds: Partial<AlertThresholds>): void {
    this.alertThresholds = { ...this.alertThresholds, ...thresholds };
    console.log('⚙️ [DB-MONITOR] Updated alert thresholds:', this.alertThresholds);
  }

  /**
   * Get health summary for API endpoints
   */
  public getHealthSummary(): any {
    const currentHealth = this.getCurrentHealth();

    return {
      isHealthy: currentHealth?.isHealthy ?? false,
      lastCheck: currentHealth?.timestamp ?? null,
      metrics: currentHealth
        ? {
            totalConnections: currentHealth.totalConnections,
            activeConnections: currentHealth.activeConnections,
            poolUtilization: `${currentHealth.poolUtilization.toFixed(1)}%`,
            avgResponseTime: `${currentHealth.avgResponseTime.toFixed(0)}ms`,
            errorRate: `${currentHealth.errorRate.toFixed(1)}%`,
          }
        : null,
      alertThresholds: this.alertThresholds,
    };
  }
}

// Export singleton instance
export const dbMonitor = DatabaseMonitorService.getInstance();
