/**
 * Tenant Service Factory
 *
 * Provides easy access to all tenant-aware service wrappers.
 * Automatically creates tenant-aware instances from request context.
 */

import { MedusaRequest } from '@medusajs/framework/http';
import { TenantAwareProductService } from './tenant-aware-product';
import { TenantAwareCustomerService } from './tenant-aware-customer';
import { TenantAwareOrderService } from './tenant-aware-order';
import { TenantAwareCartService } from './tenant-aware-cart';
import { TenantAwareProductCategoryService } from './tenant-aware-product-category';
import { TenantAwareCollectionService } from './tenant-aware-collection';
import { TenantAwareTagService } from './tenant-aware-tag';

export interface TenantServices {
  product: TenantAwareProductService;
  customer: TenantAwareCustomerService;
  order: TenantAwareOrderService;
  cart: TenantAwareCartService;
  productCategory: TenantAwareProductCategoryService;
  collection: TenantAwareCollectionService;
  tagService: TenantAwareTagService;
  tenantId: string;
  getTenantId: () => string;
}

export class TenantServiceFactory {
  /**
   * Create all tenant-aware services from request context
   */
  static fromRequest(req: MedusaRequest): TenantServices {
    const tenantId = req.tenant_id || 'default';

    console.log(`🏭 [TENANT-FACTORY] Creating tenant services for tenant: ${tenantId}`);

    const services: TenantServices = {
      product: TenantAwareProductService.fromRequest(req),
      customer: TenantAwareCustomerService.fromRequest(req),
      order: TenantAwareOrderService.fromRequest(req),
      cart: TenantAwareCartService.fromRequest(req),
      productCategory: TenantAwareProductCategoryService.fromRequest(req),
      collection: TenantAwareCollectionService.fromRequest(req),
      tagService: new TenantAwareTagService(null, tenantId),
      tenantId,
      getTenantId: () => tenantId,
    };

    console.log(`✅ [TENANT-FACTORY] Created tenant services for tenant: ${tenantId}`);
    return services;
  }

  /**
   * Create tenant-aware services with explicit tenant ID
   */
  static create(req: MedusaRequest, tenantId: string): TenantServices {
    console.log(`🏭 [TENANT-FACTORY] Creating tenant services for explicit tenant: ${tenantId}`);

    // Temporarily set tenant_id on request for service creation
    const originalTenantId = req.tenant_id;
    req.tenant_id = tenantId;

    const services = this.fromRequest(req);

    // Restore original tenant_id
    req.tenant_id = originalTenantId;

    console.log(`✅ [TENANT-FACTORY] Created tenant services for explicit tenant: ${tenantId}`);
    return services;
  }

  /**
   * Get individual tenant-aware service
   */
  static getProductService(req: MedusaRequest): TenantAwareProductService {
    return TenantAwareProductService.fromRequest(req);
  }

  static getCustomerService(req: MedusaRequest): TenantAwareCustomerService {
    return TenantAwareCustomerService.fromRequest(req);
  }

  static getOrderService(req: MedusaRequest): TenantAwareOrderService {
    return TenantAwareOrderService.fromRequest(req);
  }

  /**
   * Validate tenant context in request
   */
  static validateTenantContext(req: MedusaRequest): {
    isValid: boolean;
    tenantId: string;
    errors: string[];
  } {
    const errors: string[] = [];
    const tenantId = req.tenant_id || 'default';

    // Basic tenant ID validation
    if (!tenantId) {
      errors.push('Tenant ID is missing');
    } else if (typeof tenantId !== 'string') {
      errors.push('Tenant ID must be a string');
    } else if (tenantId.length === 0) {
      errors.push('Tenant ID cannot be empty');
    } else if (tenantId.length > 50) {
      errors.push('Tenant ID cannot exceed 50 characters');
    } else if (!/^[a-zA-Z0-9_-]+$/.test(tenantId)) {
      errors.push('Tenant ID can only contain alphanumeric characters, hyphens, and underscores');
    }

    // Check if container is available
    if (!req.scope) {
      errors.push('Request scope/container is not available');
    }

    const isValid = errors.length === 0;

    if (!isValid) {
      console.warn(
        `⚠️  [TENANT-FACTORY] Tenant context validation failed for ${tenantId}:`,
        errors
      );
    } else {
      console.log(`✅ [TENANT-FACTORY] Tenant context validation passed for ${tenantId}`);
    }

    return { isValid, tenantId, errors };
  }

  /**
   * Get tenant statistics across all services
   */
  static async getTenantStatistics(req: MedusaRequest): Promise<{
    tenantId: string;
    products: any;
    customers: any;
    orders: any;
    timestamp: string;
  }> {
    const tenantId = req.tenant_id || 'default';
    console.log(`📊 [TENANT-FACTORY] Getting comprehensive statistics for tenant: ${tenantId}`);

    try {
      const services = this.fromRequest(req);

      // Get statistics from all services
      const [productStats, customerStats, orderStats] = await Promise.all([
        services.product.getProductStats().catch(error => {
          console.error(`❌ [TENANT-FACTORY] Error getting product stats: ${error}`);
          return { error: error.message };
        }),
        services.customer.getCustomerStats().catch(error => {
          console.error(`❌ [TENANT-FACTORY] Error getting customer stats: ${error}`);
          return { error: error.message };
        }),
        services.order.getOrderStats().catch(error => {
          console.error(`❌ [TENANT-FACTORY] Error getting order stats: ${error}`);
          return { error: error.message };
        }),
      ]);

      const statistics = {
        tenantId,
        products: productStats,
        customers: customerStats,
        orders: orderStats,
        timestamp: new Date().toISOString(),
      };

      console.log(`📊 [TENANT-FACTORY] Statistics for tenant ${tenantId}:`, statistics);
      return statistics;
    } catch (error) {
      console.error(`❌ [TENANT-FACTORY] Error getting tenant statistics: ${error}`);
      throw error;
    }
  }

  /**
   * Test all tenant services connectivity
   */
  static async testTenantServices(req: MedusaRequest): Promise<{
    tenantId: string;
    services: {
      product: { available: boolean; error?: string };
      customer: { available: boolean; error?: string };
      order: { available: boolean; error?: string };
    };
    overall: { healthy: boolean; errors: string[] };
  }> {
    const tenantId = req.tenant_id || 'default';
    console.log(`🧪 [TENANT-FACTORY] Testing tenant services for tenant: ${tenantId}`);

    const results = {
      tenantId,
      services: {
        product: { available: false },
        customer: { available: false },
        order: { available: false },
      },
      overall: { healthy: false, errors: [] as string[] },
    };

    try {
      // Test product service
      try {
        const productService = this.getProductService(req);
        await productService.listProducts({}, { take: 1 });
        results.services.product.available = true;
        console.log(`✅ [TENANT-FACTORY] Product service test passed for tenant: ${tenantId}`);
      } catch (error) {
        results.services.product.error = error instanceof Error ? error.message : 'Unknown error';
        results.overall.errors.push(`Product service: ${results.services.product.error}`);
        console.error(`❌ [TENANT-FACTORY] Product service test failed: ${error}`);
      }

      // Test customer service
      try {
        const customerService = this.getCustomerService(req);
        await customerService.listCustomers({}, { take: 1 });
        results.services.customer.available = true;
        console.log(`✅ [TENANT-FACTORY] Customer service test passed for tenant: ${tenantId}`);
      } catch (error) {
        results.services.customer.error = error instanceof Error ? error.message : 'Unknown error';
        results.overall.errors.push(`Customer service: ${results.services.customer.error}`);
        console.error(`❌ [TENANT-FACTORY] Customer service test failed: ${error}`);
      }

      // Test order service
      try {
        const orderService = this.getOrderService(req);
        await orderService.listOrders({}, { take: 1 });
        results.services.order.available = true;
        console.log(`✅ [TENANT-FACTORY] Order service test passed for tenant: ${tenantId}`);
      } catch (error) {
        results.services.order.error = error instanceof Error ? error.message : 'Unknown error';
        results.overall.errors.push(`Order service: ${results.services.order.error}`);
        console.error(`❌ [TENANT-FACTORY] Order service test failed: ${error}`);
      }

      // Determine overall health
      const availableServices = Object.values(results.services).filter(
        service => service.available
      ).length;
      results.overall.healthy = availableServices === 3; // All services must be available

      if (results.overall.healthy) {
        console.log(`🎉 [TENANT-FACTORY] All tenant services healthy for tenant: ${tenantId}`);
      } else {
        console.warn(`⚠️  [TENANT-FACTORY] Some tenant services unhealthy for tenant: ${tenantId}`);
      }

      return results;
    } catch (error) {
      console.error(`❌ [TENANT-FACTORY] Error testing tenant services: ${error}`);
      results.overall.errors.push(
        `General error: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
      return results;
    }
  }

  /**
   * Create tenant-aware service with custom configuration
   */
  static createCustomService<T>(
    req: MedusaRequest,
    serviceCreator: (req: MedusaRequest) => T,
    tenantId?: string
  ): T {
    const effectiveTenantId = tenantId || req.tenant_id || 'default';
    console.log(
      `🔧 [TENANT-FACTORY] Creating custom tenant service for tenant: ${effectiveTenantId}`
    );

    // Temporarily set tenant_id if provided
    const originalTenantId = req.tenant_id;
    if (tenantId) {
      req.tenant_id = tenantId;
    }

    try {
      const service = serviceCreator(req);
      console.log(
        `✅ [TENANT-FACTORY] Created custom tenant service for tenant: ${effectiveTenantId}`
      );
      return service;
    } finally {
      // Restore original tenant_id
      req.tenant_id = originalTenantId;
    }
  }
}
