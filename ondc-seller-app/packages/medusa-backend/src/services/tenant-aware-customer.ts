/**
 * Tenant-Aware Customer Service
 * 
 * Wraps the core Medusa CustomerModuleService to automatically inject
 * tenant context into all customer operations.
 */

import { MedusaRequest } from "@medusajs/framework/http";

export class TenantAwareCustomerService {
  private customerService: any;
  private tenantId: string;

  constructor(customerService: any, tenantId: string = 'default') {
    this.customerService = customerService;
    this.tenantId = tenantId;
  }

  /**
   * Create a tenant-aware customer service instance from request
   */
  static fromRequest(req: MedusaRequest): TenantAwareCustomerService {
    const customerService = req.scope.resolve('customer');
    const tenantId = req.tenant_id || 'default';
    
    console.log(`🏢 [TENANT-CUSTOMER] Creating tenant-aware customer service for tenant: ${tenantId}`);
    
    return new TenantAwareCustomerService(customerService, tenantId);
  }

  /**
   * Inject tenant_id into customer data
   */
  private injectTenantId(data: any): any {
    if (Array.isArray(data)) {
      return data.map(item => ({ ...item, tenant_id: this.tenantId }));
    }
    return { ...data, tenant_id: this.tenantId };
  }

  /**
   * Add tenant filter to query filters
   */
  private addTenantFilter(filters: any = {}): any {
    return { ...filters, tenant_id: this.tenantId };
  }

  // ============================================================================
  // CUSTOMER CRUD OPERATIONS WITH TENANT CONTEXT
  // ============================================================================

  /**
   * Create customers with automatic tenant injection
   */
  async createCustomers(data: any[], context?: any): Promise<any> {
    console.log(`🏢 [TENANT-CUSTOMER] Creating ${data.length} customers for tenant: ${this.tenantId}`);
    
    const tenantData = this.injectTenantId(data);
    const result = await this.customerService.createCustomers(tenantData, context);
    
    console.log(`✅ [TENANT-CUSTOMER] Created ${result.length} customers for tenant: ${this.tenantId}`);
    return result;
  }

  /**
   * Update customers with tenant validation
   */
  async updateCustomers(selector: any, data: any, context?: any): Promise<any> {
    console.log(`🏢 [TENANT-CUSTOMER] Updating customers for tenant: ${this.tenantId}`);
    
    // Add tenant filter to selector to ensure we only update tenant's customers
    const tenantSelector = this.addTenantFilter(selector);
    const result = await this.customerService.updateCustomers(tenantSelector, data, context);
    
    console.log(`✅ [TENANT-CUSTOMER] Updated customers for tenant: ${this.tenantId}`);
    return result;
  }

  /**
   * List customers with automatic tenant filtering (using direct query)
   */
  async listCustomers(filters: any = {}, config?: any, context?: any): Promise<any> {
    console.log(`🏢 [TENANT-CUSTOMER] Listing customers for tenant: ${this.tenantId}`);
    
    // Since customer service doesn't have listCustomers, we'll use a direct query approach
    // This would typically be handled by the query service or direct database access
    const tenantFilters = this.addTenantFilter(filters);
    
    try {
      // For now, we'll simulate this - in real implementation, you'd use the query service
      console.log(`📋 [TENANT-CUSTOMER] Would query customers with filters:`, tenantFilters);
      
      // Placeholder - in real implementation, use query service or repository
      const result = [];
      
      console.log(`✅ [TENANT-CUSTOMER] Found ${result.length} customers for tenant: ${this.tenantId}`);
      return result;
      
    } catch (error) {
      console.error(`❌ [TENANT-CUSTOMER] Error listing customers: ${error}`);
      throw error;
    }
  }

  /**
   * Retrieve single customer with tenant validation
   */
  async retrieveCustomer(id: string, config?: any, context?: any): Promise<any> {
    console.log(`🏢 [TENANT-CUSTOMER] Retrieving customer ${id} for tenant: ${this.tenantId}`);
    
    try {
      // First validate customer belongs to tenant
      const customers = await this.listCustomers({ id }, { take: 1 }, context);
      
      if (customers.length === 0) {
        console.warn(`⚠️  [TENANT-CUSTOMER] Customer ${id} not found for tenant: ${this.tenantId}`);
        throw new Error(`Customer ${id} not found for tenant ${this.tenantId}`);
      }
      
      // For now, return the first customer from our list
      // In real implementation, you'd use the customer service's retrieve method
      const result = customers[0];
      
      console.log(`✅ [TENANT-CUSTOMER] Retrieved customer ${id} for tenant: ${this.tenantId}`);
      return result;
      
    } catch (error) {
      console.error(`❌ [TENANT-CUSTOMER] Error retrieving customer: ${error}`);
      throw error;
    }
  }

  // ============================================================================
  // CUSTOMER GROUP OPERATIONS WITH TENANT CONTEXT
  // ============================================================================

  /**
   * Create customer groups with tenant injection
   */
  async createCustomerGroups(data: any[], context?: any): Promise<any> {
    console.log(`🏢 [TENANT-CUSTOMER] Creating ${data.length} customer groups for tenant: ${this.tenantId}`);
    
    const tenantData = this.injectTenantId(data);
    const result = await this.customerService.createCustomerGroups(tenantData, context);
    
    console.log(`✅ [TENANT-CUSTOMER] Created ${result.length} customer groups for tenant: ${this.tenantId}`);
    return result;
  }

  /**
   * Update customer groups with tenant validation
   */
  async updateCustomerGroups(selector: any, data: any, context?: any): Promise<any> {
    console.log(`🏢 [TENANT-CUSTOMER] Updating customer groups for tenant: ${this.tenantId}`);
    
    const tenantSelector = this.addTenantFilter(selector);
    const result = await this.customerService.updateCustomerGroups(tenantSelector, data, context);
    
    console.log(`✅ [TENANT-CUSTOMER] Updated customer groups for tenant: ${this.tenantId}`);
    return result;
  }

  /**
   * Add customer to group with tenant validation
   */
  async addCustomerToGroup(customerId: string, groupId: string, context?: any): Promise<any> {
    console.log(`🏢 [TENANT-CUSTOMER] Adding customer ${customerId} to group ${groupId} for tenant: ${this.tenantId}`);
    
    // Validate both customer and group belong to tenant
    const customerExists = await this.validateCustomerOwnership(customerId);
    if (!customerExists) {
      throw new Error(`Customer ${customerId} not found for tenant ${this.tenantId}`);
    }
    
    const result = await this.customerService.addCustomerToGroup(customerId, groupId, context);
    
    console.log(`✅ [TENANT-CUSTOMER] Added customer to group for tenant: ${this.tenantId}`);
    return result;
  }

  /**
   * Remove customer from group with tenant validation
   */
  async removeCustomerFromGroup(customerId: string, groupId: string, context?: any): Promise<any> {
    console.log(`🏢 [TENANT-CUSTOMER] Removing customer ${customerId} from group ${groupId} for tenant: ${this.tenantId}`);
    
    // Validate both customer and group belong to tenant
    const customerExists = await this.validateCustomerOwnership(customerId);
    if (!customerExists) {
      throw new Error(`Customer ${customerId} not found for tenant ${this.tenantId}`);
    }
    
    const result = await this.customerService.removeCustomerFromGroup(customerId, groupId, context);
    
    console.log(`✅ [TENANT-CUSTOMER] Removed customer from group for tenant: ${this.tenantId}`);
    return result;
  }

  // ============================================================================
  // CUSTOMER ADDRESS OPERATIONS WITH TENANT CONTEXT
  // ============================================================================

  /**
   * Create customer addresses with tenant injection
   */
  async createCustomerAddresses(data: any[], context?: any): Promise<any> {
    console.log(`🏢 [TENANT-CUSTOMER] Creating ${data.length} customer addresses for tenant: ${this.tenantId}`);
    
    const tenantData = this.injectTenantId(data);
    const result = await this.customerService.createCustomerAddresses(tenantData, context);
    
    console.log(`✅ [TENANT-CUSTOMER] Created ${result.length} customer addresses for tenant: ${this.tenantId}`);
    return result;
  }

  /**
   * Update customer addresses with tenant validation
   */
  async updateCustomerAddresses(selector: any, data: any, context?: any): Promise<any> {
    console.log(`🏢 [TENANT-CUSTOMER] Updating customer addresses for tenant: ${this.tenantId}`);
    
    const tenantSelector = this.addTenantFilter(selector);
    const result = await this.customerService.updateCustomerAddresses(tenantSelector, data, context);
    
    console.log(`✅ [TENANT-CUSTOMER] Updated customer addresses for tenant: ${this.tenantId}`);
    return result;
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /**
   * Get tenant ID for this service instance
   */
  getTenantId(): string {
    return this.tenantId;
  }

  /**
   * Get underlying customer service
   */
  getCustomerService(): any {
    return this.customerService;
  }

  /**
   * Validate customer ownership by tenant
   */
  async validateCustomerOwnership(customerId: string): Promise<boolean> {
    try {
      const customers = await this.listCustomers({ id: customerId }, { take: 1 });
      return customers.length > 0;
    } catch (error) {
      console.error(`❌ [TENANT-CUSTOMER] Error validating customer ownership: ${error}`);
      return false;
    }
  }

  /**
   * Get customer statistics for tenant
   */
  async getCustomerStats(): Promise<{
    totalCustomers: number;
    totalGroups: number;
    totalAddresses: number;
  }> {
    console.log(`📊 [TENANT-CUSTOMER] Getting customer statistics for tenant: ${this.tenantId}`);
    
    try {
      const customers = await this.listCustomers();
      
      // Note: For groups and addresses, we'd need separate queries
      // This is a simplified version focusing on customers
      
      const stats = {
        totalCustomers: customers.length,
        totalGroups: 0, // Would need separate query
        totalAddresses: 0 // Would need separate query
      };
      
      console.log(`📊 [TENANT-CUSTOMER] Stats for tenant ${this.tenantId}:`, stats);
      return stats;
      
    } catch (error) {
      console.error(`❌ [TENANT-CUSTOMER] Error getting customer stats: ${error}`);
      throw error;
    }
  }

  /**
   * Flush customer cache (if applicable)
   */
  async flush(): Promise<void> {
    console.log(`🔄 [TENANT-CUSTOMER] Flushing customer cache for tenant: ${this.tenantId}`);
    
    if (typeof this.customerService.flush === 'function') {
      await this.customerService.flush();
      console.log(`✅ [TENANT-CUSTOMER] Flushed customer cache for tenant: ${this.tenantId}`);
    } else {
      console.log(`ℹ️  [TENANT-CUSTOMER] No flush method available for tenant: ${this.tenantId}`);
    }
  }
}
