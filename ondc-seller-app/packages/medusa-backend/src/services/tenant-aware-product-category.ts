/**
 * Tenant-Aware Product Category Service
 *
 * Provides complete CRUD operations for product categories with automatic tenant isolation.
 * Integrates with Row Level Security (RLS) for database-level tenant filtering.
 */

import { MedusaRequest } from '@medusajs/framework/http';
import { TenantQueryInterceptor } from './tenant-query-interceptor';

export class TenantAwareProductCategoryService {
  private categoryService: any;
  private tenantId: string;
  private queryInterceptor: TenantQueryInterceptor;
  private pgConnection: any;

  constructor(categoryService: any, tenantId: string = 'default', pgConnection?: any) {
    this.categoryService = categoryService;
    this.tenantId = tenantId;
    this.queryInterceptor = new TenantQueryInterceptor({
      tenantId,
      enableLogging: true,
      strictMode: false,
    });
    this.pgConnection = pgConnection;

    // Set tenant context in database for RLS
    this.setDatabaseTenantContext();
  }

  /**
   * Set tenant context in database for Row Level Security
   */
  private async setDatabaseTenantContext(): Promise<void> {
    if (this.pgConnection) {
      try {
        await this.pgConnection.query('SELECT set_config($1, $2, true)', [
          'app.tenant_context.tenant_id',
          this.tenantId,
        ]);
        console.log(`🔒 [TENANT-CATEGORY] Set database tenant context: ${this.tenantId}`);
      } catch (error) {
        console.warn(`⚠️  [TENANT-CATEGORY] Failed to set database tenant context: ${error}`);
      }
    }
  }

  /**
   * Create a tenant-aware product category service instance from request
   */
  static fromRequest(req: MedusaRequest): TenantAwareProductCategoryService {
    let categoryService: unknown = null;
    const tenantId = (req as any).tenant_id || (req.headers['x-tenant-id'] as string) || 'default';

    // Try different service names for product categories
    const possibleServiceNames = ['productCategory', 'product-category', 'category'];

    for (const serviceName of possibleServiceNames) {
      try {
        categoryService = req.scope.resolve(serviceName);
        console.log(`✅ [TENANT-CATEGORY] Found category service: ${serviceName}`);
        break;
      } catch (error) {
        // Continue trying other service names
      }
    }

    if (!categoryService) {
      console.warn(`⚠️  [TENANT-CATEGORY] No category service found, creating mock service`);
      categoryService = {
        listCategories: async () => [],
        createCategories: async (data: any[]) => data,
        updateCategories: async (data: any[]) => data,
        deleteCategories: async () => {},
        retrieveCategory: async () => null,
      };
    }

    // Try to get database connection
    let pgConnection = null;
    try {
      const manager = req.scope.resolve('manager');
      if (manager && typeof manager.query === 'function') {
        pgConnection = manager;
        console.log(`✅ [TENANT-CATEGORY] Found database connection via manager`);
      } else {
        console.warn(
          `⚠️  [TENANT-CATEGORY] No database connection found, creating direct connection`
        );
        console.warn(
          `⚠️  [TENANT-CATEGORY] No database connection found, will use manual filtering`
        );
      }
    } catch (error) {
      console.warn(`⚠️  [TENANT-CATEGORY] Error resolving database connection: ${error}`);
    }

    console.log(
      `📂 [TENANT-CATEGORY] Creating tenant-aware category service for tenant: ${tenantId}`
    );

    return new TenantAwareProductCategoryService(categoryService, tenantId, pgConnection);
  }

  // ============================================================================
  // CREATE OPERATIONS
  // ============================================================================

  /**
   * Create product categories with automatic tenant_id injection
   */
  async createCategories(categoriesData: any[]): Promise<any> {
    console.log(
      `📂 [TENANT-CATEGORY] Creating ${categoriesData.length} categories for tenant: ${this.tenantId}`
    );

    try {
      // Inject tenant_id into all category data
      const tenantCategoriesData = categoriesData.map(categoryData => ({
        ...categoryData,
        tenant_id: this.tenantId,
      }));

      const result = await this.categoryService.createCategories(tenantCategoriesData);
      console.log(
        `✅ [TENANT-CATEGORY] Created ${result.length} categories for tenant: ${this.tenantId}`
      );
      return result;
    } catch (error) {
      console.error(`❌ [TENANT-CATEGORY] Error creating categories: ${error}`);
      throw error;
    }
  }

  // ============================================================================
  // READ OPERATIONS
  // ============================================================================

  /**
   * List product categories with tenant filtering
   */
  async listCategories(filters: any = {}, config?: any, context?: any): Promise<any> {
    console.log(`📂 [TENANT-CATEGORY] Listing categories for tenant: ${this.tenantId}`);

    try {
      // Set database tenant context for RLS
      await this.setDatabaseTenantContext();

      // Use the service method directly - RLS will handle filtering
      const result = await this.categoryService.listCategories(filters, config, context);

      const categoryCount = Array.isArray(result) ? result.length : result?.categories?.length || 0;
      console.log(
        `✅ [TENANT-CATEGORY] Found ${categoryCount} categories for tenant: ${this.tenantId}`
      );

      return result;
    } catch (error) {
      console.error(`❌ [TENANT-CATEGORY] Error listing categories: ${error}`);

      // Return empty result structure
      return {
        categories: [],
        count: 0,
        offset: config?.skip || 0,
        limit: config?.take || 20,
      };
    }
  }

  /**
   * List and count product categories with tenant filtering
   */
  async listAndCountCategories(filters: any = {}, config?: any, context?: any): Promise<any> {
    console.log(
      `📂 [TENANT-CATEGORY] Listing and counting categories for tenant: ${this.tenantId}`
    );

    try {
      // Use direct database query to bypass Medusa ORM issues
      console.log(`� [TENANT-CATEGORY] Using direct database query for tenant: ${this.tenantId}`);

      return await this.listAndCountCategoriesDirectQuery(filters, config);
    } catch (error) {
      console.error(`❌ [TENANT-CATEGORY] Error listing and counting categories: ${error}`);
      return [[], 0];
    }
  }

  /**
   * Retrieve a single product category by ID (with tenant validation)
   */
  async retrieveCategory(categoryId: string, config?: any, context?: any): Promise<any> {
    console.log(
      `📂 [TENANT-CATEGORY] Retrieving category ${categoryId} for tenant: ${this.tenantId}`
    );

    try {
      // Use direct database query to get the category
      const categories = await this.listAndCountCategoriesDirectQuery({ id: categoryId }, config);

      if (!categories || categories[0].length === 0) {
        console.log(
          `❌ [TENANT-CATEGORY] Category ${categoryId} not found for tenant: ${this.tenantId}`
        );
        return null;
      }

      const category = categories[0][0]; // categories is [categories[], count]
      console.log(
        `✅ [TENANT-CATEGORY] Retrieved category ${categoryId} (${category.name}) for tenant: ${this.tenantId}`
      );

      return category;
    } catch (error) {
      console.error(`❌ [TENANT-CATEGORY] Error retrieving category ${categoryId}: ${error}`);
      throw error;
    }
  }

  // ============================================================================
  // UPDATE OPERATIONS
  // ============================================================================

  /**
   * Update product categories with tenant validation
   */
  async updateCategories(categoriesData: any[]): Promise<any> {
    console.log(
      `📂 [TENANT-CATEGORY] Updating ${categoriesData.length} categories for tenant: ${this.tenantId}`
    );

    try {
      // Set database tenant context for RLS
      await this.setDatabaseTenantContext();

      // Ensure tenant_id is preserved in updates
      const tenantCategoriesData = categoriesData.map(categoryData => ({
        ...categoryData,
        tenant_id: this.tenantId, // Ensure tenant_id is not changed
      }));

      const result = await this.categoryService.updateCategories(tenantCategoriesData);
      console.log(
        `✅ [TENANT-CATEGORY] Updated ${result.length} categories for tenant: ${this.tenantId}`
      );
      return result;
    } catch (error) {
      console.error(`❌ [TENANT-CATEGORY] Error updating categories: ${error}`);
      throw error;
    }
  }

  // ============================================================================
  // DELETE OPERATIONS
  // ============================================================================

  /**
   * Delete product categories with tenant validation
   */
  async deleteCategories(categoryIds: string[]): Promise<void> {
    console.log(
      `📂 [TENANT-CATEGORY] Deleting ${categoryIds.length} categories for tenant: ${this.tenantId}`
    );

    try {
      // Set database tenant context for RLS
      await this.setDatabaseTenantContext();

      await this.categoryService.deleteCategories(categoryIds);
      console.log(
        `✅ [TENANT-CATEGORY] Deleted ${categoryIds.length} categories for tenant: ${this.tenantId}`
      );
    } catch (error) {
      console.error(`❌ [TENANT-CATEGORY] Error deleting categories: ${error}`);
      throw error;
    }
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /**
   * Get product category statistics for tenant
   */
  async getCategoryStats(): Promise<{
    totalCategories: number;
    activeCategories: number;
    categoriesWithProducts: number;
  }> {
    console.log(`📊 [TENANT-CATEGORY] Getting category statistics for tenant: ${this.tenantId}`);

    try {
      const [categories, count] = await this.listAndCountCategories();

      // Calculate statistics
      const activeCategories = categories.filter((cat: any) => cat.is_active !== false).length;
      const categoriesWithProducts = categories.filter(
        (cat: any) => (cat.products?.length || 0) > 0
      ).length;

      const stats = {
        totalCategories: count,
        activeCategories,
        categoriesWithProducts,
      };

      console.log(`📊 [TENANT-CATEGORY] Stats for tenant ${this.tenantId}:`, stats);
      return stats;
    } catch (error) {
      console.error(`❌ [TENANT-CATEGORY] Error getting category stats: ${error}`);
      throw error;
    }
  }

  /**
   * List and count categories using direct database query with RLS
   */
  private async listAndCountCategoriesDirectQuery(
    filters: any = {},
    config?: any
  ): Promise<[any[], number]> {
    // Ensure database connection is available
    if (!this.pgConnection) {
      console.log(`🔗 [TENANT-CATEGORY] Creating database connection for direct query`);
      try {
        const { Client } = require('pg');
        this.pgConnection = new Client({
          connectionString:
            process.env.DATABASE_URL ||
            process.env.POSTGRES_URL ||
            'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
        });
        await this.pgConnection.connect();
        console.log(
          `✅ [TENANT-CATEGORY] Database connection established for tenant: ${this.tenantId}`
        );
      } catch (error) {
        console.error(`❌ [TENANT-CATEGORY] Failed to create database connection: ${error}`);
        return [[], 0];
      }
    }

    try {
      // Set tenant context for RLS
      await this.pgConnection.query('SELECT set_config($1, $2, true)', [
        'app.tenant_context.tenant_id',
        this.tenantId,
      ]);

      // Build WHERE clause from filters
      const whereConditions: string[] = [];
      const queryParams: (string | number | boolean)[] = [];
      let paramIndex = 1;

      // ALWAYS add tenant_id filter for security (manual fallback if RLS fails)
      whereConditions.push(`tenant_id = $${paramIndex++}`);
      queryParams.push(this.tenantId);

      // ALWAYS filter out soft-deleted records
      whereConditions.push(`deleted_at IS NULL`);

      // Add basic filters
      if (filters.id) {
        whereConditions.push(`id = $${paramIndex++}`);
        queryParams.push(filters.id);
      }

      if (filters.name) {
        whereConditions.push(`name ILIKE $${paramIndex++}`);
        queryParams.push(`%${filters.name}%`);
      }

      if (filters.is_active !== undefined) {
        whereConditions.push(`is_active = $${paramIndex++}`);
        queryParams.push(filters.is_active);
      }

      if (filters.is_internal !== undefined) {
        whereConditions.push(`is_internal = $${paramIndex++}`);
        queryParams.push(filters.is_internal);
      }

      if (filters.parent_category_id) {
        whereConditions.push(`parent_category_id = $${paramIndex++}`);
        queryParams.push(filters.parent_category_id);
      }

      // Build query
      let query = 'SELECT * FROM product_category';
      if (whereConditions.length > 0) {
        query += ' WHERE ' + whereConditions.join(' AND ');
      }

      // Add ordering and limits
      query += ' ORDER BY created_at DESC';

      if (config?.take) {
        query += ` LIMIT $${paramIndex++}`;
        queryParams.push(config.take);
      }

      if (config?.skip) {
        query += ` OFFSET $${paramIndex++}`;
        queryParams.push(config.skip);
      }

      console.log(
        `🔍 [TENANT-CATEGORY] Executing direct query for tenant ${this.tenantId}:`,
        query
      );

      const result = await this.pgConnection.query(query, queryParams);

      // Get count (without limit/offset)
      let countQuery = 'SELECT COUNT(*) FROM product_category';
      if (whereConditions.length > 0) {
        countQuery += ' WHERE ' + whereConditions.join(' AND ');
      }

      const countParams = queryParams.slice(
        0,
        paramIndex - 1 - (config?.take ? 1 : 0) - (config?.skip ? 1 : 0)
      );
      const countResult = await this.pgConnection.query(countQuery, countParams);
      const totalCount = parseInt(countResult.rows[0].count);

      const categories = result.rows;

      // Load relations if requested
      if (config?.relations && config.relations.length > 0) {
        console.log(
          `🔗 [TENANT-CATEGORY] Loading relations for ${categories.length} categories:`,
          config.relations
        );
        await this.loadCategoryRelations(categories, config.relations);
      }

      console.log(
        `✅ [TENANT-CATEGORY] Direct query found ${categories.length} categories (${totalCount} total) for tenant: ${this.tenantId}`
      );
      return [categories, totalCount];
    } catch (error) {
      console.error(`❌ [TENANT-CATEGORY] Direct query failed: ${error}`);
      throw error;
    }
  }

  /**
   * Load relations for categories
   */
  private async loadCategoryRelations(categories: any[], relations: string[]): Promise<void> {
    if (!categories.length) return;

    for (const relation of relations) {
      switch (relation) {
        case 'category_children':
          await this.loadCategoryChildren(categories);
          break;
        case 'parent_category':
          await this.loadParentCategories(categories);
          break;
        default:
          console.log(`⚠️ [TENANT-CATEGORY] Unknown relation: ${relation}`);
      }
    }
  }

  /**
   * Load category children for categories
   */
  private async loadCategoryChildren(categories: any[]): Promise<void> {
    if (!categories.length) return;

    try {
      const categoryIds = categories.map(cat => cat.id);

      // Query for all children of these categories
      const childrenQuery = `
        SELECT * FROM product_category
        WHERE parent_category_id = ANY($1)
        AND tenant_id = $2
        AND deleted_at IS NULL
        ORDER BY name ASC
      `;

      const result = await this.pgConnection.query(childrenQuery, [categoryIds, this.tenantId]);
      const children = result.rows;

      // Group children by parent_category_id
      const childrenByParent = children.reduce((acc, child) => {
        if (!acc[child.parent_category_id]) {
          acc[child.parent_category_id] = [];
        }
        acc[child.parent_category_id].push(child);
        return acc;
      }, {});

      // Assign children to their parent categories
      categories.forEach(category => {
        category.category_children = childrenByParent[category.id] || [];
      });

      console.log(
        `🔗 [TENANT-CATEGORY] Loaded ${children.length} children for ${categories.length} categories`
      );
    } catch (error) {
      console.error(`❌ [TENANT-CATEGORY] Error loading category children: ${error}`);
      // Set empty arrays as fallback
      categories.forEach(category => {
        category.category_children = [];
      });
    }
  }

  /**
   * Load parent categories for categories
   */
  private async loadParentCategories(categories: any[]): Promise<void> {
    if (!categories.length) return;

    try {
      const parentIds = categories
        .map(cat => cat.parent_category_id)
        .filter(id => id !== null && id !== undefined);

      if (!parentIds.length) {
        // No parent categories to load
        categories.forEach(category => {
          category.parent_category = null;
        });
        return;
      }

      // Query for all parent categories
      const parentsQuery = `
        SELECT * FROM product_category
        WHERE id = ANY($1)
        AND tenant_id = $2
        AND deleted_at IS NULL
      `;

      const result = await this.pgConnection.query(parentsQuery, [parentIds, this.tenantId]);
      const parents = result.rows;

      // Create a map of parent categories by ID
      const parentsById = parents.reduce((acc, parent) => {
        acc[parent.id] = parent;
        return acc;
      }, {});

      // Assign parent categories
      categories.forEach(category => {
        category.parent_category = category.parent_category_id
          ? parentsById[category.parent_category_id] || null
          : null;
      });

      console.log(
        `🔗 [TENANT-CATEGORY] Loaded ${parents.length} parent categories for ${categories.length} categories`
      );
    } catch (error) {
      console.error(`❌ [TENANT-CATEGORY] Error loading parent categories: ${error}`);
      // Set null as fallback
      categories.forEach(category => {
        category.parent_category = null;
      });
    }
  }
}
