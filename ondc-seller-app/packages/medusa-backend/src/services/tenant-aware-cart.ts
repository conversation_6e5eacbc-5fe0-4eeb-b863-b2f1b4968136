/**
 * Tenant-Aware Cart Service
 * 
 * Provides complete CRUD operations for carts with automatic tenant isolation.
 * Integrates with Row Level Security (RLS) for database-level tenant filtering.
 */

import { MedusaRequest } from "@medusajs/framework/http";
import { TenantQueryInterceptor } from "./tenant-query-interceptor";

export class TenantAwareCartService {
  private cartService: any;
  private tenantId: string;
  private queryInterceptor: TenantQueryInterceptor;
  private pgConnection: any;

  constructor(cartService: any, tenantId: string = 'default', pgConnection?: any) {
    this.cartService = cartService;
    this.tenantId = tenantId;
    this.queryInterceptor = new TenantQueryInterceptor({
      tenantId,
      enableLogging: true,
      strictMode: false
    });
    this.pgConnection = pgConnection;
    
    // Set tenant context in database for RLS
    this.setDatabaseTenantContext();
  }

  /**
   * Set tenant context in database for Row Level Security
   */
  private async setDatabaseTenantContext(): Promise<void> {
    if (this.pgConnection) {
      try {
        await this.pgConnection.query('SELECT set_config($1, $2, true)', [
          'app.tenant_context.tenant_id', 
          this.tenantId
        ]);
        console.log(`🔒 [TENANT-CART] Set database tenant context: ${this.tenantId}`);
      } catch (error) {
        console.warn(`⚠️  [TENANT-CART] Failed to set database tenant context: ${error}`);
      }
    }
  }

  /**
   * Create a tenant-aware cart service instance from request
   */
  static fromRequest(req: MedusaRequest): TenantAwareCartService {
    const cartService = req.scope.resolve('cart');
    const tenantId = req.tenant_id || 'default';
    
    // Try to get database connection for RLS context setting
    let pgConnection = null;
    try {
      const manager = req.scope.resolve('manager');
      if (manager && typeof manager.query === 'function') {
        pgConnection = manager;
        console.log(`✅ [TENANT-CART] Found database connection via manager`);
      }
    } catch (error) {
      console.warn(`⚠️  [TENANT-CART] Error resolving database connection: ${error}`);
    }
    
    console.log(`🛒 [TENANT-CART] Creating tenant-aware cart service for tenant: ${tenantId}`);
    
    return new TenantAwareCartService(cartService, tenantId, pgConnection);
  }

  // ============================================================================
  // CREATE OPERATIONS
  // ============================================================================

  /**
   * Create carts with automatic tenant_id injection
   */
  async createCarts(cartsData: any[]): Promise<any> {
    console.log(`🛒 [TENANT-CART] Creating ${cartsData.length} carts for tenant: ${this.tenantId}`);
    
    try {
      // Inject tenant_id into all cart data
      const tenantCartsData = cartsData.map(cartData => ({
        ...cartData,
        tenant_id: this.tenantId
      }));
      
      const result = await this.cartService.createCarts(tenantCartsData);
      console.log(`✅ [TENANT-CART] Created ${result.length} carts for tenant: ${this.tenantId}`);
      return result;
    } catch (error) {
      console.error(`❌ [TENANT-CART] Error creating carts: ${error}`);
      throw error;
    }
  }

  // ============================================================================
  // READ OPERATIONS
  // ============================================================================

  /**
   * List carts with tenant filtering
   */
  async listCarts(filters: any = {}, config?: any, context?: any): Promise<any> {
    console.log(`🛒 [TENANT-CART] Listing carts for tenant: ${this.tenantId}`);
    
    try {
      // Set database tenant context for RLS
      await this.setDatabaseTenantContext();
      
      // Use the service method directly - RLS will handle filtering
      const result = await this.cartService.listCarts(filters, config, context);
      
      const cartCount = Array.isArray(result) ? result.length : (result?.carts?.length || 0);
      console.log(`✅ [TENANT-CART] Found ${cartCount} carts for tenant: ${this.tenantId}`);
      
      return result;
    } catch (error) {
      console.error(`❌ [TENANT-CART] Error listing carts: ${error}`);
      
      // Return empty result structure
      return {
        carts: [],
        count: 0,
        offset: config?.skip || 0,
        limit: config?.take || 20
      };
    }
  }

  /**
   * List and count carts with tenant filtering
   */
  async listAndCountCarts(filters: any = {}, config?: any, context?: any): Promise<any> {
    console.log(`🛒 [TENANT-CART] Listing and counting carts for tenant: ${this.tenantId}`);
    
    try {
      // Set database tenant context for RLS
      await this.setDatabaseTenantContext();
      
      const result = await this.cartService.listAndCountCarts(filters, config, context);
      
      // Handle different result formats
      let carts = [];
      let count = 0;
      
      if (Array.isArray(result) && result.length === 2) {
        carts = result[0] || [];
        count = result[1] || 0;
      } else if (result?.carts) {
        carts = result.carts || [];
        count = result.count || carts.length;
      } else if (Array.isArray(result)) {
        carts = result;
        count = carts.length;
      }
      
      console.log(`✅ [TENANT-CART] Found ${count} total carts for tenant: ${this.tenantId}`);
      return [carts, count];
    } catch (error) {
      console.error(`❌ [TENANT-CART] Error listing and counting carts: ${error}`);
      return [[], 0];
    }
  }

  /**
   * Retrieve a single cart by ID (with tenant validation)
   */
  async retrieveCart(cartId: string, config?: any, context?: any): Promise<any> {
    console.log(`🛒 [TENANT-CART] Retrieving cart ${cartId} for tenant: ${this.tenantId}`);
    
    try {
      // Set database tenant context for RLS
      await this.setDatabaseTenantContext();
      
      const cart = await this.cartService.retrieveCart(cartId, config, context);
      
      if (!cart) {
        throw new Error(`Cart ${cartId} not found or not accessible for tenant: ${this.tenantId}`);
      }
      
      console.log(`✅ [TENANT-CART] Retrieved cart ${cartId} for tenant: ${this.tenantId}`);
      return cart;
    } catch (error) {
      console.error(`❌ [TENANT-CART] Error retrieving cart ${cartId}: ${error}`);
      throw error;
    }
  }

  // ============================================================================
  // UPDATE OPERATIONS
  // ============================================================================

  /**
   * Update carts with tenant validation
   */
  async updateCarts(cartsData: any[]): Promise<any> {
    console.log(`🛒 [TENANT-CART] Updating ${cartsData.length} carts for tenant: ${this.tenantId}`);
    
    try {
      // Set database tenant context for RLS
      await this.setDatabaseTenantContext();
      
      // Ensure tenant_id is preserved in updates
      const tenantCartsData = cartsData.map(cartData => ({
        ...cartData,
        tenant_id: this.tenantId // Ensure tenant_id is not changed
      }));
      
      const result = await this.cartService.updateCarts(tenantCartsData);
      console.log(`✅ [TENANT-CART] Updated ${result.length} carts for tenant: ${this.tenantId}`);
      return result;
    } catch (error) {
      console.error(`❌ [TENANT-CART] Error updating carts: ${error}`);
      throw error;
    }
  }

  // ============================================================================
  // DELETE OPERATIONS
  // ============================================================================

  /**
   * Delete carts with tenant validation
   */
  async deleteCarts(cartIds: string[]): Promise<void> {
    console.log(`🛒 [TENANT-CART] Deleting ${cartIds.length} carts for tenant: ${this.tenantId}`);
    
    try {
      // Set database tenant context for RLS
      await this.setDatabaseTenantContext();
      
      await this.cartService.deleteCarts(cartIds);
      console.log(`✅ [TENANT-CART] Deleted ${cartIds.length} carts for tenant: ${this.tenantId}`);
    } catch (error) {
      console.error(`❌ [TENANT-CART] Error deleting carts: ${error}`);
      throw error;
    }
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /**
   * Get cart statistics for tenant
   */
  async getCartStats(): Promise<{
    totalCarts: number;
    activeCarts: number;
    completedCarts: number;
    averageItemsPerCart: number;
  }> {
    console.log(`📊 [TENANT-CART] Getting cart statistics for tenant: ${this.tenantId}`);
    
    try {
      const [carts, count] = await this.listAndCountCarts();
      
      // Calculate statistics
      const activeCarts = carts.filter((cart: any) => cart.status === 'active' || !cart.completed_at).length;
      const completedCarts = carts.filter((cart: any) => cart.completed_at).length;
      const totalItems = carts.reduce((sum: number, cart: any) => sum + (cart.items?.length || 0), 0);
      const averageItemsPerCart = count > 0 ? Math.round(totalItems / count * 100) / 100 : 0;
      
      const stats = {
        totalCarts: count,
        activeCarts,
        completedCarts,
        averageItemsPerCart
      };
      
      console.log(`📊 [TENANT-CART] Stats for tenant ${this.tenantId}:`, stats);
      return stats;
      
    } catch (error) {
      console.error(`❌ [TENANT-CART] Error getting cart stats: ${error}`);
      throw error;
    }
  }

  /**
   * Add tenant filter to query filters
   */
  private addTenantFilter(filters: any): any {
    return {
      ...filters,
      tenant_id: this.tenantId
    };
  }
}
