/**
 * Tenant-Aware Product Service (Updated)
 *
 * Uses direct database queries with RLS instead of trying to modify service schemas.
 * This approach works with the existing Medusa v2 architecture.
 */

import { MedusaRequest } from '@medusajs/framework/http';
import { TenantQueryInterceptor } from './tenant-query-interceptor';

export class TenantAwareProductService {
  private productService: any;
  private tenantId: string;
  private queryInterceptor: TenantQueryInterceptor;
  private pgConnection: any;

  constructor(productService: any, tenantId: string = 'default', pgConnection?: any) {
    this.productService = productService;
    this.tenantId = tenantId;
    this.queryInterceptor = new TenantQueryInterceptor({
      tenantId,
      enableLogging: true,
      strictMode: false,
    });
    this.pgConnection = pgConnection;

    // Set tenant context in database for RLS
    this.setDatabaseTenantContext();
  }

  /**
   * Set tenant context in database for Row Level Security
   */
  private async setDatabaseTenantContext(): Promise<void> {
    if (this.pgConnection) {
      try {
        await this.pgConnection.query('SELECT set_config($1, $2, true)', [
          'app.tenant_context.tenant_id',
          this.tenantId,
        ]);
        console.log(`🔒 [TENANT-PRODUCT] Set database tenant context: ${this.tenantId}`);
      } catch (error) {
        console.warn(`⚠️  [TENANT-PRODUCT] Failed to set database tenant context: ${error}`);
      }
    }
  }

  /**
   * Create a tenant-aware product service instance from request
   */
  static fromRequest(req: MedusaRequest): TenantAwareProductService {
    const productService = req.scope.resolve('product');
    const tenantId = req.tenant_id || 'default';

    // Try to get database connection for RLS context setting
    let pgConnection = null;
    try {
      // Try to get the database manager/connection
      const manager = req.scope.resolve('manager');
      if (manager && typeof manager.query === 'function') {
        pgConnection = manager;
        console.log(`✅ [TENANT-PRODUCT] Found database connection via manager`);
      } else {
        // Try other connection names
        const possibleConnections = ['__pg_connection__', 'dbConnection', 'database'];

        for (const connectionName of possibleConnections) {
          try {
            const connection = req.scope.resolve(connectionName);
            if (connection && typeof connection.query === 'function') {
              pgConnection = connection;
              console.log(`✅ [TENANT-PRODUCT] Found database connection: ${connectionName}`);
              break;
            }
          } catch (error) {
            // Continue trying other connection names
          }
        }
      }

      if (!pgConnection) {
        console.warn(
          `⚠️  [TENANT-PRODUCT] No database connection found, creating direct connection`
        );
        // Create direct database connection
        try {
          const { Client } = require('pg');
          pgConnection = new Client({
            connectionString:
              process.env.DATABASE_URL ||
              process.env.POSTGRES_URL ||
              'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
          });
          // Connect asynchronously without blocking
          pgConnection
            .connect()
            .then(() => {
              console.log(
                `✅ [TENANT-PRODUCT] Created direct database connection for tenant: ${tenantId}`
              );
            })
            .catch((dbError: any) => {
              console.error(`❌ [TENANT-PRODUCT] Failed to connect to database: ${dbError}`);
            });
        } catch (dbError) {
          console.error(
            `❌ [TENANT-PRODUCT] Failed to create direct database connection: ${dbError}`
          );
          pgConnection = null;
        }
      }
    } catch (error) {
      console.warn(`⚠️  [TENANT-PRODUCT] Error resolving database connection: ${error}`);
    }

    console.log(
      `🏢 [TENANT-PRODUCT] Creating tenant-aware product service for tenant: ${tenantId}`
    );

    return new TenantAwareProductService(productService, tenantId, pgConnection);
  }

  /**
   * Inject tenant_id into product data
   */
  private injectTenantId(data: any): any {
    if (Array.isArray(data)) {
      return data.map(item => ({ ...item, tenant_id: this.tenantId }));
    }
    return { ...data, tenant_id: this.tenantId };
  }

  /**
   * Add tenant filter to query filters
   */
  private addTenantFilter(filters: any = {}): any {
    return { ...filters, tenant_id: this.tenantId };
  }

  // ============================================================================
  // PRODUCT CRUD OPERATIONS WITH TENANT CONTEXT
  // ============================================================================

  /**
   * Create products with automatic tenant injection
   */
  async createProducts(data: any[], context?: any): Promise<any> {
    console.log(
      `🏢 [TENANT-PRODUCT] Creating ${data.length} products for tenant: ${this.tenantId}`
    );

    const tenantData = this.injectTenantId(data);
    const result = await this.productService.createProducts(tenantData, context);

    console.log(
      `✅ [TENANT-PRODUCT] Created ${result.length} products for tenant: ${this.tenantId}`
    );
    return result;
  }

  /**
   * Update products with tenant validation
   */
  async updateProducts(selector: any, data: any, context?: any): Promise<any> {
    console.log(`🏢 [TENANT-PRODUCT] Updating products for tenant: ${this.tenantId}`);

    // Add tenant filter to selector to ensure we only update tenant's products
    const tenantSelector = this.addTenantFilter(selector);
    const result = await this.productService.updateProducts(tenantSelector, data, context);

    console.log(`✅ [TENANT-PRODUCT] Updated products for tenant: ${this.tenantId}`);
    return result;
  }

  /**
   * List products with tenant awareness
   */
  async listProducts(filters: any = {}, config?: any, context?: any): Promise<any> {
    console.log(`🏢 [TENANT-PRODUCT] Listing products for tenant: ${this.tenantId}`);

    try {
      // Use the service method directly - Medusa v2 handles the filtering
      const result = await this.productService.listProducts(filters, config, context);

      // Log the result
      const productCount = Array.isArray(result) ? result.length : result?.products?.length || 0;
      console.log(
        `✅ [TENANT-PRODUCT] Found ${productCount} products for tenant: ${this.tenantId}`
      );

      return result;
    } catch (error) {
      console.error(`❌ [TENANT-PRODUCT] Error listing products: ${error}`);

      // Return empty result structure that matches Medusa's expected format
      return {
        products: [],
        count: 0,
        offset: config?.skip || 0,
        limit: config?.take || 20,
      };
    }
  }

  /**
   * List and count products with tenant awareness
   */
  async listAndCountProducts(filters: any = {}, config?: any, context?: any): Promise<any> {
    console.log(`🏢 [TENANT-PRODUCT] Listing and counting products for tenant: ${this.tenantId}`);

    try {
      // Use direct database query to bypass Medusa ORM issues
      console.log(`🔍 [TENANT-PRODUCT] Using direct database query for tenant: ${this.tenantId}`);

      return await this.listAndCountProductsDirectQuery(filters, config);
    } catch (error) {
      console.error(`❌ [TENANT-PRODUCT] Error listing and counting products: ${error}`);
      return [[], 0];
    }
  }

  /**
   * Retrieve single product with tenant validation
   */
  async retrieveProduct(id: string, config?: any, context?: any): Promise<any> {
    console.log(`🏢 [TENANT-PRODUCT] Retrieving product ${id} for tenant: ${this.tenantId}`);

    // First check if product belongs to tenant
    const products = await this.listProducts({ id }, { take: 1 }, context);

    if (products.length === 0) {
      console.warn(`⚠️  [TENANT-PRODUCT] Product ${id} not found for tenant: ${this.tenantId}`);
      throw new Error(`Product ${id} not found for tenant ${this.tenantId}`);
    }

    const result = await this.productService.retrieveProduct(id, config, context);
    console.log(`✅ [TENANT-PRODUCT] Retrieved product ${id} for tenant: ${this.tenantId}`);
    return result;
  }

  // ============================================================================
  // PRODUCT VARIANT OPERATIONS WITH TENANT CONTEXT
  // ============================================================================

  /**
   * Create product variants with tenant injection
   */
  async createProductVariants(data: any[], context?: any): Promise<any> {
    console.log(
      `🏢 [TENANT-PRODUCT] Creating ${data.length} product variants for tenant: ${this.tenantId}`
    );

    const tenantData = this.injectTenantId(data);
    const result = await this.productService.createProductVariants(tenantData, context);

    console.log(
      `✅ [TENANT-PRODUCT] Created ${result.length} product variants for tenant: ${this.tenantId}`
    );
    return result;
  }

  /**
   * Update product variants with tenant validation
   */
  async updateProductVariants(selector: any, data: any, context?: any): Promise<any> {
    console.log(`🏢 [TENANT-PRODUCT] Updating product variants for tenant: ${this.tenantId}`);

    const tenantSelector = this.addTenantFilter(selector);
    const result = await this.productService.updateProductVariants(tenantSelector, data, context);

    console.log(`✅ [TENANT-PRODUCT] Updated product variants for tenant: ${this.tenantId}`);
    return result;
  }

  // ============================================================================
  // PRODUCT CATEGORY OPERATIONS WITH TENANT CONTEXT
  // ============================================================================

  /**
   * Create product categories with tenant injection
   */
  async createProductCategories(data: any[], context?: any): Promise<any> {
    console.log(
      `🏢 [TENANT-PRODUCT] Creating ${data.length} product categories for tenant: ${this.tenantId}`
    );

    const tenantData = this.injectTenantId(data);
    const result = await this.productService.createProductCategories(tenantData, context);

    console.log(
      `✅ [TENANT-PRODUCT] Created ${result.length} product categories for tenant: ${this.tenantId}`
    );
    return result;
  }

  /**
   * Update product categories with tenant validation
   */
  async updateProductCategories(selector: any, data: any, context?: any): Promise<any> {
    console.log(`🏢 [TENANT-PRODUCT] Updating product categories for tenant: ${this.tenantId}`);

    const tenantSelector = this.addTenantFilter(selector);
    const result = await this.productService.updateProductCategories(tenantSelector, data, context);

    console.log(`✅ [TENANT-PRODUCT] Updated product categories for tenant: ${this.tenantId}`);
    return result;
  }

  // ============================================================================
  // PRODUCT COLLECTION OPERATIONS WITH TENANT CONTEXT
  // ============================================================================

  /**
   * Create product collections with tenant injection
   */
  async createProductCollections(data: any[], context?: any): Promise<any> {
    console.log(
      `🏢 [TENANT-PRODUCT] Creating ${data.length} product collections for tenant: ${this.tenantId}`
    );

    const tenantData = this.injectTenantId(data);
    const result = await this.productService.createProductCollections(tenantData, context);

    console.log(
      `✅ [TENANT-PRODUCT] Created ${result.length} product collections for tenant: ${this.tenantId}`
    );
    return result;
  }

  /**
   * Update product collections with tenant validation
   */
  async updateProductCollections(selector: any, data: any, context?: any): Promise<any> {
    console.log(`🏢 [TENANT-PRODUCT] Updating product collections for tenant: ${this.tenantId}`);

    const tenantSelector = this.addTenantFilter(selector);
    const result = await this.productService.updateProductCollections(
      tenantSelector,
      data,
      context
    );

    console.log(`✅ [TENANT-PRODUCT] Updated product collections for tenant: ${this.tenantId}`);
    return result;
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /**
   * Get tenant ID for this service instance
   */
  getTenantId(): string {
    return this.tenantId;
  }

  /**
   * Get underlying product service
   */
  getProductService(): any {
    return this.productService;
  }

  /**
   * Validate product ownership by tenant
   */
  async validateProductOwnership(productId: string): Promise<boolean> {
    try {
      const products = await this.listProducts({ id: productId }, { take: 1 });
      return products.length > 0;
    } catch (error) {
      console.error(`❌ [TENANT-PRODUCT] Error validating product ownership: ${error}`);
      return false;
    }
  }

  // ============================================================================
  // DIRECT DATABASE QUERY METHODS (Using RLS)
  // ============================================================================

  /**
   * List products using direct database query with RLS
   */
  private async listProductsDirectQuery(filters: any = {}, config?: any): Promise<any> {
    // Ensure database connection is available
    if (!this.pgConnection) {
      console.log(`🔗 [TENANT-PRODUCT] Creating database connection for direct query`);
      try {
        const { Client } = require('pg');
        this.pgConnection = new Client({
          connectionString:
            process.env.DATABASE_URL ||
            process.env.POSTGRES_URL ||
            'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
        });
        await this.pgConnection.connect();
        console.log(
          `✅ [TENANT-PRODUCT] Database connection established for tenant: ${this.tenantId}`
        );
      } catch (error) {
        console.error(`❌ [TENANT-PRODUCT] Failed to create database connection: ${error}`);
        return [];
      }
    }

    try {
      // Set tenant context for RLS
      await this.pgConnection.query('SELECT set_config($1, $2, true)', [
        'app.tenant_context.tenant_id',
        this.tenantId,
      ]);

      // Build WHERE clause from filters
      const whereConditions = [];
      const queryParams = [];
      let paramIndex = 1;

      // ALWAYS add tenant_id filter for security (manual fallback if RLS fails)
      whereConditions.push(`tenant_id = $${paramIndex++}`);
      queryParams.push(this.tenantId);

      // ALWAYS filter out soft-deleted records
      whereConditions.push(`deleted_at IS NULL`);

      // Add basic filters
      if (filters.id) {
        whereConditions.push(`id = $${paramIndex++}`);
        queryParams.push(filters.id);
      }

      if (filters.title) {
        whereConditions.push(`title ILIKE $${paramIndex++}`);
        queryParams.push(`%${filters.title}%`);
      }

      if (filters.status) {
        whereConditions.push(`status = $${paramIndex++}`);
        queryParams.push(filters.status);
      }

      // Add category filtering
      if (filters.category_id) {
        whereConditions.push(`id IN (
          SELECT pcp.product_id
          FROM product_category_product pcp
          WHERE pcp.product_category_id = $${paramIndex++}
        )`);
        queryParams.push(filters.category_id);
      }

      // Build query
      let query = 'SELECT * FROM product';
      if (whereConditions.length > 0) {
        query += ' WHERE ' + whereConditions.join(' AND ');
      }

      // Add ordering and limits
      query += ' ORDER BY created_at DESC';

      if (config?.take) {
        query += ` LIMIT $${paramIndex++}`;
        queryParams.push(config.take);
      }

      if (config?.skip) {
        query += ` OFFSET $${paramIndex++}`;
        queryParams.push(config.skip);
      }

      console.log(`🔍 [TENANT-PRODUCT] Executing direct query for tenant ${this.tenantId}:`, query);

      const result = await this.pgConnection.query(query, queryParams);

      const products = result.rows;

      console.log(
        `✅ [TENANT-PRODUCT] Direct query found ${products.length} products for tenant: ${this.tenantId}`
      );

      // If no products found, return empty array
      if (products.length === 0) {
        return products;
      }

      // Load related data for each product
      const enrichedProducts = await this.loadProductRelations(products, config);

      return enrichedProducts;
    } catch (error) {
      console.error(`❌ [TENANT-PRODUCT] Direct query failed: ${error}`);
      throw error;
    }
  }

  /**
   * List and count products using direct database query with RLS
   */
  private async listAndCountProductsDirectQuery(
    filters: any = {},
    config?: any
  ): Promise<[any[], number]> {
    // Ensure database connection is available
    if (!this.pgConnection) {
      console.log(`🔗 [TENANT-PRODUCT] Creating database connection for direct query`);
      try {
        const { Client } = require('pg');
        this.pgConnection = new Client({
          connectionString:
            process.env.DATABASE_URL ||
            process.env.POSTGRES_URL ||
            'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
        });
        await this.pgConnection.connect();
        console.log(
          `✅ [TENANT-PRODUCT] Database connection established for tenant: ${this.tenantId}`
        );
      } catch (error) {
        console.error(`❌ [TENANT-PRODUCT] Failed to create database connection: ${error}`);
        return [[], 0];
      }
    }

    try {
      // Set tenant context for RLS
      await this.pgConnection.query('SELECT set_config($1, $2, true)', [
        'app.tenant_context.tenant_id',
        this.tenantId,
      ]);

      // Get products
      const products = await this.listProductsDirectQuery(filters, config);

      // Get count (without limit/offset)
      const whereConditions = [];
      const queryParams = [];
      let paramIndex = 1;

      // ALWAYS add tenant_id filter for security (manual fallback if RLS fails)
      whereConditions.push(`tenant_id = $${paramIndex++}`);
      queryParams.push(this.tenantId);

      // ALWAYS filter out soft-deleted records
      whereConditions.push(`deleted_at IS NULL`);

      if (filters.id) {
        whereConditions.push(`id = $${paramIndex++}`);
        queryParams.push(filters.id);
      }

      if (filters.title) {
        whereConditions.push(`title ILIKE $${paramIndex++}`);
        queryParams.push(`%${filters.title}%`);
      }

      if (filters.status) {
        whereConditions.push(`status = $${paramIndex++}`);
        queryParams.push(filters.status);
      }

      // Add category filtering for count query
      if (filters.category_id) {
        whereConditions.push(`id IN (
          SELECT pcp.product_id
          FROM product_category_product pcp
          WHERE pcp.product_category_id = $${paramIndex++}
        )`);
        queryParams.push(filters.category_id);
      }

      let countQuery = 'SELECT COUNT(*) FROM product';
      if (whereConditions.length > 0) {
        countQuery += ' WHERE ' + whereConditions.join(' AND ');
      }

      const countResult = await this.pgConnection.query(countQuery, queryParams);
      const totalCount = parseInt(countResult.rows[0].count);

      console.log(
        `✅ [TENANT-PRODUCT] Direct query found ${products.length} products (${totalCount} total) for tenant: ${this.tenantId}`
      );
      return [products, totalCount];
    } catch (error) {
      console.error(`❌ [TENANT-PRODUCT] Direct count query failed: ${error}`);
      throw error;
    }
  }

  /**
   * Get product statistics for tenant
   */
  async getProductStats(): Promise<{
    totalProducts: number;
    totalVariants: number;
    totalCategories: number;
    totalCollections: number;
  }> {
    console.log(`📊 [TENANT-PRODUCT] Getting product statistics for tenant: ${this.tenantId}`);

    try {
      // Use service-based approach (no direct database queries)
      const [products, count] = await this.listAndCountProducts();

      const stats = {
        totalProducts: count,
        totalVariants: 0, // Would need variant service
        totalCategories: 0, // Would need category service
        totalCollections: 0, // Would need collection service
      };

      console.log(`📊 [TENANT-PRODUCT] Stats for tenant ${this.tenantId}:`, stats);
      return stats;
    } catch (error) {
      console.error(`❌ [TENANT-PRODUCT] Error getting product stats: ${error}`);
      throw error;
    }
  }

  /**
   * Load related data for products (variants, images, categories, etc.)
   */
  private async loadProductRelations(products: any[], config?: any): Promise<any[]> {
    if (!products || products.length === 0) {
      return products;
    }

    const productIds = products.map(p => p.id);
    const relations = config?.relations || [];

    console.log(
      `🔗 [TENANT-PRODUCT] Loading relations for ${products.length} products:`,
      relations
    );

    // Load variants if requested
    if (relations.includes('variants') || relations.includes('variants.prices')) {
      await this.loadProductVariants(products, relations.includes('variants.prices'));
    }

    // Load images if requested
    if (relations.includes('images')) {
      await this.loadProductImages(products);
    }

    // Load categories if requested
    if (relations.includes('categories')) {
      await this.loadProductCategories(products);
    }

    // Load tags if requested
    if (relations.includes('tags')) {
      await this.loadProductTags(products);
    }

    return products;
  }

  /**
   * Load variants for products
   */
  private async loadProductVariants(
    products: any[],
    includePrices: boolean = false
  ): Promise<void> {
    const productIds = products.map(p => p.id);

    try {
      // Load variants
      const variantQuery = `
        SELECT * FROM product_variant
        WHERE product_id = ANY($1) AND tenant_id = $2 AND deleted_at IS NULL
        ORDER BY variant_rank ASC, created_at ASC
      `;

      const variantResult = await this.pgConnection.query(variantQuery, [
        productIds,
        this.tenantId,
      ]);
      const variants = variantResult.rows;

      console.log(
        `🔗 [TENANT-PRODUCT] Loaded ${variants.length} variants for ${products.length} products`
      );

      // Group variants by product_id
      const variantsByProduct = variants.reduce((acc, variant) => {
        if (!acc[variant.product_id]) {
          acc[variant.product_id] = [];
        }
        acc[variant.product_id].push(variant);
        return acc;
      }, {});

      // Assign variants to products
      products.forEach(product => {
        product.variants = variantsByProduct[product.id] || [];
      });

      // Load prices if requested
      if (includePrices && variants.length > 0) {
        await this.loadVariantPrices(variants);
      }
    } catch (error) {
      console.error(`❌ [TENANT-PRODUCT] Error loading variants: ${error}`);
    }
  }

  /**
   * Load prices for variants
   */
  private async loadVariantPrices(variants: any[]): Promise<void> {
    const variantIds = variants.map(v => v.id);

    try {
      const priceQuery = `
        SELECT p.*, pvps.variant_id
        FROM price p
        JOIN product_variant_price_set pvps ON p.price_set_id = pvps.price_set_id
        WHERE pvps.variant_id = ANY($1) AND p.tenant_id = $2 AND p.deleted_at IS NULL
        ORDER BY p.currency_code, p.amount
      `;

      const priceResult = await this.pgConnection.query(priceQuery, [variantIds, this.tenantId]);
      const prices = priceResult.rows;

      console.log(
        `🔗 [TENANT-PRODUCT] Loaded ${prices.length} prices for ${variants.length} variants`
      );

      // Group prices by variant_id
      const pricesByVariant = prices.reduce((acc, price) => {
        if (!acc[price.variant_id]) {
          acc[price.variant_id] = [];
        }
        acc[price.variant_id].push(price);
        return acc;
      }, {});

      // Assign prices to variants
      variants.forEach(variant => {
        variant.prices = pricesByVariant[variant.id] || [];
      });
    } catch (error) {
      console.error(`❌ [TENANT-PRODUCT] Error loading variant prices: ${error}`);
    }
  }

  /**
   * Load images for products
   */
  private async loadProductImages(products: any[]): Promise<void> {
    const productIds = products.map(p => p.id);

    try {
      const imageQuery = `
        SELECT * FROM image
        WHERE product_id = ANY($1) AND deleted_at IS NULL
        ORDER BY rank ASC, created_at ASC
      `;

      const imageResult = await this.pgConnection.query(imageQuery, [productIds]);
      const images = imageResult.rows;

      console.log(
        `🔗 [TENANT-PRODUCT] Loaded ${images.length} images for ${products.length} products`
      );

      // Group images by product_id
      const imagesByProduct = images.reduce((acc, image) => {
        if (!acc[image.product_id]) {
          acc[image.product_id] = [];
        }
        acc[image.product_id].push(image);
        return acc;
      }, {});

      // Assign images to products
      products.forEach(product => {
        product.images = imagesByProduct[product.id] || [];
      });
    } catch (error) {
      console.error(`❌ [TENANT-PRODUCT] Error loading images: ${error}`);
    }
  }

  /**
   * Load categories for products
   */
  private async loadProductCategories(products: any[]): Promise<void> {
    const productIds = products.map(p => p.id);

    try {
      const categoryQuery = `
        SELECT pc.*, pcp.product_id
        FROM product_category pc
        JOIN product_category_product pcp ON pc.id = pcp.product_category_id
        WHERE pcp.product_id = ANY($1) AND pc.tenant_id = $2 AND pc.deleted_at IS NULL
        ORDER BY pc.name ASC
      `;

      const categoryResult = await this.pgConnection.query(categoryQuery, [
        productIds,
        this.tenantId,
      ]);
      const categories = categoryResult.rows;

      console.log(
        `🔗 [TENANT-PRODUCT] Loaded ${categories.length} categories for ${products.length} products`
      );

      // Group categories by product_id
      const categoriesByProduct = categories.reduce((acc, category) => {
        if (!acc[category.product_id]) {
          acc[category.product_id] = [];
        }
        acc[category.product_id].push(category);
        return acc;
      }, {});

      // Assign categories to products
      products.forEach(product => {
        product.categories = categoriesByProduct[product.id] || [];
      });
    } catch (error) {
      console.error(`❌ [TENANT-PRODUCT] Error loading categories: ${error}`);
    }
  }

  /**
   * Load tags for products
   */
  private async loadProductTags(products: any[]): Promise<void> {
    const productIds = products.map(p => p.id);

    try {
      // Check if product_tags junction table exists and has the right structure
      const tagQuery = `
        SELECT pt.*, ptp.product_id
        FROM product_tag pt
        JOIN product_tags ptp ON pt.id = ptp.product_tag_id
        WHERE ptp.product_id = ANY($1) AND pt.deleted_at IS NULL
        ORDER BY pt.value ASC
      `;

      const tagResult = await this.pgConnection.query(tagQuery, [productIds]);
      const tags = tagResult.rows;

      console.log(`🔗 [TENANT-PRODUCT] Loaded ${tags.length} tags for ${products.length} products`);

      // Group tags by product_id
      const tagsByProduct = tags.reduce((acc, tag) => {
        if (!acc[tag.product_id]) {
          acc[tag.product_id] = [];
        }
        acc[tag.product_id].push(tag);
        return acc;
      }, {});

      // Assign tags to products
      products.forEach(product => {
        product.tags = tagsByProduct[product.id] || [];
      });
    } catch (error) {
      console.error(`❌ [TENANT-PRODUCT] Error loading tags: ${error}`);
      // Tags are optional, so don't fail the whole request
      products.forEach(product => {
        product.tags = [];
      });
    }
  }

  /**
   * Retrieve a single product by ID with tenant filtering
   */
  async retrieveProduct(productId: string, config?: any): Promise<any> {
    console.log(`🔍 [TENANT-PRODUCT] Retrieving product ${productId} for tenant: ${this.tenantId}`);

    try {
      // Use direct database query to get the product
      const products = await this.listProductsDirectQuery({ id: productId }, config);

      if (!products || products.length === 0) {
        console.log(
          `❌ [TENANT-PRODUCT] Product ${productId} not found for tenant: ${this.tenantId}`
        );
        return null;
      }

      const product = products[0];
      console.log(
        `✅ [TENANT-PRODUCT] Retrieved product ${productId} (${product.title}) for tenant: ${this.tenantId}`
      );

      return product;
    } catch (error) {
      console.error(`❌ [TENANT-PRODUCT] Error retrieving product ${productId}: ${error}`);
      throw error;
    }
  }
}
