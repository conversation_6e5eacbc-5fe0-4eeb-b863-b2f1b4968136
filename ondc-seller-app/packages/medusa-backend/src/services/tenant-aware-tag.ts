/**
 * Tenant-aware Product Tag Service
 * Provides complete CRUD operations for product tags with automatic tenant isolation.
 * Uses direct database queries to ensure proper tenant filtering.
 */

import { MedusaRequest } from '@medusajs/framework/http';

export class TenantAwareTagService {
  private tagService: any;
  private tenantId: string;
  private pgConnection: any = null;

  constructor(tagService: any, tenantId: string) {
    this.tagService = tagService;
    this.tenantId = tenantId;

    console.log(`🏢 [TENANT-TAG] Initialized for tenant: ${tenantId}`);
  }

  /**
   * Set database tenant context for RLS
   */
  private async setDatabaseTenantContext(): Promise<void> {
    try {
      if (!this.pgConnection) {
        const { Client } = require('pg');
        this.pgConnection = new Client({
          connectionString:
            process.env.DATABASE_URL ||
            process.env.POSTGRES_URL ||
            'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
        });
        await this.pgConnection.connect();
      }

      // Set tenant context for RLS
      await this.pgConnection.query('SET app.current_tenant_id = $1', [this.tenantId]);
      console.log(`🔒 [TENANT-TAG] Set database tenant context: ${this.tenantId}`);
    } catch (error) {
      console.error(`❌ [TENANT-TAG] Error setting database context: ${error}`);
    }
  }

  /**
   * List and count tags with tenant awareness
   */
  async listAndCountTags(filters: any = {}, config?: any): Promise<[any[], number]> {
    console.log(`📂 [TENANT-TAG] Listing and counting tags for tenant: ${this.tenantId}`);

    try {
      // Use direct database query to ensure proper tenant filtering
      console.log(`🔍 [TENANT-TAG] Using direct database query for tenant: ${this.tenantId}`);
      const [tags, count] = await this.listTagsDirectQuery(filters, config);

      console.log(`✅ [TENANT-TAG] Found ${count} total tags for tenant: ${this.tenantId}`);
      return [tags, count];
    } catch (error) {
      console.error(`❌ [TENANT-TAG] Error listing and counting tags: ${error}`);
      return [[], 0];
    }
  }

  /**
   * Retrieve a single tag by ID (with tenant validation)
   */
  async retrieveTag(tagId: string, config?: any, context?: any): Promise<any> {
    console.log(`📂 [TENANT-TAG] Retrieving tag ${tagId} for tenant: ${this.tenantId}`);

    try {
      // Use direct database query to get the tag
      const [tags] = await this.listTagsDirectQuery({ id: tagId }, config);

      if (!tags || tags.length === 0) {
        console.log(`❌ [TENANT-TAG] Tag ${tagId} not found for tenant: ${this.tenantId}`);
        return null;
      }

      const tag = tags[0];
      console.log(
        `✅ [TENANT-TAG] Retrieved tag ${tagId} (${tag.value}) for tenant: ${this.tenantId}`
      );
      return tag;
    } catch (error) {
      console.error(`❌ [TENANT-TAG] Error retrieving tag ${tagId}: ${error}`);
      throw error;
    }
  }

  /**
   * Direct database query for tags with tenant filtering
   */
  private async listTagsDirectQuery(filters: any = {}, config?: any): Promise<[any[], number]> {
    // Ensure database connection is available
    if (!this.pgConnection) {
      console.log(`🔗 [TENANT-TAG] Creating database connection for direct query`);
      try {
        const { Client } = require('pg');
        this.pgConnection = new Client({
          connectionString:
            process.env.DATABASE_URL ||
            process.env.POSTGRES_URL ||
            'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
        });
        await this.pgConnection.connect();
        console.log(`✅ [TENANT-TAG] Database connection established for tenant: ${this.tenantId}`);
      } catch (error) {
        console.error(`❌ [TENANT-TAG] Failed to create database connection: ${error}`);
        return [[], 0];
      }
    }

    try {
      // Build WHERE clause from filters
      const whereConditions: string[] = [];
      const queryParams: (string | number | boolean)[] = [];
      let paramIndex = 1;

      // ALWAYS add tenant_id filter for security (manual fallback if RLS fails)
      whereConditions.push(`tenant_id = $${paramIndex++}`);
      queryParams.push(this.tenantId);

      // ALWAYS filter out soft-deleted records
      whereConditions.push(`deleted_at IS NULL`);

      // Add basic filters
      if (filters.id) {
        whereConditions.push(`id = $${paramIndex++}`);
        queryParams.push(filters.id);
      }

      if (filters.value) {
        whereConditions.push(`value ILIKE $${paramIndex++}`);
        queryParams.push(`%${filters.value}%`);
      }

      // Build query to get tags
      let query = 'SELECT * FROM product_tag';
      if (whereConditions.length > 0) {
        query += ' WHERE ' + whereConditions.join(' AND ');
      }

      // Add ordering and limits
      query += ' ORDER BY value ASC';

      if (config?.take) {
        query += ` LIMIT $${paramIndex++}`;
        queryParams.push(config.take);
      }

      if (config?.skip) {
        query += ` OFFSET $${paramIndex++}`;
        queryParams.push(config.skip);
      }

      console.log(`🔍 [TENANT-TAG] Executing direct query for tenant ${this.tenantId}:`, query);

      // Use raw query to avoid prepared statement caching issues
      const rawQuery = query.replace(/\$(\d+)/g, (match, num) => {
        const paramIndex = parseInt(num) - 1;
        const value = queryParams[paramIndex];
        return typeof value === 'string' ? `'${value.replace(/'/g, "''")}'` : value;
      });

      console.log(`🔍 [TENANT-TAG] Raw query:`, rawQuery);

      const result = await this.pgConnection.query(rawQuery);
      const tags = result.rows;

      // Get total count
      let countQuery = 'SELECT COUNT(*) as total FROM product_tag';
      if (whereConditions.length > 0) {
        countQuery += ' WHERE ' + whereConditions.join(' AND ');
      }

      // Use raw query for count as well to avoid prepared statement issues
      const countParams = queryParams.slice(0, whereConditions.length);
      const rawCountQuery = countQuery.replace(/\$(\d+)/g, (match, num) => {
        const paramIndex = parseInt(num) - 1;
        const value = countParams[paramIndex];
        return typeof value === 'string' ? `'${value.replace(/'/g, "''")}'` : value;
      });

      console.log(`🔍 [TENANT-TAG] Raw count query:`, rawCountQuery);

      const countResult = await this.pgConnection.query(rawCountQuery);
      const total = parseInt(countResult.rows[0].total);

      console.log(
        `✅ [TENANT-TAG] Direct query found ${tags.length} tags (${total} total) for tenant: ${this.tenantId}`
      );

      return [tags, total];
    } catch (error) {
      console.error(`❌ [TENANT-TAG] Direct query failed: ${error}`);
      throw error;
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    try {
      if (this.pgConnection) {
        await this.pgConnection.end();
        this.pgConnection = null;
      }
      console.log(`🧹 [TENANT-TAG] Cleaned up resources for tenant: ${this.tenantId}`);
    } catch (error) {
      console.error(`❌ [TENANT-TAG] Error during cleanup: ${error}`);
    }
  }
}
