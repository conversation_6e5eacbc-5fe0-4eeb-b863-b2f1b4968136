/**
 * Direct Database Service
 *
 * Provides direct database access with automatic tenant context setting.
 * Bypasses Medusa services to ensure RLS policies are properly applied.
 * This is the solution for true tenant isolation at the database level.
 */

import { MedusaRequest } from '@medusajs/framework/http';
import { Pool } from 'pg';

export class DirectDatabaseService {
  private pool: Pool;
  private tenantId: string;

  constructor(tenantId: string = 'default') {
    this.tenantId = tenantId;

    // Create database connection pool using environment variables
    const databaseUrl = process.env.DATABASE_URL || process.env.POSTGRES_URL;

    if (databaseUrl) {
      // Use connection string from environment
      this.pool = new Pool({
        connectionString: databaseUrl,
        max: 10,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000,
      });
    } else {
      // Fallback to individual environment variables
      this.pool = new Pool({
        user: process.env.POSTGRES_USER || 'postgres',
        password: process.env.POSTGRES_PASSWORD || 'postgres',
        host: process.env.POSTGRES_HOST || 'localhost',
        port: parseInt(process.env.POSTGRES_PORT || '5432'),
        database: process.env.POSTGRES_DB || 'medusa_backend',
        max: 10,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000,
      });
    }
  }

  /**
   * Create service instance from request
   */
  static fromRequest(req: MedusaRequest): DirectDatabaseService {
    const tenantId = req.tenant_id || 'default';
    console.log(`🗄️  [DIRECT-DB] Creating direct database service for tenant: ${tenantId}`);
    return new DirectDatabaseService(tenantId);
  }

  /**
   * Execute query with automatic tenant context setting
   */
  async query(sql: string, params: any[] = []): Promise<any> {
    const client = await this.pool.connect();

    try {
      // Start transaction to ensure tenant context persists
      await client.query('BEGIN');

      // Set tenant context for RLS using the correct function
      console.log(`🔒 [DIRECT-DB] Setting tenant context: ${this.tenantId}`);

      // Try different tenant context setting methods based on available RLS functions
      try {
        // Method 1: Try set_tenant_context function (from create-rls-functions.sql)
        await client.query('SELECT set_tenant_context($1)', [this.tenantId]);
        console.log(`✅ [DIRECT-DB] Set tenant context using set_tenant_context function`);
      } catch (error1) {
        try {
          // Method 2: Try direct config setting (fallback)
          await client.query('SELECT set_config($1, $2, false)', [
            'app.current_tenant_id',
            this.tenantId,
          ]);
          console.log(`✅ [DIRECT-DB] Set tenant context using set_config`);
        } catch (error2) {
          console.warn(`⚠️ [DIRECT-DB] Failed to set tenant context: ${error2.message}`);
        }
      }

      // Verify tenant context was set
      try {
        const contextResult = await client.query(
          'SELECT current_setting($1, true) as tenant_context',
          ['app.current_tenant_id']
        );
        console.log(
          `🔍 [DIRECT-DB] Current tenant context: ${contextResult.rows[0].tenant_context}`
        );
      } catch (verifyError) {
        console.log(`🔍 [DIRECT-DB] Could not verify tenant context: ${verifyError.message}`);
      }

      // Execute the actual query
      console.log(`📝 [DIRECT-DB] Executing query: ${sql.substring(0, 100)}...`);
      const result = await client.query(sql, params);
      console.log(`✅ [DIRECT-DB] Query returned ${result.rows.length} rows`);

      // Commit transaction
      await client.query('COMMIT');

      return result;
    } catch (error) {
      // Rollback on error
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  // ============================================================================
  // PRODUCT OPERATIONS
  // ============================================================================

  async getProducts(
    limit: number = 20,
    offset: number = 0
  ): Promise<{ products: any[]; total: number }> {
    console.log(`🛍️  [DIRECT-DB] Getting products for tenant: ${this.tenantId}`);

    try {
      // Get products with count
      const countResult = await this.query('SELECT COUNT(*) FROM product');
      const total = parseInt(countResult.rows[0].count);

      const productsResult = await this.query(
        'SELECT * FROM product ORDER BY created_at DESC LIMIT $1 OFFSET $2',
        [limit, offset]
      );

      console.log(
        `✅ [DIRECT-DB] Found ${productsResult.rows.length}/${total} products for tenant: ${this.tenantId}`
      );

      return {
        products: productsResult.rows,
        total,
      };
    } catch (error) {
      console.error(`❌ [DIRECT-DB] Error getting products: ${error}`);
      throw error;
    }
  }

  async createProduct(productData: any): Promise<any> {
    console.log(`🛍️  [DIRECT-DB] Creating product for tenant: ${this.tenantId}`);

    try {
      const { title, description = '', handle, status = 'draft', ...otherData } = productData;

      const result = await this.query(
        `
        INSERT INTO product (title, description, handle, status, tenant_id, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
        RETURNING *
      `,
        [title, description, handle, status, this.tenantId]
      );

      console.log(
        `✅ [DIRECT-DB] Created product ${result.rows[0].id} for tenant: ${this.tenantId}`
      );
      return result.rows[0];
    } catch (error) {
      console.error(`❌ [DIRECT-DB] Error creating product: ${error}`);
      throw error;
    }
  }

  // ============================================================================
  // CUSTOMER OPERATIONS
  // ============================================================================

  async getCustomers(
    limit: number = 20,
    offset: number = 0
  ): Promise<{ customers: any[]; total: number }> {
    console.log(`👥 [DIRECT-DB] Getting customers for tenant: ${this.tenantId}`);

    try {
      const countResult = await this.query('SELECT COUNT(*) FROM customer');
      const total = parseInt(countResult.rows[0].count);

      const customersResult = await this.query(
        'SELECT * FROM customer ORDER BY created_at DESC LIMIT $1 OFFSET $2',
        [limit, offset]
      );

      console.log(
        `✅ [DIRECT-DB] Found ${customersResult.rows.length}/${total} customers for tenant: ${this.tenantId}`
      );

      return {
        customers: customersResult.rows,
        total,
      };
    } catch (error) {
      console.error(`❌ [DIRECT-DB] Error getting customers: ${error}`);
      throw error;
    }
  }

  async createCustomer(customerData: any): Promise<any> {
    console.log(`👥 [DIRECT-DB] Creating customer for tenant: ${this.tenantId}`);

    try {
      const { email, first_name = '', last_name = '', phone = null, ...otherData } = customerData;

      const result = await this.query(
        `
        INSERT INTO customer (email, first_name, last_name, phone, tenant_id, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
        RETURNING *
      `,
        [email, first_name, last_name, phone, this.tenantId]
      );

      console.log(
        `✅ [DIRECT-DB] Created customer ${result.rows[0].id} for tenant: ${this.tenantId}`
      );
      return result.rows[0];
    } catch (error) {
      console.error(`❌ [DIRECT-DB] Error creating customer: ${error}`);
      throw error;
    }
  }

  // ============================================================================
  // CART OPERATIONS
  // ============================================================================

  async getCarts(limit: number = 20, offset: number = 0): Promise<{ carts: any[]; total: number }> {
    console.log(`🛒 [DIRECT-DB] Getting carts for tenant: ${this.tenantId}`);

    try {
      const countResult = await this.query('SELECT COUNT(*) FROM cart');
      const total = parseInt(countResult.rows[0].count);

      const cartsResult = await this.query(
        'SELECT * FROM cart ORDER BY created_at DESC LIMIT $1 OFFSET $2',
        [limit, offset]
      );

      console.log(
        `✅ [DIRECT-DB] Found ${cartsResult.rows.length}/${total} carts for tenant: ${this.tenantId}`
      );

      return {
        carts: cartsResult.rows,
        total,
      };
    } catch (error) {
      console.error(`❌ [DIRECT-DB] Error getting carts: ${error}`);
      throw error;
    }
  }

  async createCart(cartData: any): Promise<any> {
    console.log(`🛒 [DIRECT-DB] Creating cart for tenant: ${this.tenantId}`);

    try {
      const {
        region_id,
        customer_id = null,
        sales_channel_id,
        currency_code = 'usd',
        ...otherData
      } = cartData;

      const result = await this.query(
        `
        INSERT INTO cart (region_id, customer_id, sales_channel_id, currency_code, tenant_id, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
        RETURNING *
      `,
        [region_id, customer_id, sales_channel_id, currency_code, this.tenantId]
      );

      console.log(`✅ [DIRECT-DB] Created cart ${result.rows[0].id} for tenant: ${this.tenantId}`);
      return result.rows[0];
    } catch (error) {
      console.error(`❌ [DIRECT-DB] Error creating cart: ${error}`);
      throw error;
    }
  }

  // ============================================================================
  // ORDER OPERATIONS
  // ============================================================================

  async getOrders(
    limit: number = 20,
    offset: number = 0
  ): Promise<{ orders: any[]; total: number }> {
    console.log(`📦 [DIRECT-DB] Getting orders for tenant: ${this.tenantId}`);

    try {
      const countResult = await this.query('SELECT COUNT(*) FROM "order"');
      const total = parseInt(countResult.rows[0].count);

      const ordersResult = await this.query(
        'SELECT * FROM "order" ORDER BY created_at DESC LIMIT $1 OFFSET $2',
        [limit, offset]
      );

      console.log(
        `✅ [DIRECT-DB] Found ${ordersResult.rows.length}/${total} orders for tenant: ${this.tenantId}`
      );

      return {
        orders: ordersResult.rows,
        total,
      };
    } catch (error) {
      console.error(`❌ [DIRECT-DB] Error getting orders: ${error}`);
      throw error;
    }
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  async getStats(): Promise<{
    products: number;
    customers: number;
    orders: number;
    carts: number;
  }> {
    console.log(`📊 [DIRECT-DB] Getting statistics for tenant: ${this.tenantId}`);

    try {
      const [productsResult, customersResult, ordersResult, cartsResult] = await Promise.all([
        this.query('SELECT COUNT(*) FROM product'),
        this.query('SELECT COUNT(*) FROM customer'),
        this.query('SELECT COUNT(*) FROM "order"'),
        this.query('SELECT COUNT(*) FROM cart'),
      ]);

      const stats = {
        products: parseInt(productsResult.rows[0].count),
        customers: parseInt(customersResult.rows[0].count),
        orders: parseInt(ordersResult.rows[0].count),
        carts: parseInt(cartsResult.rows[0].count),
      };

      console.log(`📊 [DIRECT-DB] Stats for tenant ${this.tenantId}:`, stats);
      return stats;
    } catch (error) {
      console.error(`❌ [DIRECT-DB] Error getting stats: ${error}`);
      throw error;
    }
  }

  /**
   * Close database connection pool
   */
  async close(): Promise<void> {
    await this.pool.end();
  }
}
