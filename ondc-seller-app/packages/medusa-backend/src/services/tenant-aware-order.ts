/**
 * Tenant-Aware Order Service
 * 
 * Wraps the core Medusa OrderModuleService to automatically inject
 * tenant context into all order operations.
 */

import { MedusaRequest } from "@medusajs/framework/http";

export class TenantAwareOrderService {
  private orderService: any;
  private tenantId: string;

  constructor(orderService: any, tenantId: string = 'default') {
    this.orderService = orderService;
    this.tenantId = tenantId;
  }

  /**
   * Create a tenant-aware order service instance from request
   */
  static fromRequest(req: MedusaRequest): TenantAwareOrderService {
    const orderService = req.scope.resolve('order');
    const tenantId = req.tenant_id || 'default';
    
    console.log(`🏢 [TENANT-ORDER] Creating tenant-aware order service for tenant: ${tenantId}`);
    
    return new TenantAwareOrderService(orderService, tenantId);
  }

  /**
   * Inject tenant_id into order data
   */
  private injectTenantId(data: any): any {
    if (Array.isArray(data)) {
      return data.map(item => ({ ...item, tenant_id: this.tenantId }));
    }
    return { ...data, tenant_id: this.tenantId };
  }

  /**
   * Add tenant filter to query filters
   */
  private addTenantFilter(filters: any = {}): any {
    return { ...filters, tenant_id: this.tenantId };
  }

  // ============================================================================
  // ORDER CRUD OPERATIONS WITH TENANT CONTEXT
  // ============================================================================

  /**
   * Create orders with automatic tenant injection
   */
  async createOrders(data: any[], context?: any): Promise<any> {
    console.log(`🏢 [TENANT-ORDER] Creating ${data.length} orders for tenant: ${this.tenantId}`);
    
    const tenantData = this.injectTenantId(data);
    const result = await this.orderService.createOrders(tenantData, context);
    
    console.log(`✅ [TENANT-ORDER] Created ${result.length} orders for tenant: ${this.tenantId}`);
    return result;
  }

  /**
   * Update orders with tenant validation
   */
  async updateOrders(selector: any, data: any, context?: any): Promise<any> {
    console.log(`🏢 [TENANT-ORDER] Updating orders for tenant: ${this.tenantId}`);
    
    // Add tenant filter to selector to ensure we only update tenant's orders
    const tenantSelector = this.addTenantFilter(selector);
    const result = await this.orderService.updateOrders(tenantSelector, data, context);
    
    console.log(`✅ [TENANT-ORDER] Updated orders for tenant: ${this.tenantId}`);
    return result;
  }

  /**
   * List orders with automatic tenant filtering
   */
  async listOrders(filters: any = {}, config?: any, context?: any): Promise<any> {
    console.log(`🏢 [TENANT-ORDER] Listing orders for tenant: ${this.tenantId}`);
    
    const tenantFilters = this.addTenantFilter(filters);
    const result = await this.orderService.listOrders(tenantFilters, config, context);
    
    console.log(`✅ [TENANT-ORDER] Found ${result.length} orders for tenant: ${this.tenantId}`);
    return result;
  }

  /**
   * List and count orders with automatic tenant filtering
   */
  async listAndCountOrders(filters: any = {}, config?: any, context?: any): Promise<any> {
    console.log(`🏢 [TENANT-ORDER] Listing and counting orders for tenant: ${this.tenantId}`);
    
    const tenantFilters = this.addTenantFilter(filters);
    const result = await this.orderService.listAndCountOrders(tenantFilters, config, context);
    
    console.log(`✅ [TENANT-ORDER] Found ${result[1]} total orders for tenant: ${this.tenantId}`);
    return result;
  }

  /**
   * Retrieve single order with tenant validation
   */
  async retrieveOrder(id: string, config?: any, context?: any): Promise<any> {
    console.log(`🏢 [TENANT-ORDER] Retrieving order ${id} for tenant: ${this.tenantId}`);
    
    // First check if order belongs to tenant
    const orders = await this.listOrders({ id }, { take: 1 }, context);
    
    if (orders.length === 0) {
      console.warn(`⚠️  [TENANT-ORDER] Order ${id} not found for tenant: ${this.tenantId}`);
      throw new Error(`Order ${id} not found for tenant ${this.tenantId}`);
    }
    
    const result = await this.orderService.retrieveOrder(id, config, context);
    console.log(`✅ [TENANT-ORDER] Retrieved order ${id} for tenant: ${this.tenantId}`);
    return result;
  }

  // ============================================================================
  // ORDER BUSINESS OPERATIONS WITH TENANT CONTEXT
  // ============================================================================

  /**
   * Complete order with tenant validation
   */
  async completeOrder(orderId: string, context?: any): Promise<any> {
    console.log(`🏢 [TENANT-ORDER] Completing order ${orderId} for tenant: ${this.tenantId}`);
    
    // Validate order belongs to tenant
    const orderExists = await this.validateOrderOwnership(orderId);
    if (!orderExists) {
      throw new Error(`Order ${orderId} not found for tenant ${this.tenantId}`);
    }
    
    const result = await this.orderService.completeOrder(orderId, context);
    
    console.log(`✅ [TENANT-ORDER] Completed order ${orderId} for tenant: ${this.tenantId}`);
    return result;
  }

  /**
   * Cancel order with tenant validation
   */
  async cancel(orderId: string, context?: any): Promise<any> {
    console.log(`🏢 [TENANT-ORDER] Canceling order ${orderId} for tenant: ${this.tenantId}`);
    
    // Validate order belongs to tenant
    const orderExists = await this.validateOrderOwnership(orderId);
    if (!orderExists) {
      throw new Error(`Order ${orderId} not found for tenant ${this.tenantId}`);
    }
    
    const result = await this.orderService.cancel(orderId, context);
    
    console.log(`✅ [TENANT-ORDER] Canceled order ${orderId} for tenant: ${this.tenantId}`);
    return result;
  }

  /**
   * Archive order with tenant validation
   */
  async archive(orderId: string, context?: any): Promise<any> {
    console.log(`🏢 [TENANT-ORDER] Archiving order ${orderId} for tenant: ${this.tenantId}`);
    
    // Validate order belongs to tenant
    const orderExists = await this.validateOrderOwnership(orderId);
    if (!orderExists) {
      throw new Error(`Order ${orderId} not found for tenant ${this.tenantId}`);
    }
    
    const result = await this.orderService.archive(orderId, context);
    
    console.log(`✅ [TENANT-ORDER] Archived order ${orderId} for tenant: ${this.tenantId}`);
    return result;
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /**
   * Get tenant ID for this service instance
   */
  getTenantId(): string {
    return this.tenantId;
  }

  /**
   * Get underlying order service
   */
  getOrderService(): any {
    return this.orderService;
  }

  /**
   * Validate order ownership by tenant
   */
  async validateOrderOwnership(orderId: string): Promise<boolean> {
    try {
      const orders = await this.listOrders({ id: orderId }, { take: 1 });
      return orders.length > 0;
    } catch (error) {
      console.error(`❌ [TENANT-ORDER] Error validating order ownership: ${error}`);
      return false;
    }
  }

  /**
   * Get order statistics for tenant
   */
  async getOrderStats(): Promise<{
    totalOrders: number;
    completedOrders: number;
    canceledOrders: number;
    pendingOrders: number;
  }> {
    console.log(`📊 [TENANT-ORDER] Getting order statistics for tenant: ${this.tenantId}`);
    
    try {
      const [orders, totalCount] = await this.listAndCountOrders();
      
      // Count orders by status
      const completedOrders = orders.filter((order: any) => order.status === 'completed').length;
      const canceledOrders = orders.filter((order: any) => order.status === 'canceled').length;
      const pendingOrders = orders.filter((order: any) => order.status === 'pending').length;
      
      const stats = {
        totalOrders: totalCount,
        completedOrders,
        canceledOrders,
        pendingOrders
      };
      
      console.log(`📊 [TENANT-ORDER] Stats for tenant ${this.tenantId}:`, stats);
      return stats;
      
    } catch (error) {
      console.error(`❌ [TENANT-ORDER] Error getting order stats: ${error}`);
      throw error;
    }
  }
}
