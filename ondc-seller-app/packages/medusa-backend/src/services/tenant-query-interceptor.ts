/**
 * Tenant Query Interceptor
 * 
 * Intercepts and modifies database queries to automatically inject tenant filtering.
 * Works at the database level to ensure tenant isolation without modifying service schemas.
 */

import { MedusaRequest } from "@medusajs/framework/http";

export interface QueryInterceptorConfig {
  tenantId: string;
  enableLogging: boolean;
  strictMode: boolean; // If true, throws error when tenant_id is missing
}

export class TenantQueryInterceptor {
  private config: QueryInterceptorConfig;

  constructor(config: QueryInterceptorConfig) {
    this.config = config;
  }

  /**
   * Create interceptor from request context
   */
  static fromRequest(req: MedusaRequest, options: Partial<QueryInterceptorConfig> = {}): TenantQueryInterceptor {
    const tenantId = req.tenant_id || 'default';
    
    const config: QueryInterceptorConfig = {
      tenantId,
      enableLogging: true,
      strictMode: false,
      ...options
    };
    
    if (config.enableLogging) {
      console.log(`🔍 [QUERY-INTERCEPTOR] Creating interceptor for tenant: ${tenantId}`);
    }
    
    return new TenantQueryInterceptor(config);
  }

  /**
   * Intercept and modify service queries to include tenant filtering
   */
  interceptServiceQuery(serviceName: string, method: string, filters: any = {}): any {
    if (this.config.enableLogging) {
      console.log(`🔍 [QUERY-INTERCEPTOR] Intercepting ${serviceName}.${method} for tenant: ${this.config.tenantId}`);
    }

    // Don't modify filters if tenant_id is already present
    if (filters.tenant_id) {
      if (this.config.enableLogging) {
        console.log(`ℹ️  [QUERY-INTERCEPTOR] Tenant filter already present: ${filters.tenant_id}`);
      }
      return filters;
    }

    // Add tenant filter based on service type
    const modifiedFilters = this.addTenantFilterForService(serviceName, filters);
    
    if (this.config.enableLogging) {
      console.log(`✅ [QUERY-INTERCEPTOR] Modified filters for ${serviceName}:`, modifiedFilters);
    }
    
    return modifiedFilters;
  }

  /**
   * Add tenant filter based on service type
   */
  private addTenantFilterForService(serviceName: string, filters: any): any {
    // For services that support tenant_id directly
    const directTenantServices = ['product', 'customer', 'order', 'cart'];
    
    if (directTenantServices.includes(serviceName)) {
      return { ...filters, tenant_id: this.config.tenantId };
    }
    
    // For services that need custom tenant filtering logic
    switch (serviceName) {
      case 'inventory':
        // Inventory might be linked to products, so we need to join
        return this.addInventoryTenantFilter(filters);
        
      case 'pricing':
        // Pricing might be linked to products or price sets
        return this.addPricingTenantFilter(filters);
        
      case 'fulfillment':
        // Fulfillment might be linked to orders
        return this.addFulfillmentTenantFilter(filters);
        
      default:
        // For unknown services, just add tenant_id and hope for the best
        return { ...filters, tenant_id: this.config.tenantId };
    }
  }

  /**
   * Add tenant filter for inventory queries
   */
  private addInventoryTenantFilter(filters: any): any {
    // Inventory items might need to be filtered by product tenant_id
    // This would require a join or subquery in real implementation
    return { 
      ...filters, 
      // For now, we'll use a placeholder approach
      _tenant_context: this.config.tenantId 
    };
  }

  /**
   * Add tenant filter for pricing queries
   */
  private addPricingTenantFilter(filters: any): any {
    // Pricing might need to be filtered by product or price set tenant_id
    return { 
      ...filters, 
      _tenant_context: this.config.tenantId 
    };
  }

  /**
   * Add tenant filter for fulfillment queries
   */
  private addFulfillmentTenantFilter(filters: any): any {
    // Fulfillment might need to be filtered by order tenant_id
    return { 
      ...filters, 
      _tenant_context: this.config.tenantId 
    };
  }

  /**
   * Intercept create operations to inject tenant_id
   */
  interceptCreateData(serviceName: string, data: any): any {
    if (this.config.enableLogging) {
      console.log(`🔍 [QUERY-INTERCEPTOR] Intercepting create data for ${serviceName}, tenant: ${this.config.tenantId}`);
    }

    if (Array.isArray(data)) {
      return data.map(item => this.injectTenantId(item));
    }
    
    return this.injectTenantId(data);
  }

  /**
   * Inject tenant_id into data object
   */
  private injectTenantId(data: any): any {
    // Don't override existing tenant_id
    if (data.tenant_id) {
      if (this.config.strictMode && data.tenant_id !== this.config.tenantId) {
        throw new Error(`Tenant ID mismatch: expected ${this.config.tenantId}, got ${data.tenant_id}`);
      }
      return data;
    }

    return { ...data, tenant_id: this.config.tenantId };
  }

  /**
   * Validate that retrieved data belongs to the current tenant
   */
  validateTenantOwnership(serviceName: string, data: any): boolean {
    if (!data) return true; // No data to validate

    if (Array.isArray(data)) {
      return data.every(item => this.validateSingleItemOwnership(item));
    }
    
    return this.validateSingleItemOwnership(data);
  }

  /**
   * Validate single item tenant ownership
   */
  private validateSingleItemOwnership(item: any): boolean {
    if (!item || typeof item !== 'object') return true;

    // If item has tenant_id, validate it matches
    if (item.tenant_id) {
      const isValid = item.tenant_id === this.config.tenantId;
      
      if (!isValid && this.config.enableLogging) {
        console.warn(`⚠️  [QUERY-INTERCEPTOR] Tenant ownership violation: expected ${this.config.tenantId}, got ${item.tenant_id}`);
      }
      
      return isValid;
    }

    // If no tenant_id, assume it's valid (might be a system entity)
    return true;
  }

  /**
   * Create a wrapped service that automatically applies tenant filtering
   */
  wrapService(service: any, serviceName: string): any {
    const interceptor = this;
    
    return new Proxy(service, {
      get(target, prop, receiver) {
        const originalMethod = Reflect.get(target, prop, receiver);
        
        if (typeof originalMethod !== 'function') {
          return originalMethod;
        }

        // Wrap query methods
        if (interceptor.isQueryMethod(prop as string)) {
          return function(...args: any[]) {
            // Intercept the first argument (usually filters)
            if (args.length > 0 && typeof args[0] === 'object') {
              args[0] = interceptor.interceptServiceQuery(serviceName, prop as string, args[0]);
            }
            
            return originalMethod.apply(this, args);
          };
        }

        // Wrap create methods
        if (interceptor.isCreateMethod(prop as string)) {
          return function(...args: any[]) {
            // Intercept the first argument (usually data)
            if (args.length > 0) {
              args[0] = interceptor.interceptCreateData(serviceName, args[0]);
            }
            
            return originalMethod.apply(this, args);
          };
        }

        // Return original method for other operations
        return originalMethod;
      }
    });
  }

  /**
   * Check if method is a query method
   */
  private isQueryMethod(methodName: string): boolean {
    const queryMethods = [
      'list', 'listAndCount', 'retrieve', 'find', 'get', 'search', 'query'
    ];
    
    return queryMethods.some(method => methodName.toLowerCase().includes(method.toLowerCase()));
  }

  /**
   * Check if method is a create method
   */
  private isCreateMethod(methodName: string): boolean {
    const createMethods = ['create', 'add', 'insert', 'upsert'];
    
    return createMethods.some(method => methodName.toLowerCase().includes(method.toLowerCase()));
  }

  /**
   * Get tenant ID
   */
  getTenantId(): string {
    return this.config.tenantId;
  }

  /**
   * Update configuration
   */
  updateConfig(updates: Partial<QueryInterceptorConfig>): void {
    this.config = { ...this.config, ...updates };
    
    if (this.config.enableLogging) {
      console.log(`🔧 [QUERY-INTERCEPTOR] Updated config for tenant: ${this.config.tenantId}`, updates);
    }
  }
}
