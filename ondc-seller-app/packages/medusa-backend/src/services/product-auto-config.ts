import { Logger } from '@medusajs/framework/types';

/**
 * ProductAutoConfigService
 *
 * Handles automatic configuration for products including:
 * - Sales channel assignment
 * - Inventory management settings
 * - Multi-tenant compatibility
 */
export class ProductAutoConfigService {
  private logger_: Logger;
  private defaultSalesChannelId_: string;
  private productService_: any;
  private productVariantService_: any;
  private salesChannelService_: any;
  private inventoryService_: any;
  private stockLocationService_: any;
  private defaultStockLocationId_: string;
  private defaultShippingProfileId_: string;

  constructor(container: any) {
    this.logger_ = container.resolve('logger');

    // Resolve services
    try {
      this.productService_ = container.resolve('productService');
      this.productVariantService_ = container.resolve('productVariantService');
      this.salesChannelService_ = container.resolve('salesChannelService');
      this.inventoryService_ = container.resolve('inventoryService');
      this.stockLocationService_ = container.resolve('stockLocationService');
    } catch (error) {
      this.logger_.warn(`🔧 [PRODUCT-AUTO-CONFIG] Some services not available: ${error.message}`);
    }

    // Set default stock location ID and shipping profile ID
    this.defaultStockLocationId_ = process.env.DEFAULT_STOCK_LOCATION_ID || 'sloc_01JZ85WAREHOUSE';
    this.defaultShippingProfileId_ = process.env.DEFAULT_SHIPPING_PROFILE_ID || 'sp_01JZ4VNZYVNWS3976TD64VQ423';

    // Get default sales channel ID from environment
    this.defaultSalesChannelId_ =
      process.env.DEFAULT_SALES_CHANNEL_ID || 'sc_01K33ENQEWWEMQNT0WDXAHMCWD';

    this.logger_.info(
      `🔧 [PRODUCT-AUTO-CONFIG] Initialized with defaults - Sales Channel: ${this.defaultSalesChannelId_}, Stock Location: ${this.defaultStockLocationId_}, Shipping Profile: ${this.defaultShippingProfileId_}`
    );
  }

  /**
   * Apply automatic configuration to a product
   * - Assigns to default sales channel
   * - Sets manage_inventory: false for all variants
   */
  async applyAutoConfiguration(productId: string, tenantId?: string): Promise<void> {
    try {
      this.logger_.info(
        `🔧 [PRODUCT-AUTO-CONFIG] Applying auto-configuration to product: ${productId}`
      );

      // Step 1: Assign product to sales channel
      await this.assignToSalesChannel(productId);

      // Step 2: Configure inventory management for all variants
      await this.configureInventoryManagement(productId);

      // Step 3: Setup inventory items and stock levels
      await this.setupInventoryItems(productId);

      // Step 4: Assign shipping profile
      await this.assignShippingProfile(productId);

      this.logger_.info(
        `✅ [PRODUCT-AUTO-CONFIG] Auto-configuration completed for product: ${productId}`
      );
    } catch (error) {
      this.logger_.error(
        `❌ [PRODUCT-AUTO-CONFIG] Error applying auto-configuration to product ${productId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Assign product to the default sales channel
   */
  private async assignToSalesChannel(productId: string): Promise<void> {
    try {
      this.logger_.info(
        `📺 [PRODUCT-AUTO-CONFIG] Assigning product ${productId} to sales channel: ${this.defaultSalesChannelId_}`
      );

      // Get the product to ensure it exists
      if (this.productService_) {
        const product = await this.productService_.retrieve(productId);
        if (!product) {
          throw new Error(`Product not found: ${productId}`);
        }
      }

      // Check if sales channel exists and add product
      if (this.salesChannelService_) {
        try {
          const salesChannel = await this.salesChannelService_.retrieve(
            this.defaultSalesChannelId_
          );
          if (salesChannel) {
            // Add product to sales channel
            await this.salesChannelService_.addProducts(this.defaultSalesChannelId_, [productId]);
          }
        } catch (error) {
          this.logger_.warn(
            `⚠️ [PRODUCT-AUTO-CONFIG] Sales channel ${this.defaultSalesChannelId_} not found, skipping assignment`
          );
          return;
        }
      } else {
        this.logger_.warn(
          `⚠️ [PRODUCT-AUTO-CONFIG] Sales channel service not available, skipping assignment`
        );
        return;
      }

      this.logger_.info(
        `✅ [PRODUCT-AUTO-CONFIG] Product ${productId} assigned to sales channel: ${this.defaultSalesChannelId_}`
      );
    } catch (error) {
      this.logger_.error(
        `❌ [PRODUCT-AUTO-CONFIG] Error assigning product to sales channel:`,
        error
      );
      throw error;
    }
  }

  /**
   * Configure inventory management for all product variants
   * Sets manage_inventory: false for all variants
   */
  private async configureInventoryManagement(productId: string): Promise<void> {
    try {
      this.logger_.info(
        `📦 [PRODUCT-AUTO-CONFIG] Configuring inventory management for product: ${productId}`
      );

      // Get all variants for the product
      if (this.productVariantService_) {
        const variants = await this.productVariantService_.list({
          product_id: productId,
        });

        if (!variants || variants.length === 0) {
          this.logger_.warn(`⚠️ [PRODUCT-AUTO-CONFIG] No variants found for product: ${productId}`);
          return;
        }

        // Update each variant to disable inventory management
        for (const variant of variants) {
          await this.productVariantService_.update(variant.id, {
            manage_inventory: false,
          });

          this.logger_.info(`✅ [PRODUCT-AUTO-CONFIG] Disabled inventory management for variant: ${variant.id}`);
        }

        this.logger_.info(`✅ [PRODUCT-AUTO-CONFIG] Inventory management configured for ${variants.length} variants`);
      } else {
        this.logger_.warn(`⚠️ [PRODUCT-AUTO-CONFIG] Product variant service not available, skipping inventory configuration`);
      }

        this.logger_.info(
          `✅ [PRODUCT-AUTO-CONFIG] Disabled inventory management for variant: ${variant.id}`
        );
      }

      this.logger_.info(
        `✅ [PRODUCT-AUTO-CONFIG] Inventory management configured for ${variants.length} variants`
      );
    } catch (error) {
      this.logger_.error(`❌ [PRODUCT-AUTO-CONFIG] Error configuring inventory management:`, error);
      throw error;
    }
  }

  /**
   * Apply auto-configuration to multiple products (for bulk operations like Excel import)
   */
  async applyAutoConfigurationBulk(productIds: string[], tenantId?: string): Promise<void> {
    this.logger_.info(
      `🔧 [PRODUCT-AUTO-CONFIG] Applying bulk auto-configuration to ${productIds.length} products`
    );

    const results = {
      successful: 0,
      failed: 0,
      errors: [] as string[],
    };

    for (const productId of productIds) {
      try {
        await this.applyAutoConfiguration(productId, tenantId);
        results.successful++;
      } catch (error) {
        results.failed++;
        results.errors.push(`Product ${productId}: ${error.message}`);
        this.logger_.error(
          `❌ [PRODUCT-AUTO-CONFIG] Failed to configure product ${productId}:`,
          error
        );
      }
    }

    this.logger_.info(
      `✅ [PRODUCT-AUTO-CONFIG] Bulk configuration completed: ${results.successful} successful, ${results.failed} failed`
    );

    if (results.failed > 0) {
      this.logger_.warn(`⚠️ [PRODUCT-AUTO-CONFIG] Bulk configuration errors:`, results.errors);
    }
  }

  /**
   * Get the default sales channel ID
   */
  getDefaultSalesChannelId(): string {
    return this.defaultSalesChannelId_;
  }

  /**
   * Validate that the default sales channel exists
   */
  async validateDefaultSalesChannel(): Promise<boolean> {
    try {
      if (this.salesChannelService_) {
        const salesChannel = await this.salesChannelService_.retrieve(this.defaultSalesChannelId_);
        return !!salesChannel;
      } else {
        this.logger_.warn(`⚠️ [PRODUCT-AUTO-CONFIG] Sales channel service not available for validation`);
        return false;
      }
    } catch (error) {
      this.logger_.error(
        `❌ [PRODUCT-AUTO-CONFIG] Default sales channel validation failed:`,
        error
      );
      return false;
    }
  }

  /**
   * Setup inventory items and stock levels for all product variants
   * This ensures variants can be added to cart without inventory errors
   */
  private async setupInventoryItems(productId: string): Promise<void> {
    try {
      this.logger_.info(
        `📦 [PRODUCT-AUTO-CONFIG] Setting up inventory items for product: ${productId}`
      );

      // Get all variants for the product
      if (this.productVariantService_) {
        const variants = await this.productVariantService_.list({
          product_id: productId,
        });

        if (!variants || variants.length === 0) {
          this.logger_.warn(`⚠️ [PRODUCT-AUTO-CONFIG] No variants found for product: ${productId}`);
          return;
        }

        for (const variant of variants) {
          await this.setupVariantInventory(variant);
        }

        this.logger_.info(`✅ [PRODUCT-AUTO-CONFIG] Inventory setup completed for ${variants.length} variants`);
      } else {
        this.logger_.warn(`⚠️ [PRODUCT-AUTO-CONFIG] Product variant service not available, skipping inventory setup`);
      }
    } catch (error) {
      this.logger_.error(`❌ [PRODUCT-AUTO-CONFIG] Error setting up inventory items:`, error);
      throw error;
    }
  }

  /**
   * Setup inventory for a single variant
   */
  private async setupVariantInventory(variant: any): Promise<void> {
    try {
      if (!this.inventoryService_) {
        this.logger_.warn(`⚠️ [PRODUCT-AUTO-CONFIG] Inventory service not available, using direct database approach`);
        await this.setupVariantInventoryDirect(variant);
        return;
      }

      // Create inventory item for the variant
      const inventoryItemId = `inv_${variant.id.slice(8)}`;

      try {
        // Check if inventory item already exists
        const existingItem = await this.inventoryService_.retrieveInventoryItem(inventoryItemId);
        if (existingItem) {
          this.logger_.info(`✅ [PRODUCT-AUTO-CONFIG] Inventory item already exists for variant: ${variant.id}`);
          return;
        }
      } catch (error) {
        // Item doesn't exist, create it
      }

      // Create inventory item
      const inventoryItem = await this.inventoryService_.createInventoryItems([{
        id: inventoryItemId,
        sku: variant.sku || variant.id,
      }]);

      // Link inventory item to variant
      await this.inventoryService_.createInventoryLevels([{
        inventory_item_id: inventoryItemId,
        location_id: this.defaultStockLocationId_,
        stocked_quantity: 100,
        reserved_quantity: 0,
        incoming_quantity: 0,
      }]);

      this.logger_.info(`✅ [PRODUCT-AUTO-CONFIG] Inventory setup completed for variant: ${variant.id}`);
    } catch (error) {
      this.logger_.warn(`⚠️ [PRODUCT-AUTO-CONFIG] Failed to setup inventory for variant ${variant.id}, trying direct approach:`, error.message);
      await this.setupVariantInventoryDirect(variant);
    }
  }

  /**
   * Setup inventory using direct database operations (fallback method)
   */
  private async setupVariantInventoryDirect(variant: any): Promise<void> {
    try {
      const { Client } = require('pg');
      const client = new Client({
        connectionString: process.env.DATABASE_URL
      });

      await client.connect();

      const inventoryItemId = `inv_${variant.id.slice(8)}`;

      // Check if inventory item already exists
      const existingItem = await client.query('SELECT id FROM inventory_item WHERE id = $1', [inventoryItemId]);
      if (existingItem.rows.length === 0) {
        // Create inventory item
        await client.query(`
          INSERT INTO inventory_item (id, sku, created_at, updated_at)
          VALUES ($1, $2, NOW(), NOW());
        `, [inventoryItemId, variant.sku || variant.id]);
      }

      // Check if variant-inventory link already exists
      const existingLink = await client.query('SELECT id FROM product_variant_inventory_item WHERE variant_id = $1 AND inventory_item_id = $2', [variant.id, inventoryItemId]);
      if (existingLink.rows.length === 0) {
        // Link inventory item to product variant
        const linkId = `pvii_${variant.id.slice(8)}_${inventoryItemId.slice(4)}`;
        await client.query(`
          INSERT INTO product_variant_inventory_item (id, variant_id, inventory_item_id, required_quantity, created_at, updated_at)
          VALUES ($1, $2, $3, 1, NOW(), NOW());
        `, [linkId, variant.id, inventoryItemId]);
      }

      // Check if inventory level already exists
      const existingLevel = await client.query('SELECT inventory_item_id FROM inventory_level WHERE inventory_item_id = $1 AND location_id = $2', [inventoryItemId, this.defaultStockLocationId_]);
      if (existingLevel.rows.length === 0) {
        // Create inventory level (stock quantity)
        const levelId = `invlvl_${inventoryItemId.slice(4)}_${this.defaultStockLocationId_.slice(5)}`;
        await client.query(`
          INSERT INTO inventory_level (id, inventory_item_id, location_id, stocked_quantity, reserved_quantity, incoming_quantity, created_at, updated_at)
          VALUES ($1, $2, $3, 100, 0, 0, NOW(), NOW());
        `, [levelId, inventoryItemId, this.defaultStockLocationId_]);
      }

      await client.end();
      this.logger_.info(`✅ [PRODUCT-AUTO-CONFIG] Direct inventory setup completed for variant: ${variant.id}`);
    } catch (error) {
      this.logger_.error(`❌ [PRODUCT-AUTO-CONFIG] Direct inventory setup failed for variant ${variant.id}:`, error);
      throw error;
    }
  }

  /**
   * Assign shipping profile to a product
   * This ensures products can be shipped and checkout can be completed
   */
  private async assignShippingProfile(productId: string): Promise<void> {
    try {
      this.logger_.info(
        `🚚 [PRODUCT-AUTO-CONFIG] Assigning shipping profile to product: ${productId}`
      );

      // Check if product already has a shipping profile
      const { Client } = require('pg');
      const client = new Client({
        connectionString: process.env.DATABASE_URL
      });

      await client.connect();

      const existingResult = await client.query(
        'SELECT * FROM product_shipping_profile WHERE product_id = $1;',
        [productId]
      );

      if (existingResult.rows.length === 0) {
        // Create the shipping profile association
        const associationId = `psp_${productId.slice(5)}_${this.defaultShippingProfileId_.slice(3)}`;
        await client.query(
          'INSERT INTO product_shipping_profile (id, product_id, shipping_profile_id, created_at, updated_at) VALUES ($1, $2, $3, NOW(), NOW());',
          [associationId, productId, this.defaultShippingProfileId_]
        );

        this.logger_.info(`✅ [PRODUCT-AUTO-CONFIG] Shipping profile assigned to product: ${productId}`);
      } else {
        this.logger_.info(`✅ [PRODUCT-AUTO-CONFIG] Product already has shipping profile: ${productId}`);
      }

      await client.end();
    } catch (error) {
      this.logger_.error(`❌ [PRODUCT-AUTO-CONFIG] Error assigning shipping profile to product ${productId}:`, error);
      throw error;
    }
  }

  /**
   * Get the default shipping profile ID
   */
  getDefaultShippingProfileId(): string {
    return this.defaultShippingProfileId_;
  }

  /**
   * Validate that the default shipping profile exists
   */
  async validateDefaultShippingProfile(): Promise<boolean> {
    try {
      const { Client } = require('pg');
      const client = new Client({
        connectionString: process.env.DATABASE_URL
      });

      await client.connect();
      const result = await client.query('SELECT id FROM shipping_profile WHERE id = $1;', [this.defaultShippingProfileId_]);
      await client.end();

      return result.rows.length > 0;
    } catch (error) {
      this.logger_.error(`❌ [PRODUCT-AUTO-CONFIG] Default shipping profile validation failed:`, error);
      return false;
    }
  }
}

export default ProductAutoConfigService;
