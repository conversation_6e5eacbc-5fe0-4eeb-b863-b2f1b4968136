/**
 * Tenant-Aware Collection Service
 *
 * Provides complete CRUD operations for collections with automatic tenant isolation.
 * Integrates with Row Level Security (RLS) for database-level tenant filtering.
 */

import { MedusaRequest } from '@medusajs/framework/http';
import { TenantQueryInterceptor } from './tenant-query-interceptor';

export class TenantAwareCollectionService {
  private collectionService: any;
  private tenantId: string;
  private pgConnection: any;
  private queryInterceptor: TenantQueryInterceptor;

  constructor(collectionService: any, tenantId: string) {
    this.collectionService = collectionService;
    this.tenantId = tenantId;
    this.queryInterceptor = new TenantQueryInterceptor(tenantId);

    console.log(`🏢 [TENANT-COLLECTION] Initialized for tenant: ${tenantId}`);
  }

  /**
   * Set database tenant context for RLS
   */
  private async setDatabaseTenantContext(): Promise<void> {
    try {
      if (!this.pgConnection) {
        const { Client } = require('pg');
        this.pgConnection = new Client({
          connectionString:
            process.env.DATABASE_URL ||
            process.env.POSTGRES_URL ||
            'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
        });
        await this.pgConnection.connect();
      }

      // Set tenant context for RLS
      await this.pgConnection.query('SELECT set_config($1, $2, true)', [
        'app.tenant_context.tenant_id',
        this.tenantId,
      ]);

      console.log(`🔒 [TENANT-COLLECTION] Set database tenant context: ${this.tenantId}`);
    } catch (error) {
      console.error(`❌ [TENANT-COLLECTION] Error setting database context: ${error}`);
    }
  }

  /**
   * Create a tenant-aware collection service instance from request
   */
  static fromRequest(req: MedusaRequest): TenantAwareCollectionService {
    let collectionService = null;
    const tenantId = req.tenant_id || 'default';

    // Try different service names for collections
    const possibleServiceNames = ['collection', 'productCollection', 'product-collection'];

    for (const serviceName of possibleServiceNames) {
      try {
        collectionService = req.scope.resolve(serviceName);
        console.log(`✅ [TENANT-COLLECTION] Found collection service: ${serviceName}`);
        break;
      } catch (error) {
        // Continue trying other service names
      }
    }

    if (!collectionService) {
      console.warn(
        `⚠️ [TENANT-COLLECTION] No collection service found, creating placeholder for tenant: ${tenantId}`
      );
      // Create a placeholder service for now
      collectionService = {
        listCollections: async () => ({ collections: [], count: 0 }),
        retrieveCollection: async () => null,
        createCollections: async (data: any[]) => data,
        updateCollections: async (data: any[]) => data,
        deleteCollections: async () => true,
      };
    }

    return new TenantAwareCollectionService(collectionService, tenantId);
  }

  /**
   * Create collections with automatic tenant_id injection
   */
  async createCollections(collectionsData: any[]): Promise<any> {
    console.log(
      `📂 [TENANT-COLLECTION] Creating ${collectionsData.length} collections for tenant: ${this.tenantId}`
    );

    try {
      // Inject tenant_id into all collection data
      const tenantCollectionsData = collectionsData.map(collectionData => ({
        ...collectionData,
        tenant_id: this.tenantId,
      }));

      const result = await this.collectionService.createCollections(tenantCollectionsData);
      console.log(
        `✅ [TENANT-COLLECTION] Created ${result.length} collections for tenant: ${this.tenantId}`
      );
      return result;
    } catch (error) {
      console.error(`❌ [TENANT-COLLECTION] Error creating collections: ${error}`);
      throw error;
    }
  }

  /**
   * List collections with tenant awareness
   */
  async listCollections(filters: any = {}, config?: any, context?: any): Promise<any> {
    console.log(`📂 [TENANT-COLLECTION] Listing collections for tenant: ${this.tenantId}`);

    try {
      // Set database tenant context for RLS
      await this.setDatabaseTenantContext();

      // Use the service method directly - RLS will handle filtering
      const result = await this.collectionService.listCollections(filters, config, context);

      const collectionCount = Array.isArray(result)
        ? result.length
        : result?.collections?.length || 0;
      console.log(
        `✅ [TENANT-COLLECTION] Found ${collectionCount} collections for tenant: ${this.tenantId}`
      );

      return result;
    } catch (error) {
      console.error(`❌ [TENANT-COLLECTION] Error listing collections: ${error}`);

      // Return empty result structure
      return {
        collections: [],
        count: 0,
        offset: config?.skip || 0,
        limit: config?.take || 20,
      };
    }
  }

  /**
   * List and count collections with tenant awareness
   */
  async listAndCountCollections(
    filters: any = {},
    config?: any,
    context?: any
  ): Promise<[any[], number]> {
    console.log(
      `📂 [TENANT-COLLECTION] Listing and counting collections for tenant: ${this.tenantId}`
    );

    try {
      // Use direct database query to ensure proper tenant filtering
      console.log(
        `🔍 [TENANT-COLLECTION] Using direct database query for tenant: ${this.tenantId}`
      );
      const [collections, count] = await this.listCollectionsDirectQuery(filters, config);

      console.log(
        `✅ [TENANT-COLLECTION] Found ${count} total collections for tenant: ${this.tenantId}`
      );
      return [collections, count];
    } catch (error) {
      console.error(`❌ [TENANT-COLLECTION] Error listing and counting collections: ${error}`);
      return [[], 0];
    }
  }

  /**
   * Retrieve a single collection by ID (with tenant validation)
   */
  async retrieveCollection(collectionId: string, config?: any, context?: any): Promise<any> {
    console.log(
      `📂 [TENANT-COLLECTION] Retrieving collection ${collectionId} for tenant: ${this.tenantId}`
    );

    try {
      // Use direct database query to get the collection
      const [collections] = await this.listCollectionsDirectQuery({ id: collectionId }, config);

      if (!collections || collections.length === 0) {
        console.log(
          `❌ [TENANT-COLLECTION] Collection ${collectionId} not found for tenant: ${this.tenantId}`
        );
        return null;
      }

      const collection = collections[0];
      console.log(
        `✅ [TENANT-COLLECTION] Retrieved collection ${collectionId} (${collection.title}) for tenant: ${this.tenantId}`
      );
      return collection;
    } catch (error) {
      console.error(`❌ [TENANT-COLLECTION] Error retrieving collection ${collectionId}: ${error}`);
      throw error;
    }
  }

  /**
   * Update collections with tenant validation
   */
  async updateCollections(collectionsData: any[]): Promise<any> {
    console.log(
      `📂 [TENANT-COLLECTION] Updating ${collectionsData.length} collections for tenant: ${this.tenantId}`
    );

    try {
      // Set database tenant context for RLS
      await this.setDatabaseTenantContext();

      // Ensure tenant_id is preserved in updates
      const tenantCollectionsData = collectionsData.map(collectionData => ({
        ...collectionData,
        tenant_id: this.tenantId, // Ensure tenant_id is not changed
      }));

      const result = await this.collectionService.updateCollections(tenantCollectionsData);
      console.log(
        `✅ [TENANT-COLLECTION] Updated ${result.length} collections for tenant: ${this.tenantId}`
      );
      return result;
    } catch (error) {
      console.error(`❌ [TENANT-COLLECTION] Error updating collections: ${error}`);
      throw error;
    }
  }

  /**
   * Delete collections with tenant validation
   */
  async deleteCollections(collectionIds: string[]): Promise<any> {
    console.log(
      `📂 [TENANT-COLLECTION] Deleting ${collectionIds.length} collections for tenant: ${this.tenantId}`
    );

    try {
      // Set database tenant context for RLS
      await this.setDatabaseTenantContext();

      const result = await this.collectionService.deleteCollections(collectionIds);
      console.log(
        `✅ [TENANT-COLLECTION] Deleted ${collectionIds.length} collections for tenant: ${this.tenantId}`
      );
      return result;
    } catch (error) {
      console.error(`❌ [TENANT-COLLECTION] Error deleting collections: ${error}`);
      throw error;
    }
  }

  /**
   * Get collection statistics for tenant
   */
  async getCollectionStats(): Promise<any> {
    console.log(
      `📊 [TENANT-COLLECTION] Getting collection statistics for tenant: ${this.tenantId}`
    );

    try {
      // Set database tenant context for RLS
      await this.setDatabaseTenantContext();

      const collections = await this.listCollections({}, { take: 1000 });
      const collectionArray = Array.isArray(collections)
        ? collections
        : collections?.collections || [];

      const stats = {
        total_collections: collectionArray.length,
        tenant_id: this.tenantId,
        timestamp: new Date().toISOString(),
      };

      console.log(`✅ [TENANT-COLLECTION] Collection stats for tenant ${this.tenantId}:`, stats);
      return stats;
    } catch (error) {
      console.error(`❌ [TENANT-COLLECTION] Error getting collection stats: ${error}`);
      return {
        total_collections: 0,
        tenant_id: this.tenantId,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Direct database query for collections with tenant filtering
   */
  private async listCollectionsDirectQuery(
    filters: any = {},
    config?: any
  ): Promise<[any[], number]> {
    // Ensure database connection is available
    if (!this.pgConnection) {
      console.log(`🔗 [TENANT-COLLECTION] Creating database connection for direct query`);
      try {
        const { Client } = require('pg');
        this.pgConnection = new Client({
          connectionString:
            process.env.DATABASE_URL ||
            process.env.POSTGRES_URL ||
            'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
        });
        await this.pgConnection.connect();
        console.log(
          `✅ [TENANT-COLLECTION] Database connection established for tenant: ${this.tenantId}`
        );
      } catch (error) {
        console.error(`❌ [TENANT-COLLECTION] Failed to create database connection: ${error}`);
        return [[], 0];
      }
    }

    try {
      // Build WHERE clause from filters
      const whereConditions: string[] = [];
      const queryParams: (string | number | boolean)[] = [];
      let paramIndex = 1;

      // ALWAYS add tenant_id filter for security (manual fallback if RLS fails)
      whereConditions.push(`tenant_id = $${paramIndex++}`);
      queryParams.push(this.tenantId);

      // ALWAYS filter out soft-deleted records (using string interpolation to avoid prepared statement issues)
      whereConditions.push(`deleted_at IS NULL`);

      // Add basic filters
      if (filters.id) {
        whereConditions.push(`id = $${paramIndex++}`);
        queryParams.push(filters.id);
      }

      if (filters.title) {
        whereConditions.push(`title ILIKE $${paramIndex++}`);
        queryParams.push(`%${filters.title}%`);
      }

      if (filters.handle) {
        whereConditions.push(`handle = $${paramIndex++}`);
        queryParams.push(filters.handle);
      }

      // Build query to get collections
      let query = 'SELECT * FROM product_collection';
      if (whereConditions.length > 0) {
        query += ' WHERE ' + whereConditions.join(' AND ');
      }

      // Add ordering and limits
      query += ' ORDER BY created_at DESC';

      if (config?.take) {
        query += ` LIMIT $${paramIndex++}`;
        queryParams.push(config.take);
      }

      if (config?.skip) {
        query += ` OFFSET $${paramIndex++}`;
        queryParams.push(config.skip);
      }

      console.log(
        `🔍 [TENANT-COLLECTION] Executing direct query for tenant ${this.tenantId}:`,
        query
      );
      console.log(`🔍 [TENANT-COLLECTION] Query parameters:`, queryParams);

      // Try using a raw query to avoid prepared statement caching issues
      const rawQuery = query.replace(/\$(\d+)/g, (match, num) => {
        const paramIndex = parseInt(num) - 1;
        const value = queryParams[paramIndex];
        return typeof value === 'string' ? `'${value.replace(/'/g, "''")}'` : value;
      });

      console.log(`🔍 [TENANT-COLLECTION] Raw query:`, rawQuery);

      const result = await this.pgConnection.query(rawQuery);
      const collections = result.rows;

      // Get total count
      let countQuery = 'SELECT COUNT(*) as total FROM product_collection';
      if (whereConditions.length > 0) {
        countQuery += ' WHERE ' + whereConditions.join(' AND ');
      }

      // Use raw query for count as well to avoid prepared statement issues
      const rawCountQuery = countQuery.replace(/\$(\d+)/g, (match, num) => {
        const paramIndex = parseInt(num) - 1;
        const value = queryParams[paramIndex];
        return typeof value === 'string' ? `'${value.replace(/'/g, "''")}'` : value;
      });

      console.log(`🔍 [TENANT-COLLECTION] Raw count query:`, rawCountQuery);

      const countResult = await this.pgConnection.query(rawCountQuery);
      const total = parseInt(countResult.rows[0].total);

      console.log(
        `✅ [TENANT-COLLECTION] Direct query found ${collections.length} collections (${total} total) for tenant: ${this.tenantId}`
      );

      return [collections, total];
    } catch (error) {
      console.error(`❌ [TENANT-COLLECTION] Direct query failed: ${error}`);
      throw error;
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    try {
      if (this.pgConnection) {
        await this.pgConnection.end();
        this.pgConnection = null;
      }
      console.log(`🧹 [TENANT-COLLECTION] Cleaned up resources for tenant: ${this.tenantId}`);
    } catch (error) {
      console.error(`❌ [TENANT-COLLECTION] Error during cleanup: ${error}`);
    }
  }
}
