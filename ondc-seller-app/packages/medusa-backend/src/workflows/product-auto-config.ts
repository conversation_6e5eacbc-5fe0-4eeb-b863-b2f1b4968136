import {
  createWorkflow,
  WorkflowData,
  WorkflowResponse,
  createStep,
  StepResponse,
} from '@medusajs/workflows-sdk';
import { createProductsWorkflow } from '@medusajs/core-flows';
import { CreateProductDTO } from '@medusajs/types';

type ProductAutoConfigInput = {
  products: CreateProductDTO[];
  tenantId?: string;
};

type ProductAutoConfigOutput = {
  products: any[];
  configurationResults: {
    successful: number;
    failed: number;
    errors: string[];
  };
};

/**
 * Step to apply automatic configuration to created products
 */
const applyProductAutoConfigStep = createStep(
  'apply-product-auto-config',
  async (input: { productIds: string[]; tenantId?: string }, { container }) => {
    const productAutoConfigService = container.resolve('productAutoConfigService');
    const logger = container.resolve('logger');

    logger.info(`🔧 [WORKFLOW] Applying auto-configuration to ${input.productIds.length} products`);

    const results = {
      successful: 0,
      failed: 0,
      errors: [] as string[],
    };

    // Apply configuration to each product
    for (const productId of input.productIds) {
      try {
        await productAutoConfigService.applyAutoConfiguration(productId, input.tenantId);
        results.successful++;
        logger.info(`✅ [WORKFLOW] Auto-configuration applied to product: ${productId}`);
      } catch (error) {
        results.failed++;
        results.errors.push(`Product ${productId}: ${error.message}`);
        logger.error(`❌ [WORKFLOW] Failed to configure product ${productId}:`, error);
      }
    }

    logger.info(
      `✅ [WORKFLOW] Auto-configuration completed: ${results.successful} successful, ${results.failed} failed`
    );

    return new StepResponse(results, {
      productIds: input.productIds,
      results,
    });
  },
  async (compensationInput, { container }) => {
    const logger = container.resolve('logger');
    logger.info(
      `🔄 [WORKFLOW] Compensating auto-configuration for products: ${compensationInput.productIds}`
    );
    // Note: In a real scenario, you might want to revert the sales channel assignments
    // For now, we'll just log the compensation
  }
);

/**
 * Enhanced product creation workflow with automatic configuration
 *
 * This workflow:
 * 1. Creates products using the standard Medusa workflow
 * 2. Automatically assigns them to the default sales channel
 * 3. Sets manage_inventory: false for all variants
 */
export const createProductsWithAutoConfigWorkflow = createWorkflow(
  'create-products-with-auto-config',
  function (input: WorkflowData<ProductAutoConfigInput>) {
    // Step 1: Create products using standard Medusa workflow
    const createdProducts = createProductsWorkflow.runAsStep({
      input: {
        products: input.products,
      },
    });

    // Step 2: Extract product IDs for configuration
    let productIds: string[] = [];

    if (Array.isArray(createdProducts)) {
      productIds = createdProducts.map((product: any) => product.id).filter(Boolean);
    } else if (createdProducts?.result && Array.isArray(createdProducts.result)) {
      productIds = createdProducts.result.map((product: any) => product.id).filter(Boolean);
    } else if (createdProducts?.id) {
      productIds = [createdProducts.id];
    } else if (createdProducts) {
      // Handle case where createdProducts is a single product object
      productIds = [createdProducts].map((product: any) => product.id).filter(Boolean);
    }

    // Step 3: Apply automatic configuration
    const configurationResults = applyProductAutoConfigStep({
      productIds,
      tenantId: input.tenantId,
    });

    // Return both the created products and configuration results
    return new WorkflowResponse({
      products: createdProducts,
      configurationResults,
    });
  }
);

/**
 * Workflow specifically for Excel import with auto-configuration
 */
export const importProductsWithAutoConfigWorkflow = createWorkflow(
  'import-products-with-auto-config',
  function (input: WorkflowData<ProductAutoConfigInput>) {
    // Step 1: Create products
    const createdProducts = createProductsWorkflow.runAsStep({
      input: {
        products: input.products,
      },
    });

    // Step 2: Apply auto-configuration with tenant context
    let productIds: string[] = [];

    if (Array.isArray(createdProducts)) {
      productIds = createdProducts.map((product: any) => product.id).filter(Boolean);
    } else if (createdProducts?.result && Array.isArray(createdProducts.result)) {
      productIds = createdProducts.result.map((product: any) => product.id).filter(Boolean);
    } else if (createdProducts?.id) {
      productIds = [createdProducts.id];
    } else if (createdProducts) {
      // Handle case where createdProducts is a single product object
      productIds = [createdProducts].map((product: any) => product.id).filter(Boolean);
    }
    const configurationResults = applyProductAutoConfigStep({
      productIds,
      tenantId: input.tenantId,
    });

    return new WorkflowResponse({
      products: createdProducts,
      configurationResults,
    });
  }
);

/**
 * Standalone workflow to apply configuration to existing products
 */
export const applyAutoConfigToExistingProductsWorkflow = createWorkflow(
  'apply-auto-config-to-existing-products',
  function (input: WorkflowData<{ productIds: string[]; tenantId?: string }>) {
    const configurationResults = applyProductAutoConfigStep({
      productIds: input.productIds,
      tenantId: input.tenantId,
    });

    return new WorkflowResponse({
      configurationResults,
    });
  }
);
