import type { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

type Body = {
  email: string;
  password: string;
  first_name?: string;
  last_name?: string;
};

// CORS headers for admin signup - dynamically set based on request origin
const getAllowedOrigins = () => {
  const adminCors = process.env.ADMIN_CORS || 'http://localhost:3000,http://localhost:3001';
  return adminCors.split(',').map(origin => origin.trim());
};

const getCorsHeaders = (requestOrigin?: string) => {
  const allowedOrigins = getAllowedOrigins();
  const origin =
    requestOrigin && allowedOrigins.includes(requestOrigin) ? requestOrigin : allowedOrigins[0]; // Default to first allowed origin

  return {
    'Access-Control-Allow-Origin': origin,
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Tenant-ID',
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Max-Age': '86400',
  };
};

// Handle preflight OPTIONS requests
export const OPTIONS = async (req: MedusaRequest, res: MedusaResponse) => {
  const origin = req.headers.origin;
  const corsHeaders = getCorsHeaders(origin);

  Object.entries(corsHeaders).forEach(([key, value]) => {
    res.setHeader(key, value);
  });
  return res.status(200).end();
};

export const POST = async (req: MedusaRequest<Body>, res: MedusaResponse) => {
  // Set CORS headers for all responses
  const origin = req.headers.origin;
  const corsHeaders = getCorsHeaders(origin);

  Object.entries(corsHeaders).forEach(([key, value]) => {
    res.setHeader(key, value);
  });

  try {
    console.log('🔐 Admin signup request:', { email: req.body?.email });
    const { email, password, first_name, last_name } = req.body;

    // Validate input
    if (!email || !password) {
      return res.status(400).json({
        message: 'Email and password are required',
      });
    }

    // Use CLI command approach - this is the only method that works reliably
    console.log('🎯 Using CLI command approach for guaranteed working authentication...');

    try {
      // Execute the CLI command from the correct directory
      const workingDirectory = process.cwd(); // Use current working directory
      const cliCommand = `npx medusa user --email "${email}" --password "${password}"`;

      console.log('🔧 Working directory:', workingDirectory);
      console.log('🔧 Executing CLI command:', cliCommand);

      const { stdout, stderr } = await execAsync(cliCommand, {
        cwd: workingDirectory,
        timeout: 30000, // 30 second timeout
      });

      console.log('🔍 CLI stdout:', stdout);
      console.log('🔍 CLI stderr:', stderr);

      // Check if the command was successful
      if (stderr && stderr.includes('Error')) {
        console.error('❌ CLI command failed with error:', stderr);
        return res.status(400).json({
          message: `Failed to create user account: ${stderr}`,
        });
      }

      if (!stdout.includes('User created successfully')) {
        console.error('❌ CLI command did not report success');
        console.log('🔍 Full stdout:', stdout);
        console.log('🔍 Full stderr:', stderr);
        return res.status(400).json({
          message: 'CLI command did not complete successfully',
        });
      }

      console.log('✅ CLI command completed successfully');
      console.log('✅ User created successfully via CLI command');
      console.log('✅ Admin signup completed successfully via CLI');
    } catch (cliError) {
      console.error('❌ CLI command execution failed:', cliError);
      console.log('🔍 CLI error details:', {
        message: cliError.message,
        code: cliError.code,
        signal: cliError.signal,
        stdout: cliError.stdout,
        stderr: cliError.stderr,
      });
      return res.status(400).json({
        message: `Failed to create user account via CLI: ${cliError.message}`,
      });
    }

    console.log('✅ Admin signup completed successfully via CLI');

    return res.status(201).json({
      message: 'Admin account created successfully',
      user: {
        email,
        first_name: first_name || 'Admin',
        last_name: last_name || 'User',
      },
      auth_identity: {
        created: true,
        linked: true,
        method: 'CLI command',
      },
    });
  } catch (error) {
    console.error('❌ Admin signup error:', error);
    return res.status(500).json({
      message: 'Internal server error during signup',
    });
  }
};
