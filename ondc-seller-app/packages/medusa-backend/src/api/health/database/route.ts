/**
 * Database Health Check Endpoint
 * 
 * Provides detailed information about database connection health,
 * pool status, and monitoring metrics.
 */

import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { createHealthCheckData } from '../../../loaders/database-monitoring-loader';

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  console.log('🏥 [HEALTH-CHECK] Database health check requested');

  try {
    const healthData = await createHealthCheckData();
    
    // Set appropriate HTTP status based on health
    const statusCode = healthData.status === 'healthy' ? 200 : 503;
    
    // Add headers for monitoring tools
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('X-Health-Check', 'database');
    res.setHeader('X-Health-Status', healthData.status);
    
    console.log(`🏥 [HEALTH-CHECK] Database health status: ${healthData.status}`);
    
    res.status(statusCode).json(healthData);
  } catch (error) {
    console.error('❌ [HEALTH-CHECK] Error during health check:', error);
    
    res.status(500).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
      message: error instanceof Error ? error.message : String(error),
    });
  }
}
