/**
 * Task 2.2: Integrate Middleware with All Medusa Endpoints
 *
 * This file defines middleware registration for all Medusa v2 routes
 * using the defineMiddlewares approach. It ensures tenant middleware
 * runs on all /admin/* and /store/* endpoints with proper execution order.
 */

console.log('🔧 [MIDDLEWARES] Loading middleware configuration...');

import { defineMiddlewares, authenticate } from '@medusajs/framework/http';
import type { MedusaRequest, MedusaResponse, MedusaNextFunction } from '@medusajs/framework/http';
import { tenantMiddleware, tenantErrorHandler } from '../middleware/tenant';

/**
 * Enhanced Tenant Middleware Wrapper
 * Wraps the tenant middleware for Medusa v2 compatibility
 */
const tenantMiddlewareWrapper = async (
  req: MedusaRequest,
  res: MedusaResponse,
  next: MedusaNextFunction
): Promise<void> => {
  try {
    await tenantMiddleware(req, res, next);
  } catch (error) {
    console.error('❌ [MIDDLEWARE] Tenant middleware error:', error);
    await tenantErrorHandler(error, req, res, next);
  }
};

/**
 * Request Logging Middleware
 * Logs all requests with tenant context for debugging
 */
const requestLogger = (req: MedusaRequest, res: MedusaResponse, next: MedusaNextFunction): void => {
  const tenantId = (req as any).tenantId || 'unknown';
  const timestamp = new Date().toISOString();

  console.log(`🌐 [${timestamp}] ${req.method} ${req.path} | Tenant: ${tenantId}`);

  // Log request headers in development
  if (process.env.NODE_ENV === 'development') {
    const tenantHeader = req.headers['x-tenant-id'];
    if (tenantHeader) {
      console.log(`   📋 Tenant Header: ${tenantHeader}`);
    }
  }

  next();
};

/**
 * Response Headers Middleware
 * Adds tenant-related headers to responses
 */
const responseHeaders = (
  req: MedusaRequest,
  res: MedusaResponse,
  next: MedusaNextFunction
): void => {
  const tenantId = (req as any).tenantId;

  if (tenantId && process.env.NODE_ENV === 'development') {
    res.setHeader('X-Tenant-Context', tenantId);
    res.setHeader('X-Multi-Tenant-Enabled', 'true');
  }

  next();
};

/**
 * Middleware Configuration
 * Defines which middleware applies to which routes
 */
export default defineMiddlewares({
  routes: [
    // ============================================================================
    // ADMIN ROUTES - All /admin/* endpoints
    // ============================================================================
    {
      matcher: '/admin/**',
      middlewares: [requestLogger, tenantMiddlewareWrapper, responseHeaders],
    },

    // ============================================================================
    // STORE ROUTES - All /store/* endpoints
    // ============================================================================
    {
      matcher: '/store/**',
      middlewares: [requestLogger, tenantMiddlewareWrapper, responseHeaders],
    },

    // ============================================================================
    // SPECIFIC ADMIN ENDPOINTS - Enhanced logging for key endpoints
    // ============================================================================
    {
      matcher: '/admin/products/**',
      middlewares: [
        (req: MedusaRequest, res: MedusaResponse, next: MedusaNextFunction) => {
          console.log(
            `🛍️  [PRODUCTS] ${req.method} ${req.path} | Tenant: ${(req as any).tenantId}`
          );
          next();
        },
      ],
    },

    {
      matcher: '/admin/customers/**',
      middlewares: [
        (req: MedusaRequest, res: MedusaResponse, next: MedusaNextFunction) => {
          console.log(
            `👥 [CUSTOMERS] ${req.method} ${req.path} | Tenant: ${(req as any).tenantId}`
          );
          next();
        },
      ],
    },

    {
      matcher: '/admin/orders/**',
      middlewares: [
        (req: MedusaRequest, res: MedusaResponse, next: MedusaNextFunction) => {
          console.log(`📦 [ORDERS] ${req.method} ${req.path} | Tenant: ${(req as any).tenantId}`);
          next();
        },
      ],
    },

    // ============================================================================
    // TENANT MANAGEMENT ENDPOINT - Special handling
    // ============================================================================
    {
      matcher: '/admin/tenant/**',
      middlewares: [
        (req: MedusaRequest, res: MedusaResponse, next: MedusaNextFunction) => {
          console.log(
            `🏢 [TENANT-MGMT] ${req.method} ${req.path} | Tenant: ${(req as any).tenantId}`
          );
          next();
        },
      ],
    },

    // ============================================================================
    // STORE PRODUCT ENDPOINTS - Enhanced logging
    // ============================================================================
    {
      matcher: '/store/products/**',
      middlewares: [
        (req: MedusaRequest, res: MedusaResponse, next: MedusaNextFunction) => {
          console.log(
            `🛒 [STORE-PRODUCTS] ${req.method} ${req.path} | Tenant: ${(req as any).tenantId}`
          );
          next();
        },
      ],
    },

    // ============================================================================
    // STORE ORDERS ENDPOINTS - Authentication + Enhanced logging
    // ============================================================================
    {
      matcher: '/store/orders/**',
      middlewares: [
        authenticate(['customer'], 'bearer'),
        (req: MedusaRequest, res: MedusaResponse, next: MedusaNextFunction) => {
          console.log(
            `📦 [STORE-ORDERS] ${req.method} ${req.path} | Tenant: ${(req as any).tenantId} | Customer: ${req.auth_context?.actor_id || 'None'}`
          );
          next();
        },
      ],
    },
  ],
});

/**
 * Middleware Execution Order:
 *
 * 1. requestLogger - Logs incoming requests with tenant context
 * 2. tenantMiddlewareWrapper - Extracts and validates tenant from headers
 * 3. responseHeaders - Adds tenant context to response headers
 * 4. Specific endpoint loggers - Additional logging for key endpoints
 *
 * This ensures:
 * - All /admin/* and /store/* routes have tenant middleware
 * - Proper error handling for tenant validation failures
 * - Comprehensive logging for debugging and monitoring
 * - Response headers for development debugging
 */
