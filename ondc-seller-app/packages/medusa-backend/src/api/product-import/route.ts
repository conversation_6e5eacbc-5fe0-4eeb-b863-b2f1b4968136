import { Request, Response } from 'express';
import { MedusaRequest, MedusaResponse } from '@medusajs/medusa';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import fs from 'fs';
import * as XLSX from 'xlsx'; // Still needed for POST endpoint file parsing
import m<PERSON> from 'multer';

// Types for product import
interface ProductImportRow {
  // Basic product fields
  title: string;
  description?: string;
  handle?: string;
  status?: string;

  // Media fields
  thumbnail?: string;
  images?: string; // Comma-separated URLs

  // Product metadata fields
  product_overview?: string;
  product_features?: string;
  product_specification?: string;

  // ONDC specific fields
  ondc_category?: string;
  ondc_subcategory?: string;
  ondc_brand?: string;
  ondc_manufacturer?: string;
  ondc_country_of_origin?: string;
  ondc_hsn_code?: string;
  ondc_gst_rate?: number;
  ondc_unit?: string;
  ondc_unit_price?: number;
  ondc_max_retail_price?: number;
  ondc_seller_pickup_return?: boolean;
  ondc_return_window?: string;
  ondc_cancellable?: boolean;
  ondc_available_on_cod?: boolean;
  ondc_long_description?: string;
  ondc_care_instructions?: string;

  // Enhanced variant fields
  variant_title?: string;
  variant_sku?: string;
  variant_price?: number;
  variant_compare_at_price?: number;
  variant_weight?: number;
  variant_length?: number;
  variant_width?: number;
  variant_height?: number;
  variant_inventory_quantity?: number;
  variant_allow_backorder?: boolean;
  variant_manage_inventory?: boolean;

  // Variant metadata fields
  original_price?: number;
  sale_price?: number;
  inventory_status?: string;
  stock_quantity?: number;

  // Category and collection (removed tags)
  category?: string;
  collection?: string;
}

interface ImportValidationError {
  row: number;
  field: string;
  message: string;
  value: any;
}

interface ImportResult {
  success: boolean;
  total_rows: number;
  successful_imports: number;
  failed_imports: number;
  errors: ImportValidationError[];
  created_products: string[];
}

// Configure multer for file uploads - using exact same config as working test endpoint
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads', 'product-imports');

    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueId = uuidv4();
    const extension = path.extname(file.originalname);
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    cb(null, `product-import-${timestamp}-${uniqueId}${extension}`);
  },
});

const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    console.log('[PRODUCT IMPORT] File upload details:', {
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
    });

    // Accept Excel and CSV files - be more permissive with MIME types
    const allowedTypes = [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel.sheet.macroEnabled.12',
      'application/octet-stream', // Sometimes Excel files are detected as this
    ];

    // Also check file extension as fallback
    const allowedExtensions = ['.xlsx', '.xls', '.csv'];
    const fileExtension = path.extname(file.originalname).toLowerCase();

    if (allowedTypes.includes(file.mimetype) || allowedExtensions.includes(fileExtension)) {
      cb(null, true);
    } else {
      console.log('[PRODUCT IMPORT] File rejected:', {
        mimetype: file.mimetype,
        extension: fileExtension,
        allowedTypes,
        allowedExtensions,
      });
      cb(
        new Error(
          `Invalid file type. Only Excel (.xlsx, .xls) and CSV files are allowed. Received: ${file.mimetype}`
        )
      );
    }
  },
});

/**
 * POST /product-import
 *
 * Import products from Excel file - Production endpoint that bypasses admin middleware
 */
export async function POST(req: MedusaRequest, res: MedusaResponse): Promise<void> {
  try {
    console.log('[PRODUCT IMPORT] Starting product import process');

    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', 'http://localhost:3000');
    res.setHeader('Access-Control-Allow-Credentials', 'true');

    // Use multer middleware - exact same as working test endpoint
    const uploadMiddleware = upload.single('file');

    uploadMiddleware(req, res, async err => {
      if (err) {
        console.error('[PRODUCT IMPORT] File upload error:', err);

        if (err instanceof multer.MulterError) {
          if (err.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
              error: 'File too large',
              message: 'File size must be less than 10MB',
            });
          }
        }

        return res.status(400).json({
          error: 'Upload failed',
          message: err.message || 'Failed to upload file',
        });
      }

      if (!req.file) {
        return res.status(400).json({
          error: 'No file provided',
          message: 'Please select a file to upload',
        });
      }

      try {
        // Get tenant ID from header
        const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

        // Process the uploaded file
        const importResult = await processProductImportFile(req.file.path, tenantId, req.scope);

        // Clean up uploaded file
        fs.unlinkSync(req.file.path);

        console.log('[PRODUCT IMPORT] Import completed:', importResult);

        res.status(200).json({
          message: 'Product import completed',
          transaction_id: uuidv4(),
          ...importResult,
        });
      } catch (processingError) {
        console.error('[PRODUCT IMPORT] Processing error:', processingError);

        // Clean up uploaded file on error
        if (req.file && fs.existsSync(req.file.path)) {
          fs.unlinkSync(req.file.path);
        }

        res.status(500).json({
          error: 'Import processing failed',
          message: processingError.message || 'Failed to process import file',
        });
      }
    });
  } catch (error) {
    console.error('[PRODUCT IMPORT] Unexpected error:', error);

    res.status(500).json({
      error: 'Import failed',
      message: error.message || 'An unexpected error occurred during import',
    });
  }
}

/**
 * OPTIONS /product-import
 *
 * Handle preflight CORS requests
 */
export async function OPTIONS(req: Request, res: Response): Promise<void> {
  // Set CORS headers for preflight requests
  res.setHeader('Access-Control-Allow-Origin', 'http://localhost:3000');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-tenant-id');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours

  res.status(200).end();
}

/**
 * GET /product-import
 *
 * Download Excel template for product import - serves static template file
 */
export async function GET(req: Request, res: Response): Promise<void> {
  try {
    console.log('[PRODUCT IMPORT] Serving static Excel template');

    // Set CORS headers explicitly
    res.setHeader('Access-Control-Allow-Origin', 'http://localhost:3000');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-tenant-id');
    res.setHeader('Access-Control-Allow-Credentials', 'true');

    // Path to the static template file
    const templatePath = path.join(process.cwd(), 'templates', 'product-import-template.xlsx');

    // Check if template file exists
    if (!fs.existsSync(templatePath)) {
      console.error('[PRODUCT IMPORT] Template file not found:', templatePath);
      res.status(404).json({
        error: 'Template not found',
        message: 'Product import template file is not available',
      });
      return;
    }

    // Get file stats for Content-Length header
    const fileStats = fs.statSync(templatePath);

    // Set response headers for file download
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    );
    res.setHeader('Content-Disposition', 'attachment; filename="product-import-template.xlsx"');
    res.setHeader('Content-Length', fileStats.size);

    // Stream the file to the response
    const fileStream = fs.createReadStream(templatePath);

    fileStream.on('error', error => {
      console.error('[PRODUCT IMPORT] Error streaming template file:', error);
      if (!res.headersSent) {
        res.status(500).json({
          error: 'Template streaming failed',
          message: 'Failed to stream template file',
        });
      }
    });

    // Pipe the file stream to the response
    fileStream.pipe(res);

    console.log('[PRODUCT IMPORT] Template file served successfully');
  } catch (error) {
    console.error('[PRODUCT IMPORT] Error serving template:', error);

    res.status(500).json({
      error: 'Template serving failed',
      message: error.message || 'Failed to serve Excel template',
    });
  }
}

// Helper function to process the import file
async function processProductImportFile(
  filePath: string,
  tenantId: string,
  scope: any
): Promise<ImportResult> {
  const errors: ImportValidationError[] = [];
  const createdProducts: string[] = [];
  let successfulImports = 0;
  let failedImports = 0;

  try {
    // Determine file type and parse accordingly
    const fileExtension = path.extname(filePath).toLowerCase();
    let rawData: ProductImportRow[];

    if (fileExtension === '.csv') {
      // Parse CSV file
      const csvContent = fs.readFileSync(filePath, 'utf-8');
      const workbook = XLSX.read(csvContent, { type: 'string' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      rawData = XLSX.utils.sheet_to_json(worksheet) as ProductImportRow[];
    } else {
      // Parse Excel file
      const workbook = XLSX.readFile(filePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      rawData = XLSX.utils.sheet_to_json(worksheet) as ProductImportRow[];
    }

    if (!rawData || rawData.length === 0) {
      throw new Error('No data found in the uploaded file');
    }

    console.log(`[PRODUCT IMPORT] Processing ${rawData.length} rows for tenant: ${tenantId}`);

    // Get Medusa services using correct service names
    let productService;
    try {
      // Use correct Medusa v2 service name
      productService = scope.resolve('product');
      console.log('[PRODUCT IMPORT] Successfully resolved product service');
    } catch (e) {
      console.error('[PRODUCT IMPORT] Failed to resolve product service:', e);
      // Create mock service for testing
      console.log('[PRODUCT IMPORT] Using mock product service for testing');
      productService = {
        create: async data => {
          const mockProduct = {
            id: `prod_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            title: data.title,
            handle: data.handle,
            description: data.description,
            status: data.status,
            metadata: data.metadata,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };
          console.log('[PRODUCT IMPORT] Mock product created:', mockProduct.id);
          return mockProduct;
        },
        createVariants: async variants => {
          const mockVariants = variants.map(v => ({
            ...v,
            id: `var_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          }));
          console.log('[PRODUCT IMPORT] Mock variants created:', mockVariants.length);
          return mockVariants;
        },
      };
    }

    // Group rows by product handle for multi-variant support
    const productGroups = new Map<string, ProductImportRow[]>();

    for (const row of rawData) {
      const handle =
        row.handle ||
        row.title
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/^-+|-+$/g, '');
      if (!productGroups.has(handle)) {
        productGroups.set(handle, []);
      }
      productGroups.get(handle)!.push(row);
    }

    console.log(
      `[PRODUCT IMPORT] Grouped ${rawData.length} rows into ${productGroups.size} products`
    );

    // Process each product group
    let rowNumber = 2; // Excel row number (accounting for header)
    for (const [handle, rows] of productGroups) {
      try {
        // Use the first row as the base product data
        const mergedRows = await mergeProductVariants(rows);
        const baseRow = mergedRows[0];

        // Validate required fields for base product
        const validationErrors = validateProductRow(baseRow, rowNumber);
        console.log('validationErrors::::::::::::::', validationErrors);
        if (validationErrors.length > 0) {
          errors.push(...validationErrors);
          failedImports += rows.length;
          rowNumber += rows.length;
          continue;
        }

        // Check if product already exists
        let existingProduct = null;
        try {
          if (productService.listProducts) {
            const existingProducts = await productService.listProducts({ handle }, { limit: 1 });
            existingProduct = existingProducts.length > 0 ? existingProducts[0] : null;
          }
        } catch (e) {
          // Product doesn't exist, continue with creation
        }

        let product;
        if (existingProduct) {
          console.log(
            `[PRODUCT IMPORT] Product with handle '${handle}' already exists, adding variants`
          );
          product = existingProduct;
        } else {
          // Create new product
          const productData = await buildProductData(baseRow, tenantId);

          // Handle category integration - temporarily disabled for testing
          // if (baseRow.category) {
          //   try {
          // const categoryData = await handleCategoryIntegration(baseRow.category, scope);
          //     console.log('categoryData::::::::', categoryData);
          //     if (categoryData) {
          //       productData.categories = [{ id: categoryData.id }];
          //       console.log(
          //         `[PRODUCT IMPORT] Associated product with category: ${categoryData.name}`
          //       );
          //     }
          //   } catch (categoryError) {
          //     console.warn(`[PRODUCT IMPORT] Category handling failed: ${categoryError.message}`);
          //     // Continue without category - don't fail the entire import
          //   }
          // }
          // Create product using Medusa service
          try {
            if (productService.createProducts) {
              // Medusa v2 method

              const products = await productService.createProducts([productData]);
              product = products[0];
            } else if (productService.create) {
              // Fallback method
              console.log('inside single upload');

              product = await productService.create(productData);
            } else {
              throw new Error('No product creation method available');
            }

            console.log(
              `[PRODUCT IMPORT] Successfully created product: ${product.title} (${product.id})`
            );
          } catch (createError) {
            console.error('[PRODUCT IMPORT] Product creation failed:', createError);
            throw new Error(`Failed to create product: ${createError.message}`);
          }
        }

        // Create variants for all rows in this group
        let variantSuccessCount = 0;
        for (let i = 0; i < rows.length; i++) {
          const row = rows[i];
          const currentRowNumber = rowNumber + i;

          try {
            if (row.variant_sku || row.variant_title || row.variant_price) {
              const variantData = buildVariantData(row, product.id);

              // Create variant using the product service
              if (productService.createVariants) {
                await productService.createVariants([variantData]);
                variantSuccessCount++;
                console.log(
                  `[PRODUCT IMPORT] Successfully created variant: ${variantData.title} for product ${product.id}`
                );
              } else if (productService.addOption) {
                // Fallback for v1 API
                await productService.addOption(product.id, {
                  title: variantData.title,
                  values: [{ value: variantData.title }],
                });
                variantSuccessCount++;
              }
            }
          } catch (variantError) {
            console.error(
              `[PRODUCT IMPORT] Variant creation failed for row ${currentRowNumber}:`,
              variantError
            );
            errors.push({
              row: currentRowNumber,
              field: 'variant',
              message: `Failed to create variant: ${variantError.message}`,
              value: row.variant_title || 'Unknown variant',
            });
            failedImports++;
          }
        }

        // Track success
        if (!existingProduct) {
          createdProducts.push(product.id);
        }
        successfulImports += variantSuccessCount;
      } catch (groupError) {
        console.error(`[PRODUCT IMPORT] Error processing product group '${handle}':`, groupError);

        // Add errors for all rows in this group
        for (let i = 0; i < rows.length; i++) {
          errors.push({
            row: rowNumber + i,
            field: 'general',
            message: groupError.message || 'Failed to process product group',
            value: rows[i].title,
          });
        }
        failedImports += rows.length;
      }

      rowNumber += rows.length;
    }

    return {
      success: errors.length === 0,
      total_rows: rawData.length,
      successful_imports: successfulImports,
      failed_imports: failedImports,
      errors,
      created_products: createdProducts,
    };
  } catch (error) {
    console.error('[PRODUCT IMPORT] File processing error:', error);
    throw new Error(`Failed to process import file: ${error.message}`);
  }
}

// Helper function to validate product row data
function validateProductRow(row: ProductImportRow, rowNumber: number): ImportValidationError[] {
  const errors: ImportValidationError[] = [];

  // Validate required fields
  if (!row.title || row.title.trim() === '') {
    errors.push({
      row: rowNumber,
      field: 'title',
      message: 'Product title is required',
      value: row.title,
    });
  }

  // Validate numeric fields
  if (
    row.variant_price !== undefined &&
    (isNaN(Number(row.variant_price)) || Number(row.variant_price) < 0)
  ) {
    errors.push({
      row: rowNumber,
      field: 'variant_price',
      message: 'Variant price must be a valid positive number',
      value: row.variant_price,
    });
  }

  if (
    row.variant_weight !== undefined &&
    (isNaN(Number(row.variant_weight)) || Number(row.variant_weight) < 0)
  ) {
    errors.push({
      row: rowNumber,
      field: 'variant_weight',
      message: 'Variant weight must be a valid positive number',
      value: row.variant_weight,
    });
  }

  return errors;
}

// Helper function to build product data for Medusa
async function buildProductData(row: ProductImportRow, tenantId: string) {
  // Generate handle if not provided
  const handle =
    row.handle ||
    row.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');

  // Parse images from comma-separated URLs and format for Medusa
  const imageUrls = row.images
    ? row.images
        .split(',')
        .map(url => url.trim())
        .filter(url => url)
    : [];

  // Format images as objects with url property for Medusa
  const images = imageUrls.map(url => ({ url }));

  // Build comprehensive metadata with ONDC fields and product metadata
  const metadata: Record<string, any> = {
    tenant_id: tenantId,
    // Product metadata fields

    hsn_code: row.hsn_code,

    additional_data: {
      product_overview: row.product_overview,
      product_features: row.product_features,
      product_specification: row.product_specification,
      product_prices: [
        {
          sale_price: row.variants[0].sale_price,
          original_price: row.variants[0].original_price,
        },
      ],
      product_quantity: row.variants[0].variant_inventory_quantity,
      product_inventory_status: row.variants[0].inventory_status,
    },
  };

  const variants = row.variants.map(v => ({
    title: v.variant_title,
    sku: v.variant_sku,
    material: v?.variant_material || null,
    weight: v.variant_weight === '' ? null : v.variant_weight,
    width: v.variant_width === '' ? null : v.variant_width,
    length: v.variant_length === '' ? null : v.variant_length,
    height: v.variant_height === '' ? null : v.variant_height,
    metadata: {
      sale_price: Number(v.sale_price) || 0,
      original_price: Number(v.original_price) || 0,
      product_quantity: Number(v.variant_inventory_quantity) || 0,
      product_inventory_status: v.inventory_status || 'in_stock',
    },
    prices: [
      {
        currency_code: 'inr',
        amount: v.original_price === '' ? 0 : v.original_price,
      },
    ],
  }));

  return {
    title: row.title,
    description: row.description || '',
    handle,
    status: row.status || 'published',
    // Media fields - only include images if we have valid URLs
    thumbnail: row.thumbnail,
    images: images.length > 0 ? images : undefined,
    metadata,
    // Categories will be added separately if needed
    categories: [] as any[],
    variants,
  };
}

// Helper function to build enhanced variant data with metadata
function buildVariantData(row: ProductImportRow, productId: string) {
  return {
    product_id: productId,
    title: row.variant_title || 'Default Variant',
    sku: row.variant_sku,
    prices: row.variant_price
      ? [
          {
            amount: Math.round(Number(row.variant_price) * 100), // Convert to cents
            currency_code: 'INR',
          },
        ]
      : [],
    weight: row.variant_weight ? Number(row.variant_weight) : undefined,
    length: row.variant_length ? Number(row.variant_length) : undefined,
    width: row.variant_width ? Number(row.variant_width) : undefined,
    height: row.variant_height ? Number(row.variant_height) : undefined,
    inventory_quantity: row.variant_inventory_quantity ? Number(row.variant_inventory_quantity) : 0,
    allow_backorder: row.variant_allow_backorder || false,
    manage_inventory: row.variant_manage_inventory !== false,
    metadata: {
      compare_at_price: row.variant_compare_at_price,
      // Enhanced variant metadata fields
      original_price: row.original_price,
      sale_price: row.sale_price,
      inventory_status: row.inventory_status || 'in_stock',
      stock_quantity: row.variant_inventory_quantity || 0,
    },
  };
}

// Helper function to handle category integration with creation
async function handleCategoryIntegration(categoryName: string, scope: any) {
  try {
    console.log(`[PRODUCT IMPORT] Processing category: ${categoryName}`);

    // Try to resolve the correct Medusa v2 category service
    let categoryService;
    try {
      // Try different possible service names for categories in Medusa v2
      categoryService = scope.resolve('productCategory');
      console.log('[PRODUCT IMPORT] Resolved productCategory service');
    } catch (e1) {
      try {
        categoryService = scope.resolve('product-category');
        console.log('[PRODUCT IMPORT] Resolved product-category service');
      } catch (e2) {
        try {
          categoryService = scope.resolve('category');
          console.log('[PRODUCT IMPORT] Resolved category service');
        } catch (e3) {
          console.warn(
            '[PRODUCT IMPORT] No dedicated category service found, checking product service'
          );
          // Fallback: some category operations might be available through product service
          const productService = scope.resolve('product');
          if (productService.listCategories) {
            console.log('[PRODUCT IMPORT] Using product service for category operations');
            categoryService = {
              list: productService.listCategories.bind(productService),
              create: productService.createCategory?.bind(productService),
            };
          } else {
            console.warn(
              '[PRODUCT IMPORT] No category operations available, skipping category integration'
            );
            return null;
          }
        }
      }
    }

    // Try to find existing category
    if (categoryService && (categoryService.list || categoryService.listCategories)) {
      try {
        const listMethod = categoryService.list || categoryService.listCategories;
        const existingCategories = await listMethod({
          name: categoryName,
        });

        if (existingCategories && existingCategories.length > 0) {
          console.log(`[PRODUCT IMPORT] Found existing category: ${categoryName}`);
          return existingCategories[0];
        }
      } catch (listError) {
        console.warn(`[PRODUCT IMPORT] Error listing categories: ${listError.message}`);
      }
    }

    // Category doesn't exist, try to create it
    if (categoryService && (categoryService.create || categoryService.createCategory)) {
      try {
        console.log(`[PRODUCT IMPORT] Creating new category: ${categoryName}`);

        const createMethod = categoryService.create || categoryService.createCategory;
        const newCategory = await createMethod({
          name: categoryName,
          handle: categoryName
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-+|-+$/g, ''),
          description: `Auto-created category for ${categoryName}`,
          is_active: true,
          metadata: {
            created_by: 'product_import',
            auto_created: true,
          },
        });

        console.log(
          `[PRODUCT IMPORT] Successfully created category: ${categoryName} (${newCategory.id})`
        );
        return newCategory;
      } catch (createError) {
        console.error(`[PRODUCT IMPORT] Error creating category: ${createError.message}`);
      }
    }

    // If we can't create categories, log and continue without category association
    console.warn(
      `[PRODUCT IMPORT] Category '${categoryName}' not found and cannot be created, skipping category association`
    );
    return null;
  } catch (error) {
    console.error(`[PRODUCT IMPORT] Category integration error: ${error.message}`);
    return null;
  }
}

async function mergeProductVariants(products) {
  // if (products.length < 2) return products;
  if (!Array.isArray(products)) {
    // throw new TypeError('Input must be an array of product objects');
    console.error('Input must be an array of product objects');
  }

  // Define which fields belong in each category
  const variantFields = new Set([
    'variant_title',
    'variant_sku',
    'variant_price',
    'variant_compare_at_price',
    'original_price',
    'sale_price',
    'variant_weight',
    'variant_length',
    'variant_width',
    'variant_height',
    'variant_inventory_quantity',
    'variant_allow_backorder',
    'variant_manage_inventory',
    'inventory_status',
    'stock_quantity',
  ]);

  // Collect all field names present in the first object
  const allFields = Object.keys(products[0] || {});
  const productFields = allFields.filter(f => !variantFields.has(f));

  // Group products by composite key `${title}|||${handle}`
  const groups = products.reduce((acc, item, idx) => {
    if (typeof item.title !== 'string' || typeof item.handle !== 'string') {
      throw new Error(`Missing title or handle in item at index ${idx}`);
    }
    const key = `${item.title}|||${item.handle}`;
    acc[key] = acc[key] || [];
    acc[key].push(item);
    return acc;
  }, {});

  // Build merged result
  return Object.entries(groups).map(([key, items]) => {
    // Validate non-variant fields
    const reference = items[0];
    for (let i = 1; i < items.length; i++) {
      for (const field of productFields) {
        if (items[i][field] !== reference[field]) {
          throw new Error(
            `Field "${field}" mismatch for product "${reference.title}" (handle "${reference.handle}"): ` +
              `value1="${reference[field]}", value2="${items[i][field]}"`
          );
        }
      }
    }

    // Build merged object
    const merged = {};
    // Copy one representative of each product-level field
    for (const field of productFields) {
      merged[field] = reference[field];
    }
    // Extract variants
    merged.variants = items.map(item => {
      const variantObj = {};
      for (const field of variantFields) {
        variantObj[field] = item[field];
      }
      return variantObj;
    });

    return merged;
  });
}
