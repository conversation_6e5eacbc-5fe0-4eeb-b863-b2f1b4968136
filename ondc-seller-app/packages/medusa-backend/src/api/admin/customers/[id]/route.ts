import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  console.log(`🚀 [TENANT FILTER] === CUSTOM CUSTOMER GET BY ID ENDPOINT CALLED ===`);

  try {
    // Extract tenant ID from header
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    // Get customer ID from URL params
    const customerId = req.params?.id as string;

    if (!customerId) {
      return res.status(400).json({
        error: 'Customer ID is required',
        tenant_id: tenantId,
      });
    }

    console.log(`🔍 [TENANT FILTER] Getting customer ${customerId} for tenant: ${tenantId}`);

    // Direct database connection approach
    const { Client } = require('pg');
    const client = new Client({
      connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
    });

    let customer: any = null;

    try {
      await client.connect();

      // Get customer with tenant validation
      const result = await client.query(
        `
        SELECT 
          id, email, first_name, last_name, phone,
          created_at, updated_at, tenant_id, metadata
        FROM customer 
        WHERE id = $1 AND tenant_id = $2
      `,
        [customerId, tenantId]
      );

      customer = result.rows[0] || null;
      console.log(`👥 [TENANT FILTER] Retrieved customer: ${customer ? 'Found' : 'Not Found'}`);
    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error:', dbError);
      throw dbError;
    } finally {
      // Always close the database connection
      try {
        await client.end();
        console.log(`🔗 [TENANT FILTER] Database connection closed`);
      } catch (closeError) {
        console.error('❌ [TENANT FILTER] Error closing database connection:', closeError);
      }
    }

    if (!customer) {
      return res.status(404).json({
        error: 'Customer not found or access denied',
        customer_id: customerId,
        tenant_id: tenantId,
      });
    }

    // Return response in Medusa format
    const response = {
      customer,
      _tenant: {
        id: tenantId,
        filtered: true,
        method: 'direct_db_connection',
      },
    };

    res.setHeader('X-Tenant-ID', tenantId);
    res.setHeader('X-Tenant-Filtered', 'true');

    res.json(response);
  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error getting customer:', error);

    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    res.status(500).json({
      error: 'Failed to get customer',
      message: error.message,
      tenant_id: tenantId,
    });
  }
}

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  console.log(`🚀 [TENANT FILTER] === CUSTOM CUSTOMER UPDATE ENDPOINT CALLED ===`);

  try {
    // Extract tenant ID from header
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    // Get customer ID from URL params
    const customerId = req.params?.id as string;

    if (!customerId) {
      return res.status(400).json({
        error: 'Customer ID is required for update',
        tenant_id: tenantId,
      });
    }

    console.log(`🔄 [TENANT FILTER] Updating customer ${customerId} for tenant: ${tenantId}`);

    // Get update data from request body
    const updateData = req.body as any;

    // Remove tenant_id from update data to prevent modification
    const { tenant_id: _, ...safeUpdateData } = updateData;

    // Direct database connection approach
    const { Client } = require('pg');
    const client = new Client({
      connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
    });

    let updatedCustomer: any = null;

    try {
      await client.connect();

      // First, verify the customer belongs to this tenant
      const checkQuery = 'SELECT id FROM customer WHERE id = $1 AND tenant_id = $2';
      const checkResult = await client.query(checkQuery, [customerId, tenantId]);

      if (checkResult.rows.length === 0) {
        await client.end();
        return res.status(404).json({
          error: 'Customer not found or access denied',
          customer_id: customerId,
          tenant_id: tenantId,
        });
      }

      // Update the customer (tenant_id cannot be changed)
      const updateQuery = `
        UPDATE customer 
        SET 
          email = COALESCE($1, email),
          first_name = COALESCE($2, first_name),
          last_name = COALESCE($3, last_name),
          phone = COALESCE($4, phone),
          metadata = COALESCE($5, metadata),
          updated_at = NOW()
        WHERE id = $6 AND tenant_id = $7
        RETURNING *
      `;

      const values = [
        safeUpdateData.email,
        safeUpdateData.first_name,
        safeUpdateData.last_name,
        safeUpdateData.phone,
        safeUpdateData.metadata ? JSON.stringify(safeUpdateData.metadata) : null,
        customerId,
        tenantId,
      ];

      const result = await client.query(updateQuery, values);
      updatedCustomer = result.rows[0];

      console.log(`✅ [TENANT FILTER] Updated customer ${customerId} for tenant: ${tenantId}`);
    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error:', dbError);
      throw dbError;
    } finally {
      // Always close the database connection
      try {
        await client.end();
        console.log(`🔗 [TENANT FILTER] Database connection closed`);
      } catch (closeError) {
        console.error('❌ [TENANT FILTER] Error closing database connection:', closeError);
      }
    }

    // Return response in Medusa format
    const response = {
      customer: updatedCustomer,
      _tenant: {
        id: tenantId,
        validated: true,
        method: 'direct_db_connection',
      },
    };

    res.setHeader('X-Tenant-ID', tenantId);
    res.setHeader('X-Tenant-Validated', 'true');

    res.json(response);
  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error updating customer:', error);

    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    res.status(500).json({
      error: 'Failed to update customer',
      message: error.message,
      tenant_id: tenantId,
    });
  }
}
