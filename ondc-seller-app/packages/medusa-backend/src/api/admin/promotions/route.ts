import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import {
  buildListQuery,
  buildResponseMetadata,
  getEntityConfig,
  parsePaginationParams,
} from '../../../utils/query-builder';

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  console.log(`🚀 [TENANT FILTER] === ENHANCED TENANT-AWARE PROMOTIONS ENDPOINT CALLED ===`);

  try {
    // Extract tenant ID from header
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    // Extract query parameters
    const queryParams = req.query || {};

    console.log(`🔍 [TENANT FILTER] Getting promotions for tenant: ${tenantId}`);
    console.log(`📋 [QUERY PARAMS] Filters:`, queryParams);

    // Direct database connection approach
    const { Client } = require('pg');
    const client = new Client({
      connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
    });

    let promotions: any[] = [];
    let totalCount = 0;
    let pagination = { limit: 20, offset: 0 };

    try {
      await client.connect();
      console.log(`🔗 [TENANT FILTER] Connected to database directly`);

      // Build query using utility functions
      const config = getEntityConfig('promotions');
      const queryResult = buildListQuery(tenantId, queryParams, config);
      pagination = queryResult.pagination;

      // Get total count
      const countResult = await client.query(
        queryResult.countQuery,
        queryResult.values.slice(0, -2)
      ); // Remove limit/offset for count
      totalCount = parseInt(countResult.rows[0].total);

      // Get promotions
      const promotionsResult = await client.query(queryResult.listQuery, queryResult.values);
      promotions = promotionsResult.rows;

      console.log(
        `📦 [TENANT FILTER] Retrieved ${promotions.length}/${totalCount} promotions for tenant ${tenantId}`
      );

      // Get application methods for each promotion
      for (const promotion of promotions) {
        try {
          const appMethodQuery = `
            SELECT 
              id, currency_code, max_quantity, apply_to_quantity, buy_rules_min_quantity,
              type, target_type, allocation, raw_value, created_at, updated_at, deleted_at,
              promotion_id
            FROM promotion_application_method 
            WHERE promotion_id = $1 AND deleted_at IS NULL
          `;

          const appMethodResult = await client.query(appMethodQuery, [promotion.id]);
          const appMethod = appMethodResult.rows[0];

          if (appMethod) {
            // Parse raw_value if it's a string
            if (typeof appMethod.raw_value === 'string') {
              try {
                appMethod.raw_value = JSON.parse(appMethod.raw_value);
              } catch (e) {
                console.log(`⚠️ Could not parse raw_value for application method ${appMethod.id}`);
              }
            }

            // Calculate value from raw_value
            if (appMethod.raw_value && appMethod.raw_value.value) {
              appMethod.value = parseFloat(appMethod.raw_value.value);
            }

            // Get target rules for this application method
            const targetRulesQuery = `
              SELECT 
                id, operator, description, attribute, values, created_at, updated_at, deleted_at
              FROM promotion_rule 
              WHERE application_method_id = $1 AND deleted_at IS NULL
            `;

            const targetRulesResult = await client.query(targetRulesQuery, [appMethod.id]);
            appMethod.target_rules = targetRulesResult.rows || [];

            // Get buy rules for this application method
            const buyRulesQuery = `
              SELECT 
                id, operator, description, attribute, values, created_at, updated_at, deleted_at
              FROM promotion_rule 
              WHERE application_method_id = $1 AND deleted_at IS NULL
            `;

            const buyRulesResult = await client.query(buyRulesQuery, [appMethod.id]);
            appMethod.buy_rules = buyRulesResult.rows || [];

            promotion.application_method = appMethod;
          } else {
            promotion.application_method = null;
          }
        } catch (e) {
          console.log(
            `⚠️ Could not fetch application method for promotion ${promotion.id}:`,
            e.message
          );
          promotion.application_method = null;
        }
      }

      // Get promotion rules for each promotion
      for (const promotion of promotions) {
        try {
          const rulesQuery = `
            SELECT 
              pr.id, pr.operator, pr.description, pr.attribute, pr.values,
              pr.created_at, pr.updated_at, pr.deleted_at
            FROM promotion_rule pr
            JOIN promotion_promotion_rule ppr ON pr.id = ppr.promotion_rule_id
            WHERE ppr.promotion_id = $1 AND pr.deleted_at IS NULL
          `;

          const rulesResult = await client.query(rulesQuery, [promotion.id]);
          promotion.rules = rulesResult.rows || [];
        } catch (e) {
          console.log(`⚠️ Could not fetch rules for promotion ${promotion.id}:`, e.message);
          promotion.rules = [];
        }
      }

      // Get campaign information for promotions that have campaign_id
      for (const promotion of promotions) {
        if (promotion.campaign_id) {
          try {
            const campaignQuery = `
              SELECT 
                id, name, description, starts_at, ends_at, created_at, updated_at, deleted_at
              FROM promotion_campaign 
              WHERE id = $1 AND deleted_at IS NULL
            `;

            const campaignResult = await client.query(campaignQuery, [promotion.campaign_id]);
            promotion.campaign = campaignResult.rows[0] || null;
          } catch (e) {
            console.log(`⚠️ Could not fetch campaign for promotion ${promotion.id}:`, e.message);
            promotion.campaign = null;
          }
        } else {
          promotion.campaign = null;
        }
      }
    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error:', dbError);
      throw dbError;
    } finally {
      // Always close the database connection
      try {
        await client.end();
        console.log(`🔗 [TENANT FILTER] Database connection closed`);
      } catch (closeError) {
        console.error('❌ [TENANT FILTER] Error closing database connection:', closeError);
      }
    }

    // Build response using utility function
    const responseMetadata = buildResponseMetadata(
      tenantId,
      queryParams,
      totalCount,
      promotions.length,
      pagination
    );

    const response = {
      promotions,
      ...responseMetadata,
    };

    // Add tenant headers
    res.setHeader('X-Tenant-ID', tenantId);
    res.setHeader('X-Tenant-Filtered', 'true');

    console.log(
      `📤 [TENANT FILTER] Returning ${promotions.length} promotions for tenant ${tenantId}`
    );

    res.json(response);
  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error getting promotions:', error);

    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    res.status(500).json({
      error: 'Failed to get promotions',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'tenant_promotions_error',
        timestamp: new Date().toISOString(),
      },
    });
  }
}

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  console.log(`🚀 [TENANT FILTER] === TENANT-AWARE PROMOTION CREATE ENDPOINT CALLED ===`);

  try {
    // Extract tenant ID from header
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    console.log(`🆕 [TENANT FILTER] Creating promotion for tenant: ${tenantId}`);

    // Get promotion data from request body
    const promotionData = req.body as any;

    // Ensure tenant_id is injected and cannot be modified
    const promotionWithTenant = {
      ...promotionData,
      tenant_id: tenantId,
    };

    // Direct database connection approach
    const { Client } = require('pg');
    const client = new Client({
      connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
    });

    let createdPromotion: any = null;

    try {
      await client.connect();
      console.log(`🔗 [TENANT FILTER] Connected to database for promotion creation`);

      // Start transaction
      await client.query('BEGIN');

      // Generate promotion ID
      const promotionId = `promo_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      // Create promotion
      const createPromotionQuery = `
        INSERT INTO promotion (
          id, code, campaign_id, is_automatic, type, status, is_tax_inclusive, tenant_id,
          created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())
        RETURNING *
      `;

      const promotionValues = [
        promotionId,
        promotionWithTenant.code,
        promotionWithTenant.campaign_id || null,
        promotionWithTenant.is_automatic || false,
        promotionWithTenant.type || 'standard',
        promotionWithTenant.status || 'draft',
        promotionWithTenant.is_tax_inclusive || false,
        tenantId,
      ];

      const promotionResult = await client.query(createPromotionQuery, promotionValues);
      createdPromotion = promotionResult.rows[0];

      // Create application method if provided
      if (promotionWithTenant.application_method) {
        const appMethod = promotionWithTenant.application_method;
        const appMethodId = `proappmet_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

        const createAppMethodQuery = `
          INSERT INTO promotion_application_method (
            id, currency_code, max_quantity, apply_to_quantity, buy_rules_min_quantity,
            type, target_type, allocation, raw_value, promotion_id, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW())
        `;

        const appMethodValues = [
          appMethodId,
          appMethod.currency_code || null,
          appMethod.max_quantity || null,
          appMethod.apply_to_quantity || null,
          appMethod.buy_rules_min_quantity || null,
          appMethod.type || 'percentage',
          appMethod.target_type || 'order',
          appMethod.allocation || null,
          appMethod.raw_value ? JSON.stringify(appMethod.raw_value) : null,
          promotionId,
        ];

        await client.query(createAppMethodQuery, appMethodValues);
      }

      // Commit transaction
      await client.query('COMMIT');
      console.log(`✅ [TENANT FILTER] Created promotion ${promotionId} for tenant ${tenantId}`);

      await client.end();
    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error during promotion creation:', dbError);
      await client.query('ROLLBACK').catch(() => {});
      await client.end().catch(() => {});
      throw dbError;
    }

    // Return response in Medusa format
    const response = {
      promotion: createdPromotion,
      _tenant: {
        id: tenantId,
        validated: true,
        method: 'direct_db_creation',
      },
    };

    // Add tenant headers
    res.setHeader('X-Tenant-ID', tenantId);
    res.setHeader('X-Tenant-Validated', 'true');

    console.log(`📤 [TENANT FILTER] Returning created promotion for tenant ${tenantId}`);

    res.json(response);
  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error creating promotion:', error);

    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    res.status(500).json({
      error: 'Failed to create promotion',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'tenant_promotion_create_error',
        timestamp: new Date().toISOString(),
      },
    });
  }
}
