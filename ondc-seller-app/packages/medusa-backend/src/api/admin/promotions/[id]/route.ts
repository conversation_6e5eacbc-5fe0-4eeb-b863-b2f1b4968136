import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
) {
  console.log(`🚀 [TENANT FILTER] === TENANT-AWARE PROMOTION GET BY ID ENDPOINT CALLED ===`)

  try {
    // Extract tenant ID from header
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    // Get promotion ID from URL params
    const promotionId = req.params?.id as string
    
    if (!promotionId) {
      return res.status(400).json({
        error: 'Promotion ID is required',
        tenant_id: tenantId
      })
    }

    console.log(`🔍 [TENANT FILTER] Getting promotion ${promotionId} for tenant: ${tenantId}`)

    // Direct database connection approach
    const { Client } = require('pg')
    const client = new Client({
      connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
    })

    let promotion: any = null

    try {
      await client.connect()
      console.log(`🔗 [TENANT FILTER] Connected to database directly`)

      // Get promotion with tenant validation
      const result = await client.query(`
        SELECT 
          id, code, campaign_id, is_automatic, type, status, is_tax_inclusive,
          created_at, updated_at, deleted_at, tenant_id
        FROM promotion 
        WHERE id = $1 AND tenant_id = $2 AND deleted_at IS NULL
      `, [promotionId, tenantId])

      promotion = result.rows[0] || null
      console.log(`📦 [TENANT FILTER] Retrieved promotion: ${promotion ? 'Found' : 'Not Found'}`)

      if (promotion) {
        // Get application method
        try {
          const appMethodQuery = `
            SELECT 
              id, currency_code, max_quantity, apply_to_quantity, buy_rules_min_quantity,
              type, target_type, allocation, raw_value, created_at, updated_at, deleted_at,
              promotion_id
            FROM promotion_application_method 
            WHERE promotion_id = $1 AND deleted_at IS NULL
          `
          
          const appMethodResult = await client.query(appMethodQuery, [promotion.id])
          const appMethod = appMethodResult.rows[0]
          
          if (appMethod) {
            // Parse raw_value if it's a string
            if (typeof appMethod.raw_value === 'string') {
              try {
                appMethod.raw_value = JSON.parse(appMethod.raw_value)
              } catch (e) {
                console.log(`⚠️ Could not parse raw_value for application method ${appMethod.id}`)
              }
            }
            
            // Calculate value from raw_value
            if (appMethod.raw_value && appMethod.raw_value.value) {
              appMethod.value = parseFloat(appMethod.raw_value.value)
            }

            // Get target rules and buy rules
            appMethod.target_rules = []
            appMethod.buy_rules = []

            promotion.application_method = appMethod
          } else {
            promotion.application_method = null
          }
        } catch (e) {
          console.log(`⚠️ Could not fetch application method for promotion ${promotion.id}:`, e.message)
          promotion.application_method = null
        }

        // Get promotion rules
        try {
          const rulesQuery = `
            SELECT 
              pr.id, pr.operator, pr.description, pr.attribute, pr.values,
              pr.created_at, pr.updated_at, pr.deleted_at
            FROM promotion_rule pr
            JOIN promotion_promotion_rule ppr ON pr.id = ppr.promotion_rule_id
            WHERE ppr.promotion_id = $1 AND pr.deleted_at IS NULL
          `
          
          const rulesResult = await client.query(rulesQuery, [promotion.id])
          promotion.rules = rulesResult.rows || []
        } catch (e) {
          console.log(`⚠️ Could not fetch rules for promotion ${promotion.id}:`, e.message)
          promotion.rules = []
        }

        // Get campaign if exists
        if (promotion.campaign_id) {
          try {
            const campaignQuery = `
              SELECT 
                id, name, description, starts_at, ends_at, created_at, updated_at, deleted_at
              FROM promotion_campaign 
              WHERE id = $1 AND deleted_at IS NULL
            `
            
            const campaignResult = await client.query(campaignQuery, [promotion.campaign_id])
            promotion.campaign = campaignResult.rows[0] || null
          } catch (e) {
            console.log(`⚠️ Could not fetch campaign for promotion ${promotion.id}:`, e.message)
            promotion.campaign = null
          }
        } else {
          promotion.campaign = null
        }
      }

      await client.end()

    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error:', dbError)
      await client.end().catch(() => {})
      throw dbError
    }

    if (!promotion) {
      return res.status(404).json({
        error: 'Promotion not found or access denied',
        promotion_id: promotionId,
        tenant_id: tenantId,
        _debug: {
          message: 'Promotion either does not exist or belongs to a different tenant'
        }
      })
    }

    // Return response in Medusa format
    const response = {
      promotion,
      _tenant: {
        id: tenantId,
        filtered: true,
        method: 'direct_db_connection'
      }
    }

    // Add tenant headers
    res.setHeader('X-Tenant-ID', tenantId)
    res.setHeader('X-Tenant-Filtered', 'true')

    console.log(`📤 [TENANT FILTER] Returning promotion ${promotionId} for tenant ${tenantId}`)
    
    res.json(response)

  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error getting promotion:', error)
    
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    res.status(500).json({
      error: 'Failed to get promotion',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'tenant_promotion_get_error',
        timestamp: new Date().toISOString()
      }
    })
  }
}

export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
) {
  console.log(`🚀 [TENANT FILTER] === TENANT-AWARE PROMOTION UPDATE ENDPOINT CALLED ===`)

  try {
    // Extract tenant ID from header
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    // Get promotion ID from URL params
    const promotionId = req.params?.id as string
    
    if (!promotionId) {
      return res.status(400).json({
        error: 'Promotion ID is required for update',
        tenant_id: tenantId
      })
    }

    console.log(`🔄 [TENANT FILTER] Updating promotion ${promotionId} for tenant: ${tenantId}`)

    // Get update data from request body
    const updateData = req.body as any

    // Ensure tenant_id is injected and cannot be modified
    const promotionWithTenant = {
      ...updateData,
      tenant_id: tenantId
    }

    // Direct database connection approach
    const { Client } = require('pg')
    const client = new Client({
      connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
    })

    let updatedPromotion: any = null

    try {
      await client.connect()
      console.log(`🔗 [TENANT FILTER] Connected to database for promotion update`)

      // Start transaction
      await client.query('BEGIN')

      // First, verify the promotion belongs to this tenant
      const checkQuery = 'SELECT * FROM promotion WHERE id = $1 AND tenant_id = $2 AND deleted_at IS NULL'
      const checkResult = await client.query(checkQuery, [promotionId, tenantId])

      if (checkResult.rows.length === 0) {
        await client.query('ROLLBACK')
        await client.end()
        return res.status(404).json({
          error: 'Promotion not found or access denied',
          promotion_id: promotionId,
          tenant_id: tenantId,
          _debug: {
            message: 'Promotion either does not exist or belongs to a different tenant'
          }
        })
      }

      // Update promotion
      const updatePromotionQuery = `
        UPDATE promotion 
        SET 
          code = COALESCE($1, code),
          campaign_id = COALESCE($2, campaign_id),
          is_automatic = COALESCE($3, is_automatic),
          type = COALESCE($4, type),
          status = COALESCE($5, status),
          is_tax_inclusive = COALESCE($6, is_tax_inclusive),
          updated_at = NOW()
        WHERE id = $7 AND tenant_id = $8
        RETURNING *
      `

      const promotionValues = [
        promotionWithTenant.code,
        promotionWithTenant.campaign_id,
        promotionWithTenant.is_automatic,
        promotionWithTenant.type,
        promotionWithTenant.status,
        promotionWithTenant.is_tax_inclusive,
        promotionId,
        tenantId
      ]

      const promotionResult = await client.query(updatePromotionQuery, promotionValues)
      updatedPromotion = promotionResult.rows[0]

      // Commit transaction
      await client.query('COMMIT')
      console.log(`✅ [TENANT FILTER] Updated promotion ${promotionId} for tenant ${tenantId}`)

      await client.end()

    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error during promotion update:', dbError)
      await client.query('ROLLBACK').catch(() => {})
      await client.end().catch(() => {})
      throw dbError
    }

    // Return response in Medusa format
    const response = {
      promotion: updatedPromotion,
      _tenant: {
        id: tenantId,
        validated: true,
        method: 'direct_db_update'
      }
    }

    // Add tenant headers
    res.setHeader('X-Tenant-ID', tenantId)
    res.setHeader('X-Tenant-Validated', 'true')

    console.log(`📤 [TENANT FILTER] Returning updated promotion for tenant ${tenantId}`)
    
    res.json(response)

  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error updating promotion:', error)
    
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    res.status(500).json({
      error: 'Failed to update promotion',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'tenant_promotion_update_error',
        timestamp: new Date().toISOString()
      }
    })
  }
}

// PUT method - alias to POST for standard REST API compatibility
export async function PUT(
  req: MedusaRequest,
  res: MedusaResponse
) {
  console.log(`🔄 [TENANT FILTER] PUT request received, delegating to POST handler`)
  return await POST(req, res)
}

export async function DELETE(
  req: MedusaRequest,
  res: MedusaResponse
) {
  console.log(`🚀 [TENANT FILTER] === TENANT-AWARE PROMOTION DELETE ENDPOINT CALLED ===`)

  try {
    // Extract tenant ID from header
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    // Get promotion ID from URL params
    const promotionId = req.params?.id as string
    
    if (!promotionId) {
      return res.status(400).json({
        error: 'Promotion ID is required for deletion',
        tenant_id: tenantId
      })
    }

    console.log(`🗑️ [TENANT FILTER] Deleting promotion ${promotionId} for tenant: ${tenantId}`)

    // Direct database connection approach
    const { Client } = require('pg')
    const client = new Client({
      connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
    })

    try {
      await client.connect()
      console.log(`🔗 [TENANT FILTER] Connected to database directly`)

      // First, verify the promotion belongs to this tenant and get it
      const checkQuery = 'SELECT * FROM promotion WHERE id = $1 AND tenant_id = $2 AND deleted_at IS NULL'
      const checkResult = await client.query(checkQuery, [promotionId, tenantId])

      if (checkResult.rows.length === 0) {
        await client.end()
        return res.status(404).json({
          error: 'Promotion not found or access denied',
          promotion_id: promotionId,
          tenant_id: tenantId,
          _debug: {
            message: 'Promotion either does not exist or belongs to a different tenant'
          }
        })
      }

      // Soft delete the promotion
      const deleteQuery = 'UPDATE promotion SET deleted_at = NOW() WHERE id = $1 AND tenant_id = $2 RETURNING *'
      const result = await client.query(deleteQuery, [promotionId, tenantId])

      console.log(`✅ [TENANT FILTER] Deleted promotion ${promotionId} for tenant: ${tenantId}`)

      await client.end()

    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error:', dbError)
      await client.end().catch(() => {})
      throw dbError
    }

    // Return response in Medusa format
    const response = {
      id: promotionId,
      object: 'promotion',
      deleted: true,
      _tenant: {
        id: tenantId,
        validated: true,
        method: 'direct_db_connection'
      }
    }

    // Add tenant headers
    res.setHeader('X-Tenant-ID', tenantId)
    res.setHeader('X-Tenant-Validated', 'true')

    console.log(`📤 [TENANT FILTER] Confirmed deletion of promotion ${promotionId} for tenant ${tenantId}`)
    
    res.json(response)

  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error deleting promotion:', error)
    
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    res.status(500).json({
      error: 'Failed to delete promotion',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'tenant_promotion_delete_error',
        timestamp: new Date().toISOString()
      }
    })
  }
}
