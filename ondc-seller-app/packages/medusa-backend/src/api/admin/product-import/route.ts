import { Request, Response } from 'express';
import { MedusaRequest, MedusaResponse } from '@medusajs/medusa';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import fs from 'fs';
import * as XLSX from 'xlsx'; // Still needed for POST endpoint file parsing
import multer from 'multer';
import { DirectDatabaseService } from '../../../services/direct-database-service';
import { applyAutoConfigToExistingProductsWorkflow } from '../../../workflows/product-auto-config';

// Types for product import
interface ProductImportRow {
  // Basic product fields
  title: string;
  description?: string;
  handle?: string;
  status?: string;

  // Media fields
  thumbnail?: string;
  images?: string; // Comma-separated URLs

  // Product metadata fields
  product_overview?: string;
  product_features?: string;
  product_specification?: string;

  // ONDC specific fields
  ondc_category?: string;
  ondc_subcategory?: string;
  ondc_brand?: string;
  ondc_manufacturer?: string;
  ondc_country_of_origin?: string;
  ondc_hsn_code?: string;
  ondc_gst_rate?: number;
  ondc_unit?: string;
  ondc_unit_price?: number;
  ondc_max_retail_price?: number;
  ondc_seller_pickup_return?: boolean;
  ondc_return_window?: string;
  ondc_cancellable?: boolean;
  ondc_available_on_cod?: boolean;
  ondc_long_description?: string;
  ondc_care_instructions?: string;

  // Enhanced variant fields
  variant_title?: string;
  variant_sku?: string;
  variant_price?: number;
  variant_compare_at_price?: number;
  variant_weight?: number;
  variant_length?: number;
  variant_width?: number;
  variant_height?: number;
  variant_inventory_quantity?: number;

  // Multi-currency price fields from CSV
  'variant price inr'?: number;
  'variant price eur'?: number;
  'variant price usd'?: number;
  variant_allow_backorder?: boolean;
  variant_manage_inventory?: boolean;

  // Variant metadata fields
  original_price?: number;
  sale_price?: number;
  inventory_status?: string;
  stock_quantity?: number;

  // Category and collection (removed tags)
  category?: string;
  subcategory?: string;
  parent_category?: string;
  collection?: string;

  // Enhanced metadata fields (additional to existing ones)
  product_quantity?: number;
  product_inventory_status?: string;

  // Variants array for merged products
  variants?: ProductImportRow[];
}

interface ImportValidationError {
  row: number;
  field: string;
  message: string;
  value: any;
  conflictType?: 'duplicate_handle' | 'duplicate_title' | 'duplicate_sku' | 'duplicate_handle_sku';
  existingRecord?: {
    id: string;
    title?: string;
    handle?: string;
    sku?: string;
    productId?: string;
  };
}

interface ImportResult {
  success: boolean;
  total_rows: number;
  successful_imports: number;
  failed_imports: number;
  errors: ImportValidationError[];
  created_products: string[];
  created_categories: string[];
  created_variants: number;
  category_stats: {
    existing_categories_used: number;
    new_categories_created: number;
    subcategories_created: number;
  };
}

// Configure multer for file uploads - using exact same config as working test endpoint
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads', 'product-imports');

    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueId = uuidv4();
    const extension = path.extname(file.originalname);
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    cb(null, `product-import-${timestamp}-${uniqueId}${extension}`);
  },
});

const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    console.log('[PRODUCT IMPORT] File upload details:', {
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
    });

    // Accept Excel and CSV files - be more permissive with MIME types
    const allowedTypes = [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel.sheet.macroEnabled.12',
      'application/octet-stream', // Sometimes Excel files are detected as this
    ];

    // Also check file extension as fallback
    const allowedExtensions = ['.xlsx', '.xls', '.csv'];
    const fileExtension = path.extname(file.originalname).toLowerCase();

    if (allowedTypes.includes(file.mimetype) || allowedExtensions.includes(fileExtension)) {
      cb(null, true);
    } else {
      console.log('[PRODUCT IMPORT] File rejected:', {
        mimetype: file.mimetype,
        extension: fileExtension,
        allowedTypes,
        allowedExtensions,
      });
      cb(
        new Error(
          `Invalid file type. Only Excel (.xlsx, .xls) and CSV files are allowed. Received: ${file.mimetype}`
        )
      );
    }
  },
});

/**
 * POST /product-import
 *
 * Import products from Excel file - Production endpoint that bypasses admin middleware
 */
export async function POST(req: MedusaRequest, res: MedusaResponse): Promise<void> {
  try {
    console.log('[PRODUCT IMPORT] Starting product import process');

    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', 'http://localhost:3000');
    res.setHeader('Access-Control-Allow-Credentials', 'true');

    // Use multer middleware - exact same as working test endpoint
    const uploadMiddleware = upload.single('file');

    uploadMiddleware(req, res, async err => {
      if (err) {
        console.error('[PRODUCT IMPORT] File upload error:', err);

        if (err instanceof multer.MulterError) {
          if (err.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
              error: 'File too large',
              message: 'File size must be less than 10MB',
            });
          }
        }

        return res.status(400).json({
          error: 'Upload failed',
          message: err.message || 'Failed to upload file',
        });
      }

      if (!req.file) {
        return res.status(400).json({
          error: 'No file provided',
          message: 'Please select a file to upload',
        });
      }

      try {
        // Get tenant ID from header - ensure it's properly extracted
        const tenantId = req.headers['x-tenant-id'] as string;

        if (!tenantId) {
          return res.status(400).json({
            error: 'Missing tenant ID',
            message: 'x-tenant-id header is required for multi-tenant operations',
          });
        }

        console.log(`[PRODUCT IMPORT] Processing import for tenant: ${tenantId}`);

        // Process the uploaded file
        const importResult = await processProductImportFile(req.file.path, tenantId, req.scope);

        // Clean up uploaded file
        fs.unlinkSync(req.file.path);

        console.log('[PRODUCT IMPORT] Import completed:', importResult);

        res.status(200).json({
          message: 'Product import completed',
          transaction_id: uuidv4(),
          ...importResult,
        });
      } catch (processingError) {
        console.error('[PRODUCT IMPORT] Processing error:', processingError);

        // Clean up uploaded file on error
        if (req.file && fs.existsSync(req.file.path)) {
          fs.unlinkSync(req.file.path);
        }

        res.status(500).json({
          error: 'Import processing failed',
          message: processingError.message || 'Failed to process import file',
        });
      }
    });
  } catch (error) {
    console.error('[PRODUCT IMPORT] Unexpected error:', error);

    res.status(500).json({
      error: 'Import failed',
      message: error.message || 'An unexpected error occurred during import',
    });
  }
}

/**
 * OPTIONS /product-import
 *
 * Handle preflight CORS requests
 */
export async function OPTIONS(req: Request, res: Response): Promise<void> {
  // Set CORS headers for preflight requests
  res.setHeader('Access-Control-Allow-Origin', 'http://localhost:3000');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-tenant-id');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours

  res.status(200).end();
}

/**
 * GET /product-import
 *
 * Download Excel template for product import - serves static template file
 */
export async function GET(req: Request, res: Response): Promise<void> {
  try {
    console.log('[PRODUCT IMPORT] Serving static Excel template');

    // Set CORS headers explicitly
    res.setHeader('Access-Control-Allow-Origin', 'http://localhost:3000');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-tenant-id');
    res.setHeader('Access-Control-Allow-Credentials', 'true');

    // Path to the static template file
    const templatePath = path.join(process.cwd(), 'templates', 'product-import-template.xlsx');

    // Check if template file exists
    if (!fs.existsSync(templatePath)) {
      console.error('[PRODUCT IMPORT] Template file not found:', templatePath);
      res.status(404).json({
        error: 'Template not found',
        message: 'Product import template file is not available',
      });
      return;
    }

    // Get file stats for Content-Length header
    const fileStats = fs.statSync(templatePath);

    // Set response headers for file download
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    );
    res.setHeader('Content-Disposition', 'attachment; filename="product-import-template.xlsx"');
    res.setHeader('Content-Length', fileStats.size);

    // Stream the file to the response
    const fileStream = fs.createReadStream(templatePath);

    fileStream.on('error', error => {
      console.error('[PRODUCT IMPORT] Error streaming template file:', error);
      if (!res.headersSent) {
        res.status(500).json({
          error: 'Template streaming failed',
          message: 'Failed to stream template file',
        });
      }
    });

    // Pipe the file stream to the response
    fileStream.pipe(res);

    console.log('[PRODUCT IMPORT] Template file served successfully');
  } catch (error) {
    console.error('[PRODUCT IMPORT] Error serving template:', error);

    res.status(500).json({
      error: 'Template serving failed',
      message: error.message || 'Failed to serve Excel template',
    });
  }
}

/**
 * Validate products and variants for duplicates before processing
 */
async function validateDuplicates(
  rawData: ProductImportRow[],
  tenantId: string,
  scope: any
): Promise<ImportValidationError[]> {
  const errors: ImportValidationError[] = [];

  try {
    // Get product service for database queries
    let productService;
    try {
      productService = scope.resolve('product');
    } catch (e) {
      console.warn(
        '[DUPLICATE VALIDATION] Product service not available, skipping database validation'
      );
      return errors;
    }

    // For handle+SKU duplicate detection
    const handleSkuToRow = new Map<string, number>();
    // For title and SKU detection (kept as in original)
    const importTitles = new Set<string>();
    const importSkus = new Set<string>();
    const titleToRow = new Map<string, number>();
    const skuToRow = new Map<string, number>();

    rawData.forEach((row, index) => {
      const rowNumber = index + 2; // Excel row number (accounting for header)

      // Generate handle if not provided
      const handle =
        row.handle ||
        (row.title && typeof row.title === 'string'
          ? row.title
              .toLowerCase()
              .replace(/[^a-z0-9]+/g, '-')
              .replace(/^-+|-+$/g, '')
          : `product-${index + 1}`);

      const sku = row.variant_sku || '';

      // Build a unique key for handle + sku
      const handleSkuKey = `${handle}|${sku}`;

      // Check for internal duplicates in import file (handle + sku)
      console.log('handleSkuToRow.has(handleSkuKey)::::::::::', handleSkuToRow.has(handleSkuKey));
      if (handleSkuToRow.has(handleSkuKey)) {
        errors.push({
          row: rowNumber,
          field: 'handle',
          message: `Duplicate handle '${handle}' with SKU '${sku}' found in import file at row ${handleSkuToRow.get(handleSkuKey)}`,
          value: handle,
          conflictType: 'duplicate_handle_sku',
        });
      } else {
        handleSkuToRow.set(handleSkuKey, rowNumber);
      }

      // Title duplicate detection (optional)
      // if (importTitles.has(row.title)) {
      //   errors.push({
      //     row: rowNumber,
      //     field: 'title',
      //     message: `Duplicate title '${row.title}' found in import file at row ${titleToRow.get(row.title)}`,
      //     value: row.title,
      //     conflictType: 'duplicate_title',
      //   });
      // } else {
      //   importTitles.add(row.title);
      //   titleToRow.set(row.title, rowNumber);
      // }

      // SKU duplicate detection (optional)
      if (row.variant_sku) {
        if (importSkus.has(row.variant_sku)) {
          errors.push({
            row: rowNumber,
            field: 'variant_sku',
            message: `Duplicate SKU '${row.variant_sku}' found in import file at row ${skuToRow.get(row.variant_sku)}`,
            value: row.variant_sku,
            conflictType: 'duplicate_sku',
          });
        } else {
          importSkus.add(row.variant_sku);
          skuToRow.set(row.variant_sku, rowNumber);
        }
      }
    });

    // Check for database duplicates
    await validateDatabaseDuplicates(rawData, tenantId, productService, errors);
  } catch (error) {
    console.error('[DUPLICATE VALIDATION] Error during validation:', error);
    errors.push({
      row: 0,
      field: 'general',
      message: `Validation error: ${error.message}`,
      value: null,
    });
  }

  return errors;
}

/**
 * Check for duplicates against existing database records
 */
async function validateDatabaseDuplicates(
  rawData: ProductImportRow[],
  tenantId: string,
  productService: any,
  errors: ImportValidationError[]
): Promise<void> {
  try {
    // Collect unique handles and titles for batch checking
    const uniqueHandles = new Set<string>();
    const uniqueTitles = new Set<string>();
    const uniqueSkus = new Set<string>();

    rawData.forEach((row, index) => {
      const handle =
        row.handle ||
        (row.title && typeof row.title === 'string'
          ? row.title
              .toLowerCase()
              .replace(/[^a-z0-9]+/g, '-')
              .replace(/^-+|-+$/g, '')
          : `product-${index + 1}`);
      uniqueHandles.add(handle);
      if (row.title && typeof row.title === 'string') {
        uniqueTitles.add(row.title);
      }
      if (row.variant_sku) {
        uniqueSkus.add(row.variant_sku);
      }
    });

    // Check for existing products by handle with proper tenant filtering
    if (productService.listProducts) {
      try {
        const existingProductsByHandle = await productService.listProducts({
          handle: Array.from(uniqueHandles),
          tenant_id: tenantId, // Use tenant_id directly, not in metadata
        });

        const existingHandles = new Set(existingProductsByHandle.map(p => p.handle));

        rawData.forEach((row, index) => {
          const rowNumber = index + 2;
          const handle =
            row.handle ||
            (row.title && typeof row.title === 'string'
              ? row.title
                  .toLowerCase()
                  .replace(/[^a-z0-9]+/g, '-')
                  .replace(/^-+|-+$/g, '')
              : `product-${index + 1}`);

          if (existingHandles.has(handle)) {
            const existingProduct = existingProductsByHandle.find(p => p.handle === handle);
            errors.push({
              row: rowNumber,
              field: 'handle',
              message: `Product with handle '${handle}' already exists in database`,
              value: handle,
              conflictType: 'duplicate_handle',
              existingRecord: {
                id: existingProduct.id,
                title: existingProduct.title,
                handle: existingProduct.handle,
              },
            });
          }
        });
      } catch (e) {
        console.warn(
          '[DUPLICATE VALIDATION] Could not check existing products by handle:',
          e.message
        );
      }

      // Check for existing products by title with proper tenant filtering
      try {
        const existingProductsByTitle = await productService.listProducts({
          title: Array.from(uniqueTitles),
          tenant_id: tenantId, // Use tenant_id directly, not in metadata
        });

        const existingTitles = new Set(existingProductsByTitle.map(p => p.title));

        rawData.forEach((row, index) => {
          const rowNumber = index + 2;
          if (row.title && typeof row.title === 'string' && existingTitles.has(row.title)) {
            const existingProduct = existingProductsByTitle.find(p => p.title === row.title);
            errors.push({
              row: rowNumber,
              field: 'title',
              message: `Product with title '${row.title}' already exists in database`,
              value: row.title,
              conflictType: 'duplicate_title',
              existingRecord: {
                id: existingProduct.id,
                title: existingProduct.title,
                handle: existingProduct.handle,
              },
            });
          }
        });
      } catch (e) {
        console.warn(
          '[DUPLICATE VALIDATION] Could not check existing products by title:',
          e.message
        );
      }
    }

    // Check for existing variants by SKU
    if (productService.listVariants && uniqueSkus.size > 0) {
      try {
        const existingVariants = await productService.listVariants({
          sku: Array.from(uniqueSkus),
        });

        const existingSkus = new Set(existingVariants.map(v => v.sku));

        rawData.forEach((row, index) => {
          const rowNumber = index + 2;
          if (row.variant_sku && existingSkus.has(row.variant_sku)) {
            const existingVariant = existingVariants.find(v => v.sku === row.variant_sku);
            errors.push({
              row: rowNumber,
              field: 'variant_sku',
              message: `Variant with SKU '${row.variant_sku}' already exists in database`,
              value: row.variant_sku,
              conflictType: 'duplicate_sku',
              existingRecord: {
                id: existingVariant.id,
                sku: existingVariant.sku,
                productId: existingVariant.product_id,
              },
            });
          }
        });
      } catch (e) {
        console.warn('[DUPLICATE VALIDATION] Could not check existing variants by SKU:', e.message);
      }
    }
  } catch (error) {
    console.error('[DUPLICATE VALIDATION] Database validation error:', error);
  }
}

// Helper function to process the import file
async function processProductImportFile(
  filePath: string,
  tenantId: string,
  scope: any
): Promise<ImportResult> {
  const errors: ImportValidationError[] = [];
  const createdProducts: string[] = [];
  const createdCategories: string[] = [];
  let successfulImports = 0;
  let failedImports = 0;
  let createdVariants = 0;
  const categoryStats = {
    existing_categories_used: 0,
    new_categories_created: 0,
    subcategories_created: 0,
  };

  try {
    // Determine file type and parse accordingly
    const fileExtension = path.extname(filePath).toLowerCase();
    let rawData: ProductImportRow[];

    if (fileExtension === '.csv') {
      // Parse CSV file
      const csvContent = fs.readFileSync(filePath, 'utf-8');
      const workbook = XLSX.read(csvContent, { type: 'string' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const parsedData = XLSX.utils.sheet_to_json(worksheet);
      rawData = parsedData.map(row => mapCsvFieldsToExpected(row));
    } else {
      // Parse Excel file
      const workbook = XLSX.readFile(filePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const parsedData = XLSX.utils.sheet_to_json(worksheet);
      rawData = parsedData.map(row => mapCsvFieldsToExpected(row));
    }

    if (!rawData || rawData.length === 0) {
      throw new Error('No data found in the uploaded file');
    }

    console.log(`[PRODUCT IMPORT] Processing ${rawData.length} rows for tenant: ${tenantId}`);

    // Perform comprehensive duplicate validation before processing
    console.log('[PRODUCT IMPORT] Starting duplicate validation...');
    const duplicateErrors = await validateDuplicates(rawData, tenantId, scope);

    if (duplicateErrors.length > 0) {
      console.log(`[PRODUCT IMPORT] Found ${duplicateErrors.length} duplicate validation errors`);

      // Return early with validation errors - don't process any products
      return {
        success: false,
        total_rows: rawData.length,
        successful_imports: 0,
        failed_imports: rawData.length,
        errors: duplicateErrors,
        created_products: [],
        created_categories: [],
        created_variants: 0,
        category_stats: {
          existing_categories_used: 0,
          new_categories_created: 0,
          subcategories_created: 0,
        },
      };
    }

    console.log('[PRODUCT IMPORT] Duplicate validation passed, proceeding with import...');

    // Use direct database operations for better control over tenant isolation
    let productService;
    try {
      // Try to resolve the product service
      productService = scope.resolve('product');
      console.log('[PRODUCT IMPORT] Successfully resolved product service');
    } catch (e) {
      console.error('[PRODUCT IMPORT] Failed to resolve product service:', e);
      // We'll use direct database operations instead
      productService = null;
    }

    // Group rows by product handle for multi-variant support
    const productGroups = new Map<string, ProductImportRow[]>();

    for (const [index, row] of rawData.entries()) {
      const handle =
        row.handle ||
        (row.title && typeof row.title === 'string'
          ? row.title
              .toLowerCase()
              .replace(/[^a-z0-9]+/g, '-')
              .replace(/^-+|-+$/g, '')
          : `product-${index + 1}`);
      if (!productGroups.has(handle)) {
        productGroups.set(handle, []);
      }
      productGroups.get(handle)!.push(row);
    }

    console.log(
      `[PRODUCT IMPORT] Grouped ${rawData.length} rows into ${productGroups.size} products`
    );

    // Process each product group
    let rowNumber = 2; // Excel row number (accounting for header)
    for (const [handle, rows] of productGroups) {
      try {
        // Use the first row as the base product data
        const mergedRows = await mergeProductVariants(rows);
        const baseRow = mergedRows[0];

        // Validate required fields for base product
        const validationErrors = validateProductRow(baseRow, rowNumber);
        console.log('validationErrors::::::::::::::', validationErrors);
        if (validationErrors.length > 0) {
          errors.push(...validationErrors);
          failedImports += rows.length;
          rowNumber += rows.length;
          continue;
        }

        // Check if product already exists
        let existingProduct = null;
        try {
          if (productService.listProducts) {
            const existingProducts = await productService.listProducts({ handle }, { limit: 1 });
            existingProduct = existingProducts.length > 0 ? existingProducts[0] : null;
          }
        } catch (e) {
          // Product doesn't exist, continue with creation
        }

        let product;
        if (existingProduct) {
          console.log(
            `[PRODUCT IMPORT] Product with handle '${handle}' already exists, adding variants`
          );
          product = existingProduct;
        } else {
          // Create new product using direct database operations
          try {
            const createResult = await createProductWithVariantsEnhanced(baseRow, tenantId, scope);
            product = createResult.product;

            // Track category statistics
            if (createResult.categoryResult) {
              createdCategories.push(...createResult.categoryResult.createdCategories);
              categoryStats.existing_categories_used +=
                createResult.categoryResult.stats.existing_categories_used;
              categoryStats.new_categories_created +=
                createResult.categoryResult.stats.new_categories_created;
              categoryStats.subcategories_created +=
                createResult.categoryResult.stats.subcategories_created;
            }

            // Track variant count
            createdVariants += createResult.variantCount;

            console.log(
              `[PRODUCT IMPORT] Successfully created product: ${product.title} (${product.id}) with ${createResult.variantCount} variants`
            );
          } catch (createError) {
            console.error('[PRODUCT IMPORT] Product creation failed:', createError);
            throw new Error(`Failed to create product: ${createError.message}`);
          }
        }

        // Track success - variants are created within createProductWithVariants
        if (!existingProduct) {
          createdProducts.push(product.id);
        }
        successfulImports += rows.length; // Count all rows as successful since variants are created with product
      } catch (groupError) {
        console.error(`[PRODUCT IMPORT] Error processing product group '${handle}':`, groupError);

        // Add errors for all rows in this group
        for (let i = 0; i < rows.length; i++) {
          errors.push({
            row: rowNumber + i,
            field: 'general',
            message: groupError.message || 'Failed to process product group',
            value: rows[i].title,
          });
        }
        failedImports += rows.length;
      }

      rowNumber += rows.length;
    }

    // Apply auto-configuration to all created products
    console.log(
      `[PRODUCT IMPORT] Applying auto-configuration to ${createdProducts.length} created products`
    );
    let autoConfigResults = {
      successful: 0,
      failed: 0,
      errors: [] as string[],
    };

    if (createdProducts.length > 0) {
      try {
        const workflowResult = await applyAutoConfigToExistingProductsWorkflow(scope).run({
          input: {
            productIds: createdProducts,
            tenantId: tenantId,
          },
        });

        autoConfigResults = workflowResult.result.configurationResults;
        console.log(`[PRODUCT IMPORT] Auto-configuration completed:`, autoConfigResults);
      } catch (autoConfigError) {
        console.error('[PRODUCT IMPORT] Auto-configuration failed:', autoConfigError);
        autoConfigResults.failed = createdProducts.length;
        autoConfigResults.errors.push(`Auto-configuration failed: ${autoConfigError.message}`);
      }
    }

    return {
      success: errors.length === 0,
      total_rows: rawData.length,
      successful_imports: successfulImports,
      failed_imports: failedImports,
      errors,
      created_products: createdProducts,
      created_categories: createdCategories,
      created_variants: createdVariants,
      category_stats: categoryStats,
      auto_configuration: autoConfigResults,
    };
  } catch (error) {
    console.error('[PRODUCT IMPORT] File processing error:', error);
    throw new Error(`Failed to process import file: ${error.message}`);
  }
}

// Enhanced helper function to create product with variants using direct database operations
async function createProductWithVariantsEnhanced(
  row: ProductImportRow,
  tenantId: string,
  _scope: any
): Promise<{
  product: any;
  variantCount: number;
  categoryResult?: {
    categoryId?: string;
    subcategoryId?: string;
    createdCategories: string[];
    stats: {
      existing_categories_used: number;
      new_categories_created: number;
      subcategories_created: number;
    };
  };
}> {
  try {
    // Use DirectDatabaseService for tenant-aware database operations
    const dbService = new DirectDatabaseService(tenantId);

    // Generate product ID and handle
    const productId = `prod_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const handle =
      row.handle ||
      (row.title && typeof row.title === 'string'
        ? row.title
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-+|-+$/g, '')
        : `product-${Date.now()}`);

    // Create product in database using correct Medusa table name
    const productQuery = `
      INSERT INTO product (
        id, tenant_id, title, handle, description, status,
        metadata, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
      RETURNING *
    `;

    // Enhanced product metadata structure as per requirements
    const productMetadata = {
      tenant_id: tenantId,
      additional_data: {
        product_prices:
          row.variants && row.variants.length > 0
            ? [
                {
                  sale_price: Number(row.variants[0].sale_price) || 0,
                  original_price: Number(row.variants[0].original_price) || 0,
                },
              ]
            : [
                {
                  sale_price: Number(row.sale_price) || 0,
                  original_price: Number(row.original_price) || 0,
                },
              ],
        product_features: row.product_features || '',
        product_overview: row.product_overview || '',
        product_quantity: Number(row.product_quantity) || 0,
        product_specifications: row.product_specification || '',
        product_inventory_status: row.product_inventory_status || 'in_stock',
      },
      hsn_code: row.ondc_hsn_code,
    };

    const productValues = [
      productId,
      tenantId,
      row.title,
      handle,
      row.description || '',
      row.status || 'published',
      JSON.stringify(productMetadata),
    ];

    const productResult = await dbService.query(productQuery, productValues);
    const product = productResult.rows[0];

    console.log(`[PRODUCT IMPORT] Created product: ${product.id}`);

    // Create variants using correct Supabase table name and schema
    const variants = row.variants && row.variants.length > 0 ? row.variants : [row];
    let variantCount = 0;

    for (const variant of variants) {
      const variantId = `variant_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      const variantQuery = `
        INSERT INTO product_variant (
          id, product_id, title, sku, tenant_id, metadata,
          weight, width, length, height, manage_inventory,
          created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW(), NOW())
        RETURNING *
      `;

      // Enhanced variant metadata structure as per requirements
      // Handle multi-currency prices from CSV
      const priceINR =
        variant['variant price inr'] || variant.variant_price || variant.sale_price || 0;
      const priceEUR = variant['variant price eur'] || 0;
      const priceUSD = variant['variant price usd'] || 0;

      const variantMetadata = {
        tenant_id: tenantId,
        sale_price: Number(priceINR) || 0,
        original_price:
          Number(variant.original_price) || Number(variant.variant_compare_at_price) || 0,
        product_quantity:
          Number(variant.variant_inventory_quantity) || Number(variant.stock_quantity) || 0,
        product_inventory_status:
          variant.inventory_status || variant.product_inventory_status || 'in_stock',
        // Store multi-currency prices
        prices: {
          inr: Number(priceINR) || 0,
          eur: Number(priceEUR) || 0,
          usd: Number(priceUSD) || 0,
        },
      };

      const variantValues = [
        variantId,
        productId,
        variant.variant_title || 'Default Variant',
        variant.variant_sku || `sku_${productId}_${Date.now()}`,
        tenantId,
        JSON.stringify(variantMetadata),
        convertDimensionToInteger(variant.variant_weight),
        convertDimensionToInteger(variant.variant_width),
        convertDimensionToInteger(variant.variant_length),
        convertDimensionToInteger(variant.variant_height),
        false, // manage_inventory: false as per requirements
      ];

      await dbService.query(variantQuery, variantValues);
      console.log(`[PRODUCT IMPORT] Created variant: ${variantId}`);
      variantCount++;
    }

    // Handle category and subcategory associations if provided
    let categoryResult;
    if (row.category || row.subcategory) {
      try {
        categoryResult = await handleCategoryAssociation(row, productId, tenantId, dbService);
      } catch (categoryError) {
        console.warn(`[PRODUCT IMPORT] Category association failed: ${categoryError.message}`);
        // Continue without failing the entire import
      }
    }

    return {
      product,
      variantCount,
      categoryResult,
    };
  } catch (error) {
    console.error('[PRODUCT IMPORT] Error creating product with variants:', error);
    throw error;
  }
}

// Helper function to validate product row data
function validateProductRow(row: ProductImportRow, rowNumber: number): ImportValidationError[] {
  const errors: ImportValidationError[] = [];

  // Validate required fields
  if (!row.title || row.title.trim() === '') {
    errors.push({
      row: rowNumber,
      field: 'title',
      message: 'Product title is required',
      value: row.title,
    });
  }

  // Validate numeric fields
  if (
    row.variant_price !== undefined &&
    (isNaN(Number(row.variant_price)) || Number(row.variant_price) < 0)
  ) {
    errors.push({
      row: rowNumber,
      field: 'variant_price',
      message: 'Variant price must be a valid positive number',
      value: row.variant_price,
    });
  }

  if (
    row.variant_weight !== undefined &&
    (isNaN(Number(row.variant_weight)) || Number(row.variant_weight) < 0)
  ) {
    errors.push({
      row: rowNumber,
      field: 'variant_weight',
      message: 'Variant weight must be a valid positive number',
      value: row.variant_weight,
    });
  }

  return errors;
}

// Enhanced helper function to handle category and subcategory associations with hierarchical support
async function handleCategoryAssociation(
  row: ProductImportRow,
  productId: string,
  tenantId: string,
  dbService: DirectDatabaseService
): Promise<{
  categoryId?: string;
  subcategoryId?: string;
  createdCategories: string[];
  stats: {
    existing_categories_used: number;
    new_categories_created: number;
    subcategories_created: number;
  };
}> {
  const result = {
    categoryId: undefined as string | undefined,
    subcategoryId: undefined as string | undefined,
    createdCategories: [] as string[],
    stats: {
      existing_categories_used: 0,
      new_categories_created: 0,
      subcategories_created: 0,
    },
  };

  try {
    // Handle main category first
    if (row.category) {
      console.log(`[PRODUCT IMPORT] Processing category: ${row.category} for tenant: ${tenantId}`);

      const categoryResult = await findOrCreateCategory(
        row.category,
        null, // No parent for main category
        tenantId,
        dbService
      );

      result.categoryId = categoryResult.categoryId;
      if (categoryResult.wasCreated) {
        result.createdCategories.push(categoryResult.categoryId);
        result.stats.new_categories_created++;
      } else {
        result.stats.existing_categories_used++;
      }

      // Associate product with main category
      await associateProductWithCategory(productId, categoryResult.categoryId, tenantId, dbService);
    }

    // Handle subcategory if provided
    if (row.subcategory) {
      console.log(
        `[PRODUCT IMPORT] Processing subcategory: ${row.subcategory} for tenant: ${tenantId}`
      );

      // Determine parent category ID
      const parentCategoryId =
        result.categoryId ||
        (row.parent_category
          ? (await findOrCreateCategory(row.parent_category, null, tenantId, dbService)).categoryId
          : null);

      const subcategoryResult = await findOrCreateCategory(
        row.subcategory,
        parentCategoryId,
        tenantId,
        dbService
      );

      result.subcategoryId = subcategoryResult.categoryId;
      if (subcategoryResult.wasCreated) {
        result.createdCategories.push(subcategoryResult.categoryId);
        result.stats.subcategories_created++;
      } else {
        result.stats.existing_categories_used++;
      }

      // Associate product with subcategory (in addition to main category)
      await associateProductWithCategory(
        productId,
        subcategoryResult.categoryId,
        tenantId,
        dbService
      );
    }

    console.log(`[PRODUCT IMPORT] Category association completed:`, result.stats);
    return result;
  } catch (error) {
    console.error(`[PRODUCT IMPORT] Error handling category association: ${error.message}`);
    throw error;
  }
}

// Helper function to find or create a category
async function findOrCreateCategory(
  categoryName: string,
  parentCategoryId: string | null,
  tenantId: string,
  dbService: DirectDatabaseService
): Promise<{ categoryId: string; wasCreated: boolean }> {
  // Check if category exists for this tenant
  const existingCategoryQuery = `
    SELECT id, name FROM product_category
    WHERE name = $1 AND tenant_id = $2 AND parent_category_id ${parentCategoryId ? '= $3' : 'IS NULL'}
    LIMIT 1
  `;

  const queryParams = parentCategoryId
    ? [categoryName, tenantId, parentCategoryId]
    : [categoryName, tenantId];

  const existingCategoryResult = await dbService.query(existingCategoryQuery, queryParams);

  if (existingCategoryResult.rows.length > 0) {
    // Category exists, use it
    const categoryId = existingCategoryResult.rows[0].id;
    console.log(`[PRODUCT IMPORT] Found existing category: ${categoryName} (${categoryId})`);
    return { categoryId, wasCreated: false };
  } else {
    // Category doesn't exist, create it
    const categoryId = `pcat_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    const handle = categoryName
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');

    // Build mpath for hierarchical structure
    let mpath = categoryId;
    if (parentCategoryId) {
      // Get parent's mpath and append current category ID
      const parentQuery = `SELECT mpath FROM product_category WHERE id = $1 AND tenant_id = $2`;
      const parentResult = await dbService.query(parentQuery, [parentCategoryId, tenantId]);
      if (parentResult.rows.length > 0) {
        mpath = `${parentResult.rows[0].mpath}.${categoryId}`;
      }
    }

    const createCategoryQuery = `
      INSERT INTO product_category (
        id, tenant_id, name, handle, description, is_active,
        rank, parent_category_id, mpath, metadata, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW())
      RETURNING id, name
    `;

    const categoryValues = [
      categoryId,
      tenantId,
      categoryName,
      handle,
      `Auto-created ${parentCategoryId ? 'subcategory' : 'category'} for ${categoryName}`,
      true,
      0,
      parentCategoryId,
      mpath,
      JSON.stringify({
        created_by: 'product_import',
        auto_created: true,
        tenant_id: tenantId,
        is_subcategory: !!parentCategoryId,
      }),
    ];

    await dbService.query(createCategoryQuery, categoryValues);
    console.log(
      `[PRODUCT IMPORT] Created new ${parentCategoryId ? 'subcategory' : 'category'}: ${categoryName} (${categoryId})`
    );
    return { categoryId, wasCreated: true };
  }
}

// Helper function to associate product with category
async function associateProductWithCategory(
  productId: string,
  categoryId: string,
  tenantId: string,
  dbService: DirectDatabaseService
): Promise<void> {
  const associationQuery = `
    INSERT INTO product_category_product (product_id, product_category_id, tenant_id, created_at, updated_at)
    VALUES ($1, $2, $3, NOW(), NOW())
    ON CONFLICT (product_id, product_category_id) DO NOTHING
  `;

  await dbService.query(associationQuery, [productId, categoryId, tenantId]);
  console.log(`[PRODUCT IMPORT] Associated product ${productId} with category ${categoryId}`);
}

// Helper function to safely convert price values to integers (cents/paise)
function convertPriceToInteger(value: any): number {
  if (value === undefined || value === null || value === '') {
    return 0;
  }

  const numValue = Number(value);
  if (isNaN(numValue)) {
    return 0;
  }

  // Convert to integer (assuming prices are in major currency units, convert to minor units)
  // For example: 10.50 USD -> 1050 cents, 599 INR -> 59900 paise
  return Math.round(numValue * 100);
}

// Helper function to safely convert dimension values to integers (millimeters)
function convertDimensionToInteger(value: any): number | null {
  if (value === undefined || value === null || value === '') {
    return null;
  }

  const numValue = Number(value);
  if (isNaN(numValue)) {
    return null;
  }

  // Convert to integer (assuming dimensions are in major units, convert to minor units)
  // For example: 1.2 cm -> 12 mm
  return Math.round(numValue * 10);
}

// Helper function to map CSV field names to our expected field names
function mapCsvFieldsToExpected(row: any): ProductImportRow {
  const fieldMapping: { [key: string]: string } = {
    // Product fields
    'Product Id': 'id',
    'Product Handle': 'handle',
    'Product Title': 'title',
    'Product Subtitle': 'subtitle',
    'Product Description': 'description',
    'Product Status': 'status',
    'Product Thumbnail': 'thumbnail',
    'Product Weight': 'weight',
    'Product Length': 'length',
    'Product Width': 'width',
    'Product Height': 'height',
    'Product HS Code': 'hs_code',
    'Product Origin Country': 'origin_country',
    'Product MID Code': 'mid_code',
    'Product Material': 'material',
    'Product Collection Id': 'collection_id',
    'Product Type Id': 'type_id',
    'Product Tag 1': 'tag_1',
    'Product Discountable': 'discountable',
    'Product External Id': 'external_id',

    // Variant fields
    'Variant Id': 'variant_id',
    'Variant Title': 'variant_title',
    'Variant SKU': 'variant_sku',
    'Variant Barcode': 'variant_barcode',
    'Variant Allow Backorder': 'variant_allow_backorder',
    'Variant Manage Inventory': 'variant_manage_inventory',
    'Variant Weight': 'variant_weight',
    'Variant Length': 'variant_length',
    'Variant Width': 'variant_width',
    'Variant Height': 'variant_height',
    'Variant HS Code': 'variant_hs_code',
    'Variant Origin Country': 'variant_origin_country',
    'Variant MID Code': 'variant_mid_code',
    'Variant Material': 'variant_material',
    'Variant Price INR': 'variant price inr',
    'Variant Price EUR': 'variant price eur',
    'Variant Price USD': 'variant price usd',
    'Variant Option 1 Name': 'variant_option_1_name',
    'Variant Option 1 Value': 'variant_option_1_value',

    // Image fields
    'Product Image 1 Url': 'image_1_url',
    'Product Image 2 Url': 'image_2_url',
  };

  const mappedRow: any = {};

  // Map known fields
  for (const [csvField, expectedField] of Object.entries(fieldMapping)) {
    if (row[csvField] !== undefined) {
      mappedRow[expectedField] = row[csvField];
    }
  }

  // Copy any unmapped fields as-is (for backward compatibility)
  for (const [key, value] of Object.entries(row)) {
    if (!fieldMapping[key] && !mappedRow[key.toLowerCase()]) {
      mappedRow[key.toLowerCase()] = value;
    }
  }

  return mappedRow as ProductImportRow;
}

// Removed unused buildProductData function - using direct database operations instead

// Removed unused buildVariantData function - using direct database operations instead

// Removed unused handleCategoryIntegration function - using direct database operations instead

async function mergeProductVariants(products: ProductImportRow[]): Promise<ProductImportRow[]> {
  // If single product, just return it as-is with variants array
  if (products.length === 1) {
    const product = products[0];
    return [
      {
        ...product,
        variants: [product], // Single variant
      },
    ];
  }

  if (!Array.isArray(products)) {
    throw new TypeError('Input must be an array of product objects');
  }

  // Define which fields belong in each category
  const variantFields = new Set([
    // Standard variant fields
    'variant_title',
    'variant_sku',
    'variant_price',
    'variant_compare_at_price',
    'original_price',
    'sale_price',
    'variant_weight',
    'variant_length',
    'variant_width',
    'variant_height',
    'variant_inventory_quantity',
    'variant_allow_backorder',
    'variant_manage_inventory',
    'inventory_status',
    'stock_quantity',

    // Multi-currency price fields (variant-specific)
    'variant price inr',
    'variant price eur',
    'variant price usd',

    // Physical properties that can vary by variant
    'weight',
    'length',
    'width',
    'height',

    // Variant-specific identifiers and options
    'variant_id',
    'variant_barcode',
    'variant_hs_code',
    'variant_origin_country',
    'variant_mid_code',
    'variant_material',
    'variant_option_1_name',
    'variant_option_1_value',
    'variant_option_2_name',
    'variant_option_2_value',
    'variant_option_3_name',
    'variant_option_3_value',
  ]);

  // Collect all field names present in the first object
  const allFields = Object.keys(products[0] || {});
  const productFields = allFields.filter(f => !variantFields.has(f));

  // Group products by composite key `${title}|||${handle}`
  const groups = products.reduce((acc: Record<string, ProductImportRow[]>, item, idx) => {
    if (typeof item.title !== 'string' || typeof item.handle !== 'string') {
      throw new Error(`Missing title or handle in item at index ${idx}`);
    }
    const key = `${item.title}|||${item.handle}`;
    acc[key] = acc[key] || [];
    acc[key].push(item);
    return acc;
  }, {});

  // Build merged result
  return Object.entries(groups).map(([_key, items]) => {
    // Validate non-variant fields
    const reference = items[0];
    for (let i = 1; i < items.length; i++) {
      for (const field of productFields) {
        if ((items[i] as any)[field] !== (reference as any)[field]) {
          throw new Error(
            `Field "${field}" mismatch for product "${reference.title}" (handle "${reference.handle}"): ` +
              `value1="${(reference as any)[field]}", value2="${(items[i] as any)[field]}"`
          );
        }
      }
    }

    // Build merged object
    const merged: ProductImportRow = { ...reference };

    // Extract variants from all items
    merged.variants = items.map(item => ({
      ...item,
      // Keep variant-specific fields
      variant_title: item.variant_title,
      variant_sku: item.variant_sku,
      variant_price: item.variant_price,
      variant_compare_at_price: item.variant_compare_at_price,
      original_price: item.original_price,
      sale_price: item.sale_price,
      variant_weight: item.variant_weight,
      variant_length: item.variant_length,
      variant_width: item.variant_width,
      variant_height: item.variant_height,
      variant_inventory_quantity: item.variant_inventory_quantity,
      variant_allow_backorder: item.variant_allow_backorder,
      variant_manage_inventory: item.variant_manage_inventory,
      inventory_status: item.inventory_status,
      stock_quantity: item.stock_quantity,
    }));

    return merged;
  });
}
