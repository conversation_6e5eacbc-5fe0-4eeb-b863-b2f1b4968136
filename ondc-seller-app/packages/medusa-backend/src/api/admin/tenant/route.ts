/**
 * Task 2.4: Create Tenant Configuration API
 * 
 * Enhanced /admin/tenant endpoint with tenant validation and configuration management
 * Provides comprehensive tenant management capabilities including status checking,
 * configuration updates, and ONDC-specific settings.
 */

import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { TenantConfig } from "../../../middleware/tenant";

/**
 * Tenant Status Types
 */
type TenantStatus = 'active' | 'inactive' | 'suspended' | 'pending';

/**
 * Tenant Configuration Update Request
 */
interface TenantUpdateRequest {
  name?: string;
  domain?: string;
  settings?: {
    currency?: string;
    timezone?: string;
    features?: string[];
    ondcConfig?: {
      participantId?: string;
      subscriberId?: string;
      bppId?: string;
    };
  };
  status?: TenantStatus;
}

/**
 * Enhanced Tenant Validation Response
 */
interface TenantValidationResponse {
  tenant_id: string;
  is_valid: boolean;
  status: TenantStatus;
  validation_details: {
    exists: boolean;
    active: boolean;
    configuration_complete: boolean;
    ondc_configured: boolean;
    last_validated: string;
  };
  issues?: string[];
}

/**
 * GET /admin/tenant
 * Retrieve current tenant configuration and validation status
 */
export const GET = async (
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> => {
  try {
    console.log('🏢 [TENANT-API] GET /admin/tenant called');

    // Get tenant information from middleware
    const tenantId = (req as any).tenantId;
    const tenant = (req as any).tenant as TenantConfig;

    if (!tenantId || !tenant) {
      res.status(400).json({
        error: 'TENANT_CONTEXT_MISSING',
        message: 'Tenant context not found. Ensure x-tenant-id header is provided.',
      });
      return;
    }

    // Perform comprehensive tenant validation
    const validation = await validateTenantConfiguration(tenant);

    // Get additional tenant metrics
    const metrics = await getTenantMetrics(tenantId);

    // Prepare comprehensive response
    const response = {
      success: true,
      tenant: {
        id: tenant.id,
        name: tenant.name,
        domain: tenant.domain,
        status: tenant.status,
        settings: tenant.settings,
        created_at: tenant.createdAt,
        updated_at: tenant.updatedAt,
      },
      validation,
      metrics,
      api_info: {
        endpoint: '/admin/tenant',
        method: 'GET',
        timestamp: new Date().toISOString(),
        tenant_context: tenantId,
      }
    };

    console.log(`✅ [TENANT-API] Tenant configuration retrieved for: ${tenantId}`);
    res.json(response);

  } catch (error) {
    console.error('❌ [TENANT-API] Error retrieving tenant configuration:', error);
    res.status(500).json({
      error: 'TENANT_CONFIG_ERROR',
      message: 'Failed to retrieve tenant configuration',
      details: error.message,
    });
  }
};

/**
 * POST /admin/tenant
 * Update tenant configuration and settings
 */
export const POST = async (
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> => {
  try {
    console.log('🏢 [TENANT-API] POST /admin/tenant called');

    // Get tenant information from middleware
    const tenantId = (req as any).tenantId;
    const currentTenant = (req as any).tenant as TenantConfig;

    if (!tenantId || !currentTenant) {
      res.status(400).json({
        error: 'TENANT_CONTEXT_MISSING',
        message: 'Tenant context not found. Ensure x-tenant-id header is provided.',
      });
      return;
    }

    // Parse and validate update request
    const updateRequest: TenantUpdateRequest = req.body;
    
    if (!updateRequest || Object.keys(updateRequest).length === 0) {
      res.status(400).json({
        error: 'INVALID_UPDATE_REQUEST',
        message: 'Update request body is required and cannot be empty.',
        allowed_fields: ['name', 'domain', 'settings', 'status'],
      });
      return;
    }

    // Validate update request
    const validationErrors = validateUpdateRequest(updateRequest);
    if (validationErrors.length > 0) {
      res.status(400).json({
        error: 'VALIDATION_FAILED',
        message: 'Tenant update validation failed',
        errors: validationErrors,
      });
      return;
    }

    // Apply updates to tenant configuration
    const updatedTenant = await updateTenantConfiguration(currentTenant, updateRequest);

    // Validate updated configuration
    const validation = await validateTenantConfiguration(updatedTenant);

    // Prepare response
    const response = {
      success: true,
      message: 'Tenant configuration updated successfully',
      tenant: {
        id: updatedTenant.id,
        name: updatedTenant.name,
        domain: updatedTenant.domain,
        status: updatedTenant.status,
        settings: updatedTenant.settings,
        updated_at: updatedTenant.updatedAt,
      },
      validation,
      changes_applied: Object.keys(updateRequest),
      api_info: {
        endpoint: '/admin/tenant',
        method: 'POST',
        timestamp: new Date().toISOString(),
        tenant_context: tenantId,
      }
    };

    console.log(`✅ [TENANT-API] Tenant configuration updated for: ${tenantId}`);
    res.json(response);

  } catch (error) {
    console.error('❌ [TENANT-API] Error updating tenant configuration:', error);
    res.status(500).json({
      error: 'TENANT_UPDATE_ERROR',
      message: 'Failed to update tenant configuration',
      details: error.message,
    });
  }
};

/**
 * Validate tenant configuration comprehensively
 */
async function validateTenantConfiguration(tenant: TenantConfig): Promise<TenantValidationResponse> {
  const issues: string[] = [];
  
  // Check basic configuration
  if (!tenant.name || tenant.name.trim() === '') {
    issues.push('Tenant name is missing or empty');
  }
  
  if (!tenant.domain) {
    issues.push('Tenant domain is not configured');
  }
  
  // Check ONDC configuration
  const ondcConfig = tenant.settings.ondcConfig;
  const ondcConfigured = ondcConfig && 
    ondcConfig.participantId && 
    ondcConfig.subscriberId && 
    ondcConfig.bppId;
  
  if (!ondcConfigured) {
    issues.push('ONDC configuration is incomplete (missing participantId, subscriberId, or bppId)');
  }
  
  // Check required features
  const requiredFeatures = ['products', 'orders', 'customers'];
  const missingFeatures = requiredFeatures.filter(
    feature => !tenant.settings.features.includes(feature)
  );
  
  if (missingFeatures.length > 0) {
    issues.push(`Missing required features: ${missingFeatures.join(', ')}`);
  }

  return {
    tenant_id: tenant.id,
    is_valid: issues.length === 0 && tenant.status === 'active',
    status: tenant.status,
    validation_details: {
      exists: true,
      active: tenant.status === 'active',
      configuration_complete: issues.length === 0,
      ondc_configured: !!ondcConfigured,
      last_validated: new Date().toISOString(),
    },
    issues: issues.length > 0 ? issues : undefined,
  };
}

/**
 * Get tenant metrics and statistics
 */
async function getTenantMetrics(tenantId: string) {
  // In a real implementation, this would query the database for tenant-specific metrics
  return {
    total_products: 0, // Would be calculated from database
    total_customers: 0, // Would be calculated from database
    total_orders: 0, // Would be calculated from database
    active_sessions: 1, // Current request
    last_activity: new Date().toISOString(),
    storage_used: '0 MB', // Would be calculated
    api_calls_today: 1, // Would be tracked
  };
}

/**
 * Validate update request
 */
function validateUpdateRequest(request: TenantUpdateRequest): string[] {
  const errors: string[] = [];
  
  if (request.name !== undefined) {
    if (typeof request.name !== 'string' || request.name.trim().length === 0) {
      errors.push('Name must be a non-empty string');
    }
    if (request.name.length > 100) {
      errors.push('Name must be less than 100 characters');
    }
  }
  
  if (request.domain !== undefined) {
    if (typeof request.domain !== 'string') {
      errors.push('Domain must be a string');
    }
    // Basic domain validation
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\..*$/;
    if (request.domain && !domainRegex.test(request.domain)) {
      errors.push('Domain must be a valid domain format');
    }
  }
  
  if (request.status !== undefined) {
    const validStatuses: TenantStatus[] = ['active', 'inactive', 'suspended', 'pending'];
    if (!validStatuses.includes(request.status)) {
      errors.push(`Status must be one of: ${validStatuses.join(', ')}`);
    }
  }
  
  return errors;
}

/**
 * Update tenant configuration
 */
async function updateTenantConfiguration(
  currentTenant: TenantConfig, 
  updates: TenantUpdateRequest
): Promise<TenantConfig> {
  // Create updated tenant configuration
  const updatedTenant: TenantConfig = {
    ...currentTenant,
    updatedAt: new Date(),
  };
  
  // Apply updates
  if (updates.name !== undefined) {
    updatedTenant.name = updates.name;
  }
  
  if (updates.domain !== undefined) {
    updatedTenant.domain = updates.domain;
  }
  
  if (updates.status !== undefined) {
    updatedTenant.status = updates.status;
  }
  
  if (updates.settings) {
    updatedTenant.settings = {
      ...updatedTenant.settings,
      ...updates.settings,
    };
    
    // Handle nested ONDC config updates
    if (updates.settings.ondcConfig) {
      updatedTenant.settings.ondcConfig = {
        ...updatedTenant.settings.ondcConfig,
        ...updates.settings.ondcConfig,
      };
    }
  }
  
  // In a real implementation, this would persist to database
  console.log(`📝 [TENANT-API] Configuration updated for tenant: ${updatedTenant.id}`);
  
  return updatedTenant;
}
