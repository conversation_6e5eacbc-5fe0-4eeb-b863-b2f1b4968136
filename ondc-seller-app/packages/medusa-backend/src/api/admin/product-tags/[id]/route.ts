import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
) {
  console.log(`🚀 [TENANT FILTER] === CUSTOM TAG GET BY ID ENDPOINT CALLED ===`)

  try {
    // Extract tenant ID from header
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    // Get tag ID from URL params
    const tagId = req.params?.id as string
    
    if (!tagId) {
      return res.status(400).json({
        error: 'Tag ID is required',
        tenant_id: tenantId
      })
    }

    console.log(`🔍 [TENANT FILTER] Getting tag ${tagId} for tenant: ${tenantId}`)

    // Direct database connection approach
    const { Client } = require('pg')
    const client = new Client({
      connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
    })

    let tag: any = null

    try {
      await client.connect()
      console.log(`🔗 [TENANT FILTER] Connected to database directly`)

      // Get tag with tenant validation
      const result = await client.query(`
        SELECT 
          id, value, metadata, created_at, updated_at, 
          deleted_at, tenant_id
        FROM product_tag 
        WHERE id = $1 AND tenant_id = $2
      `, [tagId, tenantId])

      tag = result.rows[0] || null
      console.log(`📦 [TENANT FILTER] Retrieved tag: ${tag ? 'Found' : 'Not Found'}`)

      if (tag) {
        // Parse metadata if it's a string
        if (typeof tag.metadata === 'string') {
          try {
            tag.metadata = JSON.parse(tag.metadata)
          } catch (e) {
            console.log(`⚠️ Could not parse metadata for tag ${tag.id}`)
          }
        }
      }

      await client.end()

    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error:', dbError)
      await client.end().catch(() => {})
      throw dbError
    }

    if (!tag) {
      return res.status(404).json({
        error: 'Tag not found or access denied',
        tag_id: tagId,
        tenant_id: tenantId,
        _debug: {
          message: 'Tag either does not exist or belongs to a different tenant'
        }
      })
    }

    // Return response in Medusa format
    const response = {
      product_tag: tag,
      _tenant: {
        id: tenantId,
        filtered: true,
        method: 'direct_db_connection'
      }
    }

    // Add tenant headers
    res.setHeader('X-Tenant-ID', tenantId)
    res.setHeader('X-Tenant-Filtered', 'true')

    console.log(`📤 [TENANT FILTER] Returning tag ${tagId} for tenant ${tenantId}`)
    
    res.json(response)

  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error getting tag:', error)
    
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    res.status(500).json({
      error: 'Failed to get tag',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'tenant_tag_get_error',
        timestamp: new Date().toISOString()
      }
    })
  }
}

export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
) {
  console.log(`🚀 [TENANT FILTER] === CUSTOM TAG UPDATE ENDPOINT CALLED ===`)
  console.log(`🚀 [TENANT FILTER] Body:`, JSON.stringify(req.body, null, 2))

  try {
    // Extract tenant ID from header
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    // Get tag ID from URL params
    const tagId = req.params?.id as string
    
    if (!tagId) {
      return res.status(400).json({
        error: 'Tag ID is required for update',
        tenant_id: tenantId
      })
    }

    console.log(`🔄 [TENANT FILTER] Updating tag ${tagId} for tenant: ${tenantId}`)

    // Get update data from request body
    const updateData = req.body as any

    // Remove tenant_id from update data to prevent modification
    const { tenant_id: _, ...safeUpdateData } = updateData

    // Direct database connection approach
    const { Client } = require('pg')
    const client = new Client({
      connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
    })

    let updatedTag: any = null

    try {
      await client.connect()
      console.log(`🔗 [TENANT FILTER] Connected to database directly`)

      // First, verify the tag belongs to this tenant
      const checkQuery = 'SELECT id FROM product_tag WHERE id = $1 AND tenant_id = $2'
      const checkResult = await client.query(checkQuery, [tagId, tenantId])

      if (checkResult.rows.length === 0) {
        await client.end()
        return res.status(404).json({
          error: 'Tag not found or access denied',
          tag_id: tagId,
          tenant_id: tenantId,
          _debug: {
            message: 'Tag either does not exist or belongs to a different tenant'
          }
        })
      }

      // Check if new value already exists for this tenant (if value is being updated)
      if (safeUpdateData.value) {
        const existingResult = await client.query(
          'SELECT id FROM product_tag WHERE value = $1 AND tenant_id = $2 AND id != $3',
          [safeUpdateData.value, tenantId, tagId]
        )
        
        if (existingResult.rows.length > 0) {
          await client.end()
          return res.status(400).json({
            type: 'invalid_data',
            message: `Tag with value: ${safeUpdateData.value}, already exists for tenant: ${tenantId}.`,
            tenant_id: tenantId
          })
        }
      }

      // Update the tag (tenant_id cannot be changed)
      const updateQuery = `
        UPDATE product_tag 
        SET 
          value = COALESCE($1, value),
          metadata = COALESCE($2, metadata),
          updated_at = NOW()
        WHERE id = $3 AND tenant_id = $4
        RETURNING *
      `

      const values = [
        safeUpdateData.value,
        safeUpdateData.metadata ? JSON.stringify({
          ...safeUpdateData.metadata,
          tenant_id: tenantId
        }) : null,
        tagId,
        tenantId
      ]

      const result = await client.query(updateQuery, values)
      updatedTag = result.rows[0]

      // Parse metadata
      if (typeof updatedTag.metadata === 'string') {
        try {
          updatedTag.metadata = JSON.parse(updatedTag.metadata)
        } catch (e) {
          console.log(`⚠️ Could not parse metadata for updated tag ${updatedTag.id}`)
        }
      }

      console.log(`✅ [TENANT FILTER] Updated tag ${tagId} for tenant: ${tenantId}`)

      await client.end()

    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error:', dbError)
      await client.end().catch(() => {})
      throw dbError
    }

    // Return response in Medusa format
    const response = {
      product_tag: updatedTag,
      _tenant: {
        id: tenantId,
        validated: true,
        method: 'direct_db_connection'
      }
    }

    // Add tenant headers
    res.setHeader('X-Tenant-ID', tenantId)
    res.setHeader('X-Tenant-Validated', 'true')

    console.log(`📤 [TENANT FILTER] Returning updated tag for tenant ${tenantId}`)
    
    res.json(response)

  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error updating tag:', error)
    
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    res.status(500).json({
      error: 'Failed to update tag',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'tenant_tag_update_error',
        timestamp: new Date().toISOString()
      }
    })
  }
}

export async function DELETE(
  req: MedusaRequest,
  res: MedusaResponse
) {
  console.log(`🚀 [TENANT FILTER] === CUSTOM TAG DELETE ENDPOINT CALLED ===`)

  try {
    // Extract tenant ID from header
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    // Get tag ID from URL params
    const tagId = req.params?.id as string
    
    if (!tagId) {
      return res.status(400).json({
        error: 'Tag ID is required for deletion',
        tenant_id: tenantId
      })
    }

    console.log(`🗑️ [TENANT FILTER] Deleting tag ${tagId} for tenant: ${tenantId}`)

    // Direct database connection approach
    const { Client } = require('pg')
    const client = new Client({
      connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
    })

    let deletedTag: any = null

    try {
      await client.connect()
      console.log(`🔗 [TENANT FILTER] Connected to database directly`)

      // First, verify the tag belongs to this tenant and get it
      const checkQuery = 'SELECT * FROM product_tag WHERE id = $1 AND tenant_id = $2'
      const checkResult = await client.query(checkQuery, [tagId, tenantId])

      if (checkResult.rows.length === 0) {
        await client.end()
        return res.status(404).json({
          error: 'Tag not found or access denied',
          tag_id: tagId,
          tenant_id: tenantId,
          _debug: {
            message: 'Tag either does not exist or belongs to a different tenant'
          }
        })
      }

      // Hard delete the tag (tags don't typically use soft delete)
      const deleteQuery = 'DELETE FROM product_tag WHERE id = $1 AND tenant_id = $2 RETURNING *'
      const result = await client.query(deleteQuery, [tagId, tenantId])
      deletedTag = result.rows[0]

      console.log(`✅ [TENANT FILTER] Deleted tag ${tagId} for tenant: ${tenantId}`)

      await client.end()

    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error:', dbError)
      await client.end().catch(() => {})
      throw dbError
    }

    // Return response in Medusa format
    const response = {
      id: tagId,
      object: 'product_tag',
      deleted: true,
      _tenant: {
        id: tenantId,
        validated: true,
        method: 'direct_db_connection'
      }
    }

    // Add tenant headers
    res.setHeader('X-Tenant-ID', tenantId)
    res.setHeader('X-Tenant-Validated', 'true')

    console.log(`📤 [TENANT FILTER] Confirmed deletion of tag ${tagId} for tenant ${tenantId}`)
    
    res.json(response)

  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error deleting tag:', error)
    
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    res.status(500).json({
      error: 'Failed to delete tag',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'tenant_tag_delete_error',
        timestamp: new Date().toISOString()
      }
    })
  }
}