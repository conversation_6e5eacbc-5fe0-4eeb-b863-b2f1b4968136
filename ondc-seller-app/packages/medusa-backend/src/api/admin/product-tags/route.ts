import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
) {
  console.log(`🚀 [TENANT FILTER] === CUSTOM PRODUCT TAGS ENDPOINT CALLED ===`)
  console.log(`🚀 [TENANT FILTER] Headers:`, JSON.stringify(req.headers, null, 2))
  console.log(`🚀 [TENANT FILTER] Query:`, JSON.stringify(req.query, null, 2))

  try {
    // Extract tenant ID from header
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    console.log(`🔍 [TENANT FILTER] Processing tags request for tenant: ${tenantId}`)

    // Get query parameters
    const {
      limit = 50,
      offset = 0
    } = req.query

    // Direct database connection approach
    const { Client } = require('pg')
    const client = new Client({
      connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
    })

    let tags: any[] = []
    let count = 0

    try {
      await client.connect()
      console.log(`🔗 [TENANT FILTER] Connected to database directly`)

      // Get total count for this tenant
      const countResult = await client.query(
        'SELECT COUNT(*) as total FROM product_tag WHERE tenant_id = $1',
        [tenantId]
      )
      count = parseInt(countResult.rows[0]?.total || 0)
      console.log(`📊 [TENANT FILTER] Total tags for tenant ${tenantId}: ${count}`)

      // Get tags for this tenant
      const result = await client.query(`
        SELECT 
          id, value, metadata, created_at, updated_at, 
          deleted_at, tenant_id
        FROM product_tag 
        WHERE tenant_id = $1
        ORDER BY created_at DESC 
        LIMIT $2 OFFSET $3
      `, [tenantId, parseInt(limit as string), parseInt(offset as string)])

      tags = result.rows || []

      // Parse metadata for each tag
      tags.forEach(tag => {
        if (typeof tag.metadata === 'string') {
          try {
            tag.metadata = JSON.parse(tag.metadata)
          } catch (e) {
            console.log(`⚠️ Could not parse metadata for tag ${tag.id}`)
          }
        }
      })

      console.log(`📦 [TENANT FILTER] Retrieved ${tags.length} tags`)

      await client.end()

    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error:', dbError)
      await client.end().catch(() => {})
    }

    // Return response in Medusa format
    const response = {
      product_tags: tags,
      count: tags.length,
      offset: parseInt(offset as string),
      limit: parseInt(limit as string),
      // Add tenant info for debugging
      _tenant: {
        id: tenantId,
        filtered: true,
        method: 'direct_db_connection',
        total_in_db: count
      }
    }

    // Add tenant headers for debugging
    res.setHeader('X-Tenant-ID', tenantId)
    res.setHeader('X-Tenant-Filtered', 'true')
    res.setHeader('X-Tags-Count', tags.length.toString())

    console.log(`📤 [TENANT FILTER] Returning response:`, {
      tags_count: tags.length,
      total_count: count,
      tenant_id: tenantId
    })
    
    res.json(response)

  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error getting tags:', error)
    
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    res.status(500).json({
      error: 'Failed to get tags',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'tenant_tags_get_error',
        timestamp: new Date().toISOString()
      }
    })
  }
}

export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
) {
  console.log(`🚀 [TENANT FILTER] === CUSTOM PRODUCT TAG CREATE ENDPOINT CALLED ===`)
  console.log(`🚀 [TENANT FILTER] Body:`, JSON.stringify(req.body, null, 2))

  try {
    // Extract tenant ID from header
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    console.log(`🔄 [TENANT FILTER] Creating tag for tenant: ${tenantId}`)

    // Get tag data from request body
    const tagData = req.body as any

    // Ensure tenant_id is injected and cannot be modified
    const tagWithTenant = {
      ...tagData,
      tenant_id: tenantId
    }

    // Direct database connection approach
    const { Client } = require('pg')
    const client = new Client({
      connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
    })

    let createdTag: any = null

    try {
      await client.connect()
      console.log(`🔗 [TENANT FILTER] Connected to database for tag creation`)

      // Generate tag ID
      const tagId = `ptag_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`

      // Check if value already exists for this tenant
      if (tagWithTenant.value) {
        const existingResult = await client.query(
          'SELECT id FROM product_tag WHERE value = $1 AND tenant_id = $2',
          [tagWithTenant.value, tenantId]
        )
        
        if (existingResult.rows.length > 0) {
          await client.end()
          return res.status(400).json({
            type: 'invalid_data',
            message: `Tag with value: ${tagWithTenant.value}, already exists for tenant: ${tenantId}.`,
            tenant_id: tenantId
          })
        }
      }

      // Insert tag
      const insertTagQuery = `
        INSERT INTO product_tag (
          id, value, metadata, tenant_id, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, NOW(), NOW())
        RETURNING *
      `

      const tagValues = [
        tagId,
        tagWithTenant.value,
        tagWithTenant.metadata ? JSON.stringify({
          ...tagWithTenant.metadata,
          tenant_id: tenantId
        }) : JSON.stringify({ tenant_id: tenantId }),
        tenantId
      ]

      const result = await client.query(insertTagQuery, tagValues)
      createdTag = result.rows[0]

      // Parse metadata
      if (typeof createdTag.metadata === 'string') {
        try {
          createdTag.metadata = JSON.parse(createdTag.metadata)
        } catch (e) {
          console.log(`⚠️ Could not parse metadata for created tag ${createdTag.id}`)
        }
      }

      console.log(`✅ [TENANT FILTER] Created tag ${tagId} for tenant: ${tenantId}`)

      await client.end()

    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error:', dbError)
      await client.end().catch(() => {})
      throw dbError
    }

    // Return response in Medusa format
    const response = {
      product_tag: createdTag,
      _tenant: {
        id: tenantId,
        injected: true,
        method: 'direct_db_creation'
      }
    }

    // Add tenant headers
    res.setHeader('X-Tenant-ID', tenantId)
    res.setHeader('X-Tenant-Injected', 'true')

    console.log(`📤 [TENANT FILTER] Returning created tag for tenant ${tenantId}`)
    
    res.json(response)

  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error creating tag:', error)
    
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    res.status(500).json({
      error: 'Failed to create tag',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'tenant_tag_create_error',
        timestamp: new Date().toISOString(),
        stack: error.stack
      }
    })
  }
}