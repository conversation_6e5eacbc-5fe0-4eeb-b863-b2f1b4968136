import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';

// Helper function to preprocess data and fix data types
function preprocessUpdateData(data: any): any {
  if (!data) return data;

  const processed = { ...data };

  // Fix variants data types
  if (processed.variants && Array.isArray(processed.variants)) {
    processed.variants = processed.variants.map((variant: any) => {
      const processedVariant = { ...variant };

      // Fix prices data types
      if (processedVariant.prices && Array.isArray(processedVariant.prices)) {
        processedVariant.prices = processedVariant.prices.map((price: any) => ({
          ...price,
          amount: typeof price.amount === 'string' ? parseFloat(price.amount) : price.amount,
          min_quantity:
            typeof price.min_quantity === 'string'
              ? parseInt(price.min_quantity)
              : price.min_quantity,
          max_quantity:
            typeof price.max_quantity === 'string'
              ? parseInt(price.max_quantity)
              : price.max_quantity,
        }));
      }

      // Fix numeric fields in variant
      if (processedVariant.weight && typeof processedVariant.weight === 'string') {
        processedVariant.weight = parseFloat(processedVariant.weight);
      }
      if (processedVariant.width && typeof processedVariant.width === 'string') {
        processedVariant.width = parseFloat(processedVariant.width);
      }
      if (processedVariant.length && typeof processedVariant.length === 'string') {
        processedVariant.length = parseFloat(processedVariant.length);
      }
      if (processedVariant.height && typeof processedVariant.height === 'string') {
        processedVariant.height = parseFloat(processedVariant.height);
      }

      return processedVariant;
    });
  }

  // Fix product-level numeric fields
  if (processed.weight && typeof processed.weight === 'string') {
    processed.weight = parseFloat(processed.weight);
  }
  if (processed.width && typeof processed.width === 'string') {
    processed.width = parseFloat(processed.width);
  }
  if (processed.length && typeof processed.length === 'string') {
    processed.length = parseFloat(processed.length);
  }
  if (processed.height && typeof processed.height === 'string') {
    processed.height = parseFloat(processed.height);
  }

  return processed;
}

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  console.log(`🚀 [TENANT FILTER] === CUSTOM PRODUCT GET BY ID ENDPOINT CALLED ===`);

  try {
    // Extract tenant ID from header
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    // Get product ID from URL params
    const productId = req.params?.id as string;

    if (!productId) {
      return res.status(400).json({
        error: 'Product ID is required',
        tenant_id: tenantId,
      });
    }

    console.log(`🔍 [TENANT FILTER] Getting product ${productId} for tenant: ${tenantId}`);

    // Direct database connection approach
    const { Client } = require('pg');
    const client = new Client({
      connectionString:
        process.env.DATABASE_URL ||
        process.env.POSTGRES_URL ||
        'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
    });

    let product: any = null;

    try {
      await client.connect();
      console.log(`🔗 [TENANT FILTER] Connected to database directly`);

      // Get product with tenant validation
      const result = await client.query(
        `
        SELECT 
          id, title, handle, description, status, 
          created_at, updated_at, tenant_id, metadata,
          thumbnail, weight, length, height, width,
          origin_country, hs_code, mid_code, material,
          collection_id, type_id, discountable, external_id,
          is_giftcard, subtitle, deleted_at
        FROM product 
        WHERE id = $1 AND tenant_id = $2
      `,
        [productId, tenantId]
      );

      product = result.rows[0] || null;
      console.log(`📦 [TENANT FILTER] Retrieved product: ${product ? 'Found' : 'Not Found'}`);

      if (product) {
        // Parse metadata if it's a string
        if (typeof product.metadata === 'string') {
          try {
            product.metadata = JSON.parse(product.metadata);
          } catch (e) {
            console.log(`⚠️ Could not parse metadata for product ${product.id}`);
          }
        }

        // Get variants with tenant filtering
        try {
          const variantsResult = await client.query(
            `
            SELECT id, title, sku, barcode, ean, upc, allow_backorder, 
                   manage_inventory, hs_code, origin_country, mid_code, 
                   material, weight, length, height, width, metadata,
                   variant_rank, product_id, created_at, updated_at, 
                   deleted_at, tenant_id
            FROM product_variant 
            WHERE product_id = $1 AND tenant_id = $2
          `,
            [product.id, tenantId]
          );

          product.variants = variantsResult.rows.map(variant => {
            if (typeof variant.metadata === 'string') {
              try {
                variant.metadata = JSON.parse(variant.metadata);
              } catch (e) {
                console.log(`⚠️ Could not parse variant metadata for ${variant.id}`);
              }
            }
            return variant;
          });
        } catch (e) {
          console.log(`⚠️ Could not fetch variants for product ${product.id}:`, e.message);
          product.variants = [];
        }

        // Get images (no tenant_id in image table)
        try {
          const imagesResult = await client.query(
            `
            SELECT id, url, metadata, rank, product_id, created_at, updated_at, deleted_at
            FROM image 
            WHERE product_id = $1
          `,
            [product.id]
          );
          // Filter by tenant_id from metadata
          product.images = imagesResult.rows.filter(img => {
            try {
              const metadata =
                typeof img.metadata === 'string' ? JSON.parse(img.metadata) : img.metadata;
              return metadata && metadata.tenant_id === tenantId;
            } catch {
              return false;
            }
          });
        } catch (e) {
          console.log(`⚠️ Could not fetch images for product ${product.id}:`, e.message);
          product.images = [];
        }

        // Get options (no tenant_id in product_option table)
        try {
          const optionsResult = await client.query(
            `
            SELECT id, title, metadata, product_id, created_at, updated_at, deleted_at
            FROM product_option 
            WHERE product_id = $1
          `,
            [product.id]
          );
          // Filter by tenant_id from metadata
          product.options = optionsResult.rows.filter(opt => {
            try {
              const metadata =
                typeof opt.metadata === 'string' ? JSON.parse(opt.metadata) : opt.metadata;
              return metadata && metadata.tenant_id === tenantId;
            } catch {
              return false;
            }
          });

          // Get option values for each option
          for (const option of product.options) {
            try {
              const valuesResult = await client.query(
                `
                SELECT id, value, option_id, metadata, created_at, updated_at, deleted_at
                FROM product_option_value 
                WHERE option_id = $1
              `,
                [option.id]
              );
              // Filter by tenant_id from metadata
              option.values = valuesResult.rows.filter(val => {
                try {
                  const metadata =
                    typeof val.metadata === 'string' ? JSON.parse(val.metadata) : val.metadata;
                  return metadata && metadata.tenant_id === tenantId;
                } catch {
                  return false;
                }
              });
            } catch (e) {
              console.log(`⚠️ Could not fetch option values for option ${option.id}:`, e.message);
              option.values = [];
            }
          }
        } catch (e) {
          console.log(`⚠️ Could not fetch options for product ${product.id}:`, e.message);
          product.options = [];
        }

        // Get tags
        try {
          const tagsResult = await client.query(
            `
            SELECT pt.id, pt.value, pt.metadata, pt.created_at, pt.updated_at, pt.deleted_at
            FROM product_tag pt
            JOIN product_tags ptags ON pt.id = ptags.product_tag_id
            WHERE ptags.product_id = $1 AND pt.tenant_id = $2
          `,
            [product.id, tenantId]
          );
          product.tags = tagsResult.rows || [];
        } catch (e) {
          console.log(`⚠️ Could not fetch tags for product ${product.id}:`, e.message);
          product.tags = [];
        }

        // Get categories
        try {
          const categoriesResult = await client.query(
            `
            SELECT pc.id, pc.name, pc.description, pc.handle, pc.metadata, 
                   pc.created_at, pc.updated_at, pc.deleted_at
            FROM product_category pc
            JOIN product_category_product pcp ON pc.id = pcp.product_category_id
            WHERE pcp.product_id = $1 AND pc.tenant_id = $2
          `,
            [product.id, tenantId]
          );
          product.categories = categoriesResult.rows || [];
        } catch (e) {
          console.log(`⚠️ Could not fetch categories for product ${product.id}:`, e.message);
          product.categories = [];
        }

        // Get collection if collection_id exists
        if (product.collection_id) {
          try {
            const collectionResult = await client.query(
              `
              SELECT id, title, handle, metadata, created_at, updated_at, deleted_at
              FROM product_collection 
              WHERE id = $1 AND tenant_id = $2
            `,
              [product.collection_id, tenantId]
            );
            product.collection = collectionResult.rows[0] || null;
          } catch (e) {
            console.log(`⚠️ Could not fetch collection for product ${product.id}:`, e.message);
            product.collection = null;
          }
        } else {
          product.collection = null;
        }

        // Get type if type_id exists
        if (product.type_id) {
          try {
            const typeResult = await client.query(
              `
              SELECT id, value, metadata, created_at, updated_at, deleted_at
              FROM product_type 
              WHERE id = $1 AND tenant_id = $2
            `,
              [product.type_id, tenantId]
            );
            product.type = typeResult.rows[0] || null;
          } catch (e) {
            console.log(`⚠️ Could not fetch type for product ${product.id}:`, e.message);
            product.type = null;
          }
        } else {
          product.type = null;
        }

        // Get profiles (sales channels)
        try {
          const profilesResult = await client.query(
            `
            SELECT sp.id, sp.name, sp.description, sp.is_disabled, sp.metadata,
                   sp.created_at, sp.updated_at, sp.deleted_at
            FROM sales_channel sp
            JOIN product_sales_channel psc ON sp.id = psc.sales_channel_id
            WHERE psc.product_id = $1 AND sp.tenant_id = $2
          `,
            [product.id, tenantId]
          );
          product.profiles = profilesResult.rows || [];
        } catch (e) {
          console.log(`⚠️ Could not fetch profiles for product ${product.id}:`, e.message);
          product.profiles = [];
        }

        // Get variant prices
        for (const variant of product.variants) {
          try {
            const pricesResult = await client.query(
              `
              SELECT p.id, p.currency_code, p.amount, p.raw_amount, p.min_quantity, p.max_quantity,
                     p.price_list_id, p.created_at, p.updated_at, p.deleted_at
              FROM price p
              JOIN price_set ps ON p.price_set_id = ps.id
              JOIN product_variant_price_set pvps ON ps.id = pvps.price_set_id
              WHERE pvps.variant_id = $1 AND p.tenant_id = $2
            `,
              [variant.id, tenantId]
            );
            variant.prices = pricesResult.rows || [];
          } catch (e) {
            console.log(`⚠️ Could not fetch prices for variant ${variant.id}:`, e.message);
            variant.prices = [];
          }
        }
      }
    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error:', dbError);
      throw dbError;
    } finally {
      // Always close the database connection
      try {
        await client.end();
        console.log(`🔗 [TENANT FILTER] Database connection closed`);
      } catch (closeError) {
        console.error('❌ [TENANT FILTER] Error closing database connection:', closeError);
      }
    }

    if (!product) {
      return res.status(404).json({
        error: 'Product not found or access denied',
        product_id: productId,
        tenant_id: tenantId,
        _debug: {
          message: 'Product either does not exist or belongs to a different tenant',
        },
      });
    }

    // Return response in Medusa format
    const response = {
      product,
      _tenant: {
        id: tenantId,
        filtered: true,
        method: 'direct_db_connection',
      },
    };

    // Add tenant headers
    res.setHeader('X-Tenant-ID', tenantId);
    res.setHeader('X-Tenant-Filtered', 'true');

    console.log(`📤 [TENANT FILTER] Returning product ${productId} for tenant ${tenantId}`);

    res.json(response);
  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error getting product:', error);

    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    res.status(500).json({
      error: 'Failed to get product',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'tenant_get_error',
        timestamp: new Date().toISOString(),
      },
    });
  }
}

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  // CRITICAL: Preprocess request body IMMEDIATELY to fix data types before any Medusa validation
  if (req.body) {
    req.body = preprocessUpdateData(req.body);
  }

  console.log(`🚀 [TENANT FILTER] === COMPREHENSIVE PRODUCT UPDATE ENDPOINT CALLED ===`);
  console.log(`🚀 [TENANT FILTER] Body (after preprocessing):`, JSON.stringify(req.body, null, 2));

  try {
    // Extract tenant ID from header
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    // Get product ID from URL params
    const productId = req.params?.id as string;

    if (!productId) {
      return res.status(400).json({
        error: 'Product ID is required for update',
        tenant_id: tenantId,
      });
    }

    console.log(
      `🔄 [TENANT FILTER] Comprehensive update for product ${productId}, tenant: ${tenantId}`
    );

    // Get update data from request body (already preprocessed)
    const updateData = req.body as any;

    // Ensure tenant_id is injected and cannot be modified
    const productWithTenant = {
      ...updateData,
      tenant_id: tenantId,
    };

    // Direct database connection approach
    const { Client } = require('pg');
    const client = new Client({
      connectionString:
        process.env.DATABASE_URL ||
        process.env.POSTGRES_URL ||
        'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
    });

    let updatedProduct: any = null;

    try {
      await client.connect();
      console.log(`🔗 [TENANT FILTER] Connected to database for comprehensive update`);

      // Start transaction
      await client.query('BEGIN');

      // First, verify the product belongs to this tenant
      const checkQuery = 'SELECT * FROM product WHERE id = $1 AND tenant_id = $2';
      const checkResult = await client.query(checkQuery, [productId, tenantId]);

      if (checkResult.rows.length === 0) {
        await client.query('ROLLBACK');
        throw new Error('Product not found or access denied');
      }

      // 1. Update main product fields
      const updateProductQuery = `
        UPDATE product
        SET
          title = COALESCE($1, title),
          handle = COALESCE($2, handle),
          description = COALESCE($3, description),
          status = COALESCE($4, status),
          thumbnail = COALESCE($5, thumbnail),
          collection_id = COALESCE($6, collection_id),
          metadata = COALESCE($7, metadata),
          updated_at = NOW()
        WHERE id = $8 AND tenant_id = $9
        RETURNING *
      `;

      const productValues = [
        productWithTenant.title,
        productWithTenant.handle,
        productWithTenant.description,
        productWithTenant.status,
        productWithTenant.thumbnail,
        productWithTenant.collection_id,
        productWithTenant.metadata
          ? JSON.stringify({
              ...productWithTenant.metadata,
              tenant_id: tenantId,
            })
          : null,
        productId,
        tenantId,
      ];

      const productResult = await client.query(updateProductQuery, productValues);
      updatedProduct = productResult.rows[0];

      // 2. Handle variants update (both existing and new variants)
      if (productWithTenant.variants && productWithTenant.variants.length > 0) {
        for (const variant of productWithTenant.variants) {
          if (variant.id) {
            // Update existing variant
            const updateVariantQuery = `
              UPDATE product_variant 
              SET 
                title = COALESCE($1, title),
                sku = COALESCE($2, sku),
                barcode = COALESCE($3, barcode),
                ean = COALESCE($4, ean),
                upc = COALESCE($5, upc),
                material = COALESCE($6, material),
                weight = COALESCE($7, weight),
                width = COALESCE($8, width),
                length = COALESCE($9, length),
                height = COALESCE($10, height),
                metadata = COALESCE($11, metadata),
                updated_at = NOW()
              WHERE id = $12 AND product_id = $13 AND tenant_id = $14
            `;

            const variantValues = [
              variant.title,
              variant.sku,
              variant.barcode,
              variant.ean,
              variant.upc,
              variant.material,
              variant.weight,
              variant.width,
              variant.length,
              variant.height,
              variant.metadata
                ? JSON.stringify({
                    ...variant.metadata,
                    tenant_id: tenantId,
                  })
                : null,
              variant.id,
              productId,
              tenantId,
            ];

            await client.query(updateVariantQuery, variantValues);

            // Update variant prices if provided
            if (variant.prices && variant.prices.length > 0) {
              // First, delete existing prices for this variant
              await client.query(
                `
                DELETE FROM price 
                WHERE price_set_id IN (
                  SELECT price_set_id FROM product_variant_price_set 
                  WHERE variant_id = $1
                ) AND tenant_id = $2
              `,
                [variant.id, tenantId]
              );

              // Get the price set for this variant
              const priceSetResult = await client.query(
                `
                SELECT price_set_id FROM product_variant_price_set 
                WHERE variant_id = $1
              `,
                [variant.id]
              );

              let priceSetId = priceSetResult.rows[0]?.price_set_id;

              if (!priceSetId) {
                // Create new price set if it doesn't exist
                priceSetId = `pset_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                await client.query(
                  `
                  INSERT INTO price_set (id, created_at, updated_at) 
                  VALUES ($1, NOW(), NOW())
                `,
                  [priceSetId]
                );

                const linkId = `pvps_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                await client.query(
                  `
                  INSERT INTO product_variant_price_set (id, variant_id, price_set_id, created_at, updated_at) 
                  VALUES ($1, $2, $3, NOW(), NOW())
                `,
                  [linkId, variant.id, priceSetId]
                );
              }

              // Add new prices
              for (const price of variant.prices) {
                const priceId = `price_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                const amount =
                  typeof price.amount === 'string' ? parseInt(price.amount) : price.amount;

                await client.query(
                  `
                  INSERT INTO price (
                    id, currency_code, amount, raw_amount, price_set_id, tenant_id, created_at, updated_at
                  ) VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
                `,
                  [
                    priceId,
                    price.currency_code || 'inr',
                    amount,
                    JSON.stringify({ value: amount.toString(), precision: 20 }),
                    priceSetId,
                    tenantId,
                  ]
                );
              }
            }
          } else {
            // Create new variant (no id provided)
            console.log(`🆕 [TENANT FILTER] Creating new variant: ${variant.title}`);

            const newVariantId = `variant_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

            const createVariantQuery = `
              INSERT INTO product_variant (
                id, title, sku, barcode, ean, upc, material, weight, width, length, height,
                metadata, product_id, tenant_id, created_at, updated_at
              ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, NOW(), NOW())
              RETURNING *
            `;

            const newVariantValues = [
              newVariantId,
              variant.title,
              variant.sku,
              variant.barcode || null,
              variant.ean || null,
              variant.upc || null,
              variant.material || null,
              variant.weight || null,
              variant.width || null,
              variant.length || null,
              variant.height || null,
              variant.metadata
                ? JSON.stringify({
                    ...variant.metadata,
                    tenant_id: tenantId,
                  })
                : JSON.stringify({ tenant_id: tenantId }),
              productId,
              tenantId,
            ];

            const newVariantResult = await client.query(createVariantQuery, newVariantValues);
            const createdVariant = newVariantResult.rows[0];

            console.log(`✅ [TENANT FILTER] Created new variant: ${createdVariant.id}`);

            // Create prices for new variant if provided
            if (variant.prices && variant.prices.length > 0) {
              // Create new price set for this variant
              const priceSetId = `pset_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
              await client.query(
                `
                INSERT INTO price_set (id, created_at, updated_at)
                VALUES ($1, NOW(), NOW())
              `,
                [priceSetId]
              );

              // Link variant to price set
              const linkId = `pvps_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
              await client.query(
                `
                INSERT INTO product_variant_price_set (id, variant_id, price_set_id, created_at, updated_at)
                VALUES ($1, $2, $3, NOW(), NOW())
              `,
                [linkId, createdVariant.id, priceSetId]
              );

              // Add prices
              for (const price of variant.prices) {
                const priceId = `price_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                const amount =
                  typeof price.amount === 'string' ? parseInt(price.amount) : price.amount;

                await client.query(
                  `
                  INSERT INTO price (
                    id, currency_code, amount, raw_amount, price_set_id, tenant_id, created_at, updated_at
                  ) VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
                `,
                  [
                    priceId,
                    price.currency_code || 'inr',
                    amount,
                    JSON.stringify({ value: amount.toString(), precision: 20 }),
                    priceSetId,
                    tenantId,
                  ]
                );
              }
            }
          }
        }
      }

      // 3. Handle category associations update
      if (productWithTenant.categories !== undefined) {
        console.log(`🏷️ [TENANT FILTER] Updating categories for product ${productId}`);

        // Delete existing category associations for this product and tenant
        await client.query(
          `
          DELETE FROM product_category_product
          WHERE product_id = $1 AND tenant_id = $2
        `,
          [productId, tenantId]
        );

        // Add new category associations if provided
        if (productWithTenant.categories && productWithTenant.categories.length > 0) {
          for (const category of productWithTenant.categories) {
            // Handle both string IDs and objects with id property
            const categoryId = typeof category === 'string' ? category : category.id;

            if (!categoryId) {
              console.warn(`⚠️ [TENANT FILTER] Skipping invalid category:`, category);
              continue;
            }

            // Verify category exists and belongs to this tenant
            const categoryCheckResult = await client.query(
              `
              SELECT id FROM product_category
              WHERE id = $1 AND tenant_id = $2
            `,
              [categoryId, tenantId]
            );

            if (categoryCheckResult.rows.length === 0) {
              console.warn(
                `⚠️ [TENANT FILTER] Category ${categoryId} not found for tenant ${tenantId}, skipping`
              );
              continue;
            }

            const insertCategoryLinkQuery = `
              INSERT INTO product_category_product (
                product_id, product_category_id, tenant_id, created_at, updated_at
              ) VALUES ($1, $2, $3, NOW(), NOW())
              ON CONFLICT (product_id, product_category_id) DO NOTHING
            `;

            await client.query(insertCategoryLinkQuery, [productId, categoryId, tenantId]);
          }
          console.log(
            `✅ [TENANT FILTER] Updated ${productWithTenant.categories.length} category associations`
          );
        } else {
          console.log(
            `✅ [TENANT FILTER] Removed all category associations (empty categories array)`
          );
        }
      }

      // 4. Handle tag associations update
      if (productWithTenant.tags !== undefined) {
        console.log(`🏷️ [TENANT FILTER] Updating tags for product ${productId}`);

        // Delete existing tag associations for this product
        await client.query(
          `
          DELETE FROM product_tags
          WHERE product_id = $1
        `,
          [productId]
        );

        // Add new tag associations if provided
        if (productWithTenant.tags && productWithTenant.tags.length > 0) {
          for (const tag of productWithTenant.tags) {
            // Handle both tag IDs and objects with id property
            const tagId = typeof tag === 'string' ? tag : tag.id;

            if (!tagId) {
              console.warn(`⚠️ [TENANT FILTER] Skipping invalid tag:`, tag);
              continue;
            }

            // Verify tag exists and belongs to this tenant
            const tagCheckResult = await client.query(
              `
              SELECT id FROM product_tag
              WHERE id = $1 AND tenant_id = $2
            `,
              [tagId, tenantId]
            );

            if (tagCheckResult.rows.length === 0) {
              console.warn(
                `⚠️ [TENANT FILTER] Tag ${tagId} not found for tenant ${tenantId}, skipping`
              );
              continue;
            }

            // Link tag to product
            const insertTagLinkQuery = `
              INSERT INTO product_tags (
                product_id, product_tag_id
              ) VALUES ($1, $2)
              ON CONFLICT (product_id, product_tag_id) DO NOTHING
            `;

            await client.query(insertTagLinkQuery, [productId, tagId]);
          }
          console.log(
            `✅ [TENANT FILTER] Updated ${productWithTenant.tags.length} tag associations`
          );
        } else {
          console.log(`✅ [TENANT FILTER] Removed all tag associations (empty tags array)`);
        }
      }

      // 5. Handle images update
      if (productWithTenant.images && productWithTenant.images.length > 0) {
        // Delete existing images for this product and tenant
        await client.query(
          `
          DELETE FROM image
          WHERE product_id = $1 AND metadata::jsonb @> $2
        `,
          [productId, JSON.stringify({ tenant_id: tenantId })]
        );

        // Add new images
        for (const image of productWithTenant.images) {
          const imageId = `img_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

          await client.query(
            `
            INSERT INTO image (
              id, url, product_id, metadata, rank, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
          `,
            [imageId, image.url, productId, JSON.stringify({ tenant_id: tenantId }), 0]
          );
        }
      }

      // Commit transaction
      await client.query('COMMIT');
      console.log(`✅ [TENANT FILTER] Comprehensive update completed for product ${productId}`);
    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error during update:', dbError);
      await client.query('ROLLBACK').catch(() => {});

      // Handle specific error cases
      if (dbError.message === 'Product not found or access denied') {
        return res.status(404).json({
          error: 'Product not found or access denied',
          product_id: productId,
          tenant_id: tenantId,
          _debug: {
            message: 'Product either does not exist or belongs to a different tenant',
          },
        });
      }

      throw dbError;
    } finally {
      // Always close the database connection
      try {
        await client.end();
        console.log(`🔗 [TENANT FILTER] Database connection closed`);
      } catch (closeError) {
        console.error('❌ [TENANT FILTER] Error closing database connection:', closeError);
      }
    }

    // Return response in Medusa format
    const response = {
      product: updatedProduct,
      _tenant: {
        id: tenantId,
        validated: true,
        method: 'comprehensive_db_update',
        updated_entities: {
          product: 1,
          variants: productWithTenant.variants?.length || 0,
          images: productWithTenant.images?.length || 0,
          categories: productWithTenant.categories?.length || 0,
          tags: productWithTenant.tags?.length || 0,
          collection: productWithTenant.collection_id ? 1 : 0,
        },
      },
    };

    // Add tenant headers
    res.setHeader('X-Tenant-ID', tenantId);
    res.setHeader('X-Tenant-Validated', 'true');

    console.log(
      `📤 [TENANT FILTER] Returning comprehensively updated product for tenant ${tenantId}`
    );

    res.json(response);
  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error in comprehensive product update:', error);

    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    res.status(500).json({
      error: 'Failed to update comprehensive product',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'tenant_comprehensive_update_error',
        timestamp: new Date().toISOString(),
        stack: error.stack,
      },
    });
  }
}

// PUT method - alias to POST for standard REST API compatibility
export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  console.log(`🔄 [TENANT FILTER] PUT request received, delegating to POST handler`);
  return await POST(req, res);
}

export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
  console.log(`🚀 [TENANT FILTER] === CUSTOM PRODUCT DELETE ENDPOINT CALLED ===`);

  try {
    // Extract tenant ID from header
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    // Get product ID from URL params
    const productId = req.params?.id as string;

    if (!productId) {
      return res.status(400).json({
        error: 'Product ID is required for deletion',
        tenant_id: tenantId,
      });
    }

    console.log(`🗑️ [TENANT FILTER] Deleting product ${productId} for tenant: ${tenantId}`);

    // Direct database connection approach
    const { Client } = require('pg');
    const client = new Client({
      connectionString:
        process.env.DATABASE_URL ||
        process.env.POSTGRES_URL ||
        'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
    });

    let deletedProduct: any = null;

    try {
      await client.connect();
      console.log(`🔗 [TENANT FILTER] Connected to database directly`);

      // First, verify the product belongs to this tenant and get it
      const checkQuery = 'SELECT * FROM product WHERE id = $1 AND tenant_id = $2';
      const checkResult = await client.query(checkQuery, [productId, tenantId]);

      if (checkResult.rows.length === 0) {
        throw new Error('Product not found or access denied');
      }

      // Delete the product
      const deleteQuery = 'DELETE FROM product WHERE id = $1 AND tenant_id = $2 RETURNING *';
      const result = await client.query(deleteQuery, [productId, tenantId]);
      deletedProduct = result.rows[0];

      console.log(`✅ [TENANT FILTER] Deleted product ${productId} for tenant: ${tenantId}`);
    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error:', dbError);

      // Handle specific error cases
      if (dbError.message === 'Product not found or access denied') {
        return res.status(404).json({
          error: 'Product not found or access denied',
          product_id: productId,
          tenant_id: tenantId,
          _debug: {
            message: 'Product either does not exist or belongs to a different tenant',
          },
        });
      }

      throw dbError;
    } finally {
      // Always close the database connection
      try {
        await client.end();
        console.log(`🔗 [TENANT FILTER] Database connection closed`);
      } catch (closeError) {
        console.error('❌ [TENANT FILTER] Error closing database connection:', closeError);
      }
    }

    // Return response in Medusa format
    const response = {
      id: productId,
      object: 'product',
      deleted: true,
      _tenant: {
        id: tenantId,
        validated: true,
        method: 'direct_db_connection',
      },
    };

    // Add tenant headers
    res.setHeader('X-Tenant-ID', tenantId);
    res.setHeader('X-Tenant-Validated', 'true');

    console.log(
      `📤 [TENANT FILTER] Confirmed deletion of product ${productId} for tenant ${tenantId}`
    );

    res.json(response);
  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error deleting product:', error);

    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    res.status(500).json({
      error: 'Failed to delete product',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'tenant_delete_error',
        timestamp: new Date().toISOString(),
      },
    });
  }
}
