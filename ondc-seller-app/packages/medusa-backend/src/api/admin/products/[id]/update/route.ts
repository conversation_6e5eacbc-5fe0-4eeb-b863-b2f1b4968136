import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"

// Helper function to preprocess data and fix data types
function preprocessUpdateData(data: any): any {
  if (!data) return data

  const processed = { ...data }

  // Fix variants data types
  if (processed.variants && Array.isArray(processed.variants)) {
    processed.variants = processed.variants.map((variant: any) => {
      const processedVariant = { ...variant }

      // Fix prices data types
      if (processedVariant.prices && Array.isArray(processedVariant.prices)) {
        processedVariant.prices = processedVariant.prices.map((price: any) => ({
          ...price,
          amount: typeof price.amount === 'string' ? parseFloat(price.amount) : price.amount,
          min_quantity: typeof price.min_quantity === 'string' ? parseInt(price.min_quantity) : price.min_quantity,
          max_quantity: typeof price.max_quantity === 'string' ? parseInt(price.max_quantity) : price.max_quantity
        }))
      }

      // Fix numeric fields in variant
      if (processedVariant.weight && typeof processedVariant.weight === 'string') {
        processedVariant.weight = parseFloat(processedVariant.weight)
      }
      if (processedVariant.width && typeof processedVariant.width === 'string') {
        processedVariant.width = parseFloat(processedVariant.width)
      }
      if (processedVariant.length && typeof processedVariant.length === 'string') {
        processedVariant.length = parseFloat(processedVariant.length)
      }
      if (processedVariant.height && typeof processedVariant.height === 'string') {
        processedVariant.height = parseFloat(processedVariant.height)
      }

      return processedVariant
    })
  }

  // Fix product-level numeric fields
  if (processed.weight && typeof processed.weight === 'string') {
    processed.weight = parseFloat(processed.weight)
  }
  if (processed.width && typeof processed.width === 'string') {
    processed.width = parseFloat(processed.width)
  }
  if (processed.length && typeof processed.length === 'string') {
    processed.length = parseFloat(processed.length)
  }
  if (processed.height && typeof processed.height === 'string') {
    processed.height = parseFloat(processed.height)
  }

  return processed
}

export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
) {
  console.log(`🚀 [TENANT FILTER] === CUSTOM PRODUCT UPDATE ENDPOINT (NO VALIDATION) ===`)
  console.log(`🚀 [TENANT FILTER] Raw Body:`, JSON.stringify(req.body, null, 2))

  try {
    // Extract tenant ID from header
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    // Get product ID from URL params
    const productId = req.params?.id as string
    
    if (!productId) {
      return res.status(400).json({
        error: 'Product ID is required for update',
        tenant_id: tenantId
      })
    }

    console.log(`🔄 [TENANT FILTER] Custom update for product ${productId}, tenant: ${tenantId}`)

    // Get update data from request body and preprocess it
    const rawUpdateData = req.body as any
    
    // Preprocess data to fix data types
    const updateData = preprocessUpdateData(rawUpdateData)
    console.log(`🔧 [TENANT FILTER] Preprocessed data:`, JSON.stringify(updateData, null, 2))

    // Ensure tenant_id is injected and cannot be modified
    const productWithTenant = {
      ...updateData,
      tenant_id: tenantId
    }

    // Direct database connection approach
    const { Client } = require('pg')
    const client = new Client({
      connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
    })

    let updatedProduct: any = null

    try {
      await client.connect()
      console.log(`🔗 [TENANT FILTER] Connected to database for custom update`)

      // Start transaction
      await client.query('BEGIN')

      // First, verify the product belongs to this tenant
      const checkQuery = 'SELECT * FROM product WHERE id = $1 AND tenant_id = $2'
      const checkResult = await client.query(checkQuery, [productId, tenantId])

      if (checkResult.rows.length === 0) {
        await client.query('ROLLBACK')
        await client.end()
        return res.status(404).json({
          error: 'Product not found or access denied',
          product_id: productId,
          tenant_id: tenantId,
          _debug: {
            message: 'Product either does not exist or belongs to a different tenant'
          }
        })
      }

      // 1. Update main product fields
      const updateProductQuery = `
        UPDATE product 
        SET 
          title = COALESCE($1, title),
          handle = COALESCE($2, handle),
          description = COALESCE($3, description),
          status = COALESCE($4, status),
          thumbnail = COALESCE($5, thumbnail),
          metadata = COALESCE($6, metadata),
          updated_at = NOW()
        WHERE id = $7 AND tenant_id = $8
        RETURNING *
      `

      const productValues = [
        productWithTenant.title,
        productWithTenant.handle,
        productWithTenant.description,
        productWithTenant.status,
        productWithTenant.thumbnail,
        productWithTenant.metadata ? JSON.stringify({
          ...productWithTenant.metadata,
          tenant_id: tenantId
        }) : null,
        productId,
        tenantId
      ]

      const productResult = await client.query(updateProductQuery, productValues)
      updatedProduct = productResult.rows[0]

      // 2. Handle variants update
      if (productWithTenant.variants && productWithTenant.variants.length > 0) {
        console.log(`🔄 [TENANT FILTER] Processing ${productWithTenant.variants.length} variants`)
        
        for (let i = 0; i < productWithTenant.variants.length; i++) {
          const variant = productWithTenant.variants[i]
          console.log(`🔄 [TENANT FILTER] Processing variant ${i + 1}:`, {
            id: variant.id,
            title: variant.title,
            sku: variant.sku,
            hasId: !!variant.id,
            action: variant.id ? 'UPDATE' : 'CREATE'
          })
          
          if (variant.id) {
            // Update existing variant
            const updateVariantQuery = `
              UPDATE product_variant 
              SET 
                title = COALESCE($1, title),
                sku = COALESCE($2, sku),
                barcode = COALESCE($3, barcode),
                ean = COALESCE($4, ean),
                upc = COALESCE($5, upc),
                material = COALESCE($6, material),
                weight = COALESCE($7, weight),
                width = COALESCE($8, width),
                length = COALESCE($9, length),
                height = COALESCE($10, height),
                metadata = COALESCE($11, metadata),
                updated_at = NOW()
              WHERE id = $12 AND product_id = $13 AND tenant_id = $14
            `

            const variantValues = [
              variant.title,
              variant.sku,
              variant.barcode,
              variant.ean,
              variant.upc,
              variant.material,
              variant.weight,
              variant.width,
              variant.length,
              variant.height,
              variant.metadata ? JSON.stringify({
                ...variant.metadata,
                tenant_id: tenantId
              }) : null,
              variant.id,
              productId,
              tenantId
            ]

            await client.query(updateVariantQuery, variantValues)

            // Update variant prices if provided
            if (variant.prices && variant.prices.length > 0) {
              // First, delete existing prices for this variant
              await client.query(`
                DELETE FROM price 
                WHERE price_set_id IN (
                  SELECT price_set_id FROM product_variant_price_set 
                  WHERE variant_id = $1
                ) AND tenant_id = $2
              `, [variant.id, tenantId])

              // Get the price set for this variant
              const priceSetResult = await client.query(`
                SELECT price_set_id FROM product_variant_price_set 
                WHERE variant_id = $1
              `, [variant.id])

              let priceSetId = priceSetResult.rows[0]?.price_set_id

              if (!priceSetId) {
                // Create new price set if it doesn't exist
                priceSetId = `pset_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
                await client.query(`
                  INSERT INTO price_set (id, created_at, updated_at) 
                  VALUES ($1, NOW(), NOW())
                `, [priceSetId])

                const linkId = `pvps_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
                await client.query(`
                  INSERT INTO product_variant_price_set (id, variant_id, price_set_id, created_at, updated_at) 
                  VALUES ($1, $2, $3, NOW(), NOW())
                `, [linkId, variant.id, priceSetId])
              }

              // Add new prices
              for (const price of variant.prices) {
                const priceId = `price_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
                const amount = typeof price.amount === 'string' ? parseFloat(price.amount) : price.amount

                await client.query(`
                  INSERT INTO price (
                    id, currency_code, amount, raw_amount, price_set_id, tenant_id, created_at, updated_at
                  ) VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
                `, [
                  priceId,
                  price.currency_code || 'inr',
                  amount,
                  JSON.stringify({ value: amount.toString(), precision: 20 }),
                  priceSetId,
                  tenantId
                ])
              }
            }
          } else {
            // Create new variant
            console.log(`✨ [TENANT FILTER] Creating new variant:`, {
              title: variant.title,
              sku: variant.sku,
              hasPrices: !!(variant.prices && variant.prices.length > 0),
              pricesCount: variant.prices?.length || 0
            })
            
            const variantId = `variant_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
            
            const insertVariantQuery = `
              INSERT INTO product_variant (
                id, title, sku, product_id, tenant_id, 
                metadata, weight, width, length, height,
                barcode, ean, upc, material, variant_rank,
                allow_backorder, manage_inventory, created_at, updated_at
              ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, NOW(), NOW())
              RETURNING *
            `

            const variantValues = [
              variantId,
              variant.title || 'Default',
              variant.sku,
              productId,
              tenantId,
              JSON.stringify({
                ...variant.metadata,
                tenant_id: tenantId
              }),
              variant.weight,
              variant.width,
              variant.length,
              variant.height,
              variant.barcode,
              variant.ean,
              variant.upc,
              variant.material,
              0, // variant_rank
              variant.allow_backorder || false,
              variant.manage_inventory !== false // default to true
            ]

            console.log(`🔧 [TENANT FILTER] Executing variant insert with values:`, {
              variantId,
              title: variant.title || 'Default',
              sku: variant.sku,
              productId,
              tenantId
            })

            const variantResult = await client.query(insertVariantQuery, variantValues)
            console.log(`✅ [TENANT FILTER] New variant created successfully:`, variantResult.rows[0]?.id)

            // Add prices for new variant
            if (variant.prices && variant.prices.length > 0) {
              // Create price set for new variant
              const priceSetId = `pset_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
              await client.query(`
                INSERT INTO price_set (id, created_at, updated_at) 
                VALUES ($1, NOW(), NOW())
              `, [priceSetId])

              const linkId = `pvps_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
              await client.query(`
                INSERT INTO product_variant_price_set (id, variant_id, price_set_id, created_at, updated_at) 
                VALUES ($1, $2, $3, NOW(), NOW())
              `, [linkId, variantId, priceSetId])

              // Add prices
              for (const price of variant.prices) {
                const priceId = `price_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
                const amount = typeof price.amount === 'string' ? parseFloat(price.amount) : price.amount

                await client.query(`
                  INSERT INTO price (
                    id, currency_code, amount, raw_amount, price_set_id, tenant_id, created_at, updated_at
                  ) VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
                `, [
                  priceId,
                  price.currency_code || 'inr',
                  amount,
                  JSON.stringify({ value: amount.toString(), precision: 20 }),
                  priceSetId,
                  tenantId
                ])
              }
            }
          }
        }
      }

      // 3. Handle images update
      if (productWithTenant.images && productWithTenant.images.length > 0) {
        // Delete existing images for this product and tenant
        await client.query(`
          DELETE FROM image 
          WHERE product_id = $1 AND metadata::jsonb @> $2
        `, [productId, JSON.stringify({ tenant_id: tenantId })])

        // Add new images
        for (const image of productWithTenant.images) {
          const imageId = `img_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
          
          await client.query(`
            INSERT INTO image (
              id, url, product_id, metadata, rank, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
          `, [
            imageId,
            image.url,
            productId,
            JSON.stringify({ tenant_id: tenantId }),
            0
          ])
        }
      }

      // Commit transaction
      await client.query('COMMIT')
      console.log(`✅ [TENANT FILTER] Custom update completed for product ${productId}`)

      await client.end()

    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error during custom update:', dbError)
      await client.query('ROLLBACK').catch(() => {})
      await client.end().catch(() => {})
      throw dbError
    }

    // Return response in Medusa format
    const response = {
      product: updatedProduct,
      _tenant: {
        id: tenantId,
        validated: true,
        method: 'custom_db_update_no_validation',
        updated_entities: {
          product: 1,
          variants: productWithTenant.variants?.length || 0,
          images: productWithTenant.images?.length || 0
        }
      }
    }

    // Add tenant headers
    res.setHeader('X-Tenant-ID', tenantId)
    res.setHeader('X-Tenant-Validated', 'true')

    console.log(`📤 [TENANT FILTER] Returning custom updated product for tenant ${tenantId}`)
    
    res.json(response)

  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error in custom product update:', error)
    
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    res.status(500).json({
      error: 'Failed to update product via custom endpoint',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'tenant_custom_update_error',
        timestamp: new Date().toISOString(),
        stack: error.stack
      }
    })
  }
}