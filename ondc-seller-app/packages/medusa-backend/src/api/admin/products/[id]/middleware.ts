import { MedusaRequest, MedusaResponse, MedusaNextFunction } from "@medusajs/framework/http"

// Middleware to preprocess request data and fix data types
export function preprocessProductData(
  req: MedusaRequest,
  res: MedusaResponse,
  next: MedusaNextFunction
) {
  console.log(`🔧 [MIDDLEWARE] Preprocessing product update data`)
  
  if (req.method === 'POST' && req.body) {
    try {
      const originalBody = req.body as any
      console.log(`🔧 [MIDDLEWARE] Original body:`, JSON.stringify(originalBody, null, 2))

      // Fix variants data types
      if (originalBody.variants && Array.isArray(originalBody.variants)) {
        originalBody.variants = originalBody.variants.map((variant: any) => {
          const processedVariant = { ...variant }

          // Fix prices data types
          if (processedVariant.prices && Array.isArray(processedVariant.prices)) {
            processedVariant.prices = processedVariant.prices.map((price: any) => ({
              ...price,
              amount: typeof price.amount === 'string' ? parseFloat(price.amount) : price.amount,
              min_quantity: typeof price.min_quantity === 'string' ? parseInt(price.min_quantity) : price.min_quantity,
              max_quantity: typeof price.max_quantity === 'string' ? parseInt(price.max_quantity) : price.max_quantity
            }))
          }

          // Fix numeric fields in variant
          if (processedVariant.weight && typeof processedVariant.weight === 'string') {
            processedVariant.weight = parseFloat(processedVariant.weight)
          }
          if (processedVariant.width && typeof processedVariant.width === 'string') {
            processedVariant.width = parseFloat(processedVariant.width)
          }
          if (processedVariant.length && typeof processedVariant.length === 'string') {
            processedVariant.length = parseFloat(processedVariant.length)
          }
          if (processedVariant.height && typeof processedVariant.height === 'string') {
            processedVariant.height = parseFloat(processedVariant.height)
          }

          return processedVariant
        })
      }

      // Fix product-level numeric fields
      if (originalBody.weight && typeof originalBody.weight === 'string') {
        originalBody.weight = parseFloat(originalBody.weight)
      }
      if (originalBody.width && typeof originalBody.width === 'string') {
        originalBody.width = parseFloat(originalBody.width)
      }
      if (originalBody.length && typeof originalBody.length === 'string') {
        originalBody.length = parseFloat(originalBody.length)
      }
      if (originalBody.height && typeof originalBody.height === 'string') {
        originalBody.height = parseFloat(originalBody.height)
      }

      // Update the request body
      req.body = originalBody
      console.log(`✅ [MIDDLEWARE] Preprocessed body:`, JSON.stringify(req.body, null, 2))
      
    } catch (error) {
      console.error(`❌ [MIDDLEWARE] Error preprocessing data:`, error)
    }
  }

  next()
}