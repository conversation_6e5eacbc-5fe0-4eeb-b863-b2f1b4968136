import { MedusaRequest, MedusaResponse } from "@medusajs/medusa";
import { z } from "zod";

// Query parameter validation schema
const CustomerQuerySchema = z.object({
  period: z.enum(['7d', '30d', '90d', '1y']).default('30d'),
  segment: z.enum(['all', 'new', 'repeat', 'vip', 'inactive']).default('all'),
  tenant_id: z.string().optional(),
  sales_channel_id: z.string().optional(),
});

interface CustomerSegment {
  segment: string;
  count: number;
  revenue: number;
  average_order_value: number;
  percentage: number;
  orders_per_customer: number;
}

interface CustomerAcquisition {
  date: string;
  new_customers: number;
  repeat_customers: number;
  total_customers: number;
}

interface CustomerLifetimeValue {
  segment: string;
  average_ltv: number;
  median_ltv: number;
  total_customers: number;
}

interface CustomerAnalytics {
  summary: {
    total_customers: number;
    new_customers: number;
    repeat_customers: number;
    customer_retention_rate: number;
    average_customer_lifetime_value: number;
    churn_rate: number;
  };
  segments: CustomerSegment[];
  acquisition_trends: CustomerAcquisition[];
  lifetime_value: CustomerLifetimeValue[];
  top_customers: Array<{
    customer_id: string;
    name: string;
    email: string;
    total_orders: number;
    total_spent: number;
    last_order_date: string;
  }>;
  geographic_distribution: Array<{
    country: string;
    customers: number;
    revenue: number;
    percentage: number;
  }>;
}

/**
 * GET /admin/analytics/customers
 * 
 * Customer analytics endpoint that provides:
 * - Customer summary metrics
 * - Customer segmentation analysis
 * - Customer acquisition trends
 * - Customer lifetime value analysis
 * - Top customers by value
 * - Geographic distribution
 */
export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  try {
    // Validate query parameters
    const query = CustomerQuerySchema.parse(req.query);
    const { period, segment, tenant_id, sales_channel_id } = query;

    // Get services
    const customerService = req.scope.resolve("customerService");
    const orderService = req.scope.resolve("orderService");

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    
    switch (period) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
    }

    // Build filters
    const customerFilters: any = {};
    const orderFilters: any = {
      created_at: {
        gte: startDate,
        lte: endDate,
      },
      status: ['completed', 'shipped', 'delivered'],
    };

    if (tenant_id) {
      customerFilters.tenant_id = tenant_id;
      orderFilters.tenant_id = tenant_id;
    }

    if (sales_channel_id) {
      orderFilters.sales_channel_id = sales_channel_id;
    }

    // Fetch customers and orders
    const customers = await customerService.list(customerFilters, {
      relations: ["orders"],
    });

    const orders = await orderService.list(orderFilters, {
      relations: ["customer"],
      order: { created_at: "DESC" },
    });

    // Calculate customer metrics
    const totalCustomers = customers.length;
    const customersWithOrders = new Set(orders.map(order => order.customer_id));
    const activeCustomers = customersWithOrders.size;

    // Identify new vs repeat customers
    const customerOrderCounts = new Map<string, number>();
    const customerRevenue = new Map<string, number>();
    const customerFirstOrder = new Map<string, Date>();
    const customerLastOrder = new Map<string, Date>();

    orders.forEach(order => {
      const customerId = order.customer_id;
      if (customerId) {
        // Order count
        customerOrderCounts.set(customerId, (customerOrderCounts.get(customerId) || 0) + 1);
        
        // Revenue
        customerRevenue.set(customerId, (customerRevenue.get(customerId) || 0) + (order.total || 0));
        
        // First and last order dates
        const orderDate = new Date(order.created_at);
        if (!customerFirstOrder.has(customerId) || orderDate < customerFirstOrder.get(customerId)!) {
          customerFirstOrder.set(customerId, orderDate);
        }
        if (!customerLastOrder.has(customerId) || orderDate > customerLastOrder.get(customerId)!) {
          customerLastOrder.set(customerId, orderDate);
        }
      }
    });

    // Categorize customers
    const newCustomers = Array.from(customerFirstOrder.entries())
      .filter(([_, firstOrder]) => firstOrder >= startDate)
      .map(([customerId]) => customerId);

    const repeatCustomers = Array.from(customerOrderCounts.entries())
      .filter(([_, count]) => count > 1)
      .map(([customerId]) => customerId);

    const vipCustomers = Array.from(customerRevenue.entries())
      .filter(([_, revenue]) => revenue > 10000) // VIP threshold: ₹10,000
      .map(([customerId]) => customerId);

    // Calculate segments
    const segments: CustomerSegment[] = [
      {
        segment: 'New Customers',
        count: newCustomers.length,
        revenue: newCustomers.reduce((sum, id) => sum + (customerRevenue.get(id) || 0), 0),
        average_order_value: 0,
        percentage: totalCustomers > 0 ? (newCustomers.length / totalCustomers) * 100 : 0,
        orders_per_customer: 0,
      },
      {
        segment: 'Repeat Customers',
        count: repeatCustomers.length,
        revenue: repeatCustomers.reduce((sum, id) => sum + (customerRevenue.get(id) || 0), 0),
        average_order_value: 0,
        percentage: totalCustomers > 0 ? (repeatCustomers.length / totalCustomers) * 100 : 0,
        orders_per_customer: 0,
      },
      {
        segment: 'VIP Customers',
        count: vipCustomers.length,
        revenue: vipCustomers.reduce((sum, id) => sum + (customerRevenue.get(id) || 0), 0),
        average_order_value: 0,
        percentage: totalCustomers > 0 ? (vipCustomers.length / totalCustomers) * 100 : 0,
        orders_per_customer: 0,
      },
    ];

    // Calculate additional metrics for segments
    segments.forEach(segment => {
      let customerIds: string[] = [];
      switch (segment.segment) {
        case 'New Customers':
          customerIds = newCustomers;
          break;
        case 'Repeat Customers':
          customerIds = repeatCustomers;
          break;
        case 'VIP Customers':
          customerIds = vipCustomers;
          break;
      }

      const totalOrders = customerIds.reduce((sum, id) => sum + (customerOrderCounts.get(id) || 0), 0);
      segment.average_order_value = totalOrders > 0 ? segment.revenue / totalOrders : 0;
      segment.orders_per_customer = segment.count > 0 ? totalOrders / segment.count : 0;
    });

    // Customer acquisition trends
    const acquisitionTrends: CustomerAcquisition[] = [];
    const days = Math.min(30, Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)));
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dayStart = new Date(date.setHours(0, 0, 0, 0));
      const dayEnd = new Date(date.setHours(23, 59, 59, 999));

      const dayNewCustomers = Array.from(customerFirstOrder.entries())
        .filter(([_, firstOrder]) => firstOrder >= dayStart && firstOrder <= dayEnd)
        .length;

      const dayRepeatCustomers = orders
        .filter(order => {
          const orderDate = new Date(order.created_at);
          return orderDate >= dayStart && orderDate <= dayEnd && 
                 (customerOrderCounts.get(order.customer_id) || 0) > 1;
        })
        .map(order => order.customer_id)
        .filter((id, index, arr) => arr.indexOf(id) === index)
        .length;

      acquisitionTrends.push({
        date: dayStart.toISOString().split('T')[0],
        new_customers: dayNewCustomers,
        repeat_customers: dayRepeatCustomers,
        total_customers: dayNewCustomers + dayRepeatCustomers,
      });
    }

    // Customer lifetime value analysis
    const lifetimeValue: CustomerLifetimeValue[] = [
      {
        segment: 'All Customers',
        average_ltv: Array.from(customerRevenue.values()).reduce((sum, rev) => sum + rev, 0) / customerRevenue.size,
        median_ltv: 0, // Would need proper median calculation
        total_customers: customerRevenue.size,
      },
    ];

    // Top customers
    const topCustomers = Array.from(customerRevenue.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([customerId, revenue]) => {
        const customer = customers.find(c => c.id === customerId);
        return {
          customer_id: customerId,
          name: customer ? `${customer.first_name || ''} ${customer.last_name || ''}`.trim() : 'Unknown',
          email: customer?.email || '',
          total_orders: customerOrderCounts.get(customerId) || 0,
          total_spent: revenue,
          last_order_date: customerLastOrder.get(customerId)?.toISOString() || '',
        };
      });

    // Geographic distribution (simplified - would need address data)
    const geographicDistribution = [
      {
        country: 'India',
        customers: totalCustomers,
        revenue: Array.from(customerRevenue.values()).reduce((sum, rev) => sum + rev, 0),
        percentage: 100,
      },
    ];

    // Calculate retention and churn rates
    const retentionRate = totalCustomers > 0 ? (repeatCustomers.length / totalCustomers) * 100 : 0;
    const churnRate = 100 - retentionRate;

    // Build response
    const analytics: CustomerAnalytics = {
      summary: {
        total_customers: totalCustomers,
        new_customers: newCustomers.length,
        repeat_customers: repeatCustomers.length,
        customer_retention_rate: retentionRate,
        average_customer_lifetime_value: lifetimeValue[0].average_ltv,
        churn_rate: churnRate,
      },
      segments,
      acquisition_trends: acquisitionTrends,
      lifetime_value: lifetimeValue,
      top_customers: topCustomers,
      geographic_distribution: geographicDistribution,
    };

    res.status(200).json(analytics);
  } catch (error) {
    console.error('Customer analytics error:', error);
    res.status(500).json({
      error: 'Failed to fetch customer analytics',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
