import { MedusaRequest, MedusaResponse } from '@medusajs/medusa';
import { z } from 'zod';

// Query parameter validation schema
const SalesQuerySchema = z.object({
  period: z.enum(['7d', '30d', '90d', '1y']).default('30d'),
  group_by: z.enum(['day', 'week', 'month']).default('day'),
  sales_channel_id: z.string().optional(),
  tenant_id: z.string().optional(),
  currency: z.string().default('INR'),
  category_id: z.string().optional(),
});

interface SalesData {
  period: string;
  revenue: number;
  orders: number;
  units_sold: number;
  average_order_value: number;
  growth_rate: number;
}

interface SalesByCategory {
  category_id: string;
  category_name: string;
  revenue: number;
  orders: number;
  products: number;
  growth_rate: number;
}

interface SalesByChannel {
  channel_id: string;
  channel_name: string;
  revenue: number;
  orders: number;
  conversion_rate: number;
}

interface SalesAnalytics {
  summary: {
    total_revenue: number;
    total_orders: number;
    total_units_sold: number;
    average_order_value: number;
    growth_rate: number;
  };
  trends: SalesData[];
  by_category: SalesByCategory[];
  by_channel: SalesByChannel[];
  top_performing_days: Array<{
    date: string;
    revenue: number;
    orders: number;
  }>;
}

/**
 * GET /admin/analytics/sales
 *
 * Detailed sales analytics endpoint that provides:
 * - Sales summary and growth metrics
 * - Sales trends over time (grouped by day/week/month)
 * - Sales breakdown by category
 * - Sales breakdown by sales channel
 * - Top performing days
 */
export async function GET(req: MedusaRequest, res: MedusaResponse): Promise<void> {
  try {
    // Validate query parameters
    const query = SalesQuerySchema.parse(req.query);
    const { period, group_by, sales_channel_id, tenant_id, currency, category_id } = query;

    // Get services using proper Medusa v2 service resolution
    const orderService = req.scope.resolve('order');
    // Note: Category and sales channel services might have different names in v2
    // For now, we'll work with orders and products

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
    }

    // Build filters (without date filtering since Medusa v2 doesn't support it)
    const filters: any = {};

    if (tenant_id) {
      filters.tenant_id = tenant_id;
    }

    if (sales_channel_id) {
      filters.sales_channel_id = sales_channel_id;
    }

    // Fetch orders with supported relations using correct method name
    const ordersResult = await orderService.listAndCountOrders(filters, {
      relations: ['items'],
      order: { created_at: 'DESC' },
    });

    const allOrders = ordersResult[0]; // First element is the orders array

    // Filter orders by date range and successful status in memory
    const orders = allOrders.filter(order => {
      const orderDate = new Date(order.created_at);
      const isInDateRange = orderDate >= startDate && orderDate <= endDate;
      const isSuccessful = ['completed', 'shipped', 'delivered'].includes(order.status);
      return isInDateRange && isSuccessful;
    });

    // Also get all orders for comparison (not just successful ones)
    const allOrdersInRange = allOrders.filter(order => {
      const orderDate = new Date(order.created_at);
      return orderDate >= startDate && orderDate <= endDate;
    });

    // Calculate summary metrics
    const totalRevenue = orders.reduce((sum, order) => sum + (order.total || 0), 0);
    const totalOrders = orders.length;
    const totalUnits = orders.reduce(
      (sum, order) =>
        sum + (order.items?.reduce((itemSum, item) => itemSum + (item.quantity || 0), 0) || 0),
      0
    );
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    // Calculate growth rate (compare with previous period)
    const previousStartDate = new Date(startDate);
    const previousEndDate = new Date(startDate);
    const periodDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    previousStartDate.setDate(previousStartDate.getDate() - periodDays);

    // Filter previous period orders from the same dataset
    const previousOrders = allOrders.filter(order => {
      const orderDate = new Date(order.created_at);
      const isInPreviousRange = orderDate >= previousStartDate && orderDate <= previousEndDate;
      const isSuccessful = ['completed', 'shipped', 'delivered'].includes(order.status);
      return isInPreviousRange && isSuccessful;
    });

    const previousRevenue = previousOrders.reduce((sum, order) => sum + (order.total || 0), 0);
    const growthRate =
      previousRevenue > 0 ? ((totalRevenue - previousRevenue) / previousRevenue) * 100 : 0;

    // Generate trends data based on group_by
    const trends: SalesData[] = [];
    const groupedData = new Map<string, { revenue: number; orders: number; units: number }>();

    orders.forEach(order => {
      const orderDate = new Date(order.created_at);
      let periodKey: string;

      switch (group_by) {
        case 'day':
          periodKey = orderDate.toISOString().split('T')[0];
          break;
        case 'week':
          const weekStart = new Date(orderDate);
          weekStart.setDate(orderDate.getDate() - orderDate.getDay());
          periodKey = weekStart.toISOString().split('T')[0];
          break;
        case 'month':
          periodKey = `${orderDate.getFullYear()}-${String(orderDate.getMonth() + 1).padStart(2, '0')}`;
          break;
        default:
          periodKey = orderDate.toISOString().split('T')[0];
      }

      const existing = groupedData.get(periodKey) || { revenue: 0, orders: 0, units: 0 };
      existing.revenue += order.total || 0;
      existing.orders += 1;
      existing.units += order.items?.reduce((sum, item) => sum + (item.quantity || 0), 0) || 0;
      groupedData.set(periodKey, existing);
    });

    // Convert to array and sort
    Array.from(groupedData.entries())
      .sort((a, b) => a[0].localeCompare(b[0]))
      .forEach(([period, data]) => {
        trends.push({
          period,
          revenue: data.revenue,
          orders: data.orders,
          units_sold: data.units,
          average_order_value: data.orders > 0 ? data.revenue / data.orders : 0,
          growth_rate: 0, // Would need historical comparison
        });
      });

    // Sales by category
    const categoryData = new Map<
      string,
      { name: string; revenue: number; orders: Set<string>; products: Set<string> }
    >();

    orders.forEach(order => {
      order.items?.forEach(item => {
        const product = item.variant?.product;
        if (product?.categories) {
          product.categories.forEach(category => {
            const existing = categoryData.get(category.id) || {
              name: category.name,
              revenue: 0,
              orders: new Set(),
              products: new Set(),
            };
            existing.revenue += (item.unit_price || 0) * (item.quantity || 0);
            existing.orders.add(order.id);
            existing.products.add(product.id);
            categoryData.set(category.id, existing);
          });
        }
      });
    });

    const byCategory: SalesByCategory[] = Array.from(categoryData.entries())
      .map(([categoryId, data]) => ({
        category_id: categoryId,
        category_name: data.name,
        revenue: data.revenue,
        orders: data.orders.size,
        products: data.products.size,
        growth_rate: 0, // Would need historical comparison
      }))
      .sort((a, b) => b.revenue - a.revenue);

    // Sales by channel
    const channelData = new Map<string, { name: string; revenue: number; orders: number }>();

    orders.forEach(order => {
      const channelId = order.sales_channel_id || 'default';
      const channelName = order.sales_channel?.name || 'Default Channel';

      const existing = channelData.get(channelId) || { name: channelName, revenue: 0, orders: 0 };
      existing.revenue += order.total || 0;
      existing.orders += 1;
      channelData.set(channelId, existing);
    });

    const byChannel: SalesByChannel[] = Array.from(channelData.entries())
      .map(([channelId, data]) => ({
        channel_id: channelId,
        channel_name: data.name,
        revenue: data.revenue,
        orders: data.orders,
        conversion_rate: 0, // Would need traffic data
      }))
      .sort((a, b) => b.revenue - a.revenue);

    // Top performing days
    const dailyData = new Map<string, { revenue: number; orders: number }>();

    orders.forEach(order => {
      const date = new Date(order.created_at).toISOString().split('T')[0];
      const existing = dailyData.get(date) || { revenue: 0, orders: 0 };
      existing.revenue += order.total || 0;
      existing.orders += 1;
      dailyData.set(date, existing);
    });

    const topPerformingDays = Array.from(dailyData.entries())
      .map(([date, data]) => ({
        date,
        revenue: data.revenue,
        orders: data.orders,
      }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10);

    // Build response
    const analytics: SalesAnalytics = {
      summary: {
        total_revenue: totalRevenue,
        total_orders: totalOrders,
        total_units_sold: totalUnits,
        average_order_value: averageOrderValue,
        growth_rate: growthRate,
      },
      trends,
      by_category: byCategory,
      by_channel: byChannel,
      top_performing_days: topPerformingDays,
    };

    res.status(200).json(analytics);
  } catch (error) {
    console.error('Sales analytics error:', error);
    res.status(500).json({
      error: 'Failed to fetch sales analytics',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
