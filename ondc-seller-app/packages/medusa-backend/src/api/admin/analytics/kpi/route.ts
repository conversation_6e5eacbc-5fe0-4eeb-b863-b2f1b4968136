import { MedusaRequest, MedusaResponse } from '@medusajs/medusa';
import { z } from 'zod';

// Query parameter validation schema
const KPIQuerySchema = z.object({
  period: z.enum(['7d', '30d', '90d', '1y']).default('30d'),
  tenant_id: z.string().optional(),
  sales_channel_id: z.string().optional(),
  compare_previous: z.boolean().default(true),
});

interface KPIMetric {
  id: string;
  title: string;
  value: number;
  formatted_value: string;
  previous_value?: number;
  change?: {
    value: number;
    percentage: number;
    type: 'increase' | 'decrease' | 'neutral';
  };
  trend?: Array<{ date: string; value: number }>;
  target?: number;
  unit: string;
  category: 'revenue' | 'orders' | 'customers' | 'products' | 'operations';
  priority: 'high' | 'medium' | 'low';
  description: string;
}

interface KPIDashboard {
  summary: {
    period: string;
    generated_at: string;
    total_metrics: number;
    metrics_with_targets: number;
    metrics_meeting_targets: number;
  };
  metrics: KPIMetric[];
  performance_score: {
    overall_score: number;
    category_scores: {
      revenue: number;
      orders: number;
      customers: number;
      products: number;
      operations: number;
    };
  };
  alerts: Array<{
    metric_id: string;
    alert_type: 'target_missed' | 'significant_change' | 'trend_concern';
    severity: 'high' | 'medium' | 'low';
    message: string;
  }>;
}

/**
 * GET /admin/analytics/kpi
 *
 * Comprehensive KPI dashboard endpoint that provides:
 * - Key performance indicators across all business areas
 * - Period-over-period comparisons
 * - Trend analysis for each metric
 * - Performance scoring and alerts
 * - Target tracking and achievement status
 */
export async function GET(req: MedusaRequest, res: MedusaResponse): Promise<void> {
  try {
    // Validate query parameters
    const query = KPIQuerySchema.parse(req.query);
    const { period, tenant_id, sales_channel_id, compare_previous } = query;

    // Get services using proper Medusa v2 service resolution
    const orderService = req.scope.resolve('order');
    const productService = req.scope.resolve('product');
    const customerService = req.scope.resolve('customer');

    // Calculate date ranges
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
    }

    // Previous period for comparison
    const periodDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const previousStartDate = new Date(startDate);
    const previousEndDate = new Date(startDate);
    previousStartDate.setDate(previousStartDate.getDate() - periodDays);

    // Build filters (without date filtering since Medusa v2 doesn't support it)
    const filters: any = {};
    const previousFilters: any = {};

    if (tenant_id) {
      filters.tenant_id = tenant_id;
      previousFilters.tenant_id = tenant_id;
    }

    if (sales_channel_id) {
      filters.sales_channel_id = sales_channel_id;
      previousFilters.sales_channel_id = sales_channel_id;
    }

    // Fetch current period data using correct method names and supported relations
    const [ordersResult, productsResult] = await Promise.all([
      orderService.listAndCountOrders(filters, { relations: ['items'] }),
      productService.listProducts(tenant_id ? { tenant_id } : {}),
    ]);

    // Extract data from results
    const allOrders = ordersResult[0]; // First element is the orders array
    const products = Array.isArray(productsResult) ? productsResult : productsResult[0] || [];

    // Filter orders by date range in memory (since Medusa v2 doesn't support date filtering)
    const orders = allOrders.filter(order => {
      const orderDate = new Date(order.created_at);
      return orderDate >= startDate && orderDate <= endDate;
    });

    // For customers, count unique customer IDs from filtered orders
    const uniqueCustomerIds = new Set();
    orders.forEach(order => {
      if (order.customer_id) {
        uniqueCustomerIds.add(order.customer_id);
      }
    });
    const totalCustomers = uniqueCustomerIds.size;

    // Filter previous period data for comparison
    let previousOrders = [];
    if (compare_previous) {
      // Filter from the same allOrders data for previous period
      previousOrders = allOrders.filter(order => {
        const orderDate = new Date(order.created_at);
        return orderDate >= previousStartDate && orderDate <= previousEndDate;
      });
    }

    // Calculate metrics
    const totalRevenue = orders.reduce((sum, order) => sum + (order.total || 0), 0);
    const totalOrders = orders.length;
    // totalCustomers is already calculated above from unique customer IDs
    const totalProducts = products.length;
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
    const completedOrders = orders.filter(order => order.status === 'completed');
    const conversionRate = totalOrders > 0 ? (completedOrders.length / totalOrders) * 100 : 0;

    // Previous period metrics
    const previousRevenue = previousOrders.reduce((sum, order) => sum + (order.total || 0), 0);
    const previousOrderCount = previousOrders.length;
    const previousAOV = previousOrderCount > 0 ? previousRevenue / previousOrderCount : 0;

    // Helper function to calculate change
    const calculateChange = (current: number, previous: number) => {
      if (previous === 0) return { value: current, percentage: 0, type: 'neutral' as const };
      const value = current - previous;
      const percentage = (value / previous) * 100;
      const type = value > 0 ? 'increase' : value < 0 ? 'decrease' : 'neutral';
      return { value, percentage, type };
    };

    // Generate trend data for the last 7 days
    const generateTrend = (orders: any[], days: number = 7) => {
      const trend = [];
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dayStart = new Date(date.setHours(0, 0, 0, 0));
        const dayEnd = new Date(date.setHours(23, 59, 59, 999));

        const dayOrders = orders.filter(order => {
          const orderDate = new Date(order.created_at);
          return orderDate >= dayStart && orderDate <= dayEnd;
        });

        trend.push({
          date: dayStart.toISOString().split('T')[0],
          value: dayOrders.reduce((sum, order) => sum + (order.total || 0), 0),
        });
      }
      return trend;
    };

    // Build KPI metrics
    const metrics: KPIMetric[] = [
      // Revenue Metrics
      {
        id: 'total_revenue',
        title: 'Total Revenue',
        value: totalRevenue,
        formatted_value: `₹${totalRevenue.toLocaleString('en-IN')}`,
        previous_value: previousRevenue,
        change: calculateChange(totalRevenue, previousRevenue),
        trend: generateTrend(orders),
        target: 500000, // ₹5 lakh target
        unit: 'INR',
        category: 'revenue',
        priority: 'high',
        description: 'Total revenue generated from all orders',
      },
      {
        id: 'average_order_value',
        title: 'Average Order Value',
        value: averageOrderValue,
        formatted_value: `₹${Math.round(averageOrderValue).toLocaleString('en-IN')}`,
        previous_value: previousAOV,
        change: calculateChange(averageOrderValue, previousAOV),
        target: 2000, // ₹2000 target AOV
        unit: 'INR',
        category: 'revenue',
        priority: 'high',
        description: 'Average value per order',
      },
      // Order Metrics
      {
        id: 'total_orders',
        title: 'Total Orders',
        value: totalOrders,
        formatted_value: totalOrders.toLocaleString('en-IN'),
        previous_value: previousOrderCount,
        change: calculateChange(totalOrders, previousOrderCount),
        target: 500, // 500 orders target
        unit: 'count',
        category: 'orders',
        priority: 'high',
        description: 'Total number of orders placed',
      },
      {
        id: 'conversion_rate',
        title: 'Order Completion Rate',
        value: conversionRate,
        formatted_value: `${conversionRate.toFixed(1)}%`,
        target: 85, // 85% completion rate target
        unit: 'percentage',
        category: 'orders',
        priority: 'medium',
        description: 'Percentage of orders that are completed',
      },
      // Customer Metrics
      {
        id: 'total_customers',
        title: 'Total Customers',
        value: totalCustomers,
        formatted_value: totalCustomers.toLocaleString('en-IN'),
        target: 1000, // 1000 customers target
        unit: 'count',
        category: 'customers',
        priority: 'medium',
        description: 'Total number of registered customers',
      },
      {
        id: 'customer_lifetime_value',
        title: 'Customer Lifetime Value',
        value: totalCustomers > 0 ? totalRevenue / totalCustomers : 0,
        formatted_value: `₹${totalCustomers > 0 ? Math.round(totalRevenue / totalCustomers).toLocaleString('en-IN') : '0'}`,
        target: 5000, // ₹5000 CLV target
        unit: 'INR',
        category: 'customers',
        priority: 'medium',
        description: 'Average revenue per customer',
      },
      // Product Metrics
      {
        id: 'total_products',
        title: 'Total Products',
        value: totalProducts,
        formatted_value: totalProducts.toLocaleString('en-IN'),
        target: 100, // 100 products target
        unit: 'count',
        category: 'products',
        priority: 'low',
        description: 'Total number of products in catalog',
      },
      {
        id: 'products_sold',
        title: 'Products Sold',
        value: orders.reduce((sum, order) => sum + (order.items?.length || 0), 0),
        formatted_value: orders
          .reduce((sum, order) => sum + (order.items?.length || 0), 0)
          .toLocaleString('en-IN'),
        unit: 'count',
        category: 'products',
        priority: 'medium',
        description: 'Total number of product units sold',
      },
      // Operational Metrics
      {
        id: 'pending_orders',
        title: 'Pending Orders',
        value: orders.filter(order => order.status === 'pending').length,
        formatted_value: orders.filter(order => order.status === 'pending').length.toString(),
        target: 10, // Keep pending orders under 10
        unit: 'count',
        category: 'operations',
        priority: 'high',
        description: 'Number of orders awaiting processing',
      },
    ];

    // Calculate performance scores
    const calculateCategoryScore = (categoryMetrics: KPIMetric[]) => {
      const metricsWithTargets = categoryMetrics.filter(m => m.target !== undefined);
      if (metricsWithTargets.length === 0) return 100;

      const scores = metricsWithTargets.map(metric => {
        const target = metric.target!;
        const achievement = Math.min((metric.value / target) * 100, 100);
        return achievement;
      });

      return scores.reduce((sum, score) => sum + score, 0) / scores.length;
    };

    const categoryScores = {
      revenue: calculateCategoryScore(metrics.filter(m => m.category === 'revenue')),
      orders: calculateCategoryScore(metrics.filter(m => m.category === 'orders')),
      customers: calculateCategoryScore(metrics.filter(m => m.category === 'customers')),
      products: calculateCategoryScore(metrics.filter(m => m.category === 'products')),
      operations: calculateCategoryScore(metrics.filter(m => m.category === 'operations')),
    };

    const overallScore = Object.values(categoryScores).reduce((sum, score) => sum + score, 0) / 5;

    // Generate alerts
    const alerts = [];
    metrics.forEach(metric => {
      if (metric.target && metric.value < metric.target * 0.8) {
        alerts.push({
          metric_id: metric.id,
          alert_type: 'target_missed' as const,
          severity: 'high' as const,
          message: `${metric.title} is ${((1 - metric.value / metric.target) * 100).toFixed(1)}% below target`,
        });
      }

      if (metric.change && Math.abs(metric.change.percentage) > 20) {
        alerts.push({
          metric_id: metric.id,
          alert_type: 'significant_change' as const,
          severity: 'medium' as const,
          message: `${metric.title} has ${metric.change.type}d by ${Math.abs(metric.change.percentage).toFixed(1)}%`,
        });
      }
    });

    // Build response
    const dashboard: KPIDashboard = {
      summary: {
        period,
        generated_at: new Date().toISOString(),
        total_metrics: metrics.length,
        metrics_with_targets: metrics.filter(m => m.target !== undefined).length,
        metrics_meeting_targets: metrics.filter(m => m.target && m.value >= m.target).length,
      },
      metrics,
      performance_score: {
        overall_score: Math.round(overallScore),
        category_scores: {
          revenue: Math.round(categoryScores.revenue),
          orders: Math.round(categoryScores.orders),
          customers: Math.round(categoryScores.customers),
          products: Math.round(categoryScores.products),
          operations: Math.round(categoryScores.operations),
        },
      },
      alerts,
    };

    res.status(200).json(dashboard);
  } catch (error) {
    console.error('KPI analytics error:', error);
    res.status(500).json({
      error: 'Failed to fetch KPI analytics',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
