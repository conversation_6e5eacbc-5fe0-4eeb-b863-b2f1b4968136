/**
 * Medusa API Endpoints Tenant Isolation Test
 * 
 * Tests actual Medusa API endpoints to verify tenant isolation works
 * across all standard Medusa Commerce operations.
 */

import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";

interface EndpointTestResult {
  endpoint: string;
  method: string;
  success: boolean;
  statusCode?: number;
  tenantId: string;
  dataCount?: number;
  error?: string;
  responseTime?: number;
}

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const tenantId = req.tenant_id || 'default';
    console.log(`🧪 [MEDUSA-ENDPOINTS-TEST] Testing Medusa API endpoints for tenant: ${tenantId}`);

    const startTime = Date.now();
    const results: any = {
      timestamp: new Date().toISOString(),
      tenantId,
      testSummary: {
        totalEndpoints: 0,
        successfulEndpoints: 0,
        failedEndpoints: 0,
        averageResponseTime: 0
      },
      endpointResults: [],
      crossTenantTest: null
    };

    // Define Medusa API endpoints to test
    const endpointsToTest = [
      { path: '/admin/products', method: 'GET', description: 'List Products' },
      { path: '/admin/customers', method: 'GET', description: 'List Customers' },
      { path: '/admin/orders', method: 'GET', description: 'List Orders' },
      { path: '/admin/product-categories', method: 'GET', description: 'List Product Categories' },
      { path: '/admin/collections', method: 'GET', description: 'List Collections' },
      { path: '/admin/regions', method: 'GET', description: 'List Regions' },
      { path: '/admin/sales-channels', method: 'GET', description: 'List Sales Channels' },
      { path: '/admin/shipping-options', method: 'GET', description: 'List Shipping Options' },
      { path: '/admin/tax-rates', method: 'GET', description: 'List Tax Rates' },
      { path: '/admin/discounts', method: 'GET', description: 'List Discounts' }
    ];

    // Test each endpoint
    for (const endpoint of endpointsToTest) {
      const testResult = await testMedusaEndpoint(req, endpoint, tenantId);
      results.endpointResults.push(testResult);
      results.testSummary.totalEndpoints++;
      
      if (testResult.success) {
        results.testSummary.successfulEndpoints++;
      } else {
        results.testSummary.failedEndpoints++;
      }
    }

    // Test cross-tenant isolation
    results.crossTenantTest = await testCrossTenantIsolation(req);

    // Calculate summary statistics
    const totalResponseTime = results.endpointResults.reduce((sum: number, result: EndpointTestResult) => 
      sum + (result.responseTime || 0), 0);
    results.testSummary.averageResponseTime = totalResponseTime / results.testSummary.totalEndpoints;
    results.testSummary.successRate = `${((results.testSummary.successfulEndpoints / results.testSummary.totalEndpoints) * 100).toFixed(1)}%`;

    // Performance metrics
    results.performance = {
      totalTime: Date.now() - startTime,
      averageTestTime: (Date.now() - startTime) / results.testSummary.totalEndpoints
    };

    console.log(`🧪 [MEDUSA-ENDPOINTS-TEST] Test completed:`, results.testSummary);

    return res.json({
      success: true,
      message: 'Medusa API endpoints tenant isolation test completed',
      results
    });

  } catch (error) {
    console.error(`❌ [MEDUSA-ENDPOINTS-TEST] Test failed:`, error);
    
    return res.status(500).json({
      success: false,
      message: 'Medusa endpoints test failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
};

async function testMedusaEndpoint(req: MedusaRequest, endpoint: any, tenantId: string): Promise<EndpointTestResult> {
  const testResult: EndpointTestResult = {
    endpoint: endpoint.path,
    method: endpoint.method,
    success: false,
    tenantId
  };

  const startTime = Date.now();

  try {
    // Simulate internal API call by using the services directly
    let data = null;
    let count = 0;

    switch (endpoint.path) {
      case '/admin/products':
        const productService = req.scope.resolve('product');
        data = await productService.listProducts({}, { take: 10 });
        count = Array.isArray(data) ? data.length : (data?.products?.length || 0);
        break;

      case '/admin/customers':
        const customerService = req.scope.resolve('customer');
        data = await customerService.listCustomers({}, { take: 10 });
        count = Array.isArray(data) ? data.length : (data?.customers?.length || 0);
        break;

      case '/admin/orders':
        const orderService = req.scope.resolve('order');
        data = await orderService.listOrders({}, { take: 10 });
        count = Array.isArray(data) ? data.length : (data?.orders?.length || 0);
        break;

      case '/admin/product-categories':
        try {
          const categoryService = req.scope.resolve('productCategory');
          data = await categoryService.listCategories({}, { take: 10 });
          count = Array.isArray(data) ? data.length : 0;
        } catch (error) {
          // Try alternative service names
          try {
            const categoryService = req.scope.resolve('product-category');
            data = await categoryService.listCategories({}, { take: 10 });
            count = Array.isArray(data) ? data.length : 0;
          } catch (error2) {
            throw new Error('Product category service not available');
          }
        }
        break;

      case '/admin/collections':
        try {
          const collectionService = req.scope.resolve('productCollection');
          data = await collectionService.listCollections({}, { take: 10 });
          count = Array.isArray(data) ? data.length : 0;
        } catch (error) {
          try {
            const collectionService = req.scope.resolve('collection');
            data = await collectionService.listCollections({}, { take: 10 });
            count = Array.isArray(data) ? data.length : 0;
          } catch (error2) {
            throw new Error('Collection service not available');
          }
        }
        break;

      case '/admin/regions':
        const regionService = req.scope.resolve('region');
        data = await regionService.listRegions({}, { take: 10 });
        count = Array.isArray(data) ? data.length : 0;
        break;

      default:
        throw new Error(`Endpoint ${endpoint.path} not implemented in test`);
    }

    testResult.success = true;
    testResult.statusCode = 200;
    testResult.dataCount = count;
    testResult.responseTime = Date.now() - startTime;

  } catch (error) {
    testResult.success = false;
    testResult.statusCode = 500;
    testResult.error = error instanceof Error ? error.message : 'Unknown error';
    testResult.responseTime = Date.now() - startTime;
  }

  return testResult;
}

async function testCrossTenantIsolation(req: MedusaRequest): Promise<any> {
  const currentTenantId = req.tenant_id || 'default';
  const testTenantId = 'test-tenant-' + Date.now();

  console.log(`🧪 [MEDUSA-ENDPOINTS-TEST] Testing cross-tenant isolation: ${currentTenantId} vs ${testTenantId}`);

  try {
    // Test with current tenant
    const productService1 = req.scope.resolve('product');
    const currentTenantProducts = await productService1.listProducts({}, { take: 5 });
    const currentCount = Array.isArray(currentTenantProducts) ? currentTenantProducts.length : 0;

    // Create mock request with different tenant
    const mockReq = {
      ...req,
      tenant_id: testTenantId,
      tenantId: testTenantId
    } as MedusaRequest;

    // Test with different tenant (same service instance, but different context)
    const productService2 = mockReq.scope.resolve('product');
    const differentTenantProducts = await productService2.listProducts({}, { take: 5 });
    const differentCount = Array.isArray(differentTenantProducts) ? differentTenantProducts.length : 0;

    return {
      success: true,
      currentTenant: {
        id: currentTenantId,
        productCount: currentCount
      },
      testTenant: {
        id: testTenantId,
        productCount: differentCount
      },
      isolationWorking: currentCount === differentCount, // Same data = no isolation yet
      message: currentCount === differentCount 
        ? 'Same data returned - RLS not yet implemented' 
        : 'Different data returned - isolation working'
    };

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Cross-tenant isolation test failed'
    };
  }
}

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const tenantId = req.tenant_id || 'default';
    const { endpoint, testData } = req.body;

    console.log(`🧪 [MEDUSA-ENDPOINTS-TEST] Testing CREATE operation for ${endpoint} with tenant: ${tenantId}`);

    let result = null;

    switch (endpoint) {
      case '/admin/products':
        const productService = req.scope.resolve('product');
        const productData = {
          title: testData?.title || `Test Product ${Date.now()}`,
          description: testData?.description || 'Test product for tenant verification',
          handle: testData?.handle || `test-product-${Date.now()}`,
          status: 'draft',
          ...testData
        };
        result = await productService.createProducts([productData]);
        break;

      case '/admin/customers':
        const customerService = req.scope.resolve('customer');
        const customerData = {
          email: testData?.email || `test-${Date.now()}@example.com`,
          first_name: testData?.first_name || 'Test',
          last_name: testData?.last_name || 'Customer',
          ...testData
        };
        result = await customerService.createCustomers([customerData]);
        break;

      default:
        throw new Error(`CREATE test for ${endpoint} not implemented`);
    }

    return res.json({
      success: true,
      message: `CREATE operation tested for ${endpoint}`,
      data: {
        endpoint,
        tenantId,
        created: result,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error(`❌ [MEDUSA-ENDPOINTS-TEST] CREATE test failed:`, error);
    
    return res.status(500).json({
      success: false,
      message: 'CREATE operation test failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
};
