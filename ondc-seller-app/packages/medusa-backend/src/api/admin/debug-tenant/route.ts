/**
 * Debug Tenant Context Endpoint
 * 
 * Simple endpoint to debug tenant context extraction and middleware functionality.
 */

import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log(`🔍 [DEBUG-TENANT] Debugging tenant context`);

    const debugInfo = {
      timestamp: new Date().toISOString(),
      request: {
        method: req.method,
        path: req.path,
        url: req.url
      },
      headers: {
        'x-tenant-id': req.headers['x-tenant-id'],
        'authorization': req.headers['authorization'] ? 'Bearer ***' : 'Not provided',
        'content-type': req.headers['content-type'],
        'user-agent': req.headers['user-agent']
      },
      tenantContext: {
        'req.tenant_id': (req as any).tenant_id,
        'req.tenantId': (req as any).tenantId,
        'req.tenant': (req as any).tenant
      },
      query: req.query,
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        DEFAULT_TENANT_ID: process.env.DEFAULT_TENANT_ID
      }
    };

    console.log(`🔍 [DEBUG-TENANT] Debug info:`, debugInfo);

    return res.json({
      success: true,
      message: 'Tenant context debug information',
      data: debugInfo
    });

  } catch (error) {
    console.error(`❌ [DEBUG-TENANT] Debug failed:`, error);
    
    return res.status(500).json({
      success: false,
      message: 'Tenant debug failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
};
