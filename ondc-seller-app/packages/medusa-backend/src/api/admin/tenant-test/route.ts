/**
 * Tenant API Testing Endpoint
 * 
 * Comprehensive testing endpoint for tenant-aware services and data isolation.
 * Tests all CRUD operations with tenant context.
 */

import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { TenantServiceFactory } from "../../../services/tenant-service-factory";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log(`🧪 [TENANT-TEST] Starting comprehensive tenant API test`);
    console.log(`🧪 [TENANT-TEST] Request headers:`, req.headers);
    console.log(`🧪 [TENANT-TEST] Tenant ID from middleware:`, req.tenant_id);

    const startTime = Date.now();
    const results: any = {
      timestamp: new Date().toISOString(),
      tenantId: req.tenant_id || 'default',
      tests: {},
      summary: {
        totalTests: 0,
        passedTests: 0,
        failedTests: 0,
        errors: []
      }
    };

    // Test 1: Tenant Service Factory Creation
    console.log(`🧪 [TENANT-TEST] Test 1: Service Factory Creation`);
    try {
      const services = TenantServiceFactory.fromRequest(req);
      results.tests.serviceFactory = {
        success: true,
        message: 'Service factory created successfully',
        tenantId: services.getTenantId()
      };
      results.summary.passedTests++;
    } catch (error) {
      results.tests.serviceFactory = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      results.summary.failedTests++;
      results.summary.errors.push(`Service Factory: ${error}`);
    }
    results.summary.totalTests++;

    // Test 2: Product Service Operations
    console.log(`🧪 [TENANT-TEST] Test 2: Product Service Operations`);
    try {
      const services = TenantServiceFactory.fromRequest(req);
      
      // Test product listing
      const products = await services.product.listProducts({}, { take: 5 });
      
      // Test product statistics
      const stats = await services.product.getProductStats();
      
      results.tests.productService = {
        success: true,
        message: 'Product service operations completed',
        data: {
          productsFound: Array.isArray(products) ? products.length : 0,
          statistics: stats
        }
      };
      results.summary.passedTests++;
    } catch (error) {
      results.tests.productService = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      results.summary.failedTests++;
      results.summary.errors.push(`Product Service: ${error}`);
    }
    results.summary.totalTests++;

    // Test 3: Customer Service Operations
    console.log(`🧪 [TENANT-TEST] Test 3: Customer Service Operations`);
    try {
      const services = TenantServiceFactory.fromRequest(req);
      
      // Test customer listing
      const customers = await services.customer.listCustomers({}, { take: 5 });
      
      // Test customer statistics
      const stats = await services.customer.getCustomerStats();
      
      results.tests.customerService = {
        success: true,
        message: 'Customer service operations completed',
        data: {
          customersFound: Array.isArray(customers) ? customers.length : 0,
          statistics: stats
        }
      };
      results.summary.passedTests++;
    } catch (error) {
      results.tests.customerService = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      results.summary.failedTests++;
      results.summary.errors.push(`Customer Service: ${error}`);
    }
    results.summary.totalTests++;

    // Test 4: Order Service Operations
    console.log(`🧪 [TENANT-TEST] Test 4: Order Service Operations`);
    try {
      const services = TenantServiceFactory.fromRequest(req);
      
      // Test order listing
      const orders = await services.order.listOrders({}, { take: 5 });
      
      // Test order statistics
      const stats = await services.order.getOrderStats();
      
      results.tests.orderService = {
        success: true,
        message: 'Order service operations completed',
        data: {
          ordersFound: Array.isArray(orders) ? orders.length : 0,
          statistics: stats
        }
      };
      results.summary.passedTests++;
    } catch (error) {
      results.tests.orderService = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      results.summary.failedTests++;
      results.summary.errors.push(`Order Service: ${error}`);
    }
    results.summary.totalTests++;

    // Test 5: Tenant Context Validation
    console.log(`🧪 [TENANT-TEST] Test 5: Tenant Context Validation`);
    try {
      const validation = TenantServiceFactory.validateTenantContext(req);
      
      results.tests.tenantValidation = {
        success: validation.isValid,
        message: validation.isValid ? 'Tenant context is valid' : 'Tenant context validation failed',
        data: validation
      };
      
      if (validation.isValid) {
        results.summary.passedTests++;
      } else {
        results.summary.failedTests++;
        results.summary.errors.push(`Tenant Validation: ${validation.errors.join(', ')}`);
      }
    } catch (error) {
      results.tests.tenantValidation = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      results.summary.failedTests++;
      results.summary.errors.push(`Tenant Validation: ${error}`);
    }
    results.summary.totalTests++;

    // Test 6: Cross-Tenant Data Isolation
    console.log(`🧪 [TENANT-TEST] Test 6: Cross-Tenant Data Isolation`);
    try {
      const currentServices = TenantServiceFactory.fromRequest(req);
      
      // Create a mock request with different tenant
      const mockReq = {
        ...req,
        tenant_id: 'different-tenant-' + Date.now()
      } as MedusaRequest;
      
      const differentServices = TenantServiceFactory.fromRequest(mockReq);
      
      // Compare tenant IDs
      const currentTenant = currentServices.getTenantId();
      const differentTenant = differentServices.getTenantId();
      
      const isolationWorking = currentTenant !== differentTenant;
      
      results.tests.dataIsolation = {
        success: isolationWorking,
        message: isolationWorking ? 'Data isolation working correctly' : 'Data isolation failed',
        data: {
          currentTenant,
          differentTenant,
          isolated: isolationWorking
        }
      };
      
      if (isolationWorking) {
        results.summary.passedTests++;
      } else {
        results.summary.failedTests++;
        results.summary.errors.push('Data Isolation: Tenants not properly isolated');
      }
    } catch (error) {
      results.tests.dataIsolation = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      results.summary.failedTests++;
      results.summary.errors.push(`Data Isolation: ${error}`);
    }
    results.summary.totalTests++;

    // Calculate final metrics
    const endTime = Date.now();
    results.performance = {
      totalTime: endTime - startTime,
      averageTestTime: (endTime - startTime) / results.summary.totalTests
    };

    results.summary.successRate = `${((results.summary.passedTests / results.summary.totalTests) * 100).toFixed(1)}%`;
    results.summary.overallSuccess = results.summary.failedTests === 0;

    console.log(`🧪 [TENANT-TEST] Test completed:`, results.summary);

    return res.json({
      success: true,
      message: 'Tenant API test completed',
      results
    });

  } catch (error) {
    console.error(`❌ [TENANT-TEST] Test failed:`, error);
    
    return res.status(500).json({
      success: false,
      message: 'Tenant API test failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
};

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log(`🧪 [TENANT-TEST] Starting tenant data creation test`);
    
    const { testType, data } = req.body;
    const services = TenantServiceFactory.fromRequest(req);
    
    const results: any = {
      timestamp: new Date().toISOString(),
      tenantId: req.tenant_id || 'default',
      testType,
      success: false,
      data: null,
      error: null
    };

    switch (testType) {
      case 'create-product':
        console.log(`🧪 [TENANT-TEST] Creating test product for tenant: ${req.tenant_id}`);
        
        const productData = {
          title: data?.title || `Test Product ${Date.now()}`,
          description: data?.description || 'Test product created by tenant API test',
          handle: data?.handle || `test-product-${Date.now()}`,
          status: 'draft',
          ...data
        };
        
        const createdProduct = await services.product.createProducts([productData]);
        
        results.success = true;
        results.data = createdProduct;
        results.message = 'Product created successfully';
        break;

      case 'create-customer':
        console.log(`🧪 [TENANT-TEST] Creating test customer for tenant: ${req.tenant_id}`);
        
        const customerData = {
          email: data?.email || `test-${Date.now()}@example.com`,
          first_name: data?.first_name || 'Test',
          last_name: data?.last_name || 'Customer',
          ...data
        };
        
        const createdCustomer = await services.customer.createCustomers([customerData]);
        
        results.success = true;
        results.data = createdCustomer;
        results.message = 'Customer created successfully';
        break;

      default:
        throw new Error(`Unknown test type: ${testType}`);
    }

    console.log(`✅ [TENANT-TEST] Creation test completed:`, results);

    return res.json(results);

  } catch (error) {
    console.error(`❌ [TENANT-TEST] Creation test failed:`, error);
    
    return res.status(500).json({
      success: false,
      message: 'Tenant creation test failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
};
