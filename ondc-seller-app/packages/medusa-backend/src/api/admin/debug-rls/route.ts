/**
 * Debug RLS API
 * 
 * Simple test to debug Row Level Security implementation
 */

import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { Pool } from 'pg';

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const tenantId = req.tenant_id || 'default';
    console.log(`🐛 [DEBUG-RLS] Testing RLS for tenant: ${tenantId}`);

    // Create database connection
    const pool = new Pool({
      user: 'medusa_app',
      password: 'medusa_app_password',
      host: 'localhost',
      port: 5432,
      database: 'medusa_backend',
      max: 1,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    });

    const client = await pool.connect();
    
    try {
      // Start a transaction to ensure all operations use the same connection
      await client.query('BEGIN');

      // Test 1: Check current tenant context (should be empty initially)
      const initialContext = await client.query('SELECT current_setting($1, true) as initial_context', [
        'app.tenant_context.tenant_id'
      ]);
      console.log(`🐛 [DEBUG-RLS] Initial tenant context: "${initialContext.rows[0].initial_context}"`);

      // Test 2: Set tenant context using different methods
      console.log(`🐛 [DEBUG-RLS] Setting tenant context to: ${tenantId}`);

      // Method 1: Using set_config function
      const setConfigResult = await client.query('SELECT set_config($1, $2, false) as set_result', [
        'app.tenant_context.tenant_id',
        tenantId
      ]);
      console.log(`🐛 [DEBUG-RLS] set_config result:`, setConfigResult.rows[0]);

      // Test 3: Verify tenant context was set
      const verifyContext = await client.query('SELECT current_setting($1, true) as current_context', [
        'app.tenant_context.tenant_id'
      ]);
      console.log(`🐛 [DEBUG-RLS] Verified tenant context: "${verifyContext.rows[0].current_context}"`);

      // Test 4: Try alternative method if first didn't work
      if (!verifyContext.rows[0].current_context || verifyContext.rows[0].current_context === '') {
        console.log(`🐛 [DEBUG-RLS] First method failed, trying alternative...`);

        // Method 2: Using SET command
        await client.query(`SET app.tenant_context.tenant_id = '${tenantId}'`);

        const verifyContext2 = await client.query('SELECT current_setting($1, true) as current_context', [
          'app.tenant_context.tenant_id'
        ]);
        console.log(`🐛 [DEBUG-RLS] Alternative method result: "${verifyContext2.rows[0].current_context}"`);
      }

      // Test 5: Count products with RLS
      const productCount = await client.query('SELECT COUNT(*) as count FROM product');
      console.log(`🐛 [DEBUG-RLS] Product count with RLS: ${productCount.rows[0].count}`);

      // Test 6: Get sample products
      const sampleProducts = await client.query('SELECT id, title, tenant_id FROM product LIMIT 3');
      console.log(`🐛 [DEBUG-RLS] Sample products:`, sampleProducts.rows);

      // Test 7: Count all tenant data
      const allTenantCounts = await client.query(`
        SELECT
          (SELECT COUNT(*) FROM product) as products,
          (SELECT COUNT(*) FROM customer) as customers,
          (SELECT COUNT(*) FROM cart) as carts,
          (SELECT COUNT(*) FROM "order") as orders
      `);
      console.log(`🐛 [DEBUG-RLS] All counts:`, allTenantCounts.rows[0]);

      // Commit transaction
      await client.query('COMMIT');

      const response = {
        success: true,
        message: `RLS debug test completed for tenant: ${tenantId}`,
        debug_info: {
          tenant_id: tenantId,
          initial_context: initialContext.rows[0].initial_context,
          set_context: tenantId,
          verified_context: verifyContext.rows[0].current_context,
          product_count: parseInt(productCount.rows[0].count),
          sample_products: sampleProducts.rows,
          all_counts: allTenantCounts.rows[0],
          context_matches: verifyContext.rows[0].current_context === tenantId,
          rls_working: parseInt(productCount.rows[0].count) > 0 || tenantId === 'default'
        },
        timestamp: new Date().toISOString()
      };

      client.release();
      await pool.end();

      return res.json(response);

    } catch (error) {
      client.release();
      await pool.end();
      throw error;
    }

  } catch (error) {
    console.error(`❌ [DEBUG-RLS] Test failed: ${error}`);
    
    return res.status(500).json({
      success: false,
      message: 'RLS debug test failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      tenant_id: req.tenant_id || 'default',
      timestamp: new Date().toISOString()
    });
  }
};
