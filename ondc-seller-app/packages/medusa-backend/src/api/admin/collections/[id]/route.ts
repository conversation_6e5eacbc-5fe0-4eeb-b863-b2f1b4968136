import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
) {
  console.log(`🚀 [TENANT FILTER] === CUSTOM COLLECTION GET BY ID ENDPOINT CALLED ===`)

  try {
    // Extract tenant ID from header
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    // Get collection ID from URL params
    const collectionId = req.params?.id as string
    
    if (!collectionId) {
      return res.status(400).json({
        error: 'Collection ID is required',
        tenant_id: tenantId
      })
    }

    console.log(`🔍 [TENANT FILTER] Getting collection ${collectionId} for tenant: ${tenantId}`)

    // Direct database connection approach
    const { Client } = require('pg')
    const client = new Client({
      connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
    })

    let collection: any = null

    try {
      await client.connect()
      console.log(`🔗 [TENANT FILTER] Connected to database directly`)

      // Get collection with tenant validation
      const result = await client.query(`
        SELECT 
          id, title, handle, metadata, created_at, updated_at, 
          deleted_at, tenant_id
        FROM product_collection 
        WHERE id = $1 AND tenant_id = $2 AND deleted_at IS NULL
      `, [collectionId, tenantId])

      collection = result.rows[0] || null
      console.log(`📦 [TENANT FILTER] Retrieved collection: ${collection ? 'Found' : 'Not Found'}`)

      if (collection) {
        // Parse metadata if it's a string
        if (typeof collection.metadata === 'string') {
          try {
            collection.metadata = JSON.parse(collection.metadata)
          } catch (e) {
            console.log(`⚠️ Could not parse metadata for collection ${collection.id}`)
          }
        }
      }

      await client.end()

    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error:', dbError)
      await client.end().catch(() => {})
      throw dbError
    }

    if (!collection) {
      return res.status(404).json({
        error: 'Collection not found or access denied',
        collection_id: collectionId,
        tenant_id: tenantId,
        _debug: {
          message: 'Collection either does not exist or belongs to a different tenant'
        }
      })
    }

    // Return response in Medusa format
    const response = {
      collection,
      _tenant: {
        id: tenantId,
        filtered: true,
        method: 'direct_db_connection'
      }
    }

    // Add tenant headers
    res.setHeader('X-Tenant-ID', tenantId)
    res.setHeader('X-Tenant-Filtered', 'true')

    console.log(`📤 [TENANT FILTER] Returning collection ${collectionId} for tenant ${tenantId}`)
    
    res.json(response)

  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error getting collection:', error)
    
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    res.status(500).json({
      error: 'Failed to get collection',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'tenant_collection_get_error',
        timestamp: new Date().toISOString()
      }
    })
  }
}

export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
) {
  console.log(`🚀 [TENANT FILTER] === CUSTOM COLLECTION UPDATE ENDPOINT CALLED ===`)
  console.log(`🚀 [TENANT FILTER] Body:`, JSON.stringify(req.body, null, 2))

  try {
    // Extract tenant ID from header
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    // Get collection ID from URL params
    const collectionId = req.params?.id as string
    
    if (!collectionId) {
      return res.status(400).json({
        error: 'Collection ID is required for update',
        tenant_id: tenantId
      })
    }

    console.log(`🔄 [TENANT FILTER] Updating collection ${collectionId} for tenant: ${tenantId}`)

    // Get update data from request body
    const updateData = req.body as any

    // Remove tenant_id from update data to prevent modification
    const { tenant_id: _, ...safeUpdateData } = updateData

    // Direct database connection approach
    const { Client } = require('pg')
    const client = new Client({
      connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
    })

    let updatedCollection: any = null

    try {
      await client.connect()
      console.log(`🔗 [TENANT FILTER] Connected to database directly`)

      // First, verify the collection belongs to this tenant
      const checkQuery = 'SELECT id FROM product_collection WHERE id = $1 AND tenant_id = $2 AND deleted_at IS NULL'
      const checkResult = await client.query(checkQuery, [collectionId, tenantId])

      if (checkResult.rows.length === 0) {
        await client.end()
        return res.status(404).json({
          error: 'Collection not found or access denied',
          collection_id: collectionId,
          tenant_id: tenantId,
          _debug: {
            message: 'Collection either does not exist or belongs to a different tenant'
          }
        })
      }

      // Check if new handle already exists for this tenant (if handle is being updated)
      if (safeUpdateData.handle) {
        const existingResult = await client.query(
          'SELECT id FROM product_collection WHERE handle = $1 AND tenant_id = $2 AND id != $3 AND deleted_at IS NULL',
          [safeUpdateData.handle, tenantId, collectionId]
        )
        
        if (existingResult.rows.length > 0) {
          await client.end()
          return res.status(400).json({
            type: 'invalid_data',
            message: `Collection with handle: ${safeUpdateData.handle}, already exists for tenant: ${tenantId}.`,
            tenant_id: tenantId
          })
        }
      }

      // Update the collection (tenant_id cannot be changed)
      const updateQuery = `
        UPDATE product_collection 
        SET 
          title = COALESCE($1, title),
          handle = COALESCE($2, handle),
          metadata = COALESCE($3, metadata),
          updated_at = NOW()
        WHERE id = $4 AND tenant_id = $5
        RETURNING *
      `

      const values = [
        safeUpdateData.title,
        safeUpdateData.handle,
        safeUpdateData.metadata ? JSON.stringify({
          ...safeUpdateData.metadata,
          tenant_id: tenantId
        }) : null,
        collectionId,
        tenantId
      ]

      const result = await client.query(updateQuery, values)
      updatedCollection = result.rows[0]

      // Parse metadata
      if (typeof updatedCollection.metadata === 'string') {
        try {
          updatedCollection.metadata = JSON.parse(updatedCollection.metadata)
        } catch (e) {
          console.log(`⚠️ Could not parse metadata for updated collection ${updatedCollection.id}`)
        }
      }

      console.log(`✅ [TENANT FILTER] Updated collection ${collectionId} for tenant: ${tenantId}`)

      await client.end()

    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error:', dbError)
      await client.end().catch(() => {})
      throw dbError
    }

    // Return response in Medusa format
    const response = {
      collection: updatedCollection,
      _tenant: {
        id: tenantId,
        validated: true,
        method: 'direct_db_connection'
      }
    }

    // Add tenant headers
    res.setHeader('X-Tenant-ID', tenantId)
    res.setHeader('X-Tenant-Validated', 'true')

    console.log(`📤 [TENANT FILTER] Returning updated collection for tenant ${tenantId}`)
    
    res.json(response)

  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error updating collection:', error)
    
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    res.status(500).json({
      error: 'Failed to update collection',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'tenant_collection_update_error',
        timestamp: new Date().toISOString()
      }
    })
  }
}

export async function DELETE(
  req: MedusaRequest,
  res: MedusaResponse
) {
  console.log(`🚀 [TENANT FILTER] === CUSTOM COLLECTION DELETE ENDPOINT CALLED ===`)

  try {
    // Extract tenant ID from header
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    // Get collection ID from URL params
    const collectionId = req.params?.id as string
    
    if (!collectionId) {
      return res.status(400).json({
        error: 'Collection ID is required for deletion',
        tenant_id: tenantId
      })
    }

    console.log(`🗑️ [TENANT FILTER] Deleting collection ${collectionId} for tenant: ${tenantId}`)

    // Direct database connection approach
    const { Client } = require('pg')
    const client = new Client({
      connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
    })

    let deletedCollection: any = null

    try {
      await client.connect()
      console.log(`🔗 [TENANT FILTER] Connected to database directly`)

      // First, verify the collection belongs to this tenant and get it
      const checkQuery = 'SELECT * FROM product_collection WHERE id = $1 AND tenant_id = $2 AND deleted_at IS NULL'
      const checkResult = await client.query(checkQuery, [collectionId, tenantId])

      if (checkResult.rows.length === 0) {
        await client.end()
        return res.status(404).json({
          error: 'Collection not found or access denied',
          collection_id: collectionId,
          tenant_id: tenantId,
          _debug: {
            message: 'Collection either does not exist or belongs to a different tenant'
          }
        })
      }

      // Soft delete the collection (set deleted_at timestamp)
      const deleteQuery = 'UPDATE product_collection SET deleted_at = NOW(), updated_at = NOW() WHERE id = $1 AND tenant_id = $2 RETURNING *'
      const result = await client.query(deleteQuery, [collectionId, tenantId])
      deletedCollection = result.rows[0]

      console.log(`✅ [TENANT FILTER] Soft deleted collection ${collectionId} for tenant: ${tenantId}`)

      await client.end()

    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error:', dbError)
      await client.end().catch(() => {})
      throw dbError
    }

    // Return response in Medusa format
    const response = {
      id: collectionId,
      object: 'collection',
      deleted: true,
      _tenant: {
        id: tenantId,
        validated: true,
        method: 'direct_db_connection'
      }
    }

    // Add tenant headers
    res.setHeader('X-Tenant-ID', tenantId)
    res.setHeader('X-Tenant-Validated', 'true')

    console.log(`📤 [TENANT FILTER] Confirmed deletion of collection ${collectionId} for tenant ${tenantId}`)
    
    res.json(response)

  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error deleting collection:', error)
    
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    res.status(500).json({
      error: 'Failed to delete collection',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'tenant_collection_delete_error',
        timestamp: new Date().toISOString()
      }
    })
  }
}