import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
) {
  console.log(`🚀 [TENANT FILTER] === CUSTOM COLLECTIONS ENDPOINT CALLED ===`)
  console.log(`🚀 [TENANT FILTER] Headers:`, JSON.stringify(req.headers, null, 2))
  console.log(`🚀 [TENANT FILTER] Query:`, JSON.stringify(req.query, null, 2))

  try {
    // Extract tenant ID from header
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    console.log(`🔍 [TENANT FILTER] Processing collections request for tenant: ${tenantId}`)

    // Get query parameters
    const {
      limit = 50,
      offset = 0
    } = req.query

    // Direct database connection approach
    const { Client } = require('pg')
    const client = new Client({
      connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
    })

    let collections: any[] = []
    let count = 0

    try {
      await client.connect()
      console.log(`🔗 [TENANT FILTER] Connected to database directly`)

      // Get total count for this tenant (exclude soft-deleted)
      const countResult = await client.query(
        'SELECT COUNT(*) as total FROM product_collection WHERE tenant_id = $1 AND deleted_at IS NULL',
        [tenantId]
      )
      count = parseInt(countResult.rows[0]?.total || 0)
      console.log(`📊 [TENANT FILTER] Total collections for tenant ${tenantId}: ${count}`)

      // Get collections for this tenant (exclude soft-deleted)
      const result = await client.query(`
        SELECT 
          id, title, handle, metadata, created_at, updated_at, 
          deleted_at, tenant_id
        FROM product_collection 
        WHERE tenant_id = $1 AND deleted_at IS NULL
        ORDER BY created_at DESC 
        LIMIT $2 OFFSET $3
      `, [tenantId, parseInt(limit as string), parseInt(offset as string)])

      collections = result.rows || []

      // Parse metadata for each collection
      collections.forEach(collection => {
        if (typeof collection.metadata === 'string') {
          try {
            collection.metadata = JSON.parse(collection.metadata)
          } catch (e) {
            console.log(`⚠️ Could not parse metadata for collection ${collection.id}`)
          }
        }
      })

      console.log(`📦 [TENANT FILTER] Retrieved ${collections.length} collections`)

      await client.end()

    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error:', dbError)
      await client.end().catch(() => {})
    }

    // Return response in Medusa format
    const response = {
      collections,
      count: collections.length,
      offset: parseInt(offset as string),
      limit: parseInt(limit as string),
      // Add tenant info for debugging
      _tenant: {
        id: tenantId,
        filtered: true,
        method: 'direct_db_connection',
        total_in_db: count
      }
    }

    // Add tenant headers for debugging
    res.setHeader('X-Tenant-ID', tenantId)
    res.setHeader('X-Tenant-Filtered', 'true')
    res.setHeader('X-Collections-Count', collections.length.toString())

    console.log(`📤 [TENANT FILTER] Returning response:`, {
      collections_count: collections.length,
      total_count: count,
      tenant_id: tenantId
    })
    
    res.json(response)

  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error getting collections:', error)
    
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    res.status(500).json({
      error: 'Failed to get collections',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'tenant_collections_get_error',
        timestamp: new Date().toISOString()
      }
    })
  }
}

export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
) {
  console.log(`🚀 [TENANT FILTER] === CUSTOM COLLECTION CREATE ENDPOINT CALLED ===`)
  console.log(`🚀 [TENANT FILTER] Body:`, JSON.stringify(req.body, null, 2))

  try {
    // Extract tenant ID from header
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    console.log(`🔄 [TENANT FILTER] Creating collection for tenant: ${tenantId}`)

    // Get collection data from request body
    const collectionData = req.body as any

    // Ensure tenant_id is injected and cannot be modified
    const collectionWithTenant = {
      ...collectionData,
      tenant_id: tenantId
    }

    // Direct database connection approach
    const { Client } = require('pg')
    const client = new Client({
      connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
    })

    let createdCollection: any = null

    try {
      await client.connect()
      console.log(`🔗 [TENANT FILTER] Connected to database for collection creation`)

      // Generate collection ID
      const collectionId = `pcol_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`

      // Check if handle already exists for this tenant
      if (collectionWithTenant.handle) {
        const existingResult = await client.query(
          'SELECT id FROM product_collection WHERE handle = $1 AND tenant_id = $2',
          [collectionWithTenant.handle, tenantId]
        )
        
        if (existingResult.rows.length > 0) {
          await client.end()
          return res.status(400).json({
            type: 'invalid_data',
            message: `Collection with handle: ${collectionWithTenant.handle}, already exists for tenant: ${tenantId}.`,
            tenant_id: tenantId
          })
        }
      }

      // Insert collection
      const insertCollectionQuery = `
        INSERT INTO product_collection (
          id, title, handle, metadata, tenant_id, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
        RETURNING *
      `

      const collectionValues = [
        collectionId,
        collectionWithTenant.title,
        collectionWithTenant.handle,
        collectionWithTenant.metadata ? JSON.stringify({
          ...collectionWithTenant.metadata,
          tenant_id: tenantId
        }) : JSON.stringify({ tenant_id: tenantId }),
        tenantId
      ]

      const result = await client.query(insertCollectionQuery, collectionValues)
      createdCollection = result.rows[0]

      // Parse metadata
      if (typeof createdCollection.metadata === 'string') {
        try {
          createdCollection.metadata = JSON.parse(createdCollection.metadata)
        } catch (e) {
          console.log(`⚠️ Could not parse metadata for created collection ${createdCollection.id}`)
        }
      }

      console.log(`✅ [TENANT FILTER] Created collection ${collectionId} for tenant: ${tenantId}`)

      await client.end()

    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error:', dbError)
      await client.end().catch(() => {})
      throw dbError
    }

    // Return response in Medusa format
    const response = {
      collection: createdCollection,
      _tenant: {
        id: tenantId,
        injected: true,
        method: 'direct_db_creation'
      }
    }

    // Add tenant headers
    res.setHeader('X-Tenant-ID', tenantId)
    res.setHeader('X-Tenant-Injected', 'true')

    console.log(`📤 [TENANT FILTER] Returning created collection for tenant ${tenantId}`)
    
    res.json(response)

  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error creating collection:', error)
    
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    res.status(500).json({
      error: 'Failed to create collection',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'tenant_collection_create_error',
        timestamp: new Date().toISOString(),
        stack: error.stack
      }
    })
  }
}