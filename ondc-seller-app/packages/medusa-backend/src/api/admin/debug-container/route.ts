/**
 * Debug Container Endpoint
 * 
 * Helps debug what services and connections are available in the Medusa container.
 */

import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log(`🔍 [DEBUG-CONTAINER] Inspecting Medusa container`);

    const containerInfo: any = {
      timestamp: new Date().toISOString(),
      availableServices: [],
      databaseConnections: [],
      containerKeys: [],
      errors: []
    };

    // Get all registered services/keys in the container
    try {
      // Try to access the container's registration map
      const container = req.scope;
      
      // Try different ways to get container information
      if (container && typeof container === 'object') {
        // Get all keys if possible
        try {
          const keys = Object.keys(container);
          containerInfo.containerKeys = keys.slice(0, 50); // Limit to first 50 keys
        } catch (error) {
          containerInfo.errors.push(`Error getting container keys: ${error}`);
        }
      }
    } catch (error) {
      containerInfo.errors.push(`Error accessing container: ${error}`);
    }

    // Test common service names
    const commonServices = [
      'product', 'customer', 'order', 'cart', 'inventory', 'pricing',
      'productService', 'customerService', 'orderService',
      'manager', 'dbConnection', 'database', 'dataSource',
      '__pg_connection__', 'pgConnection', 'db', 'connection'
    ];

    for (const serviceName of commonServices) {
      try {
        const service = req.scope.resolve(serviceName);
        containerInfo.availableServices.push({
          name: serviceName,
          type: typeof service,
          available: true,
          hasQuery: typeof service?.query === 'function',
          constructor: service?.constructor?.name || 'Unknown'
        });
        
        // Check if this might be a database connection
        if (typeof service?.query === 'function') {
          containerInfo.databaseConnections.push({
            name: serviceName,
            type: service?.constructor?.name || 'Unknown',
            hasQuery: true
          });
        }
      } catch (error) {
        containerInfo.availableServices.push({
          name: serviceName,
          available: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // Try to get database manager/connection through different paths
    const dbPaths = [
      'manager',
      'dbConnection', 
      'database',
      'dataSource',
      '__pg_connection__'
    ];

    for (const path of dbPaths) {
      try {
        const connection = req.scope.resolve(path);
        if (connection && typeof connection.query === 'function') {
          // Test a simple query
          try {
            await connection.query('SELECT 1 as test');
            containerInfo.databaseConnections.push({
              name: path,
              working: true,
              tested: true
            });
          } catch (queryError) {
            containerInfo.databaseConnections.push({
              name: path,
              working: false,
              tested: true,
              queryError: queryError instanceof Error ? queryError.message : 'Unknown error'
            });
          }
        }
      } catch (error) {
        // Service not available
      }
    }

    console.log(`🔍 [DEBUG-CONTAINER] Container inspection complete`);

    return res.json({
      success: true,
      message: 'Container debug information',
      data: containerInfo
    });

  } catch (error) {
    console.error(`❌ [DEBUG-CONTAINER] Debug failed:`, error);
    
    return res.status(500).json({
      success: false,
      message: 'Container debug failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
};
