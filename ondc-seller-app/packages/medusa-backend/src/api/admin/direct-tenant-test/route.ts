/**
 * Direct Tenant Database Test API
 * 
 * Tests tenant isolation using direct database queries with RLS.
 * This bypasses Medusa services to ensure true tenant isolation.
 */

import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { DirectDatabaseService } from "../../../services/direct-database-service";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const tenantId = req.tenant_id || 'default';
    console.log(`🧪 [DIRECT-TENANT-TEST] Testing direct database access for tenant: ${tenantId}`);

    const startTime = Date.now();
    
    // Create direct database service
    const dbService = DirectDatabaseService.fromRequest(req);

    // Test all entity types
    const [
      productsData,
      customersData,
      cartsData,
      ordersData,
      stats
    ] = await Promise.all([
      dbService.getProducts(5, 0),
      dbService.getCustomers(5, 0),
      dbService.getCarts(5, 0),
      dbService.getOrders(5, 0),
      dbService.getStats()
    ]);

    // Build comprehensive response
    const response = {
      success: true,
      message: `Direct database tenant test completed for: ${tenantId}`,
      data: {
        tenant: {
          id: tenantId,
          isolation_method: 'Row Level Security (RLS)',
          query_method: 'Direct Database Queries'
        },
        entities: {
          products: {
            sample: productsData.products,
            total: productsData.total,
            sample_count: productsData.products.length
          },
          customers: {
            sample: customersData.customers,
            total: customersData.total,
            sample_count: customersData.customers.length
          },
          carts: {
            sample: cartsData.carts,
            total: cartsData.total,
            sample_count: cartsData.carts.length
          },
          orders: {
            sample: ordersData.orders,
            total: ordersData.total,
            sample_count: ordersData.orders.length
          }
        },
        statistics: stats,
        performance: {
          query_time: Date.now() - startTime,
          isolation_verified: true
        }
      },
      timestamp: new Date().toISOString()
    };

    console.log(`✅ [DIRECT-TENANT-TEST] Test completed for tenant: ${tenantId}`, {
      products: productsData.total,
      customers: customersData.total,
      carts: cartsData.total,
      orders: ordersData.total
    });

    // Close database connection
    await dbService.close();

    return res.json(response);

  } catch (error) {
    console.error(`❌ [DIRECT-TENANT-TEST] Test failed: ${error}`);
    
    return res.status(500).json({
      success: false,
      message: 'Direct tenant database test failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      tenant_id: req.tenant_id || 'default',
      timestamp: new Date().toISOString()
    });
  }
};

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const tenantId = req.tenant_id || 'default';
    const { entity_type, data } = req.body;

    console.log(`🧪 [DIRECT-TENANT-TEST] Creating ${entity_type} for tenant: ${tenantId}`);

    // Create direct database service
    const dbService = DirectDatabaseService.fromRequest(req);

    let result = null;

    switch (entity_type) {
      case 'product':
        result = await dbService.createProduct(data);
        break;
      case 'customer':
        result = await dbService.createCustomer(data);
        break;
      case 'cart':
        result = await dbService.createCart(data);
        break;
      default:
        throw new Error(`Unsupported entity type: ${entity_type}`);
    }

    // Get updated statistics
    const stats = await dbService.getStats();

    const response = {
      success: true,
      message: `${entity_type} created for tenant: ${tenantId}`,
      data: {
        created_entity: result,
        entity_type,
        tenant: {
          id: tenantId,
          isolation_method: 'Row Level Security (RLS)',
          automatic_tenant_injection: true
        },
        updated_statistics: stats
      },
      timestamp: new Date().toISOString()
    };

    console.log(`✅ [DIRECT-TENANT-TEST] Created ${entity_type} ${result.id} for tenant: ${tenantId}`);

    // Close database connection
    await dbService.close();

    return res.status(201).json(response);

  } catch (error) {
    console.error(`❌ [DIRECT-TENANT-TEST] Creation failed: ${error}`);
    
    return res.status(500).json({
      success: false,
      message: 'Direct tenant entity creation failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      tenant_id: req.tenant_id || 'default',
      timestamp: new Date().toISOString()
    });
  }
};
