import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
) {
  console.log(`🚀 [TENANT FILTER] === CUSTOM PRODUCT CATEGORIES ENDPOINT CALLED ===`)
  console.log(`🚀 [TENANT FILTER] Headers:`, JSON.stringify(req.headers, null, 2))
  console.log(`🚀 [TENANT FILTER] Query:`, JSON.stringify(req.query, null, 2))

  try {
    // Extract tenant ID from header
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    console.log(`🔍 [TENANT FILTER] Processing categories request for tenant: ${tenantId}`)

    // Get query parameters
    const {
      limit = 50,
      offset = 0,
      parent_category_id,
      include_descendants_tree = false
    } = req.query

    // Direct database connection approach
    const { Client } = require('pg')
    const client = new Client({
      connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
    })

    let categories: any[] = []
    let count = 0

    try {
      await client.connect()
      console.log(`🔗 [TENANT FILTER] Connected to database directly`)

      // Get total count for this tenant (exclude soft-deleted)
      const countResult = await client.query(
        'SELECT COUNT(*) as total FROM product_category WHERE tenant_id = $1 AND deleted_at IS NULL',
        [tenantId]
      )
      count = parseInt(countResult.rows[0]?.total || 0)
      console.log(`📊 [TENANT FILTER] Total categories for tenant ${tenantId}: ${count}`)

      // Build query based on parent_category_id filter (exclude soft-deleted)
      let query = `
        SELECT 
          id, name, handle, description, is_active, is_internal,
          parent_category_id, rank, created_at, updated_at, 
          deleted_at, tenant_id, metadata
        FROM product_category 
        WHERE tenant_id = $1 AND deleted_at IS NULL
      `
      let queryParams = [tenantId]

      if (parent_category_id) {
        query += ` AND parent_category_id = $2`
        queryParams.push(parent_category_id as string)
      } else if (parent_category_id === null || parent_category_id === 'null') {
        query += ` AND parent_category_id IS NULL`
      }

      query += ` ORDER BY rank ASC, created_at DESC LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}`
      queryParams.push(parseInt(limit as string), parseInt(offset as string))

      const result = await client.query(query, queryParams)
      categories = result.rows || []

      // Parse metadata for each category
      categories.forEach(category => {
        if (typeof category.metadata === 'string') {
          try {
            category.metadata = JSON.parse(category.metadata)
          } catch (e) {
            console.log(`⚠️ Could not parse metadata for category ${category.id}`)
          }
        }
      })

      console.log(`📦 [TENANT FILTER] Retrieved ${categories.length} categories`)

      await client.end()

    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error:', dbError)
      await client.end().catch(() => {})
    }

    // Return response in Medusa format
    const response = {
      product_categories: categories,
      count: categories.length,
      offset: parseInt(offset as string),
      limit: parseInt(limit as string),
      // Add tenant info for debugging
      _tenant: {
        id: tenantId,
        filtered: true,
        method: 'direct_db_connection',
        total_in_db: count
      }
    }

    // Add tenant headers for debugging
    res.setHeader('X-Tenant-ID', tenantId)
    res.setHeader('X-Tenant-Filtered', 'true')
    res.setHeader('X-Categories-Count', categories.length.toString())

    console.log(`📤 [TENANT FILTER] Returning response:`, {
      categories_count: categories.length,
      total_count: count,
      tenant_id: tenantId
    })
    
    res.json(response)

  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error getting categories:', error)
    
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    res.status(500).json({
      error: 'Failed to get categories',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'tenant_categories_get_error',
        timestamp: new Date().toISOString()
      }
    })
  }
}

export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
) {
  console.log(`🚀 [TENANT FILTER] === CUSTOM PRODUCT CATEGORY CREATE ENDPOINT CALLED ===`)
  console.log(`🚀 [TENANT FILTER] Body:`, JSON.stringify(req.body, null, 2))

  try {
    // Extract tenant ID from header
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    console.log(`🔄 [TENANT FILTER] Creating category for tenant: ${tenantId}`)

    // Get category data from request body
    const categoryData = req.body as any

    // Ensure tenant_id is injected and cannot be modified
    const categoryWithTenant = {
      ...categoryData,
      tenant_id: tenantId
    }

    // Direct database connection approach
    const { Client } = require('pg')
    const client = new Client({
      connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
    })

    let createdCategory: any = null

    try {
      await client.connect()
      console.log(`🔗 [TENANT FILTER] Connected to database for category creation`)

      // Generate category ID
      const categoryId = `pcat_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`

      // Check if handle already exists for this tenant
      if (categoryWithTenant.handle) {
        const existingResult = await client.query(
          'SELECT id FROM product_category WHERE handle = $1 AND tenant_id = $2',
          [categoryWithTenant.handle, tenantId]
        )
        
        if (existingResult.rows.length > 0) {
          await client.end()
          return res.status(400).json({
            type: 'invalid_data',
            message: `Product category with handle: ${categoryWithTenant.handle}, already exists for tenant: ${tenantId}.`,
            tenant_id: tenantId
          })
        }
      }

      // Calculate mpath based on parent category
      let mpath = categoryId // Default for root categories
      
      if (categoryWithTenant.parent_category_id) {
        // Get parent category's mpath
        const parentResult = await client.query(
          'SELECT mpath FROM product_category WHERE id = $1 AND tenant_id = $2',
          [categoryWithTenant.parent_category_id, tenantId]
        )
        
        if (parentResult.rows.length === 0) {
          await client.end()
          return res.status(400).json({
            type: 'invalid_data',
            message: `Parent category with id: ${categoryWithTenant.parent_category_id} not found for tenant: ${tenantId}.`,
            tenant_id: tenantId
          })
        }
        
        const parentMpath = parentResult.rows[0].mpath
        mpath = `${parentMpath}.${categoryId}`
      }

      // Insert category
      const insertCategoryQuery = `
        INSERT INTO product_category (
          id, name, handle, description, mpath, is_active, is_internal,
          parent_category_id, rank, tenant_id, metadata,
          created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW(), NOW())
        RETURNING *
      `

      const categoryValues = [
        categoryId,
        categoryWithTenant.name,
        categoryWithTenant.handle,
        categoryWithTenant.description || '',
        mpath,
        categoryWithTenant.is_active !== false, // default to true
        categoryWithTenant.is_internal || false,
        categoryWithTenant.parent_category_id || null,
        categoryWithTenant.rank || 0,
        tenantId,
        categoryWithTenant.metadata ? JSON.stringify({
          ...categoryWithTenant.metadata,
          tenant_id: tenantId
        }) : JSON.stringify({ tenant_id: tenantId })
      ]

      const result = await client.query(insertCategoryQuery, categoryValues)
      createdCategory = result.rows[0]

      // Parse metadata
      if (typeof createdCategory.metadata === 'string') {
        try {
          createdCategory.metadata = JSON.parse(createdCategory.metadata)
        } catch (e) {
          console.log(`⚠️ Could not parse metadata for created category ${createdCategory.id}`)
        }
      }

      console.log(`✅ [TENANT FILTER] Created category ${categoryId} for tenant: ${tenantId}`)

      await client.end()

    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error:', dbError)
      await client.end().catch(() => {})
      throw dbError
    }

    // Return response in Medusa format
    const response = {
      product_category: createdCategory,
      _tenant: {
        id: tenantId,
        injected: true,
        method: 'direct_db_creation'
      }
    }

    // Add tenant headers
    res.setHeader('X-Tenant-ID', tenantId)
    res.setHeader('X-Tenant-Injected', 'true')

    console.log(`📤 [TENANT FILTER] Returning created category for tenant ${tenantId}`)
    
    res.json(response)

  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error creating category:', error)
    
    const tenantId = req.headers['x-tenant-id'] as string || 'default'
    
    res.status(500).json({
      error: 'Failed to create category',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'tenant_category_create_error',
        timestamp: new Date().toISOString(),
        stack: error.stack
      }
    })
  }
}