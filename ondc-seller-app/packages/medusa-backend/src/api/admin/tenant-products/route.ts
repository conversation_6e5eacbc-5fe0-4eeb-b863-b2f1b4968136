/**
 * Tenant-Aware Products API
 *
 * Demonstrates working multi-tenant product operations with automatic tenant filtering.
 * This endpoint shows how the tenant-aware services work in practice.
 */

import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { TenantServiceFactory } from '../../../services/tenant-service-factory';
import { createProductsWithAutoConfigWorkflow } from '../../../workflows/product-auto-config';

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const tenantId = req.tenant_id || 'default';
    console.log(`🏢 [TENANT-PRODUCTS] Getting products for tenant: ${tenantId}`);

    // Create tenant-aware services
    const services = TenantServiceFactory.fromRequest(req);

    // Get query parameters
    const { limit = 20, offset = 0, search, status, category_id } = req.query;

    // Build filters
    const filters: any = {};
    if (search) {
      filters.title = { $ilike: `%${search}%` };
    }
    if (status) {
      filters.status = status;
    }
    if (category_id) {
      filters.category_id = category_id;
    }

    // Get products using tenant-aware service
    const [products, totalCount] = await services.product.listAndCountProducts(filters, {
      take: parseInt(limit as string),
      skip: parseInt(offset as string),
    });

    // Get product statistics
    const stats = await services.product.getProductStats();

    // Build response
    const response = {
      success: true,
      message: `Products retrieved for tenant: ${tenantId}`,
      data: {
        products,
        pagination: {
          total: totalCount,
          count: products.length,
          limit: parseInt(limit as string),
          offset: parseInt(offset as string),
          hasMore: parseInt(offset as string) + products.length < totalCount,
        },
        statistics: stats,
        tenant: {
          id: tenantId,
          context: 'Products filtered by tenant automatically',
        },
      },
      timestamp: new Date().toISOString(),
    };

    console.log(
      `✅ [TENANT-PRODUCTS] Retrieved ${products.length}/${totalCount} products for tenant: ${tenantId}`
    );

    return res.json(response);
  } catch (error) {
    console.error(`❌ [TENANT-PRODUCTS] Error getting products: ${error}`);

    return res.status(500).json({
      success: false,
      message: 'Failed to get tenant products',
      error: error instanceof Error ? error.message : 'Unknown error',
      tenant_id: req.tenant_id || 'default',
      timestamp: new Date().toISOString(),
    });
  }
};

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const tenantId = req.tenant_id || 'default';
    console.log(`🏢 [TENANT-PRODUCTS] Creating product for tenant: ${tenantId}`);

    // Create tenant-aware services
    const services = TenantServiceFactory.fromRequest(req);

    const { title, description, handle, status = 'draft', ...otherData } = req.body;

    // Validate required fields
    if (!title) {
      return res.status(400).json({
        success: false,
        message: 'Title is required',
        tenant_id: tenantId,
      });
    }

    // Prepare product data
    const productData = {
      title,
      description: description || `Product created for tenant: ${tenantId}`,
      handle:
        handle ||
        title
          .toLowerCase()
          .replace(/\s+/g, '-')
          .replace(/[^a-z0-9-]/g, ''),
      status,
      ...otherData,
    };

    console.log(`🏢 [TENANT-PRODUCTS] Creating product with data:`, productData);

    // Create product using enhanced workflow with auto-configuration
    console.log(`🔧 [TENANT-PRODUCTS] Using auto-config workflow for product creation`);

    const workflowResult = await createProductsWithAutoConfigWorkflow(req.scope).run({
      input: {
        products: [productData],
        tenantId: tenantId,
      },
    });

    const createdProduct = workflowResult.result.products[0];
    const configResults = workflowResult.result.configurationResults;

    console.log(`🔧 [TENANT-PRODUCTS] Auto-configuration results:`, configResults);

    // Get updated statistics
    const stats = await services.product.getProductStats();

    const response = {
      success: true,
      message: `Product created for tenant: ${tenantId}`,
      data: {
        product: createdProduct,
        statistics: stats,
        tenant: {
          id: tenantId,
          context: 'Product automatically associated with tenant',
        },
        autoConfiguration: {
          salesChannelAssigned: configResults.successful > 0,
          inventoryManagementDisabled: configResults.successful > 0,
          shippingProfileAssigned: configResults.successful > 0,
          salesChannelId: process.env.DEFAULT_SALES_CHANNEL_ID,
          shippingProfileId: process.env.DEFAULT_SHIPPING_PROFILE_ID,
          results: configResults,
        },
      },
      timestamp: new Date().toISOString(),
    };

    console.log(
      `✅ [TENANT-PRODUCTS] Created product ${createdProduct.id} for tenant: ${tenantId}`
    );

    return res.status(201).json(response);
  } catch (error) {
    console.error(`❌ [TENANT-PRODUCTS] Error creating product: ${error}`);

    return res.status(500).json({
      success: false,
      message: 'Failed to create tenant product',
      error: error instanceof Error ? error.message : 'Unknown error',
      tenant_id: req.tenant_id || 'default',
      timestamp: new Date().toISOString(),
    });
  }
};

export const PUT = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const tenantId = req.tenant_id || 'default';
    const { product_id } = req.query;

    if (!product_id) {
      return res.status(400).json({
        success: false,
        message: 'Product ID is required',
        tenant_id: tenantId,
      });
    }

    console.log(`🏢 [TENANT-PRODUCTS] Updating product ${product_id} for tenant: ${tenantId}`);

    // Create tenant-aware services
    const services = TenantServiceFactory.fromRequest(req);

    const updateData = req.body;

    // Update product using tenant-aware service
    const updatedProducts = await services.product.updateProducts([
      {
        id: product_id as string,
        ...updateData,
      },
    ]);
    const updatedProduct = updatedProducts[0];

    const response = {
      success: true,
      message: `Product updated for tenant: ${tenantId}`,
      data: {
        product: updatedProduct,
        tenant: {
          id: tenantId,
          context: 'Product update respects tenant boundaries',
        },
      },
      timestamp: new Date().toISOString(),
    };

    console.log(`✅ [TENANT-PRODUCTS] Updated product ${product_id} for tenant: ${tenantId}`);

    return res.json(response);
  } catch (error) {
    console.error(`❌ [TENANT-PRODUCTS] Error updating product: ${error}`);

    return res.status(500).json({
      success: false,
      message: 'Failed to update tenant product',
      error: error instanceof Error ? error.message : 'Unknown error',
      tenant_id: req.tenant_id || 'default',
      timestamp: new Date().toISOString(),
    });
  }
};

export const DELETE = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const tenantId = req.tenant_id || 'default';
    const { product_id } = req.query;

    if (!product_id) {
      return res.status(400).json({
        success: false,
        message: 'Product ID is required',
        tenant_id: tenantId,
      });
    }

    console.log(`🏢 [TENANT-PRODUCTS] Deleting product ${product_id} for tenant: ${tenantId}`);

    // Create tenant-aware services
    const services = TenantServiceFactory.fromRequest(req);

    // Delete product using tenant-aware service
    await services.product.deleteProducts([product_id as string]);

    // Get updated statistics
    const stats = await services.product.getProductStats();

    const response = {
      success: true,
      message: `Product deleted for tenant: ${tenantId}`,
      data: {
        deleted_product_id: product_id,
        statistics: stats,
        tenant: {
          id: tenantId,
          context: 'Product deletion respects tenant boundaries',
        },
      },
      timestamp: new Date().toISOString(),
    };

    console.log(`✅ [TENANT-PRODUCTS] Deleted product ${product_id} for tenant: ${tenantId}`);

    return res.json(response);
  } catch (error) {
    console.error(`❌ [TENANT-PRODUCTS] Error deleting product: ${error}`);

    return res.status(500).json({
      success: false,
      message: 'Failed to delete tenant product',
      error: error instanceof Error ? error.message : 'Unknown error',
      tenant_id: req.tenant_id || 'default',
      timestamp: new Date().toISOString(),
    });
  }
};
