/**
 * Comprehensive Tenant Isolation Analysis
 * 
 * Provides detailed analysis of tenant isolation status across all Medusa entities
 * and generates actionable recommendations for complete multi-tenant implementation.
 */

import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";

interface EntityAnalysis {
  entityType: string;
  tableName: string;
  hasTenantColumn: boolean;
  hasRLSPolicy: boolean;
  serviceAvailable: boolean;
  crudOperations: {
    create: boolean;
    read: boolean;
    update: boolean;
    delete: boolean;
  };
  tenantIsolationStatus: 'COMPLETE' | 'PARTIAL' | 'MISSING';
  recommendations: string[];
}

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const tenantId = req.tenant_id || 'default';
    console.log(`📊 [TENANT-ISOLATION-ANALYSIS] Analyzing tenant isolation for: ${tenantId}`);

    const startTime = Date.now();
    const analysis: any = {
      timestamp: new Date().toISOString(),
      tenantId,
      summary: {
        totalEntities: 0,
        completeIsolation: 0,
        partialIsolation: 0,
        missingIsolation: 0,
        overallStatus: 'INCOMPLETE',
        readinessPercentage: 0
      },
      entityAnalyses: [],
      criticalGaps: [],
      implementationPlan: [],
      nextSteps: []
    };

    // Define all Medusa entities that need tenant isolation
    const entitiesToAnalyze = [
      {
        entityType: 'Products',
        tableName: 'product',
        serviceNames: ['product'],
        priority: 'HIGH'
      },
      {
        entityType: 'Product Variants',
        tableName: 'product_variant',
        serviceNames: ['product'],
        priority: 'HIGH'
      },
      {
        entityType: 'Customers',
        tableName: 'customer',
        serviceNames: ['customer'],
        priority: 'HIGH'
      },
      {
        entityType: 'Orders',
        tableName: 'order',
        serviceNames: ['order'],
        priority: 'HIGH'
      },
      {
        entityType: 'Carts',
        tableName: 'cart',
        serviceNames: ['cart'],
        priority: 'HIGH'
      },
      {
        entityType: 'Product Categories',
        tableName: 'product_category',
        serviceNames: ['productCategory', 'product-category'],
        priority: 'MEDIUM'
      },
      {
        entityType: 'Product Collections',
        tableName: 'product_collection',
        serviceNames: ['productCollection', 'collection'],
        priority: 'MEDIUM'
      },
      {
        entityType: 'Product Tags',
        tableName: 'product_tag',
        serviceNames: ['productTag', 'tag'],
        priority: 'MEDIUM'
      },
      {
        entityType: 'Regions',
        tableName: 'region',
        serviceNames: ['region'],
        priority: 'LOW'
      },
      {
        entityType: 'Sales Channels',
        tableName: 'sales_channel',
        serviceNames: ['salesChannel'],
        priority: 'MEDIUM'
      },
      {
        entityType: 'Discounts',
        tableName: 'discount',
        serviceNames: ['discount'],
        priority: 'MEDIUM'
      },
      {
        entityType: 'Gift Cards',
        tableName: 'gift_card',
        serviceNames: ['giftCard'],
        priority: 'LOW'
      }
    ];

    // Analyze each entity
    for (const entity of entitiesToAnalyze) {
      const entityAnalysis = await analyzeEntity(req, entity);
      analysis.entityAnalyses.push(entityAnalysis);
      analysis.summary.totalEntities++;

      switch (entityAnalysis.tenantIsolationStatus) {
        case 'COMPLETE':
          analysis.summary.completeIsolation++;
          break;
        case 'PARTIAL':
          analysis.summary.partialIsolation++;
          break;
        case 'MISSING':
          analysis.summary.missingIsolation++;
          break;
      }
    }

    // Calculate overall status
    analysis.summary.readinessPercentage = Math.round(
      ((analysis.summary.completeIsolation + (analysis.summary.partialIsolation * 0.5)) / analysis.summary.totalEntities) * 100
    );

    if (analysis.summary.readinessPercentage >= 90) {
      analysis.summary.overallStatus = 'READY';
    } else if (analysis.summary.readinessPercentage >= 50) {
      analysis.summary.overallStatus = 'PARTIAL';
    } else {
      analysis.summary.overallStatus = 'INCOMPLETE';
    }

    // Generate critical gaps and implementation plan
    generateCriticalGaps(analysis);
    generateImplementationPlan(analysis);
    generateNextSteps(analysis);

    // Performance metrics
    analysis.performance = {
      analysisTime: Date.now() - startTime,
      entitiesAnalyzed: analysis.summary.totalEntities
    };

    console.log(`📊 [TENANT-ISOLATION-ANALYSIS] Analysis completed:`, analysis.summary);

    return res.json({
      success: true,
      message: 'Comprehensive tenant isolation analysis completed',
      analysis
    });

  } catch (error) {
    console.error(`❌ [TENANT-ISOLATION-ANALYSIS] Analysis failed:`, error);
    
    return res.status(500).json({
      success: false,
      message: 'Tenant isolation analysis failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
};

async function analyzeEntity(req: MedusaRequest, entity: any): Promise<EntityAnalysis> {
  const analysis: EntityAnalysis = {
    entityType: entity.entityType,
    tableName: entity.tableName,
    hasTenantColumn: false, // Would need database inspection
    hasRLSPolicy: false,    // Would need database inspection
    serviceAvailable: false,
    crudOperations: {
      create: false,
      read: false,
      update: false,
      delete: false
    },
    tenantIsolationStatus: 'MISSING',
    recommendations: []
  };

  // Test service availability
  for (const serviceName of entity.serviceNames) {
    try {
      const service = req.scope.resolve(serviceName);
      if (service) {
        analysis.serviceAvailable = true;
        
        // Test CRUD operations
        if (typeof service.listProducts === 'function' || 
            typeof service.listCustomers === 'function' ||
            typeof service.listOrders === 'function') {
          analysis.crudOperations.read = true;
        }
        
        if (typeof service.createProducts === 'function' ||
            typeof service.createCustomers === 'function') {
          analysis.crudOperations.create = true;
        }
        
        if (typeof service.updateProducts === 'function' ||
            typeof service.updateCustomers === 'function') {
          analysis.crudOperations.update = true;
        }
        
        if (typeof service.deleteProducts === 'function' ||
            typeof service.deleteCustomers === 'function') {
          analysis.crudOperations.delete = true;
        }
        
        break; // Found working service
      }
    } catch (error) {
      // Service not available, continue to next
    }
  }

  // Determine isolation status
  const crudCount = Object.values(analysis.crudOperations).filter(Boolean).length;
  
  if (analysis.serviceAvailable && crudCount >= 3) {
    analysis.tenantIsolationStatus = 'PARTIAL'; // Service available but no RLS
  } else if (analysis.serviceAvailable && crudCount >= 1) {
    analysis.tenantIsolationStatus = 'PARTIAL';
  } else {
    analysis.tenantIsolationStatus = 'MISSING';
  }

  // Generate recommendations
  generateEntityRecommendations(analysis, entity);

  return analysis;
}

function generateEntityRecommendations(analysis: EntityAnalysis, entity: any): void {
  const recommendations = [];

  if (!analysis.serviceAvailable) {
    recommendations.push(`Implement or fix ${entity.entityType} service resolution`);
  }

  if (!analysis.hasTenantColumn) {
    recommendations.push(`Add tenant_id column to ${analysis.tableName} table`);
  }

  if (!analysis.hasRLSPolicy) {
    recommendations.push(`Implement RLS policy for ${analysis.tableName} table`);
  }

  if (!analysis.crudOperations.create) {
    recommendations.push(`Implement CREATE operation with tenant_id injection`);
  }

  if (!analysis.crudOperations.read) {
    recommendations.push(`Implement READ operations with tenant filtering`);
  }

  if (!analysis.crudOperations.update) {
    recommendations.push(`Implement UPDATE operations with tenant validation`);
  }

  if (!analysis.crudOperations.delete) {
    recommendations.push(`Implement DELETE operations with tenant validation`);
  }

  if (entity.priority === 'HIGH') {
    recommendations.push(`HIGH PRIORITY: Critical for multi-tenant functionality`);
  }

  analysis.recommendations = recommendations;
}

function generateCriticalGaps(analysis: any): void {
  const criticalGaps = [];

  const highPriorityMissing = analysis.entityAnalyses.filter((e: EntityAnalysis) => 
    e.tenantIsolationStatus === 'MISSING' && 
    ['Products', 'Customers', 'Orders', 'Carts'].includes(e.entityType)
  );

  if (highPriorityMissing.length > 0) {
    criticalGaps.push({
      type: 'HIGH_PRIORITY_ENTITIES_MISSING',
      description: 'Critical entities lack tenant isolation',
      entities: highPriorityMissing.map((e: EntityAnalysis) => e.entityType),
      impact: 'CRITICAL'
    });
  }

  const noRLSPolicies = analysis.entityAnalyses.filter((e: EntityAnalysis) => !e.hasRLSPolicy);
  if (noRLSPolicies.length > 0) {
    criticalGaps.push({
      type: 'NO_RLS_POLICIES',
      description: 'Database-level tenant isolation not implemented',
      count: noRLSPolicies.length,
      impact: 'HIGH'
    });
  }

  const noTenantColumns = analysis.entityAnalyses.filter((e: EntityAnalysis) => !e.hasTenantColumn);
  if (noTenantColumns.length > 0) {
    criticalGaps.push({
      type: 'NO_TENANT_COLUMNS',
      description: 'Database tables missing tenant_id columns',
      count: noTenantColumns.length,
      impact: 'HIGH'
    });
  }

  analysis.criticalGaps = criticalGaps;
}

function generateImplementationPlan(analysis: any): void {
  const plan = [
    {
      phase: 'Phase 1: Database Schema',
      priority: 'CRITICAL',
      tasks: [
        'Add tenant_id columns to all entity tables',
        'Create database migration scripts',
        'Implement RLS policies for all tables',
        'Test RLS policies with sample data'
      ],
      estimatedTime: '2-3 days'
    },
    {
      phase: 'Phase 2: Service Layer',
      priority: 'HIGH',
      tasks: [
        'Complete tenant-aware service implementations',
        'Add missing CRUD operations',
        'Implement tenant validation in all services',
        'Add comprehensive error handling'
      ],
      estimatedTime: '3-4 days'
    },
    {
      phase: 'Phase 3: API Integration',
      priority: 'HIGH',
      tasks: [
        'Update all Medusa API endpoints',
        'Implement tenant-aware middleware for all routes',
        'Add tenant validation to all endpoints',
        'Test all CRUD operations'
      ],
      estimatedTime: '2-3 days'
    },
    {
      phase: 'Phase 4: Data Migration',
      priority: 'MEDIUM',
      tasks: [
        'Migrate existing data with tenant assignments',
        'Validate data integrity after migration',
        'Test cross-tenant isolation',
        'Performance testing'
      ],
      estimatedTime: '1-2 days'
    },
    {
      phase: 'Phase 5: Frontend Integration',
      priority: 'MEDIUM',
      tasks: [
        'Update frontend API clients',
        'Implement tenant switching UI',
        'Test end-to-end functionality',
        'User acceptance testing'
      ],
      estimatedTime: '3-4 days'
    }
  ];

  analysis.implementationPlan = plan;
}

function generateNextSteps(analysis: any): void {
  const nextSteps = [];

  if (analysis.summary.readinessPercentage < 50) {
    nextSteps.push('URGENT: Implement database schema changes (tenant_id columns and RLS policies)');
    nextSteps.push('Complete service layer implementations for high-priority entities');
    nextSteps.push('Test basic CRUD operations with tenant isolation');
  } else if (analysis.summary.readinessPercentage < 90) {
    nextSteps.push('Complete remaining entity implementations');
    nextSteps.push('Implement comprehensive testing suite');
    nextSteps.push('Prepare for data migration');
  } else {
    nextSteps.push('Finalize testing and validation');
    nextSteps.push('Prepare for production deployment');
    nextSteps.push('Begin frontend integration');
  }

  nextSteps.push('Set up monitoring and logging for tenant operations');
  nextSteps.push('Create documentation for multi-tenant API usage');

  analysis.nextSteps = nextSteps;
}
