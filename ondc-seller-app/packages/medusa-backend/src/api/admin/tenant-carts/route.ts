/**
 * Tenant-Aware Carts API
 * 
 * Complete CRUD operations for carts with automatic tenant isolation.
 * All operations respect tenant boundaries and inject tenant_id automatically.
 */

import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { TenantServiceFactory } from "../../../services/tenant-service-factory";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const tenantId = req.tenant_id || 'default';
    console.log(`🛒 [TENANT-CARTS] Getting carts for tenant: ${tenantId}`);

    // Create tenant-aware services
    const services = TenantServiceFactory.fromRequest(req);

    // Get query parameters
    const { 
      limit = 20, 
      offset = 0, 
      status,
      customer_id 
    } = req.query;

    // Build filters
    const filters: any = {};
    if (status) {
      filters.status = status;
    }
    if (customer_id) {
      filters.customer_id = customer_id;
    }

    // Get carts using tenant-aware service
    const [carts, totalCount] = await services.cart.listAndCountCarts(
      filters,
      {
        take: parseInt(limit as string),
        skip: parseInt(offset as string)
      }
    );

    // Get cart statistics
    const stats = await services.cart.getCartStats();

    // Build response
    const response = {
      success: true,
      message: `Carts retrieved for tenant: ${tenantId}`,
      data: {
        carts,
        pagination: {
          total: totalCount,
          count: carts.length,
          limit: parseInt(limit as string),
          offset: parseInt(offset as string),
          hasMore: (parseInt(offset as string) + carts.length) < totalCount
        },
        statistics: stats,
        tenant: {
          id: tenantId,
          context: 'Carts filtered by tenant automatically'
        }
      },
      timestamp: new Date().toISOString()
    };

    console.log(`✅ [TENANT-CARTS] Retrieved ${carts.length}/${totalCount} carts for tenant: ${tenantId}`);

    return res.json(response);

  } catch (error) {
    console.error(`❌ [TENANT-CARTS] Error getting carts: ${error}`);
    
    return res.status(500).json({
      success: false,
      message: 'Failed to get tenant carts',
      error: error instanceof Error ? error.message : 'Unknown error',
      tenant_id: req.tenant_id || 'default',
      timestamp: new Date().toISOString()
    });
  }
};

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const tenantId = req.tenant_id || 'default';
    console.log(`🛒 [TENANT-CARTS] Creating cart for tenant: ${tenantId}`);

    // Create tenant-aware services
    const services = TenantServiceFactory.fromRequest(req);

    const { 
      customer_id,
      region_id,
      sales_channel_id,
      currency_code = 'usd',
      ...otherData 
    } = req.body;

    // Prepare cart data
    const cartData = {
      customer_id,
      region_id,
      sales_channel_id,
      currency_code,
      ...otherData
    };

    console.log(`🛒 [TENANT-CARTS] Creating cart with data:`, cartData);

    // Create cart using tenant-aware service
    const createdCarts = await services.cart.createCarts([cartData]);
    const createdCart = createdCarts[0];

    // Get updated statistics
    const stats = await services.cart.getCartStats();

    const response = {
      success: true,
      message: `Cart created for tenant: ${tenantId}`,
      data: {
        cart: createdCart,
        statistics: stats,
        tenant: {
          id: tenantId,
          context: 'Cart automatically associated with tenant'
        }
      },
      timestamp: new Date().toISOString()
    };

    console.log(`✅ [TENANT-CARTS] Created cart ${createdCart.id} for tenant: ${tenantId}`);

    return res.status(201).json(response);

  } catch (error) {
    console.error(`❌ [TENANT-CARTS] Error creating cart: ${error}`);
    
    return res.status(500).json({
      success: false,
      message: 'Failed to create tenant cart',
      error: error instanceof Error ? error.message : 'Unknown error',
      tenant_id: req.tenant_id || 'default',
      timestamp: new Date().toISOString()
    });
  }
};

export const PUT = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const tenantId = req.tenant_id || 'default';
    const { cart_id } = req.query;

    if (!cart_id) {
      return res.status(400).json({
        success: false,
        message: 'Cart ID is required',
        tenant_id: tenantId
      });
    }

    console.log(`🛒 [TENANT-CARTS] Updating cart ${cart_id} for tenant: ${tenantId}`);

    // Create tenant-aware services
    const services = TenantServiceFactory.fromRequest(req);

    const updateData = req.body;

    // Update cart using tenant-aware service
    const updatedCarts = await services.cart.updateCarts([{
      id: cart_id as string,
      ...updateData
    }]);
    const updatedCart = updatedCarts[0];

    const response = {
      success: true,
      message: `Cart updated for tenant: ${tenantId}`,
      data: {
        cart: updatedCart,
        tenant: {
          id: tenantId,
          context: 'Cart update respects tenant boundaries'
        }
      },
      timestamp: new Date().toISOString()
    };

    console.log(`✅ [TENANT-CARTS] Updated cart ${cart_id} for tenant: ${tenantId}`);

    return res.json(response);

  } catch (error) {
    console.error(`❌ [TENANT-CARTS] Error updating cart: ${error}`);
    
    return res.status(500).json({
      success: false,
      message: 'Failed to update tenant cart',
      error: error instanceof Error ? error.message : 'Unknown error',
      tenant_id: req.tenant_id || 'default',
      timestamp: new Date().toISOString()
    });
  }
};

export const DELETE = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const tenantId = req.tenant_id || 'default';
    const { cart_id } = req.query;

    if (!cart_id) {
      return res.status(400).json({
        success: false,
        message: 'Cart ID is required',
        tenant_id: tenantId
      });
    }

    console.log(`🛒 [TENANT-CARTS] Deleting cart ${cart_id} for tenant: ${tenantId}`);

    // Create tenant-aware services
    const services = TenantServiceFactory.fromRequest(req);

    // Delete cart using tenant-aware service
    await services.cart.deleteCarts([cart_id as string]);

    // Get updated statistics
    const stats = await services.cart.getCartStats();

    const response = {
      success: true,
      message: `Cart deleted for tenant: ${tenantId}`,
      data: {
        deleted_cart_id: cart_id,
        statistics: stats,
        tenant: {
          id: tenantId,
          context: 'Cart deletion respects tenant boundaries'
        }
      },
      timestamp: new Date().toISOString()
    };

    console.log(`✅ [TENANT-CARTS] Deleted cart ${cart_id} for tenant: ${tenantId}`);

    return res.json(response);

  } catch (error) {
    console.error(`❌ [TENANT-CARTS] Error deleting cart: ${error}`);
    
    return res.status(500).json({
      success: false,
      message: 'Failed to delete tenant cart',
      error: error instanceof Error ? error.message : 'Unknown error',
      tenant_id: req.tenant_id || 'default',
      timestamp: new Date().toISOString()
    });
  }
};
