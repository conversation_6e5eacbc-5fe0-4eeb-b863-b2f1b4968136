import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { ContainerRegistrationKeys } from '@medusajs/framework/utils';

/**
 * Complete Cart - Standard Medusa v2 Cart Completion
 * This endpoint properly handles cart completion with customer and tenant association
 */
export async function POST(req: MedusaRequest, res: MedusaResponse): Promise<void> {
  try {
    const cartId = req.params.id;
    const cartModuleService = req.scope.resolve('cart');
    const orderModuleService = req.scope.resolve('order');
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    // Extract tenant ID and customer ID from request
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    // Enhanced customer ID extraction - handle both customer and admin tokens
    let customerId = null;
    if (req.auth_context?.actor_id) {
      const actorType = req.auth_context.actor_type;
      const actorId = req.auth_context.actor_id;

      if (actorType === 'customer') {
        // Direct customer token
        customerId = actorId;
        logger.info(`[CART-COMPLETE] Using customer token: ${customerId}`);
      } else if (actorType === 'user' && req.auth_context.app_metadata?.customer_id) {
        // Admin token with customer_id in metadata (for admin-initiated orders)
        customerId = req.auth_context.app_metadata.customer_id;
        logger.info(`[CART-COMPLETE] Using admin token with customer metadata: ${customerId}`);
      } else {
        logger.warn(`[CART-COMPLETE] Unsupported actor type: ${actorType}, creating guest order`);
      }
    }

    logger.info(`[CART-COMPLETE] Starting cart completion for cart: ${cartId}`);
    logger.info(
      `[CART-COMPLETE] Tenant ID: ${tenantId}, Customer ID: ${customerId}, Actor Type: ${req.auth_context?.actor_type || 'none'}`
    );

    // Get cart details without relations to avoid MikroORM issues
    const cart = await cartModuleService.retrieveCart(cartId);

    if (!cart) {
      logger.error(`[CART-COMPLETE] Cart not found: ${cartId}`);
      res.status(404).json({
        error: 'Cart not found',
        message: `Cart with ID ${cartId} not found`,
      });
      return;
    }

    // FALLBACK: If no customer ID from token, try to get it from cart
    if (!customerId && cart.customer_id) {
      customerId = cart.customer_id;
      logger.info(`[CART-COMPLETE] Using customer ID from cart: ${customerId}`);
    }

    // Get cart items separately to avoid relation issues
    let cartItems = [];
    try {
      const cartWithItems = await cartModuleService.retrieveCart(cartId, {
        relations: ['items'],
      });
      cartItems = cartWithItems.items || [];
    } catch (error) {
      logger.warn(
        `[CART-COMPLETE] Could not fetch cart items, proceeding with empty items: ${error.message}`
      );
      cartItems = [];
    }

    if (cartItems.length === 0) {
      logger.error(`[CART-COMPLETE] Empty cart: ${cartId}`);
      res.status(400).json({
        error: 'Empty cart',
        message: 'Cannot create order from empty cart',
      });
      return;
    }

    logger.info(`[CART-COMPLETE] Cart found with ${cartItems.length} items, total: ${cart.total}`);

    // Validate customer authentication for non-guest orders
    if (!customerId) {
      logger.warn(`[CART-COMPLETE] No customer authentication found, creating guest order`);
    }

    // Create comprehensive order data
    const orderData = {
      cart_id: cartId,
      region_id: cart.region_id,
      currency_code: cart.currency_code,
      email: cart.email || (customerId ? '<EMAIL>' : '<EMAIL>'),

      // Customer and tenant association - CRITICAL for order visibility
      customer_id: customerId,
      tenant_id: tenantId,
      sales_channel_id: cart.sales_channel_id,

      // Order totals
      total: cart.total || 0,
      subtotal: cart.subtotal || 0,
      tax_total: cart.tax_total || 0,
      shipping_total: cart.shipping_total || 0,
      discount_total: cart.discount_total || 0,

      // Order status
      status: 'pending',
      payment_status: 'awaiting',
      fulfillment_status: 'not_fulfilled',

      // Order items with safe mapping (using separately fetched items)
      items: cartItems.map((item: any) => ({
        variant_id: item.variant_id,
        product_id: item.product_id,
        title: item.title || 'Product',
        quantity: item.quantity,
        unit_price: item.unit_price,
        total: item.quantity * item.unit_price,
        metadata: {
          cart_item_id: item.id,
          variant_id: item.variant_id,
          product_id: item.product_id,
        },
      })),

      // Addresses (with fallback defaults)
      shipping_address: {
        first_name: customerId ? 'Customer' : 'Guest',
        last_name: customerId ? 'User' : 'Customer',
        address_1: 'Default Address',
        city: 'Default City',
        postal_code: '00000',
        country_code: 'in',
      },

      billing_address: {
        first_name: customerId ? 'Customer' : 'Guest',
        last_name: customerId ? 'User' : 'Customer',
        address_1: 'Default Address',
        city: 'Default City',
        postal_code: '00000',
        country_code: 'in',
      },

      // Shipping methods (simplified - will be handled by Medusa workflows)
      shipping_methods: [],

      // Comprehensive metadata for tracking and debugging
      metadata: {
        cart_id: cartId,
        tenant_id: tenantId,
        customer_id: customerId,
        created_via: 'store_api',
        completion_method: 'standard',
        customer_type: customerId ? 'authenticated' : 'guest',
        completion_timestamp: new Date().toISOString(),
      },
    };

    logger.info(
      `[CART-COMPLETE] Creating order with customer_id: ${customerId}, tenant_id: ${tenantId}`
    );
    logger.info(`[CART-COMPLETE] Order data summary:`, {
      cart_id: orderData.cart_id,
      customer_id: orderData.customer_id,
      tenant_id: orderData.tenant_id,
      email: orderData.email,
      total: orderData.total,
      items_count: orderData.items.length,
    });

    // Create the order
    const order = await orderModuleService.createOrders(orderData);

    logger.info(
      `[CART-COMPLETE] Order created successfully: ${order.id} for customer: ${customerId} in tenant: ${tenantId}`
    );

    // CRITICAL FIX: Ensure tenant_id is properly set in database
    // The Medusa orderModuleService may not respect custom tenant_id field
    // Use direct database update as a workaround
    if (tenantId && tenantId !== 'default') {
      try {
        const { Client } = require('pg');
        const client = new Client({ connectionString: process.env.DATABASE_URL });
        await client.connect();

        const updateResult = await client.query(
          'UPDATE "order" SET tenant_id = $1, metadata = COALESCE(metadata, \'{}\') || $2 WHERE id = $3 RETURNING id, tenant_id;',
          [
            tenantId,
            JSON.stringify({
              tenant_id_fix_applied: true,
              tenant_id_fix_timestamp: new Date().toISOString(),
            }),
            order.id,
          ]
        );

        await client.end();

        if (updateResult.rows.length > 0) {
          logger.info(
            `[CART-COMPLETE] Tenant ID fix applied via direct DB update: ${order.id} → ${tenantId}`
          );
        } else {
          logger.error(`[CART-COMPLETE] Direct DB update failed for order: ${order.id}`);
        }
      } catch (error) {
        logger.error(
          `[CART-COMPLETE] Failed to apply tenant ID fix via direct DB: ${error.message}`
        );
      }
    }

    // Mark cart as completed
    await cartModuleService.updateCarts(cartId, {
      completed_at: new Date(),
      metadata: {
        ...cart.metadata,
        order_id: order.id,
        completed_via: 'store_api',
        completion_timestamp: new Date().toISOString(),
      },
    });

    logger.info(`[CART-COMPLETE] Cart ${cartId} marked as completed`);

    // Return success response in Medusa v2 format
    res.status(200).json({
      type: 'order',
      order: {
        id: order.id,
        cart_id: cartId,
        customer_id: customerId,
        tenant_id: tenantId,
        email: orderData.email,
        status: orderData.status,
        payment_status: orderData.payment_status,
        fulfillment_status: orderData.fulfillment_status,
        total: orderData.total,
        subtotal: orderData.subtotal,
        tax_total: orderData.tax_total,
        shipping_total: orderData.shipping_total,
        currency_code: orderData.currency_code,
        items: orderData.items,
        shipping_address: orderData.shipping_address,
        billing_address: orderData.billing_address,
        created_at: new Date().toISOString(),
        metadata: orderData.metadata,
      },
      _debug: {
        customer_associated: !!customerId,
        tenant_associated: !!tenantId,
        completion_method: 'standard_medusa_v2',
      },
    });
  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error(`[CART-COMPLETE] Error completing cart:`, error);

    res.status(500).json({
      error: 'Cart completion failed',
      message: error.message || 'Failed to complete cart and create order',
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined,
    });
  }
}
