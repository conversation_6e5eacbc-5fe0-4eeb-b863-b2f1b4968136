import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { ContainerRegistrationKeys } from '@medusajs/framework/utils';

/**
 * Complete Cart with Cash on Delivery
 * This endpoint bypasses complex payment sessions and creates an order with COD payment
 */
export async function POST(req: MedusaRequest, res: MedusaResponse): Promise<void> {
  try {
    const cartId = req.params.id;
    const cartModuleService = req.scope.resolve('cart');
    const orderModuleService = req.scope.resolve('order');
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    // Extract tenant ID and customer ID from request
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    // Enhanced customer ID extraction - handle both customer and admin tokens
    let customerId = null;
    if (req.auth_context?.actor_id) {
      const actorType = req.auth_context.actor_type;
      const actorId = req.auth_context.actor_id;

      if (actorType === 'customer') {
        // Direct customer token
        customerId = actorId;
        logger.info(`[COD Order] Using customer token: ${customerId}`);
      } else if (actorType === 'user' && req.auth_context.app_metadata?.customer_id) {
        // Admin token with customer_id in metadata (for admin-initiated orders)
        customerId = req.auth_context.app_metadata.customer_id;
        logger.info(`[COD Order] Using admin token with customer metadata: ${customerId}`);
      } else {
        logger.warn(`[COD Order] Unsupported actor type: ${actorType}, creating guest order`);
      }
    }

    logger.info(`[COD Order] Starting COD order creation for cart: ${cartId}`);
    logger.info(
      `[COD Order] Tenant ID: ${tenantId}, Customer ID: ${customerId}, Actor Type: ${req.auth_context?.actor_type || 'none'}`
    );

    // Get cart details
    const cart = await cartModuleService.retrieveCart(cartId, {
      relations: ['items', 'shipping_address', 'billing_address'],
    });

    if (!cart) {
      res.status(404).json({
        error: 'Cart not found',
        message: `Cart with ID ${cartId} not found`,
      });
      return;
    }

    // FALLBACK: If no customer ID from token, try to get it from cart
    if (!customerId && cart.customer_id) {
      customerId = cart.customer_id;
      logger.info(`[COD Order] Using customer ID from cart: ${customerId}`);
    }

    if (!cart.items || cart.items.length === 0) {
      res.status(400).json({
        error: 'Empty cart',
        message: 'Cannot create order from empty cart',
      });
      return;
    }

    logger.info(`[COD Order] Cart found with ${cart.items.length} items, total: ${cart.total}`);

    // Create order data
    const orderData = {
      cart_id: cartId,
      region_id: cart.region_id,
      currency_code: cart.currency_code,
      email: cart.email || '<EMAIL>',

      // Customer and tenant association
      customer_id: customerId,
      tenant_id: tenantId,

      // Order totals
      total: cart.total || 0,
      subtotal: cart.subtotal || 0,
      tax_total: cart.tax_total || 0,
      shipping_total: cart.shipping_total || 0,

      // Payment information for COD
      payment_status: 'awaiting',
      payment_method: 'cash_on_delivery',

      // Fulfillment status
      fulfillment_status: 'not_fulfilled',

      // Order items
      items: cart.items.map((item: any) => ({
        variant_id: item.variant_id,
        product_id: item.product_id,
        title: item.title || item.product?.title || 'Product',
        quantity: item.quantity,
        unit_price: item.unit_price,
        total: item.quantity * item.unit_price,
        metadata: {
          product_title: item.product?.title,
          variant_title: item.variant?.title,
          sku: item.variant?.sku,
        },
      })),

      // Addresses
      shipping_address: cart.shipping_address || {
        first_name: 'Guest',
        last_name: 'Customer',
        address_1: 'Default Address',
        city: 'Default City',
        postal_code: '00000',
        country_code: 'us',
      },

      billing_address: cart.billing_address ||
        cart.shipping_address || {
          first_name: 'Guest',
          last_name: 'Customer',
          address_1: 'Default Address',
          city: 'Default City',
          postal_code: '00000',
          country_code: 'us',
        },

      // Metadata
      metadata: {
        payment_method: 'cash_on_delivery',
        payment_provider: 'manual',
        created_via: 'cod_api',
        cart_id: cartId,
        tenant_id: tenantId,
        customer_id: customerId,
      },
    };

    logger.info(
      `[COD Order] Creating order with customer_id: ${customerId}, tenant_id: ${tenantId}`
    );
    logger.info(`[COD Order] Order data:`, JSON.stringify(orderData, null, 2));

    // Create the order
    const order = await orderModuleService.createOrders(orderData);

    logger.info(
      `[COD Order] Order created successfully: ${order.id} for customer: ${customerId} in tenant: ${tenantId}`
    );

    // CRITICAL FIX: Ensure tenant_id is properly set in database
    // The Medusa orderModuleService may not respect custom tenant_id field
    // Use direct database update as a workaround
    if (tenantId && tenantId !== 'default') {
      try {
        const { Client } = require('pg');
        const client = new Client({ connectionString: process.env.DATABASE_URL });
        await client.connect();

        const updateResult = await client.query(
          'UPDATE "order" SET tenant_id = $1, metadata = COALESCE(metadata, \'{}\') || $2 WHERE id = $3 RETURNING id, tenant_id;',
          [
            tenantId,
            JSON.stringify({
              tenant_id_fix_applied: true,
              tenant_id_fix_timestamp: new Date().toISOString(),
            }),
            order.id,
          ]
        );

        await client.end();

        if (updateResult.rows.length > 0) {
          logger.info(
            `[COD Order] Tenant ID fix applied via direct DB update: ${order.id} → ${tenantId}`
          );
        } else {
          logger.error(`[COD Order] Direct DB update failed for order: ${order.id}`);
        }
      } catch (error) {
        logger.error(`[COD Order] Failed to apply tenant ID fix via direct DB: ${error.message}`);
      }
    }

    // Mark cart as completed (optional - you might want to keep it for reference)
    await cartModuleService.updateCarts(cartId, {
      completed_at: new Date(),
      metadata: {
        ...cart.metadata,
        order_id: order.id,
        completed_via: 'cod_api',
      },
    });

    // Return success response
    res.status(200).json({
      success: true,
      order: {
        id: order.id,
        cart_id: cartId,
        total: orderData.total,
        currency_code: orderData.currency_code,
        payment_method: 'cash_on_delivery',
        payment_status: 'awaiting',
        fulfillment_status: 'not_fulfilled',
        items: orderData.items,
        shipping_address: orderData.shipping_address,
        created_at: new Date().toISOString(),
        metadata: orderData.metadata,
      },
      message: 'Order created successfully with Cash on Delivery payment',
    });
  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error(`[COD Order] Error creating COD order:`, error);

    res.status(500).json({
      error: 'Order creation failed',
      message: error.message || 'Failed to create order with Cash on Delivery',
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined,
    });
  }
}
