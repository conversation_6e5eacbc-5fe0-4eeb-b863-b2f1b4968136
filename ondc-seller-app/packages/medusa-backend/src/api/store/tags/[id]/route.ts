import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { TenantServiceFactory } from "../../../../services/tenant-service-factory";

/**
 * GET /store/tags/{id}
 * Get a specific product tag by ID with tenant filtering
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const { id } = req.params;
  
  console.log(`🛒 [STORE-TAG-DETAIL] Getting tag ${id} for tenant: ${req.tenantId}`);

  try {
    // Get tenant-aware services
    const tenantServices = TenantServiceFactory.fromRequest(req);

    // Build config
    const config = {
      relations: req.query.expand ? (req.query.expand as string).split(',') : []
    };

    console.log(`🛒 [STORE-TAG-DETAIL] Config:`, config);

    // Get tag using tenant-aware service
    const tag = await tenantServices.tagService.retrieveTag(id, config);

    if (!tag) {
      console.log(`❌ [STORE-TAG-DETAIL] Tag ${id} not found for tenant: ${req.tenantId}`);
      return res.status(404).json({
        error: "Not found",
        message: `Tag with id ${id} not found`
      });
    }

    console.log(`✅ [STORE-TAG-DETAIL] Retrieved tag ${id} (${tag.value}) for tenant: ${req.tenantId}`);

    res.json({
      tag
    });
  } catch (error) {
    console.error(`❌ [STORE-TAG-DETAIL] Error getting tag ${id}: ${error}`);
    res.status(500).json({
      error: "Internal server error",
      message: error.message
    });
  }
}
