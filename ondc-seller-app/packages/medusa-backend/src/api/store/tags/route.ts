import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { TenantServiceFactory } from '../../../services/tenant-service-factory';
import {
  parseQueryParameters,
  buildSQLFilters,
  buildSQLSorting,
} from '../../../utils/enhanced-query-parser';
import { TAGS_CONFIG, validateQueryParams } from '../../../config/store-api-configs';

/**
 * GET /store/tags
 * List all product tags with tenant filtering
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  console.log(`🛒 [STORE-TAGS] Getting tags for tenant: ${req.tenantId}`);
  console.log(`🛒 [STORE-TAGS] Query params:`, req.query);

  try {
    // Get tenant-aware services
    const tenantServices = TenantServiceFactory.fromRequest(req);

    // Parse query parameters
    const limit = parseInt(req.query.limit as string) || 20;
    const offset = parseInt(req.query.offset as string) || 0;

    // Build filters
    const filters: any = {};

    if (req.query.value) {
      filters.value = req.query.value;
    }

    // Build config
    const config = {
      take: limit,
      skip: offset,
      relations: req.query.expand ? (req.query.expand as string).split(',') : [],
    };

    console.log(`🛒 [STORE-TAGS] Filters:`, filters);
    console.log(`🛒 [STORE-TAGS] Config:`, config);

    // Get tags using tenant-aware service
    const [tags, count] = await tenantServices.tagService.listAndCountTags(filters, config);

    console.log(
      `✅ [STORE-TAGS] Retrieved ${tags.length}/${count} tags for tenant: ${req.tenantId}`
    );

    res.json({
      tags,
      count: tags.length,
      total: count,
      offset,
      limit,
    });
  } catch (error) {
    console.error(`❌ [STORE-TAGS] Error getting tags: ${error}`);
    res.status(500).json({
      error: 'Internal server error',
      message: error.message,
    });
  }
}
