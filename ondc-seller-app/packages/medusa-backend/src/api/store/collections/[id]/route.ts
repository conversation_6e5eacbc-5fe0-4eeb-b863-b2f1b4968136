import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { TenantServiceFactory } from '../../../../services/tenant-service-factory';

/**
 * Store Collection Detail Endpoint - Customer-facing API with tenant filtering
 * 
 * This endpoint provides tenant-filtered individual collection details for the store/customer-facing API.
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    // Extract tenant ID and collection ID
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';
    const collectionId = req.params.id;
    
    console.log(`🛒 [STORE-COLLECTION-DETAIL] Getting collection ${collectionId} for tenant: ${tenantId}`);

    if (!collectionId) {
      return res.status(400).json({
        error: 'Collection ID is required',
        message: 'Collection ID must be provided in the URL path',
        tenant_id: tenantId
      });
    }

    // Get query parameters
    const {
      fields,
      expand
    } = req.query;

    // Create tenant-aware services
    const services = TenantServiceFactory.fromRequest(req);

    // Build config for field selection and relations
    const config: any = {};

    // Add field selection if specified
    if (fields) {
      const fieldArray = Array.isArray(fields) ? fields : [fields];
      config.select = fieldArray;
    }

    // Add relations to expand if specified
    if (expand) {
      const expandArray = Array.isArray(expand) ? expand : [expand];
      config.relations = expandArray;
    } else {
      // Default relations for store API
      config.relations = [
        'products',
        'products.variants',
        'products.variants.prices',
        'products.images',
        'products.categories'
      ];
    }

    console.log(`🛒 [STORE-COLLECTION-DETAIL] Config:`, config);

    // Get collection using tenant-aware service
    const collection = await services.collection.retrieveCollection(collectionId, config);

    if (!collection) {
      console.log(`❌ [STORE-COLLECTION-DETAIL] Collection ${collectionId} not found for tenant: ${tenantId}`);
      return res.status(404).json({
        error: 'Collection not found',
        message: `Collection with ID ${collectionId} not found or not accessible for tenant ${tenantId}`,
        collection_id: collectionId,
        tenant_id: tenantId
      });
    }

    // Build response in Medusa store API format
    const response = {
      collection: collection
    };

    console.log(`✅ [STORE-COLLECTION-DETAIL] Retrieved collection ${collectionId} (${collection.title}) for tenant: ${tenantId}`);

    // Set response headers for debugging
    res.setHeader('X-Tenant-ID', tenantId);
    res.setHeader('X-Store-API', 'true');
    res.setHeader('X-Tenant-Filtered', 'true');

    return res.status(200).json(response);

  } catch (error: any) {
    console.error('❌ [STORE-COLLECTION-DETAIL] Error:', error);

    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';
    const collectionId = req.params.id;

    return res.status(500).json({
      error: 'Failed to fetch collection',
      message: error.message,
      collection_id: collectionId,
      tenant_id: tenantId,
      _debug: {
        error_type: 'store_collection_detail_error',
        timestamp: new Date().toISOString(),
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      }
    });
  }
}
