import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { TenantServiceFactory } from '../../../services/tenant-service-factory';
import {
  parseQueryParameters,
  buildSQLFilters,
  buildSQLSorting,
} from '../../../utils/enhanced-query-parser';
import { COLLECTIONS_CONFIG, validateQueryParams } from '../../../config/store-api-configs';

/**
 * Store Collections Endpoint - Customer-facing API with tenant filtering
 *
 * This endpoint provides tenant-filtered collections for the store/customer-facing API.
 * It uses the tenant-aware services to ensure proper data isolation.
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    // Extract tenant ID from header
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    console.log(`🛒 [STORE-COLLECTIONS] Getting collections for tenant: ${tenantId}`);
    console.log(`🛒 [STORE-COLLECTIONS] Query params:`, req.query);

    // Validate query parameters
    const validation = validateQueryParams(req.query, COLLECTIONS_CONFIG);
    if (!validation.valid) {
      console.warn(`⚠️ [STORE-COLLECTIONS] Invalid query parameters:`, validation.errors);
      return res.status(400).json({
        error: 'Invalid query parameters',
        details: validation.errors,
        tenant_id: tenantId,
      });
    }

    // Parse query parameters with comprehensive support
    const parsedQuery = parseQueryParameters(req, COLLECTIONS_CONFIG);
    console.log(`🔍 [STORE-COLLECTIONS] Parsed query:`, parsedQuery);

    // Create tenant-aware services
    const services = TenantServiceFactory.fromRequest(req);

    // Build filters for store API (customer-facing)
    const filters: any = {};

    // Add handle filter
    if (handle) {
      filters.handle = handle;
    }

    // Add title search filter
    if (title) {
      filters.title = { $ilike: `%${title}%` };
    }

    // Add date filters
    if (created_at) {
      filters.created_at = created_at;
    }

    if (updated_at) {
      filters.updated_at = updated_at;
    }

    // Build config for pagination and field selection
    const config: any = {
      take: parseInt(limit as string),
      skip: parseInt(offset as string),
    };

    // Add field selection if specified
    if (fields) {
      const fieldArray = Array.isArray(fields) ? fields : [fields];
      config.select = fieldArray;
    }

    // Add relations to expand if specified
    if (expand) {
      const expandArray = Array.isArray(expand) ? expand : [expand];
      config.relations = expandArray;
    } else {
      // Default relations for store API
      config.relations = ['products'];
    }

    console.log(`🛒 [STORE-COLLECTIONS] Filters:`, filters);
    console.log(`🛒 [STORE-COLLECTIONS] Config:`, config);

    // Get collections using tenant-aware service
    const [collections, totalCount] = await services.collection.listAndCountCollections(
      filters,
      config
    );

    // Build response in Medusa store API format
    const response = {
      collections: collections || [],
      count: collections?.length || 0,
      offset: parseInt(offset as string),
      limit: parseInt(limit as string),
      total: totalCount || 0,
    };

    console.log(
      `✅ [STORE-COLLECTIONS] Retrieved ${response.count}/${response.total} collections for tenant: ${tenantId}`
    );

    // Set response headers for debugging
    res.setHeader('X-Tenant-ID', tenantId);
    res.setHeader('X-Store-API', 'true');
    res.setHeader('X-Tenant-Filtered', 'true');

    return res.status(200).json(response);
  } catch (error: any) {
    console.error('❌ [STORE-COLLECTIONS] Error:', error);

    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    return res.status(500).json({
      error: 'Failed to fetch collections',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'store_collections_error',
        timestamp: new Date().toISOString(),
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      },
    });
  }
}
