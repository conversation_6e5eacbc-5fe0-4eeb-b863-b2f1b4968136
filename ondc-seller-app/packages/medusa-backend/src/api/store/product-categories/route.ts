import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { TenantServiceFactory } from '../../../services/tenant-service-factory';
import {
  parseQueryParameters,
  buildSQLFilters,
  buildSQLSorting,
} from '../../../utils/enhanced-query-parser';
import { PRODUCT_CATEGORIES_CONFIG, validateQueryParams } from '../../../config/store-api-configs';

/**
 * Store Product Categories Endpoint - Customer-facing API with tenant filtering
 *
 * This endpoint provides tenant-filtered product categories for the store/customer-facing API.
 * It uses the tenant-aware services to ensure proper data isolation.
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    // Extract tenant ID from header
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    console.log(`🛒 [STORE-CATEGORIES] Getting categories for tenant: ${tenantId}`);
    console.log(`🛒 [STORE-CATEGORIES] Query params:`, req.query);

    // Validate query parameters
    const validation = validateQueryParams(req.query, PRODUCT_CATEGORIES_CONFIG);
    if (!validation.valid) {
      console.warn(`⚠️ [STORE-CATEGORIES] Invalid query parameters:`, validation.errors);
      return res.status(400).json({
        error: 'Invalid query parameters',
        details: validation.errors,
        tenant_id: tenantId,
      });
    }

    // Parse query parameters with comprehensive support
    const parsedQuery = parseQueryParameters(req, PRODUCT_CATEGORIES_CONFIG);
    console.log(`🔍 [STORE-CATEGORIES] Parsed query:`, parsedQuery);

    // Create tenant-aware services
    const services = TenantServiceFactory.fromRequest(req);

    // Build filters for store API (customer-facing)
    const filters: any = {
      is_active: true, // Store API should only show active categories
      is_internal: false, // Store API should only show public categories (not internal)
      ...parsedQuery.filters, // Add parsed filters
    };

    // Add date filters
    if (parsedQuery.dateFilters.created_after) {
      filters.created_at = { ...filters.created_at, $gte: parsedQuery.dateFilters.created_after };
    }
    if (parsedQuery.dateFilters.created_before) {
      filters.created_at = { ...filters.created_at, $lte: parsedQuery.dateFilters.created_before };
    }
    if (parsedQuery.dateFilters.updated_after) {
      filters.updated_at = { ...filters.updated_at, $gte: parsedQuery.dateFilters.updated_after };
    }
    if (parsedQuery.dateFilters.updated_before) {
      filters.updated_at = { ...filters.updated_at, $lte: parsedQuery.dateFilters.updated_before };
    }

    // Add search filters
    if (parsedQuery.search) {
      const searchConditions = parsedQuery.search.fields.map(field => ({
        [field]: { $ilike: `%${parsedQuery.search!.query}%` },
      }));
      filters.$or = searchConditions;
    }

    // Build config for pagination, field selection, and sorting
    const config: any = {
      take: parsedQuery.pagination.limit,
      skip: parsedQuery.pagination.offset,
    };

    // Add field selection if specified
    if (parsedQuery.fields) {
      config.select = parsedQuery.fields;
    }

    // Add relations to expand if specified
    if (parsedQuery.expand) {
      config.relations = parsedQuery.expand;
    } else {
      // Default relations for store API
      config.relations = ['parent_category', 'category_children'];
    }

    // Add sorting configuration
    if (parsedQuery.sorting.length > 0) {
      config.order = {};
      parsedQuery.sorting.forEach(sort => {
        config.order[sort.field] = sort.direction;
      });
    }

    // Include descendants tree if requested (legacy support)
    const includeDescendantsTree = req.query.include_descendants_tree;
    if (includeDescendantsTree === 'true' || includeDescendantsTree === true) {
      config.include_descendants_tree = true;
    }

    console.log(`🛒 [STORE-CATEGORIES] Filters:`, filters);
    console.log(`🛒 [STORE-CATEGORIES] Config:`, config);

    // Get categories using tenant-aware service
    const [categories, totalCount] = await services.productCategory.listAndCountCategories(
      filters,
      config
    );

    // Build response in Medusa store API format with enhanced metadata
    const response = {
      product_categories: categories || [],
      count: categories?.length || 0,
      offset: parsedQuery.pagination.offset,
      limit: parsedQuery.pagination.limit,
      total: totalCount || 0,
      // Enhanced metadata
      _meta: {
        pagination: {
          page:
            parsedQuery.pagination.page ||
            Math.floor(parsedQuery.pagination.offset / parsedQuery.pagination.limit) + 1,
          per_page: parsedQuery.pagination.limit,
          total_pages: Math.ceil((totalCount || 0) / parsedQuery.pagination.limit),
          has_more:
            parsedQuery.pagination.offset + parsedQuery.pagination.limit < (totalCount || 0),
        },
        filters_applied: Object.keys(parsedQuery.filters).length > 0 ? parsedQuery.filters : null,
        sorting: parsedQuery.sorting,
        search: parsedQuery.search ? parsedQuery.search.query : null,
        tenant_id: tenantId,
      },
    };

    console.log(
      `✅ [STORE-CATEGORIES] Retrieved ${response.count}/${response.total} categories for tenant: ${tenantId}`
    );

    // Set response headers for debugging
    res.setHeader('X-Tenant-ID', tenantId);
    res.setHeader('X-Store-API', 'true');
    res.setHeader('X-Tenant-Filtered', 'true');

    return res.status(200).json(response);
  } catch (error: any) {
    console.error('❌ [STORE-CATEGORIES] Error:', error);

    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    return res.status(500).json({
      error: 'Failed to fetch product categories',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'store_categories_error',
        timestamp: new Date().toISOString(),
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      },
    });
  }
}
