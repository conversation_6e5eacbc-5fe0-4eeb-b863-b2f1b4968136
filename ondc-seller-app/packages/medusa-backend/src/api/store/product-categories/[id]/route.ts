import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { TenantServiceFactory } from '../../../../services/tenant-service-factory';

/**
 * Store Product Category Detail Endpoint - Customer-facing API with tenant filtering
 * 
 * This endpoint provides tenant-filtered individual category details for the store/customer-facing API.
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    // Extract tenant ID and category ID
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';
    const categoryId = req.params.id;
    
    console.log(`🛒 [STORE-CATEGORY-DETAIL] Getting category ${categoryId} for tenant: ${tenantId}`);

    if (!categoryId) {
      return res.status(400).json({
        error: 'Category ID is required',
        message: 'Category ID must be provided in the URL path',
        tenant_id: tenantId
      });
    }

    // Get query parameters
    const {
      fields,
      expand,
      include_descendants_tree = false
    } = req.query;

    // Create tenant-aware services
    const services = TenantServiceFactory.fromRequest(req);

    // Build config for field selection and relations
    const config: any = {};

    // Add field selection if specified
    if (fields) {
      const fieldArray = Array.isArray(fields) ? fields : [fields];
      config.select = fieldArray;
    }

    // Add relations to expand if specified
    if (expand) {
      const expandArray = Array.isArray(expand) ? expand : [expand];
      config.relations = expandArray;
    } else {
      // Default relations for store API
      config.relations = [
        'parent_category',
        'category_children',
        'products'
      ];
    }

    // Include descendants tree if requested
    if (include_descendants_tree === 'true' || include_descendants_tree === true) {
      config.include_descendants_tree = true;
    }

    console.log(`🛒 [STORE-CATEGORY-DETAIL] Config:`, config);

    // Get category using tenant-aware service
    const category = await services.productCategory.retrieveCategory(categoryId, config);

    if (!category) {
      console.log(`❌ [STORE-CATEGORY-DETAIL] Category ${categoryId} not found for tenant: ${tenantId}`);
      return res.status(404).json({
        error: 'Category not found',
        message: `Category with ID ${categoryId} not found or not accessible for tenant ${tenantId}`,
        category_id: categoryId,
        tenant_id: tenantId
      });
    }

    // Ensure category is active and not internal (store API should only show public categories)
    if (category.is_active === false) {
      console.log(`❌ [STORE-CATEGORY-DETAIL] Category ${categoryId} not active for tenant: ${tenantId}`);
      return res.status(404).json({
        error: 'Category not found',
        message: `Category with ID ${categoryId} is not available`,
        category_id: categoryId,
        tenant_id: tenantId
      });
    }

    if (category.is_internal === true) {
      console.log(`❌ [STORE-CATEGORY-DETAIL] Category ${categoryId} is internal for tenant: ${tenantId}`);
      return res.status(404).json({
        error: 'Category not found',
        message: `Category with ID ${categoryId} is not available`,
        category_id: categoryId,
        tenant_id: tenantId
      });
    }

    // Build response in Medusa store API format
    const response = {
      product_category: category
    };

    console.log(`✅ [STORE-CATEGORY-DETAIL] Retrieved category ${categoryId} (${category.name}) for tenant: ${tenantId}`);

    // Set response headers for debugging
    res.setHeader('X-Tenant-ID', tenantId);
    res.setHeader('X-Store-API', 'true');
    res.setHeader('X-Tenant-Filtered', 'true');

    return res.status(200).json(response);

  } catch (error: any) {
    console.error('❌ [STORE-CATEGORY-DETAIL] Error:', error);

    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';
    const categoryId = req.params.id;

    return res.status(500).json({
      error: 'Failed to fetch category',
      message: error.message,
      category_id: categoryId,
      tenant_id: tenantId,
      _debug: {
        error_type: 'store_category_detail_error',
        timestamp: new Date().toISOString(),
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      }
    });
  }
}
