import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { TenantServiceFactory } from '../../../../services/tenant-service-factory';

/**
 * Store Product Detail Endpoint - Customer-facing API with tenant filtering
 * 
 * This endpoint provides tenant-filtered individual product details for the store/customer-facing API.
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    // Extract tenant ID and product ID
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';
    const productId = req.params.id;
    
    console.log(`🛒 [STORE-PRODUCT-DETAIL] Getting product ${productId} for tenant: ${tenantId}`);

    if (!productId) {
      return res.status(400).json({
        error: 'Product ID is required',
        message: 'Product ID must be provided in the URL path',
        tenant_id: tenantId
      });
    }

    // Get query parameters
    const {
      fields,
      expand,
      region_id,
      currency_code,
      sales_channel_id
    } = req.query;

    // Create tenant-aware services
    const services = TenantServiceFactory.fromRequest(req);

    // Build config for field selection and relations
    const config: any = {};

    // Add field selection if specified
    if (fields) {
      const fieldArray = Array.isArray(fields) ? fields : [fields];
      config.select = fieldArray;
    }

    // Add relations to expand if specified
    if (expand) {
      const expandArray = Array.isArray(expand) ? expand : [expand];
      config.relations = expandArray;
    } else {
      // Default relations for store API
      config.relations = [
        'variants',
        'variants.prices',
        'variants.options',
        'images',
        'categories',
        'collections',
        'tags',
        'type',
        'profile'
      ];
    }

    // Add region context if specified
    if (region_id) {
      config.region_id = region_id;
    }

    // Add currency context if specified
    if (currency_code) {
      config.currency_code = currency_code;
    }

    // Add sales channel context if specified
    if (sales_channel_id) {
      config.sales_channel_id = sales_channel_id;
    }

    console.log(`🛒 [STORE-PRODUCT-DETAIL] Config:`, config);

    // Get product using tenant-aware service
    const product = await services.product.retrieveProduct(productId, config);

    if (!product) {
      console.log(`❌ [STORE-PRODUCT-DETAIL] Product ${productId} not found for tenant: ${tenantId}`);
      return res.status(404).json({
        error: 'Product not found',
        message: `Product with ID ${productId} not found or not accessible for tenant ${tenantId}`,
        product_id: productId,
        tenant_id: tenantId
      });
    }

    // Ensure product is published (store API should only show published products)
    if (product.status !== 'published') {
      console.log(`❌ [STORE-PRODUCT-DETAIL] Product ${productId} not published for tenant: ${tenantId}`);
      return res.status(404).json({
        error: 'Product not found',
        message: `Product with ID ${productId} is not available`,
        product_id: productId,
        tenant_id: tenantId
      });
    }

    // Build response in Medusa store API format
    const response = {
      product: product
    };

    console.log(`✅ [STORE-PRODUCT-DETAIL] Retrieved product ${productId} (${product.title}) for tenant: ${tenantId}`);

    // Set response headers for debugging
    res.setHeader('X-Tenant-ID', tenantId);
    res.setHeader('X-Store-API', 'true');
    res.setHeader('X-Tenant-Filtered', 'true');

    return res.status(200).json(response);

  } catch (error: any) {
    console.error('❌ [STORE-PRODUCT-DETAIL] Error:', error);

    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';
    const productId = req.params.id;

    return res.status(500).json({
      error: 'Failed to fetch product',
      message: error.message,
      product_id: productId,
      tenant_id: tenantId,
      _debug: {
        error_type: 'store_product_detail_error',
        timestamp: new Date().toISOString(),
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      }
    });
  }
}
