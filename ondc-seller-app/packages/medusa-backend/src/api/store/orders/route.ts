import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { authenticate } from '@medusajs/framework';

/**
 * Store Orders Endpoint - Customer-facing API with tenant filtering and authentication
 *
 * This endpoint provides tenant-filtered orders for authenticated customers.
 * It ensures proper multi-tenant isolation and customer authentication.
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  console.log(`📦 [STORE-ORDERS] Getting orders for authenticated customer`);
  console.log(`📦 [STORE-ORDERS] Headers:`, {
    authorization: req.headers.authorization ? 'Bearer [REDACTED]' : 'None',
    'x-tenant-id': req.headers['x-tenant-id'],
    'x-publishable-api-key': req.headers['x-publishable-api-key'],
  });

  try {
    // Extract tenant ID from header
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';
    console.log(`📦 [STORE-ORDERS] Processing orders request for tenant: ${tenantId}`);

    // Check if user is authenticated
    if (!req.auth_context || !req.auth_context.actor_id) {
      console.log(`❌ [STORE-ORDERS] No authentication context found`);
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required to access orders',
      });
    }

    const customerId = req.auth_context.actor_id;
    console.log(`📦 [STORE-ORDERS] Authenticated customer ID: ${customerId}`);

    // Get query parameters
    const { limit = 50, offset = 0, fields, order = '-created_at', ...filters } = req.query;

    console.log(`📦 [STORE-ORDERS] Query params:`, { limit, offset, order, filters });

    // Use direct database connection for now to ensure proper tenant filtering
    const { Client } = require('pg');
    const client = new Client({
      connectionString:
        process.env.DATABASE_URL ||
        'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
    });

    let orders: any[] = [];
    let count = 0;

    try {
      await client.connect();
      console.log(`🔗 [STORE-ORDERS] Connected to database`);

      // Get total count for this customer and tenant
      const countResult = await client.query(
        `SELECT COUNT(*) as total 
         FROM "order" 
         WHERE customer_id = $1 
         AND tenant_id = $2 
         AND deleted_at IS NULL`,
        [customerId, tenantId]
      );
      count = parseInt(countResult.rows[0]?.total || 0);
      console.log(
        `📊 [STORE-ORDERS] Total orders for customer ${customerId} in tenant ${tenantId}: ${count}`
      );

      if (count === 0) {
        console.log(`📦 [STORE-ORDERS] No orders found for customer`);
        return res.status(200).json({
          orders: [],
          count: 0,
          offset: parseInt(offset as string),
          limit: parseInt(limit as string),
        });
      }

      // Get orders with pagination and totals
      const result = await client.query(
        `
        SELECT
          o.id, o.status, o.currency_code, o.email, o.display_id,
          o.created_at, o.updated_at, o.tenant_id, o.metadata,
          o.customer_id, o.region_id, o.sales_channel_id,
          o.shipping_address_id, o.billing_address_id,
          o.is_draft_order, o.no_notification,
          os.totals as order_totals
        FROM "order" o
        LEFT JOIN order_summary os ON o.id = os.order_id AND os.deleted_at IS NULL
        WHERE o.customer_id = $1
        AND o.tenant_id = $2
        AND o.deleted_at IS NULL
        ORDER BY o.created_at DESC
        LIMIT $3 OFFSET $4
      `,
        [customerId, tenantId, parseInt(limit as string), parseInt(offset as string)]
      );

      orders = result.rows;
      console.log(`✅ [STORE-ORDERS] Found ${orders.length} orders for customer`);

      // Populate order items for each order
      for (let i = 0; i < orders.length; i++) {
        const order = orders[i];
        console.log(`📦 [STORE-ORDERS] Fetching items for order: ${order.id}`);

        const itemsResult = await client.query(
          `
          SELECT
            oi.id as order_item_id,
            oi.quantity,
            oi.fulfilled_quantity,
            oi.shipped_quantity,
            oi.unit_price as order_item_unit_price,
            oli.id as line_item_id,
            oli.title,
            oli.subtitle,
            oli.thumbnail,
            oli.variant_id,
            oli.product_id,
            oli.product_title,
            oli.product_description,
            oli.variant_title,
            oli.variant_sku,
            oli.unit_price,
            oli.compare_at_unit_price,
            oli.metadata as item_metadata,
            oli.created_at as item_created_at,
            oli.updated_at as item_updated_at
          FROM order_item oi
          JOIN order_line_item oli ON oi.item_id = oli.id
          WHERE oi.order_id = $1
          AND oi.deleted_at IS NULL
          AND oli.deleted_at IS NULL
          ORDER BY oi.created_at ASC
          `,
          [order.id]
        );

        orders[i].items = itemsResult.rows;
        console.log(
          `📦 [STORE-ORDERS] Found ${itemsResult.rows.length} items for order ${order.id}`
        );

        // Fetch shipping methods for this order
        const shippingResult = await client.query(
          `
          SELECT
            osm.name as shipping_method_name,
            osm.amount as shipping_amount,
            osm.raw_amount as raw_shipping_amount,
            osm.is_tax_inclusive as shipping_tax_inclusive
          FROM order_shipping os
          JOIN order_shipping_method osm ON os.shipping_method_id = osm.id
          WHERE os.order_id = $1
          AND os.deleted_at IS NULL
          AND osm.deleted_at IS NULL
          `,
          [order.id]
        );

        // Calculate totals from items and shipping
        const items = itemsResult.rows;
        let itemSubtotal = 0;
        let itemTotal = 0;

        items.forEach(item => {
          const unitPrice = parseFloat(item.unit_price || 0);
          const quantity = parseFloat(item.quantity || 0);
          const itemAmount = unitPrice * quantity;
          itemSubtotal += itemAmount;
          itemTotal += itemAmount;
        });

        // Get shipping total
        let shippingTotal = 0;
        let shippingSubtotal = 0;
        shippingResult.rows.forEach(shipping => {
          const amount = parseFloat(shipping.shipping_amount || 0);
          shippingTotal += amount;
          shippingSubtotal += amount;
        });

        // Parse order totals from order_summary
        const orderTotals = order.order_totals || {};
        const currentOrderTotal = orderTotals.current_order_total || 0;
        const originalOrderTotal = orderTotals.original_order_total || 0;

        // Add calculated financial fields to the order
        orders[i] = {
          ...orders[i],
          // Item totals
          original_item_total: itemTotal,
          original_item_subtotal: itemSubtotal,
          original_item_tax_total: 0, // Would need tax calculation logic
          item_total: itemTotal,
          item_subtotal: itemSubtotal,
          item_tax_total: 0, // Would need tax calculation logic

          // Order totals
          original_total: originalOrderTotal,
          original_subtotal: originalOrderTotal - shippingTotal,
          original_tax_total: 0, // Would need tax calculation logic
          total: currentOrderTotal,
          subtotal: currentOrderTotal - shippingTotal,
          tax_total: 0, // Would need tax calculation logic

          // Discount totals
          discount_total: 0, // Would need discount calculation logic
          discount_tax_total: 0,

          // Gift card totals
          gift_card_total: 0, // Would need gift card logic
          gift_card_tax_total: 0,

          // Shipping totals
          shipping_total: shippingTotal,
          shipping_subtotal: shippingSubtotal,
          shipping_tax_total: 0, // Would need shipping tax calculation
          original_shipping_total: shippingTotal,
          original_shipping_subtotal: shippingSubtotal,
          original_shipping_tax_total: 0,

          // Payment totals from order_summary
          paid_total: orderTotals.paid_total || 0,
          refunded_total: orderTotals.refunded_total || 0,
          pending_difference: orderTotals.pending_difference || 0,
        };
      }
    } catch (dbError) {
      console.error('❌ [STORE-ORDERS] Database error:', dbError);
      throw dbError;
    } finally {
      await client.end();
    }

    // Return response in Medusa v2 format
    const response = {
      orders,
      count,
      offset: parseInt(offset as string),
      limit: parseInt(limit as string),
    };

    console.log(`✅ [STORE-ORDERS] Returning ${orders.length} orders`);
    return res.status(200).json(response);
  } catch (error) {
    console.error('❌ [STORE-ORDERS] Error fetching orders:', error);
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch orders',
    });
  }
}
