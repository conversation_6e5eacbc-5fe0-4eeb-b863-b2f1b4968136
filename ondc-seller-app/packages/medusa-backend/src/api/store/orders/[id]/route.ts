import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { authenticate } from '@medusajs/framework';

/**
 * Store Single Order Endpoint - Customer-facing API with tenant filtering and authentication
 *
 * This endpoint provides tenant-filtered individual order details for authenticated customers.
 * It ensures proper multi-tenant isolation and customer authentication.
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const orderId = req.params.id;

  console.log(`📦 [STORE-ORDER-DETAIL] Getting order ${orderId} for authenticated customer`);
  console.log(`📦 [STORE-ORDER-DETAIL] Headers:`, {
    authorization: req.headers.authorization ? 'Bearer [REDACTED]' : 'None',
    'x-tenant-id': req.headers['x-tenant-id'],
    'x-publishable-api-key': req.headers['x-publishable-api-key'],
  });

  try {
    // Extract tenant ID from header
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';
    console.log(`📦 [STORE-ORDER-DETAIL] Processing order request for tenant: ${tenantId}`);

    // Validate order ID
    if (!orderId) {
      console.log(`❌ [STORE-ORDER-DETAIL] Order ID is required`);
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Order ID is required',
        tenant_id: tenantId,
      });
    }

    // Check if user is authenticated
    if (!req.auth_context || !req.auth_context.actor_id) {
      console.log(`❌ [STORE-ORDER-DETAIL] No authentication context found`);
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required to access order details',
      });
    }

    const customerId = req.auth_context.actor_id;
    console.log(`📦 [STORE-ORDER-DETAIL] Authenticated customer ID: ${customerId}`);

    // Get query parameters for field selection and expansion
    const { fields, expand } = req.query;
    console.log(`📦 [STORE-ORDER-DETAIL] Query params:`, { fields, expand });

    // Use direct database connection for now to ensure proper tenant filtering
    const { Client } = require('pg');
    const client = new Client({
      connectionString:
        process.env.DATABASE_URL ||
        'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
    });

    let order: any = null;

    try {
      await client.connect();
      console.log(`🔗 [STORE-ORDER-DETAIL] Connected to database`);

      // First, check if order exists and belongs to the authenticated customer and tenant
      const orderCheckResult = await client.query(
        `SELECT id, customer_id, tenant_id 
         FROM "order" 
         WHERE id = $1 
         AND customer_id = $2 
         AND tenant_id = $3 
         AND deleted_at IS NULL`,
        [orderId, customerId, tenantId]
      );

      if (orderCheckResult.rows.length === 0) {
        console.log(
          `❌ [STORE-ORDER-DETAIL] Order ${orderId} not found for customer ${customerId} in tenant ${tenantId}`
        );
        return res.status(404).json({
          error: 'Not Found',
          message: 'Order not found or access denied',
          order_id: orderId,
          tenant_id: tenantId,
        });
      }

      console.log(`✅ [STORE-ORDER-DETAIL] Order ${orderId} found and belongs to customer`);

      // Get comprehensive order details with related data
      const result = await client.query(
        `
        SELECT
          o.id, o.status, o.currency_code, o.email, o.display_id,
          o.created_at, o.updated_at, o.tenant_id, o.metadata,
          o.customer_id, o.region_id, o.sales_channel_id,
          o.shipping_address_id, o.billing_address_id,
          o.is_draft_order, o.no_notification,
          os.totals as order_totals,

          -- Customer information
          c.id as customer_id, c.email as customer_email,
          c.first_name as customer_first_name, c.last_name as customer_last_name,
          c.phone as customer_phone,

          -- Shipping address
          sa.id as shipping_address_id, sa.first_name as shipping_first_name,
          sa.last_name as shipping_last_name, sa.address_1 as shipping_address_1,
          sa.address_2 as shipping_address_2, sa.city as shipping_city,
          sa.postal_code as shipping_postal_code, sa.province as shipping_province,
          sa.country_code as shipping_country_code, sa.phone as shipping_phone,

          -- Billing address
          ba.id as billing_address_id, ba.first_name as billing_first_name,
          ba.last_name as billing_last_name, ba.address_1 as billing_address_1,
          ba.address_2 as billing_address_2, ba.city as billing_city,
          ba.postal_code as billing_postal_code, ba.province as billing_province,
          ba.country_code as billing_country_code, ba.phone as billing_phone

        FROM "order" o
        LEFT JOIN order_summary os ON o.id = os.order_id AND os.deleted_at IS NULL
        LEFT JOIN customer c ON o.customer_id = c.id AND c.deleted_at IS NULL
        LEFT JOIN order_address sa ON o.shipping_address_id = sa.id AND sa.deleted_at IS NULL
        LEFT JOIN order_address ba ON o.billing_address_id = ba.id AND ba.deleted_at IS NULL
        WHERE o.id = $1
        AND o.customer_id = $2
        AND o.tenant_id = $3
        AND o.deleted_at IS NULL
      `,
        [orderId, customerId, tenantId]
      );

      if (result.rows.length === 0) {
        console.log(`❌ [STORE-ORDER-DETAIL] Order ${orderId} not found after detailed query`);
        return res.status(404).json({
          error: 'Not Found',
          message: 'Order not found',
          order_id: orderId,
        });
      }

      const orderRow = result.rows[0];
      console.log(`✅ [STORE-ORDER-DETAIL] Retrieved order details for ${orderId}`);

      // Get order line items with order_item quantities
      const itemsResult = await client.query(
        `
        SELECT
          oli.id, oli.title, oli.subtitle, oli.thumbnail,
          oli.unit_price, oli.metadata, oli.variant_id, oli.product_id,
          oli.created_at, oli.updated_at, oli.product_title, oli.product_description,
          oli.variant_title, oli.variant_sku, oli.variant_barcode,

          -- Order item quantities and totals
          oi.quantity, oi.fulfilled_quantity, oi.shipped_quantity,

          -- Product information
          p.id as product_id, p.title as product_title,
          p.description as product_description, p.thumbnail as product_thumbnail,

          -- Variant information
          pv.id as variant_id, pv.title as variant_title,
          pv.sku as variant_sku, pv.barcode as variant_barcode

        FROM order_line_item oli
        LEFT JOIN order_item oi ON oli.id = oi.item_id AND oi.deleted_at IS NULL
        LEFT JOIN product p ON oli.product_id = p.id AND p.deleted_at IS NULL
        LEFT JOIN product_variant pv ON oli.variant_id = pv.id AND pv.deleted_at IS NULL
        WHERE oi.order_id = $1
        AND oli.deleted_at IS NULL
        ORDER BY oli.created_at ASC
      `,
        [orderId]
      );

      const items = itemsResult.rows.map(item => ({
        id: item.id,
        title: item.title,
        subtitle: item.subtitle,
        thumbnail: item.thumbnail,
        quantity: item.quantity,
        fulfilled_quantity: item.fulfilled_quantity,
        shipped_quantity: item.shipped_quantity,
        unit_price: item.unit_price,
        metadata: item.metadata,
        variant_id: item.variant_id,
        product_id: item.product_id,
        created_at: item.created_at,
        updated_at: item.updated_at,
        product: item.product_id
          ? {
              id: item.product_id,
              title: item.product_title,
              description: item.product_description,
              thumbnail: item.product_thumbnail,
            }
          : null,
        variant: item.variant_id
          ? {
              id: item.variant_id,
              title: item.variant_title,
              sku: item.variant_sku,
              barcode: item.variant_barcode,
            }
          : null,
      }));

      console.log(`📦 [STORE-ORDER-DETAIL] Found ${items.length} items for order ${orderId}`);

      // Get comprehensive financial totals (same as store orders list endpoint)
      const financialResult = await client.query(
        `
        SELECT
          -- Item totals
          COALESCE(SUM(oli.unit_price * oi.quantity), 0) as original_item_total,
          COALESCE(SUM(oli.unit_price * oi.quantity), 0) as original_item_subtotal,
          0 as original_item_tax_total,
          COALESCE(SUM(oli.unit_price * oi.quantity), 0) as item_total,
          COALESCE(SUM(oli.unit_price * oi.quantity), 0) as item_subtotal,
          0 as item_tax_total,

          -- Shipping totals (default to 50 for now, can be made dynamic)
          50 as original_shipping_total,
          50 as original_shipping_subtotal,
          0 as original_shipping_tax_total,
          50 as shipping_total,
          50 as shipping_subtotal,
          0 as shipping_tax_total,

          -- Discount and gift card totals
          0 as discount_total,
          0 as discount_tax_total,
          0 as gift_card_total,
          0 as gift_card_tax_total,

          -- Payment totals
          0 as paid_total,
          0 as refunded_total

        FROM order_line_item oli
        LEFT JOIN order_item oi ON oli.id = oi.item_id AND oi.deleted_at IS NULL
        WHERE oi.order_id = $1
        AND oli.deleted_at IS NULL
      `,
        [orderId]
      );

      const financials = financialResult.rows[0] || {};

      // Calculate derived totals (corrected logic to match store orders list)
      // Ensure all values are properly converted to numbers
      const originalSubtotal = Number(financials.original_item_subtotal) || 0; // Items only
      const originalTaxTotal =
        (Number(financials.original_item_tax_total) || 0) +
        (Number(financials.original_shipping_tax_total) || 0);
      const originalTotal =
        originalSubtotal +
        (Number(financials.original_shipping_subtotal) || 0) +
        originalTaxTotal -
        (Number(financials.discount_total) || 0) -
        (Number(financials.gift_card_total) || 0);

      const subtotal = Number(financials.item_subtotal) || 0; // Items only
      const taxTotal =
        (Number(financials.item_tax_total) || 0) + (Number(financials.shipping_tax_total) || 0);
      const total =
        subtotal +
        (Number(financials.shipping_subtotal) || 0) +
        taxTotal -
        (Number(financials.discount_total) || 0) -
        (Number(financials.gift_card_total) || 0);
      const pendingDifference = total - (Number(financials.paid_total) || 0);

      // Build comprehensive order object with all financial fields
      order = {
        id: orderRow.id,
        status: orderRow.status,
        currency_code: orderRow.currency_code,
        email: orderRow.email,
        display_id: orderRow.display_id,
        created_at: orderRow.created_at,
        updated_at: orderRow.updated_at,
        tenant_id: orderRow.tenant_id,
        metadata: orderRow.metadata,
        customer_id: orderRow.customer_id,
        region_id: orderRow.region_id,
        sales_channel_id: orderRow.sales_channel_id,
        shipping_address_id: orderRow.shipping_address_id,
        billing_address_id: orderRow.billing_address_id,
        is_draft_order: orderRow.is_draft_order,
        no_notification: orderRow.no_notification,

        // Comprehensive financial fields (matching store orders list format)
        original_item_total: financials.original_item_total || 0,
        original_item_subtotal: financials.original_item_subtotal || 0,
        original_item_tax_total: financials.original_item_tax_total || 0,
        item_total: financials.item_total || 0,
        item_subtotal: financials.item_subtotal || 0,
        item_tax_total: financials.item_tax_total || 0,
        original_total: originalTotal,
        original_subtotal: originalSubtotal,
        original_tax_total: originalTaxTotal,
        total: total,
        subtotal: subtotal,
        tax_total: taxTotal,
        discount_total: financials.discount_total || 0,
        discount_tax_total: financials.discount_tax_total || 0,
        gift_card_total: financials.gift_card_total || 0,
        gift_card_tax_total: financials.gift_card_tax_total || 0,
        shipping_total: financials.shipping_total || 0,
        shipping_subtotal: financials.shipping_subtotal || 0,
        shipping_tax_total: financials.shipping_tax_total || 0,
        original_shipping_total: financials.original_shipping_total || 0,
        original_shipping_subtotal: financials.original_shipping_subtotal || 0,
        original_shipping_tax_total: financials.original_shipping_tax_total || 0,
        paid_total: financials.paid_total || 0,
        refunded_total: financials.refunded_total || 0,
        pending_difference: pendingDifference,

        // Totals from order_summary (for compatibility)
        totals: orderRow.order_totals,

        // Customer information
        customer: orderRow.customer_id
          ? {
              id: orderRow.customer_id,
              email: orderRow.customer_email,
              first_name: orderRow.customer_first_name,
              last_name: orderRow.customer_last_name,
              phone: orderRow.customer_phone,
            }
          : null,

        // Shipping address
        shipping_address: orderRow.shipping_address_id
          ? {
              id: orderRow.shipping_address_id,
              first_name: orderRow.shipping_first_name,
              last_name: orderRow.shipping_last_name,
              address_1: orderRow.shipping_address_1,
              address_2: orderRow.shipping_address_2,
              city: orderRow.shipping_city,
              postal_code: orderRow.shipping_postal_code,
              province: orderRow.shipping_province,
              country_code: orderRow.shipping_country_code,
              phone: orderRow.shipping_phone,
            }
          : null,

        // Billing address
        billing_address: orderRow.billing_address_id
          ? {
              id: orderRow.billing_address_id,
              first_name: orderRow.billing_first_name,
              last_name: orderRow.billing_last_name,
              address_1: orderRow.billing_address_1,
              address_2: orderRow.billing_address_2,
              city: orderRow.billing_city,
              postal_code: orderRow.billing_postal_code,
              province: orderRow.billing_province,
              country_code: orderRow.billing_country_code,
              phone: orderRow.billing_phone,
            }
          : null,

        // Order items
        items: items,
      };
    } catch (dbError) {
      console.error('❌ [STORE-ORDER-DETAIL] Database error:', dbError);
      throw dbError;
    } finally {
      await client.end();
      console.log(`🔗 [STORE-ORDER-DETAIL] Database connection closed`);
    }

    // Return response in Medusa v2 format
    const response = {
      order,
    };

    console.log(`✅ [STORE-ORDER-DETAIL] Returning order ${orderId} for customer ${customerId}`);
    return res.status(200).json(response);
  } catch (error) {
    console.error('❌ [STORE-ORDER-DETAIL] Error fetching order:', error);
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch order details',
    });
  }
}
