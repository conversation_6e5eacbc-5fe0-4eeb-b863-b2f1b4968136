import { 
  createSalesChannelsWorkflow,
  createApiKeysWorkflow,
  linkSalesChannelsToApiKeyWorkflow,
  createRegionsWorkflow,
  updateStoresWorkflow
} from "@medusajs/core-flows"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"

/**
 * Setup Tenant Sales Channels
 * 
 * This script creates sales channels for each tenant and generates
 * publishable API keys for proper tenant isolation using Medusa's
 * native multi-tenancy capabilities.
 */

interface TenantConfig {
  id: string
  name: string
  description: string
  domain: string
  metadata: {
    tenantId: string
    ondcConfig: {
      participantId: string
      subscriberId: string
      bppId: string
      domain: string
      region: string
    }
    currency: string
    timezone: string
    features: string[]
    branding: {
      primaryColor: string
      secondaryColor: string
      logo?: string
    }
  }
}

const TENANT_CONFIGURATIONS: TenantConfig[] = [
  {
    id: 'tenant-electronics-001',
    name: 'Electronics Store',
    description: 'Electronics and gadgets marketplace for ONDC',
    domain: 'electronics.ondc-seller.com',
    metadata: {
      tenantId: 'tenant-electronics-001',
      ondcConfig: {
        participantId: 'electronics-participant-001',
        subscriberId: 'electronics-subscriber-001',
        bppId: 'ondc-bpp-electronics-001',
        domain: 'electronics',
        region: 'IND'
      },
      currency: 'INR',
      timezone: 'Asia/Kolkata',
      features: ['products', 'orders', 'customers', 'analytics', 'inventory'],
      branding: {
        primaryColor: '#2563eb',
        secondaryColor: '#1e40af',
        logo: '/logos/electronics-logo.png'
      }
    }
  },
  {
    id: 'tenant-fashion-002',
    name: 'Fashion Store',
    description: 'Fashion and clothing marketplace for ONDC',
    domain: 'fashion.ondc-seller.com',
    metadata: {
      tenantId: 'tenant-fashion-002',
      ondcConfig: {
        participantId: 'fashion-participant-002',
        subscriberId: 'fashion-subscriber-002',
        bppId: 'ondc-bpp-fashion-002',
        domain: 'fashion',
        region: 'IND'
      },
      currency: 'INR',
      timezone: 'Asia/Kolkata',
      features: ['products', 'orders', 'customers', 'analytics', 'inventory'],
      branding: {
        primaryColor: '#ec4899',
        secondaryColor: '#db2777',
        logo: '/logos/fashion-logo.png'
      }
    }
  },
  {
    id: 'default',
    name: 'Default Store',
    description: 'Default marketplace for ONDC',
    domain: 'localhost',
    metadata: {
      tenantId: 'default',
      ondcConfig: {
        participantId: 'default-participant',
        subscriberId: 'default-subscriber',
        bppId: 'ondc-bpp-default',
        domain: 'general',
        region: 'IND'
      },
      currency: 'INR',
      timezone: 'Asia/Kolkata',
      features: ['products', 'orders', 'customers', 'analytics', 'inventory'],
      branding: {
        primaryColor: '#059669',
        secondaryColor: '#047857',
        logo: '/logos/default-logo.png'
      }
    }
  }
]

export async function setupTenantSalesChannels(container: any) {
  const logger = container.resolve(ContainerRegistrationKeys.LOGGER)
  
  try {
    logger.info("🚀 Starting tenant sales channels setup...")

    // Step 1: Create India region if it doesn't exist
    logger.info("📍 Setting up India region...")
    
    const { result: regions } = await createRegionsWorkflow(container).run({
      input: {
        regions: [{
          name: "India",
          currency_code: "inr",
          countries: ["in"],
          payment_providers: ["pp_system_default"],
          metadata: {
            timezone: "Asia/Kolkata",
            ondcRegion: true
          }
        }]
      }
    })
    
    const indiaRegion = regions[0]
    logger.info(`✅ India region created: ${indiaRegion.id}`)

    // Step 2: Create sales channels for each tenant
    logger.info("🏪 Creating tenant sales channels...")
    
    const { result: salesChannels } = await createSalesChannelsWorkflow(container).run({
      input: {
        salesChannelsData: TENANT_CONFIGURATIONS.map(config => ({
          name: config.name,
          description: config.description,
          is_disabled: false,
          metadata: {
            ...config.metadata,
            regionId: indiaRegion.id,
            setupDate: new Date().toISOString()
          }
        }))
      }
    })

    logger.info(`✅ Created ${salesChannels.length} sales channels`)

    // Step 3: Create publishable API keys for each sales channel
    logger.info("🔑 Creating publishable API keys...")
    
    const tenantApiKeys: Record<string, string> = {}

    for (let i = 0; i < salesChannels.length; i++) {
      const salesChannel = salesChannels[i]
      const config = TENANT_CONFIGURATIONS[i]

      // Create API key
      const { result: apiKeys } = await createApiKeysWorkflow(container).run({
        input: {
          api_keys: [{
            title: `${config.name} Publishable Key`,
            type: "publishable",
            created_by: "system",
            metadata: {
              tenantId: config.id,
              salesChannelId: salesChannel.id,
              createdAt: new Date().toISOString()
            }
          }]
        }
      })

      const apiKey = apiKeys[0]

      // Link API key to sales channel
      await linkSalesChannelsToApiKeyWorkflow(container).run({
        input: {
          id: apiKey.id,
          add: [salesChannel.id]
        }
      })

      tenantApiKeys[config.id] = apiKey.token

      logger.info(`✅ Tenant: ${config.id}`)
      logger.info(`   Sales Channel ID: ${salesChannel.id}`)
      logger.info(`   API Key: ${apiKey.token}`)
      logger.info(`   Domain: ${config.domain}`)
    }

    // Step 4: Update store configuration
    logger.info("🏬 Updating store configuration...")
    
    const query = container.resolve(ContainerRegistrationKeys.QUERY)
    const { data: stores } = await query.graph({
      entity: "store",
      fields: ["id"],
      filters: {}
    })

    if (stores.length > 0) {
      const store = stores[0]
      
      await updateStoresWorkflow(container).run({
        input: {
          selector: { id: store.id },
          update: {
            supported_currencies: [
              {
                currency_code: "inr",
                is_default: true
              }
            ],
            default_sales_channel_id: salesChannels[0].id, // Default to first sales channel
            metadata: {
              multiTenantEnabled: true,
              ondcCompliant: true,
              setupDate: new Date().toISOString()
            }
          }
        }
      })

      logger.info(`✅ Store updated with multi-tenant configuration`)
    }

    // Step 5: Generate environment variables
    logger.info("📝 Generating environment variables...")
    
    const envVars = [
      "# Tenant-specific Publishable API Keys",
      "# Add these to your frontend .env.local file",
      ""
    ]

    Object.entries(tenantApiKeys).forEach(([tenantId, apiKey]) => {
      const envVarName = `NEXT_PUBLIC_${tenantId.toUpperCase().replace(/-/g, '_')}_API_KEY`
      envVars.push(`${envVarName}=${apiKey}`)
    })

    envVars.push("")
    envVars.push("# Sales Channel IDs for admin operations")
    
    salesChannels.forEach((channel, index) => {
      const config = TENANT_CONFIGURATIONS[index]
      const envVarName = `${config.id.toUpperCase().replace(/-/g, '_')}_SALES_CHANNEL_ID`
      envVars.push(`${envVarName}=${channel.id}`)
    })

    logger.info("\n" + "=".repeat(60))
    logger.info("🎉 TENANT SALES CHANNELS SETUP COMPLETE!")
    logger.info("=".repeat(60))
    logger.info("\n📋 SUMMARY:")
    logger.info(`   • Created ${salesChannels.length} sales channels`)
    logger.info(`   • Generated ${Object.keys(tenantApiKeys).length} publishable API keys`)
    logger.info(`   • Configured India region with INR currency`)
    logger.info(`   • Updated store for multi-tenant support`)
    
    logger.info("\n🔧 NEXT STEPS:")
    logger.info("   1. Add the environment variables below to your frontend")
    logger.info("   2. Update your frontend API client to use tenant-specific keys")
    logger.info("   3. Remove custom tenant middleware and endpoints")
    logger.info("   4. Test the native Medusa multi-tenancy")
    
    logger.info("\n📝 ENVIRONMENT VARIABLES:")
    logger.info("-".repeat(40))
    envVars.forEach(line => logger.info(line))
    logger.info("-".repeat(40))

    return {
      salesChannels,
      apiKeys: tenantApiKeys,
      region: indiaRegion,
      success: true
    }

  } catch (error) {
    logger.error("❌ Error setting up tenant sales channels:", error)
    throw error
  }
}

// CLI execution
if (require.main === module) {
  const { getContainer } = require("@medusajs/framework")
  
  async function run() {
    const container = getContainer()
    await setupTenantSalesChannels(container)
    process.exit(0)
  }

  run().catch(error => {
    console.error("Setup failed:", error)
    process.exit(1)
  })
}
