#!/usr/bin/env node

/**
 * Test Script for Product Auto-Configuration
 * 
 * This script tests the automatic sales channel assignment and inventory management
 * configuration for products created via API and Excel import.
 */

import { MedusaApp } from "@medusajs/framework"
import { createProductsWithAutoConfigWorkflow } from "../workflows/product-auto-config"

async function testProductAutoConfig() {
  console.log("🧪 Starting Product Auto-Configuration Test")
  
  try {
    // Initialize Medusa app
    const { container } = await MedusaApp({
      directory: process.cwd(),
    })

    console.log("✅ Medusa app initialized")

    // Test 1: Validate default sales channel exists
    console.log("\n📺 Test 1: Validating default sales channel...")
    const productAutoConfigService = container.resolve("productAutoConfigService")
    
    const isValidSalesChannel = await productAutoConfigService.validateDefaultSalesChannel()
    if (isValidSalesChannel) {
      console.log(`✅ Default sales channel exists: ${productAutoConfigService.getDefaultSalesChannelId()}`)
    } else {
      console.log(`❌ Default sales channel not found: ${productAutoConfigService.getDefaultSalesChannelId()}`)
      console.log("⚠️  Please ensure the sales channel is created before running this test")
    }

    // Test 2: Create a test product using the auto-config workflow
    console.log("\n🛍️  Test 2: Creating test product with auto-configuration...")
    
    const testProductData = {
      title: "Test Auto-Config Product",
      handle: "test-auto-config-product",
      description: "A test product to verify automatic sales channel assignment and inventory management configuration",
      status: "draft",
      variants: [
        {
          title: "Default Variant",
          sku: "TEST-AUTO-CONFIG-001",
          prices: [
            {
              currency_code: "inr",
              amount: 1000
            }
          ]
        }
      ]
    }

    const workflowResult = await createProductsWithAutoConfigWorkflow(container).run({
      input: {
        products: [testProductData],
        tenantId: "default"
      }
    })

    const createdProduct = workflowResult.result.products[0]
    const configResults = workflowResult.result.configurationResults

    console.log(`✅ Product created: ${createdProduct.id}`)
    console.log(`📊 Auto-configuration results:`, configResults)

    // Test 3: Verify the configuration was applied
    console.log("\n🔍 Test 3: Verifying auto-configuration...")
    
    // Check sales channel assignment
    const salesChannelService = container.resolve("salesChannelService")
    try {
      const salesChannels = await salesChannelService.listAndCount({
        id: [productAutoConfigService.getDefaultSalesChannelId()]
      })
      
      if (salesChannels[1] > 0) {
        console.log(`✅ Sales channel verified: ${salesChannels[0][0].name}`)
      } else {
        console.log(`❌ Sales channel not found`)
      }
    } catch (error) {
      console.log(`⚠️  Could not verify sales channel: ${error.message}`)
    }

    // Check variant inventory management
    const productVariantService = container.resolve("productVariantService")
    try {
      const variants = await productVariantService.list({
        product_id: createdProduct.id
      })
      
      for (const variant of variants) {
        if (variant.manage_inventory === false) {
          console.log(`✅ Variant ${variant.id} has manage_inventory: false`)
        } else {
          console.log(`❌ Variant ${variant.id} has manage_inventory: ${variant.manage_inventory}`)
        }
      }
    } catch (error) {
      console.log(`⚠️  Could not verify variant configuration: ${error.message}`)
    }

    console.log("\n🎉 Product Auto-Configuration Test Completed!")
    console.log("\n📋 Summary:")
    console.log(`- Default Sales Channel ID: ${productAutoConfigService.getDefaultSalesChannelId()}`)
    console.log(`- Sales Channel Valid: ${isValidSalesChannel ? 'Yes' : 'No'}`)
    console.log(`- Test Product Created: ${createdProduct.id}`)
    console.log(`- Auto-Config Successful: ${configResults.successful}`)
    console.log(`- Auto-Config Failed: ${configResults.failed}`)
    
    if (configResults.errors.length > 0) {
      console.log(`- Errors: ${configResults.errors.join(', ')}`)
    }

  } catch (error) {
    console.error("❌ Test failed:", error)
    process.exit(1)
  }
}

// Run the test
if (require.main === module) {
  testProductAutoConfig()
    .then(() => {
      console.log("\n✅ Test script completed successfully")
      process.exit(0)
    })
    .catch((error) => {
      console.error("\n❌ Test script failed:", error)
      process.exit(1)
    })
}

export { testProductAutoConfig }
