import { ContainerRegistrationKeys } from "@medusajs/framework/utils"
import { linkProductsToSalesChannelWorkflow } from "@medusajs/core-flows"

/**
 * Migrate Tenant Data to Sales Channels
 * 
 * This script migrates existing tenant-based data (products, customers, orders)
 * to Medusa's native Sales Channel system for proper multi-tenancy.
 */

interface MigrationStats {
  productsProcessed: number
  productsMigrated: number
  customersProcessed: number
  customersMigrated: number
  ordersProcessed: number
  ordersMigrated: number
  errors: string[]
}

export async function migrateTenantDataToSalesChannels(container: any): Promise<MigrationStats> {
  const logger = container.resolve(ContainerRegistrationKeys.LOGGER)
  const query = container.resolve(ContainerRegistrationKeys.QUERY)
  
  const stats: MigrationStats = {
    productsProcessed: 0,
    productsMigrated: 0,
    customersProcessed: 0,
    customersMigrated: 0,
    ordersProcessed: 0,
    ordersMigrated: 0,
    errors: []
  }

  try {
    logger.info("🔄 Starting tenant data migration to sales channels...")

    // Step 1: Get all sales channels and create tenant mapping
    logger.info("📋 Building tenant to sales channel mapping...")
    
    const { data: salesChannels } = await query.graph({
      entity: "sales_channel",
      fields: ["id", "name", "metadata"],
      filters: {}
    })

    const tenantToSalesChannel = new Map<string, string>()
    const salesChannelToTenant = new Map<string, string>()
    
    salesChannels.forEach((channel: any) => {
      const tenantId = channel.metadata?.tenantId
      if (tenantId) {
        tenantToSalesChannel.set(tenantId, channel.id)
        salesChannelToTenant.set(channel.id, tenantId)
        logger.info(`   • ${tenantId} → ${channel.id} (${channel.name})`)
      }
    })

    if (tenantToSalesChannel.size === 0) {
      throw new Error("No sales channels with tenant metadata found. Please run setup-tenant-sales-channels first.")
    }

    // Step 2: Migrate Products
    logger.info("📦 Migrating products to sales channels...")
    
    const { data: products } = await query.graph({
      entity: "product",
      fields: ["id", "title", "metadata"],
      filters: {}
    })

    stats.productsProcessed = products.length
    logger.info(`   Found ${products.length} products to process`)

    const productsBySalesChannel = new Map<string, string[]>()

    for (const product of products) {
      try {
        const tenantId = product.metadata?.tenant_id
        
        if (tenantId && tenantToSalesChannel.has(tenantId)) {
          const salesChannelId = tenantToSalesChannel.get(tenantId)!
          
          if (!productsBySalesChannel.has(salesChannelId)) {
            productsBySalesChannel.set(salesChannelId, [])
          }
          
          productsBySalesChannel.get(salesChannelId)!.push(product.id)
          stats.productsMigrated++
        } else if (tenantId) {
          stats.errors.push(`Product ${product.id} has unknown tenant_id: ${tenantId}`)
        }
      } catch (error) {
        stats.errors.push(`Error processing product ${product.id}: ${error.message}`)
      }
    }

    // Link products to sales channels in batches
    for (const [salesChannelId, productIds] of productsBySalesChannel.entries()) {
      try {
        const tenantId = salesChannelToTenant.get(salesChannelId)
        logger.info(`   Linking ${productIds.length} products to ${tenantId} (${salesChannelId})`)
        
        // Process in batches of 50 to avoid overwhelming the system
        const batchSize = 50
        for (let i = 0; i < productIds.length; i += batchSize) {
          const batch = productIds.slice(i, i + batchSize)
          
          await linkProductsToSalesChannelWorkflow(container).run({
            input: {
              id: salesChannelId,
              add: batch
            }
          })
          
          logger.info(`     Processed batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(productIds.length / batchSize)}`)
        }
        
        logger.info(`   ✅ Linked ${productIds.length} products to ${tenantId}`)
      } catch (error) {
        const errorMsg = `Failed to link products to sales channel ${salesChannelId}: ${error.message}`
        stats.errors.push(errorMsg)
        logger.error(`   ❌ ${errorMsg}`)
      }
    }

    // Step 3: Migrate Customers (Update metadata to include sales channel reference)
    logger.info("👥 Migrating customers to sales channels...")
    
    const { data: customers } = await query.graph({
      entity: "customer",
      fields: ["id", "email", "metadata"],
      filters: {}
    })

    stats.customersProcessed = customers.length
    logger.info(`   Found ${customers.length} customers to process`)

    for (const customer of customers) {
      try {
        const tenantId = customer.metadata?.tenant_id
        
        if (tenantId && tenantToSalesChannel.has(tenantId)) {
          const salesChannelId = tenantToSalesChannel.get(tenantId)!
          
          // Update customer metadata to include sales channel reference
          await query.graph({
            entity: "customer",
            fields: ["id"],
            filters: { id: customer.id },
            data: {
              metadata: {
                ...customer.metadata,
                sales_channel_id: salesChannelId,
                migrated_at: new Date().toISOString()
              }
            }
          })
          
          stats.customersMigrated++
        } else if (tenantId) {
          stats.errors.push(`Customer ${customer.id} has unknown tenant_id: ${tenantId}`)
        }
      } catch (error) {
        stats.errors.push(`Error processing customer ${customer.id}: ${error.message}`)
      }
    }

    logger.info(`   ✅ Migrated ${stats.customersMigrated} customers`)

    // Step 4: Migrate Orders (Update metadata to include sales channel reference)
    logger.info("📋 Migrating orders to sales channels...")
    
    const { data: orders } = await query.graph({
      entity: "order",
      fields: ["id", "display_id", "metadata"],
      filters: {}
    })

    stats.ordersProcessed = orders.length
    logger.info(`   Found ${orders.length} orders to process`)

    for (const order of orders) {
      try {
        const tenantId = order.metadata?.tenant_id
        
        if (tenantId && tenantToSalesChannel.has(tenantId)) {
          const salesChannelId = tenantToSalesChannel.get(tenantId)!
          
          // Update order metadata to include sales channel reference
          await query.graph({
            entity: "order",
            fields: ["id"],
            filters: { id: order.id },
            data: {
              metadata: {
                ...order.metadata,
                sales_channel_id: salesChannelId,
                migrated_at: new Date().toISOString()
              }
            }
          })
          
          stats.ordersMigrated++
        } else if (tenantId) {
          stats.errors.push(`Order ${order.id} has unknown tenant_id: ${tenantId}`)
        }
      } catch (error) {
        stats.errors.push(`Error processing order ${order.id}: ${error.message}`)
      }
    }

    logger.info(`   ✅ Migrated ${stats.ordersMigrated} orders`)

    // Step 5: Generate migration report
    logger.info("\n" + "=".repeat(60))
    logger.info("🎉 TENANT DATA MIGRATION COMPLETE!")
    logger.info("=".repeat(60))
    logger.info("\n📊 MIGRATION STATISTICS:")
    logger.info(`   Products: ${stats.productsMigrated}/${stats.productsProcessed} migrated`)
    logger.info(`   Customers: ${stats.customersMigrated}/${stats.customersProcessed} migrated`)
    logger.info(`   Orders: ${stats.ordersMigrated}/${stats.ordersProcessed} migrated`)
    logger.info(`   Errors: ${stats.errors.length}`)

    if (stats.errors.length > 0) {
      logger.info("\n❌ ERRORS ENCOUNTERED:")
      stats.errors.forEach((error, index) => {
        logger.info(`   ${index + 1}. ${error}`)
      })
    }

    logger.info("\n🔧 NEXT STEPS:")
    logger.info("   1. Verify that products appear correctly in store APIs with publishable keys")
    logger.info("   2. Test admin APIs with sales channel filtering")
    logger.info("   3. Remove custom tenant middleware and endpoints")
    logger.info("   4. Update frontend to use native Medusa API client")
    logger.info("   5. Consider removing tenant_id columns after verification")

    return stats

  } catch (error) {
    logger.error("❌ Migration failed:", error)
    stats.errors.push(`Migration failed: ${error.message}`)
    throw error
  }
}

/**
 * Verify migration by testing API endpoints
 */
export async function verifyMigration(container: any): Promise<boolean> {
  const logger = container.resolve(ContainerRegistrationKeys.LOGGER)
  const query = container.resolve(ContainerRegistrationKeys.QUERY)

  try {
    logger.info("🔍 Verifying migration...")

    // Get sales channels
    const { data: salesChannels } = await query.graph({
      entity: "sales_channel",
      fields: ["id", "name", "metadata"],
      filters: {}
    })

    for (const channel of salesChannels) {
      const tenantId = channel.metadata?.tenantId
      if (!tenantId) continue

      // Check if products are linked to this sales channel
      const { data: products } = await query.graph({
        entity: "product",
        fields: ["id", "title"],
        filters: {
          sales_channels: {
            id: channel.id
          }
        }
      })

      logger.info(`   ${tenantId}: ${products.length} products linked to sales channel`)
    }

    logger.info("✅ Migration verification complete")
    return true

  } catch (error) {
    logger.error("❌ Migration verification failed:", error)
    return false
  }
}

// CLI execution
if (require.main === module) {
  const { getContainer } = require("@medusajs/framework")
  
  async function run() {
    const container = getContainer()
    
    const stats = await migrateTenantDataToSalesChannels(container)
    
    if (stats.errors.length === 0) {
      await verifyMigration(container)
    }
    
    process.exit(0)
  }

  run().catch(error => {
    console.error("Migration failed:", error)
    process.exit(1)
  })
}
