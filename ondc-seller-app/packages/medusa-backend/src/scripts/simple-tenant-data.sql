-- Simple Tenant Data Seeding Script
-- Creates sample data for two tenants using existing Medusa tables

-- ============================================================================
-- 1. CUSTOMERS FOR ELECTRONICS TENANT
-- ============================================================================

-- Electronics Store Customers
INSERT INTO "customer" (id, email, first_name, last_name, phone, has_account, tenant_id, created_at, updated_at) VALUES 
('cust_electronics_001', '<EMAIL>', 'John', 'Doe', '+91-**********', true, 'tenant-electronics-001', NOW(), NOW()),
('cust_electronics_002', '<EMAIL>', 'Sarah', 'Johnson', '+91-**********', true, 'tenant-electronics-001', NOW(), NOW()),
('cust_electronics_003', '<EMAIL>', 'Mike', 'Wilson', '+91-**********', true, 'tenant-electronics-001', NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET 
  email = EXCLUDED.email,
  first_name = EXCLUDED.first_name,
  last_name = EXCLUDED.last_name,
  phone = EXCLUDED.phone,
  tenant_id = EXCLUDED.tenant_id,
  updated_at = NOW();

-- ============================================================================
-- 2. CUSTOMERS FOR FASHION TENANT
-- ============================================================================

-- Fashion Store Customers
INSERT INTO "customer" (id, email, first_name, last_name, phone, has_account, tenant_id, created_at, updated_at) VALUES 
('cust_fashion_001', '<EMAIL>', 'Emma', 'Davis', '+91-**********', true, 'tenant-fashion-002', NOW(), NOW()),
('cust_fashion_002', '<EMAIL>', 'Alex', 'Brown', '+91-**********', true, 'tenant-fashion-002', NOW(), NOW()),
('cust_fashion_003', '<EMAIL>', 'Lisa', 'Miller', '+91-**********', true, 'tenant-fashion-002', NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET 
  email = EXCLUDED.email,
  first_name = EXCLUDED.first_name,
  last_name = EXCLUDED.last_name,
  phone = EXCLUDED.phone,
  tenant_id = EXCLUDED.tenant_id,
  updated_at = NOW();

-- ============================================================================
-- 3. PRODUCTS FOR ELECTRONICS TENANT
-- ============================================================================

-- Electronics Products
INSERT INTO "product" (id, title, handle, description, status, thumbnail, weight, length, height, width, hs_code, origin_country, material, tenant_id, created_at, updated_at) VALUES 
('prod_electronics_001', 'iPhone 15 Pro', 'iphone-15-pro', 'Latest iPhone with advanced camera system and A17 Pro chip', 'published', '/images/iphone-15-pro.jpg', 174, 14.67, 0.83, 7.09, '8517.12.00', 'US', 'Titanium', 'tenant-electronics-001', NOW(), NOW()),
('prod_electronics_002', 'Samsung Galaxy S24 Ultra', 'samsung-galaxy-s24-ultra', 'Premium Android smartphone with S Pen and 200MP camera', 'published', '/images/galaxy-s24-ultra.jpg', 232, 16.26, 0.86, 7.90, '8517.12.00', 'KR', 'Aluminum', 'tenant-electronics-001', NOW(), NOW()),
('prod_electronics_003', 'MacBook Pro 14"', 'macbook-pro-14', 'Powerful laptop with M3 chip for professionals', 'published', '/images/macbook-pro-14.jpg', 1600, 31.26, 1.55, 22.12, '8471.30.00', 'US', 'Aluminum', 'tenant-electronics-001', NOW(), NOW()),
('prod_electronics_004', 'Dell XPS 13', 'dell-xps-13', 'Ultra-portable laptop with InfinityEdge display', 'published', '/images/dell-xps-13.jpg', 1200, 29.57, 1.47, 19.90, '8471.30.00', 'US', 'Carbon Fiber', 'tenant-electronics-001', NOW(), NOW()),
('prod_electronics_005', 'Sony WH-1000XM5', 'sony-wh-1000xm5', 'Industry-leading noise canceling headphones', 'published', '/images/sony-wh-1000xm5.jpg', 250, 26.40, 21.60, 7.30, '8518.30.00', 'JP', 'Plastic', 'tenant-electronics-001', NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET 
  title = EXCLUDED.title,
  handle = EXCLUDED.handle,
  description = EXCLUDED.description,
  status = EXCLUDED.status,
  tenant_id = EXCLUDED.tenant_id,
  updated_at = NOW();

-- ============================================================================
-- 4. PRODUCTS FOR FASHION TENANT
-- ============================================================================

-- Fashion Products
INSERT INTO "product" (id, title, handle, description, status, thumbnail, weight, length, height, width, hs_code, origin_country, material, tenant_id, created_at, updated_at) VALUES 
('prod_fashion_001', 'Men''s Casual Shirt', 'mens-casual-shirt', 'Comfortable cotton casual shirt for everyday wear', 'published', '/images/mens-casual-shirt.jpg', 200, 70, 50, 2, '6205.20.00', 'IN', 'Cotton', 'tenant-fashion-002', NOW(), NOW()),
('prod_fashion_002', 'Women''s Summer Dress', 'womens-summer-dress', 'Elegant floral summer dress perfect for any occasion', 'published', '/images/womens-summer-dress.jpg', 300, 120, 60, 3, '6204.44.00', 'IN', 'Polyester', 'tenant-fashion-002', NOW(), NOW()),
('prod_fashion_003', 'Men''s Formal Blazer', 'mens-formal-blazer', 'Professional blazer for business and formal events', 'published', '/images/mens-formal-blazer.jpg', 800, 75, 55, 5, '6203.31.00', 'IN', 'Wool Blend', 'tenant-fashion-002', NOW(), NOW()),
('prod_fashion_004', 'Women''s Denim Jeans', 'womens-denim-jeans', 'Classic blue denim jeans with perfect fit', 'published', '/images/womens-denim-jeans.jpg', 500, 100, 40, 3, '6204.52.00', 'IN', 'Denim', 'tenant-fashion-002', NOW(), NOW()),
('prod_fashion_005', 'Designer Handbag', 'designer-handbag', 'Premium leather handbag with modern design', 'published', '/images/designer-handbag.jpg', 600, 35, 25, 15, '4202.21.00', 'IN', 'Leather', 'tenant-fashion-002', NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET 
  title = EXCLUDED.title,
  handle = EXCLUDED.handle,
  description = EXCLUDED.description,
  status = EXCLUDED.status,
  tenant_id = EXCLUDED.tenant_id,
  updated_at = NOW();

-- ============================================================================
-- 5. PRODUCT CATEGORIES
-- ============================================================================

-- Electronics Categories
INSERT INTO "product_category" (id, name, handle, description, is_active, tenant_id, created_at, updated_at) VALUES 
('cat_electronics_smartphones', 'Smartphones', 'smartphones', 'Latest smartphones and mobile devices', true, 'tenant-electronics-001', NOW(), NOW()),
('cat_electronics_laptops', 'Laptops', 'laptops', 'Laptops and computers', true, 'tenant-electronics-001', NOW(), NOW()),
('cat_electronics_audio', 'Audio & Headphones', 'audio-headphones', 'Headphones, speakers, and audio equipment', true, 'tenant-electronics-001', NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET 
  name = EXCLUDED.name,
  handle = EXCLUDED.handle,
  description = EXCLUDED.description,
  tenant_id = EXCLUDED.tenant_id,
  updated_at = NOW();

-- Fashion Categories
INSERT INTO "product_category" (id, name, handle, description, is_active, tenant_id, created_at, updated_at) VALUES 
('cat_fashion_mens', 'Men''s Fashion', 'mens-fashion', 'Men''s clothing and accessories', true, 'tenant-fashion-002', NOW(), NOW()),
('cat_fashion_womens', 'Women''s Fashion', 'womens-fashion', 'Women''s clothing and accessories', true, 'tenant-fashion-002', NOW(), NOW()),
('cat_fashion_accessories', 'Fashion Accessories', 'fashion-accessories', 'Bags, jewelry, and fashion accessories', true, 'tenant-fashion-002', NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET 
  name = EXCLUDED.name,
  handle = EXCLUDED.handle,
  description = EXCLUDED.description,
  tenant_id = EXCLUDED.tenant_id,
  updated_at = NOW();

-- ============================================================================
-- 6. VERIFICATION QUERIES
-- ============================================================================

-- Verify tenant configurations
SELECT 'Tenant Configurations' as table_name, COUNT(*) as count FROM "tenant_config";

-- Verify customers by tenant
SELECT 'Customers by Tenant' as info, tenant_id, COUNT(*) as count FROM "customer" WHERE tenant_id != 'default' GROUP BY tenant_id;

-- Verify products by tenant
SELECT 'Products by Tenant' as info, tenant_id, COUNT(*) as count FROM "product" WHERE tenant_id != 'default' GROUP BY tenant_id;

-- Verify categories by tenant
SELECT 'Categories by Tenant' as info, tenant_id, COUNT(*) as count FROM "product_category" WHERE tenant_id != 'default' GROUP BY tenant_id;

-- Show sample data
SELECT 'Electronics Customers' as info, id, email, first_name, last_name, tenant_id FROM "customer" WHERE tenant_id = 'tenant-electronics-001';
SELECT 'Fashion Customers' as info, id, email, first_name, last_name, tenant_id FROM "customer" WHERE tenant_id = 'tenant-fashion-002';
SELECT 'Electronics Products' as info, id, title, tenant_id FROM "product" WHERE tenant_id = 'tenant-electronics-001';
SELECT 'Fashion Products' as info, id, title, tenant_id FROM "product" WHERE tenant_id = 'tenant-fashion-002';
