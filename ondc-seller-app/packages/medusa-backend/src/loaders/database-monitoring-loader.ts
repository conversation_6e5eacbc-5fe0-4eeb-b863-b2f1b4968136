/**
 * Database Monitoring Loader
 * 
 * Initializes database connection monitoring and health checks
 * when the Medusa backend starts up.
 */

import { dbMonitor } from '../services/database-monitor';
import { dbPool } from '../utils/database-pool';

export interface MonitoringConfig {
  enabled: boolean;
  intervalMs: number;
  alertThresholds?: {
    maxPoolUtilization?: number;
    maxResponseTime?: number;
    maxErrorRate?: number;
    minHealthyConnections?: number;
  };
}

/**
 * Initialize database monitoring
 */
export async function initializeDatabaseMonitoring(config?: MonitoringConfig): Promise<void> {
  const defaultConfig: MonitoringConfig = {
    enabled: process.env.NODE_ENV !== 'test', // Disable in test environment
    intervalMs: 30000, // 30 seconds
    alertThresholds: {
      maxPoolUtilization: 80,
      maxResponseTime: 5000,
      maxErrorRate: 10,
      minHealthyConnections: 2,
    },
  };

  const finalConfig = { ...defaultConfig, ...config };

  console.log('🔧 [DB-MONITORING-LOADER] Initializing database monitoring...');

  try {
    // Initialize the database pool first
    dbPool.initialize();
    console.log('✅ [DB-MONITORING-LOADER] Database pool initialized');

    // Perform initial health check
    const isHealthy = await dbPool.healthCheck();
    if (!isHealthy) {
      console.warn('⚠️ [DB-MONITORING-LOADER] Initial database health check failed');
    } else {
      console.log('✅ [DB-MONITORING-LOADER] Initial database health check passed');
    }

    // Configure alert thresholds if provided
    if (finalConfig.alertThresholds) {
      dbMonitor.updateAlertThresholds(finalConfig.alertThresholds);
    }

    // Start monitoring if enabled
    if (finalConfig.enabled) {
      dbMonitor.startMonitoring(finalConfig.intervalMs);
      console.log(`✅ [DB-MONITORING-LOADER] Database monitoring started (interval: ${finalConfig.intervalMs}ms)`);
    } else {
      console.log('ℹ️ [DB-MONITORING-LOADER] Database monitoring disabled');
    }

    // Set up graceful shutdown
    setupGracefulShutdown();

    console.log('✅ [DB-MONITORING-LOADER] Database monitoring initialization complete');

  } catch (error) {
    console.error('❌ [DB-MONITORING-LOADER] Failed to initialize database monitoring:', error);
    throw error;
  }
}

/**
 * Set up graceful shutdown handlers
 */
function setupGracefulShutdown(): void {
  const shutdownHandler = async (signal: string) => {
    console.log(`🛑 [DB-MONITORING-LOADER] Received ${signal}, shutting down gracefully...`);
    
    try {
      // Stop monitoring
      dbMonitor.stopMonitoring();
      console.log('✅ [DB-MONITORING-LOADER] Database monitoring stopped');

      // Close database pool
      await dbPool.close();
      console.log('✅ [DB-MONITORING-LOADER] Database pool closed');

      console.log('✅ [DB-MONITORING-LOADER] Graceful shutdown complete');
      process.exit(0);
    } catch (error) {
      console.error('❌ [DB-MONITORING-LOADER] Error during graceful shutdown:', error);
      process.exit(1);
    }
  };

  // Handle various shutdown signals
  process.on('SIGTERM', () => shutdownHandler('SIGTERM'));
  process.on('SIGINT', () => shutdownHandler('SIGINT'));
  process.on('SIGUSR2', () => shutdownHandler('SIGUSR2')); // Nodemon restart

  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    console.error('❌ [DB-MONITORING-LOADER] Uncaught exception:', error);
    shutdownHandler('uncaughtException');
  });

  // Handle unhandled promise rejections
  process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ [DB-MONITORING-LOADER] Unhandled rejection at:', promise, 'reason:', reason);
    shutdownHandler('unhandledRejection');
  });
}

/**
 * Get current monitoring status
 */
export function getMonitoringStatus(): any {
  return {
    isActive: dbMonitor.getCurrentHealth() !== null,
    healthSummary: dbMonitor.getHealthSummary(),
    poolStats: dbPool.getStats(),
  };
}

/**
 * Create health check endpoint data
 */
export async function createHealthCheckData(): Promise<any> {
  try {
    const poolHealthy = await dbPool.healthCheck();
    const monitoringHealth = dbMonitor.getHealthSummary();
    const poolStats = dbPool.getStats();

    return {
      status: poolHealthy && monitoringHealth.isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      database: {
        pool: {
          healthy: poolHealthy,
          stats: poolStats,
        },
        monitoring: monitoringHealth,
      },
      uptime: process.uptime(),
      memory: process.memoryUsage(),
    };
  } catch (error) {
    return {
      status: 'error',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : String(error),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
    };
  }
}

// Export for use in other modules
export { dbMonitor, dbPool };
