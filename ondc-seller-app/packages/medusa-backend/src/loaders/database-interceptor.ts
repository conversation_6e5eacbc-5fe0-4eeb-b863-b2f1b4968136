import { MedusaContainer } from '@medusajs/medusa';

/**
 * Database Query Interceptor
 * Intercepts database queries to add tenant filtering
 */
export default async (container: MedusaContainer): Promise<void> => {
  try {
    console.log('🔧 Setting up database query interceptor for tenant filtering...');

    // This will be called after the database connection is established
    setTimeout(() => {
      try {
        // Get the database manager
        const manager = container.resolve('manager');

        if (manager && manager.query) {
          const originalQuery = manager.query.bind(manager);

          // Intercept all database queries
          manager.query = function (sql: string, parameters?: any[]) {
            // Add tenant filtering to product queries
            if (sql.includes('SELECT') && sql.includes('product') && !sql.includes('tenant_id')) {
              // This is a basic example - you'd need more sophisticated SQL parsing
              console.log('🔍 Intercepted product query:', sql);
            }

            return originalQuery(sql, parameters);
          };
        }

        console.log('✅ Database query interceptor set up successfully');
      } catch (error) {
        console.error('❌ Failed to set up database interceptor:', error);
      }
    }, 1000);
  } catch (error) {
    console.error('❌ Failed to register database interceptor:', error);
    throw error;
  }
};
