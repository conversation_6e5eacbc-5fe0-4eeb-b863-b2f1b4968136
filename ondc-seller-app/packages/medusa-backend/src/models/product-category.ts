import { ProductCategory as MedusaProductCategory } from "@medusajs/medusa"
import { Entity, Property } from "@mikro-orm/core"

/**
 * Extends Medusa's default ProductCategory entity to include:
 * - `tenant_id` VARCHAR column for multi-tenant data isolation
 *
 * A migration is provided to add the column at the database level.
 */
@Entity({ tableName: "product_category" })
export class ProductCategory extends MedusaProductCategory {
  @Property({ type: "varchar", length: 255, nullable: false, default: "default" })
  tenant_id: string = "default"
}
