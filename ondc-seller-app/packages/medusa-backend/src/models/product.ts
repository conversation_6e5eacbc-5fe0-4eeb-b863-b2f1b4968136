import { Product as MedusaProduct } from '@medusajs/medusa';
import { Entity, Property } from '@mikro-orm/core';

/**
 * Extends Medusa's default Product entity to include:
 * - `additional_data` JSONB column for storing arbitrary product-specific data
 * - `tenant_id` VARCHAR column for multi-tenant data isolation
 *
 * Migrations are provided to add the columns at the database level.
 */
@Entity({ tableName: 'product' })
export class Product extends MedusaProduct {
  @Property({ type: 'jsonb', nullable: true })
  additional_data?: Record<string, unknown>;

  @Property({ type: 'varchar', length: 255, nullable: false, default: 'default' })
  tenant_id: string = 'default';
}
