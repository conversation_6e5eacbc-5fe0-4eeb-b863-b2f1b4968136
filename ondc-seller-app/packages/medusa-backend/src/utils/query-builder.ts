/**
 * Query Builder Utilities for Tenant-Aware APIs
 * 
 * Provides reusable functions for building database queries with:
 * - Tenant isolation
 * - Filtering
 * - Sorting  
 * - Pagination
 * - Search functionality
 */

export interface QueryParams {
  // Pagination
  limit?: string | number
  offset?: string | number
  
  // Sorting
  order?: string
  sort?: string
  
  // Search
  q?: string
  
  // Date filters
  created_after?: string
  created_before?: string
  updated_after?: string
  updated_before?: string
  
  // Entity-specific filters (will be passed through)
  [key: string]: any
}

export interface WhereClauseResult {
  whereClause: string
  values: any[]
  nextParamIndex: number
}

export interface QueryBuilderConfig {
  tableName: string
  tenantIdField?: string
  searchFields?: string[]
  validSortFields?: string[]
  customFilters?: { [key: string]: string } // field -> SQL condition template
}

/**
 * Build WHERE clause with tenant isolation and filters
 */
export function buildWhereClause(
  tenantId: string, 
  queryParams: QueryParams,
  config: QueryBuilderConfig
): WhereClauseResult {
  const tenantField = config.tenantIdField || 'tenant_id'
  let whereConditions = [`${tenantField} = $1`, 'deleted_at IS NULL']
  let values = [tenantId]
  let paramIndex = 2

  // Search functionality
  if (queryParams.q && config.searchFields && config.searchFields.length > 0) {
    const searchConditions = config.searchFields.map(field => 
      `${field} ILIKE $${paramIndex}`
    ).join(' OR ')
    
    whereConditions.push(`(${searchConditions})`)
    
    // Add the search term for each search field
    config.searchFields.forEach(() => {
      values.push(`%${queryParams.q}%`)
      paramIndex++
    })
  }

  // Date range filters
  if (queryParams.created_after) {
    whereConditions.push(`created_at >= $${paramIndex}`)
    values.push(queryParams.created_after)
    paramIndex++
  }

  if (queryParams.created_before) {
    whereConditions.push(`created_at <= $${paramIndex}`)
    values.push(queryParams.created_before)
    paramIndex++
  }

  if (queryParams.updated_after) {
    whereConditions.push(`updated_at >= $${paramIndex}`)
    values.push(queryParams.updated_after)
    paramIndex++
  }

  if (queryParams.updated_before) {
    whereConditions.push(`updated_at <= $${paramIndex}`)
    values.push(queryParams.updated_before)
    paramIndex++
  }

  // Custom filters
  if (config.customFilters) {
    Object.entries(config.customFilters).forEach(([paramKey, sqlCondition]) => {
      if (queryParams[paramKey] !== undefined) {
        // Replace {value} placeholder with parameter
        const condition = sqlCondition.replace('{value}', `$${paramIndex}`)
        whereConditions.push(condition)
        values.push(queryParams[paramKey])
        paramIndex++
      }
    })
  }

  return {
    whereClause: whereConditions.join(' AND '),
    values,
    nextParamIndex: paramIndex
  }
}

/**
 * Build ORDER BY clause with validation
 */
export function buildOrderClause(queryParams: QueryParams, validSortFields: string[]): string {
  const order = queryParams.order || queryParams.sort || 'created_at:desc'
  
  if (typeof order === 'string') {
    const [field, direction = 'asc'] = order.split(':')
    
    if (validSortFields.includes(field)) {
      const dir = direction.toLowerCase() === 'desc' ? 'DESC' : 'ASC'
      return `ORDER BY ${field} ${dir}`
    }
  }
  
  // Default sorting
  return 'ORDER BY created_at DESC'
}

/**
 * Parse and validate pagination parameters
 */
export function parsePaginationParams(queryParams: QueryParams): { limit: number, offset: number } {
  const limit = Math.min(parseInt(queryParams.limit as string) || 20, 100) // Max 100 items
  const offset = Math.max(parseInt(queryParams.offset as string) || 0, 0)
  
  return { limit, offset }
}

/**
 * Build complete query for listing entities with all features
 */
export function buildListQuery(
  tenantId: string,
  queryParams: QueryParams,
  config: QueryBuilderConfig
): {
  countQuery: string
  listQuery: string
  values: any[]
  pagination: { limit: number, offset: number }
} {
  const { whereClause, values, nextParamIndex } = buildWhereClause(tenantId, queryParams, config)
  const orderClause = buildOrderClause(queryParams, config.validSortFields || [])
  const pagination = parsePaginationParams(queryParams)

  const countQuery = `
    SELECT COUNT(*) as total
    FROM ${config.tableName}
    WHERE ${whereClause}
  `

  const listQuery = `
    SELECT *
    FROM ${config.tableName}
    WHERE ${whereClause}
    ${orderClause}
    LIMIT $${nextParamIndex} OFFSET $${nextParamIndex + 1}
  `

  return {
    countQuery,
    listQuery,
    values: [...values, pagination.limit, pagination.offset],
    pagination
  }
}

/**
 * Build response metadata
 */
export function buildResponseMetadata(
  tenantId: string,
  queryParams: QueryParams,
  totalCount: number,
  currentCount: number,
  pagination: { limit: number, offset: number }
) {
  const appliedFilters = Object.keys(queryParams).filter(key => 
    !['limit', 'offset', 'order', 'sort'].includes(key) && queryParams[key] !== undefined
  )

  return {
    count: currentCount,
    total: totalCount,
    offset: pagination.offset,
    limit: pagination.limit,
    has_more: (pagination.offset + pagination.limit) < totalCount,
    _tenant: {
      id: tenantId,
      filtered: true,
      method: 'enhanced_db_connection'
    },
    _query: {
      applied_filters: appliedFilters,
      sort_by: queryParams.order || queryParams.sort || 'created_at:desc',
      pagination: {
        current_page: Math.floor(pagination.offset / pagination.limit) + 1,
        total_pages: Math.ceil(totalCount / pagination.limit),
        per_page: pagination.limit
      }
    }
  }
}

/**
 * Predefined configurations for common entities
 */
export const ENTITY_CONFIGS: { [key: string]: QueryBuilderConfig } = {
  promotions: {
    tableName: 'promotion',
    searchFields: ['code', 'campaign_id'],
    validSortFields: ['created_at', 'updated_at', 'code', 'status', 'type'],
    customFilters: {
      status: 'status = {value}',
      code: 'code = {value}',
      type: 'type = {value}',
      is_automatic: 'is_automatic = {value}'
    }
  },
  
  products: {
    tableName: 'product',
    searchFields: ['title', 'description', 'handle'],
    validSortFields: ['created_at', 'updated_at', 'title', 'status', 'handle'],
    customFilters: {
      status: 'status = {value}',
      title: 'title ILIKE {value}',
      handle: 'handle = {value}',
      type_id: 'type_id = {value}',
      collection_id: 'collection_id = {value}'
    }
  },
  
  orders: {
    tableName: 'order',
    searchFields: ['display_id', 'email'],
    validSortFields: ['created_at', 'updated_at', 'total', 'status'],
    customFilters: {
      status: 'status = {value}',
      email: 'email ILIKE {value}',
      payment_status: 'payment_status = {value}',
      fulfillment_status: 'fulfillment_status = {value}'
    }
  },
  
  customers: {
    tableName: 'customer',
    searchFields: ['email', 'first_name', 'last_name'],
    validSortFields: ['created_at', 'updated_at', 'email', 'first_name', 'last_name'],
    customFilters: {
      email: 'email ILIKE {value}',
      first_name: 'first_name ILIKE {value}',
      last_name: 'last_name ILIKE {value}',
      has_account: 'has_account = {value}'
    }
  }
}

/**
 * Quick helper to get entity config
 */
export function getEntityConfig(entityName: string): QueryBuilderConfig {
  return ENTITY_CONFIGS[entityName] || {
    tableName: entityName,
    searchFields: [],
    validSortFields: ['created_at', 'updated_at'],
    customFilters: {}
  }
}
