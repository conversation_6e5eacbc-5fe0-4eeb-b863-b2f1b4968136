/**
 * Centralized Database Connection Pool Manager
 *
 * Provides a single, shared connection pool for all API endpoints to prevent
 * connection exhaustion and ensure proper resource management.
 *
 * ENHANCED VERSION - Fixes connection pool exhaustion issues
 */

import { Pool, PoolClient } from 'pg';

interface ConnectionMetrics {
  totalConnections: number;
  idleConnections: number;
  waitingRequests: number;
  activeConnections: number;
}

class DatabasePoolManager {
  private static instance: DatabasePoolManager;
  private pool: Pool | null = null;
  private isInitialized = false;
  private connectionCount = 0;
  private maxConnectionsReached = false;

  private constructor() {
    // Private constructor to enforce singleton pattern
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): DatabasePoolManager {
    if (!DatabasePoolManager.instance) {
      DatabasePoolManager.instance = new DatabasePoolManager();
    }
    return DatabasePoolManager.instance;
  }

  /**
   * Initialize the connection pool with enhanced configuration
   */
  public initialize(): void {
    if (this.isInitialized && this.pool) {
      return;
    }

    const databaseUrl =
      process.env.DATABASE_URL ||
      process.env.POSTGRES_URL ||
      'postgresql://strapi:strapi_password@localhost:5432/medusa_backend';

    // Enhanced pool configuration for high-frequency requests
    this.pool = new Pool({
      connectionString: databaseUrl,
      max: 50, // Increased from 20 to handle more concurrent requests
      min: 5, // Increased minimum to ensure availability
      idleTimeoutMillis: 60000, // Increased to 60 seconds for better reuse
      connectionTimeoutMillis: 10000, // Increased timeout for connection establishment
      application_name: 'medusa-backend-api-enhanced', // Updated name for monitoring
      // Additional PostgreSQL-specific optimizations
      statement_timeout: 30000, // 30 second statement timeout
      query_timeout: 30000, // 30 second query timeout
      keepAlive: true, // Enable TCP keep-alive
      keepAliveInitialDelayMillis: 10000, // 10 second initial delay
    });

    // Enhanced error handling
    this.pool.on('error', (err, client) => {
      console.error('❌ [DB-POOL] Unexpected error on idle client:', err);
      console.error('❌ [DB-POOL] Client info:', {
        connected: client ? 'present' : 'missing',
        error: err.message,
      });
      // Attempt to recover by reinitializing if needed
      this.handlePoolError(err);
    });

    // Enhanced connection tracking
    this.pool.on('connect', client => {
      this.connectionCount++;
      const metrics = this.getConnectionMetrics();
      console.log(`🔗 [DB-POOL] New client connected. Metrics:`, metrics);

      // Set up client-specific error handling
      client.on('error', err => {
        console.error('❌ [DB-POOL] Client error:', err);
      });
    });

    // Enhanced removal tracking
    this.pool.on('remove', client => {
      this.connectionCount--;
      const metrics = this.getConnectionMetrics();
      console.log(`🔌 [DB-POOL] Client removed. Metrics:`, metrics);
    });

    // Monitor for connection pool exhaustion
    this.pool.on('acquire', client => {
      const metrics = this.getConnectionMetrics();
      if (metrics.totalConnections >= (this.pool?.options.max || 50) * 0.9) {
        if (!this.maxConnectionsReached) {
          console.warn('⚠️ [DB-POOL] Approaching maximum connections:', metrics);
          this.maxConnectionsReached = true;
        }
      } else {
        this.maxConnectionsReached = false;
      }
    });

    this.isInitialized = true;
    console.log('✅ [DB-POOL] Enhanced database connection pool initialized successfully');
    console.log(
      `📊 [DB-POOL] Pool configuration: max=${this.pool.options.max}, min=${this.pool.options.min}`
    );
  }

  /**
   * Get a connection from the pool with enhanced error handling
   */
  public async getConnection(): Promise<PoolClient> {
    if (!this.pool) {
      this.initialize();
    }

    if (!this.pool) {
      throw new Error('Database pool is not initialized');
    }

    const startTime = Date.now();
    try {
      const client = await this.pool.connect();
      const acquireTime = Date.now() - startTime;
      const metrics = this.getConnectionMetrics();

      console.log(`🔗 [DB-POOL] Connection acquired in ${acquireTime}ms. Metrics:`, metrics);

      // Set up connection-specific timeout handling
      const originalQuery = client.query.bind(client);
      client.query = async (...args: any[]) => {
        const queryStart = Date.now();
        try {
          const result = await originalQuery(...args);
          const queryTime = Date.now() - queryStart;
          if (queryTime > 5000) {
            // Log slow queries
            console.warn(`⚠️ [DB-POOL] Slow query detected: ${queryTime}ms`);
          }
          return result;
        } catch (error) {
          console.error(`❌ [DB-POOL] Query error after ${Date.now() - queryStart}ms:`, error);
          throw error;
        }
      };

      return client;
    } catch (error) {
      const acquireTime = Date.now() - startTime;
      console.error(`❌ [DB-POOL] Failed to acquire connection after ${acquireTime}ms:`, error);

      // Log current pool state for debugging
      const metrics = this.getConnectionMetrics();
      console.error(`❌ [DB-POOL] Pool state during failure:`, metrics);

      throw error;
    }
  }

  /**
   * Execute a query with automatic connection management and enhanced error handling
   */
  public async query<T = any>(text: string, params?: any[]): Promise<T[]> {
    const client = await this.getConnection();

    try {
      const start = Date.now();
      const result = await client.query(text, params);
      const duration = Date.now() - start;

      // Enhanced logging with performance metrics
      const logData = {
        text: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
        duration: `${duration}ms`,
        rows: result.rowCount,
        params: params ? params.length : 0,
      };

      if (duration > 1000) {
        console.warn('⚠️ [DB-POOL] Slow query detected:', logData);
      } else {
        console.log('📊 [DB-POOL] Query executed:', logData);
      }

      return result.rows;
    } catch (error) {
      console.error('❌ [DB-POOL] Query execution failed:', {
        error: error instanceof Error ? error.message : error,
        query: text.substring(0, 100),
        params: params ? params.length : 0,
      });
      throw error;
    } finally {
      // Always release the connection back to the pool
      try {
        client.release();
        console.log('🔌 [DB-POOL] Connection released back to pool');
      } catch (releaseError) {
        console.error('❌ [DB-POOL] Error releasing connection:', releaseError);
        // Don't throw here as it would mask the original error
      }
    }
  }

  /**
   * Execute a transaction with automatic connection management
   */
  public async transaction<T>(callback: (client: PoolClient) => Promise<T>): Promise<T> {
    const client = await this.getConnection();

    try {
      await client.query('BEGIN');
      console.log('🔄 [DB-POOL] Transaction started');

      const result = await callback(client);

      await client.query('COMMIT');
      console.log('✅ [DB-POOL] Transaction committed');

      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('❌ [DB-POOL] Transaction rolled back:', error);
      throw error;
    } finally {
      client.release();
      console.log('🔌 [DB-POOL] Transaction connection released');
    }
  }

  /**
   * Get connection metrics for monitoring
   */
  public getConnectionMetrics(): ConnectionMetrics {
    if (!this.pool) {
      return {
        totalConnections: 0,
        idleConnections: 0,
        waitingRequests: 0,
        activeConnections: 0,
      };
    }

    return {
      totalConnections: this.pool.totalCount,
      idleConnections: this.pool.idleCount,
      waitingRequests: this.pool.waitingCount,
      activeConnections: this.pool.totalCount - this.pool.idleCount,
    };
  }

  /**
   * Handle pool errors and attempt recovery
   */
  private handlePoolError(error: Error): void {
    console.error('❌ [DB-POOL] Pool error detected, attempting recovery:', error);

    // Log current metrics
    const metrics = this.getConnectionMetrics();
    console.error('❌ [DB-POOL] Pool metrics during error:', metrics);

    // If we have too many failed connections, consider reinitializing
    if (error.message.includes('connection') || error.message.includes('timeout')) {
      console.warn('⚠️ [DB-POOL] Connection-related error detected, monitoring for recovery');

      // Set a flag to reinitialize if needed
      setTimeout(() => {
        if (this.pool && this.pool.totalCount === 0) {
          console.warn('⚠️ [DB-POOL] No active connections, considering reinitialization');
        }
      }, 5000);
    }
  }

  /**
   * Get pool statistics
   */
  public getStats() {
    if (!this.pool) {
      return null;
    }

    return {
      totalCount: this.pool.totalCount,
      idleCount: this.pool.idleCount,
      waitingCount: this.pool.waitingCount,
      maxConnections: this.pool.options.max,
      minConnections: this.pool.options.min,
    };
  }

  /**
   * Close the pool (for graceful shutdown)
   */
  public async close(): Promise<void> {
    if (this.pool) {
      await this.pool.end();
      console.log('🔒 [DB-POOL] Database connection pool closed');
      this.pool = null;
      this.isInitialized = false;
    }
  }

  /**
   * Health check for the database connection
   */
  public async healthCheck(): Promise<boolean> {
    try {
      const result = await this.query('SELECT 1 as health_check');
      return result.length > 0 && result[0].health_check === 1;
    } catch (error) {
      console.error('❌ [DB-POOL] Health check failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const dbPool = DatabasePoolManager.getInstance();

// Initialize the pool when the module is loaded
dbPool.initialize();

// Export the class for testing purposes
export { DatabasePoolManager };
