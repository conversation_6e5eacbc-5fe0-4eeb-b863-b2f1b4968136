/**
 * Task 2.5: Add Tenant Context to Request Objects
 * 
 * Comprehensive tenant context utilities for Medusa v2 multi-tenancy.
 * Provides standardized access to tenant information across all request handlers.
 */

import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { TenantConfig } from "../middleware/tenant";

/**
 * Extended Request Interface with Tenant Context
 * This interface documents what tenant information is available on request objects
 */
export interface TenantAwareRequest extends MedusaRequest {
  tenantId: string;
  tenant: TenantConfig;
  tenantQueryBuilder?: any; // From tenant-query-filter middleware
}

/**
 * Tenant Context Information
 * Complete tenant context available in request handlers
 */
export interface TenantContext {
  id: string;
  config: TenantConfig;
  isValid: boolean;
  isActive: boolean;
  features: string[];
  ondcConfig: {
    participantId: string;
    subscriberId: string;
    bppId: string;
  };
  metadata: {
    extractedFrom: 'header' | 'query' | 'subdomain' | 'default';
    timestamp: string;
    requestPath: string;
    requestMethod: string;
  };
}

/**
 * Tenant Context Validation Result
 */
export interface TenantValidationResult {
  isValid: boolean;
  tenantId: string;
  errors: string[];
  warnings: string[];
}

// ============================================================================
// CORE TENANT CONTEXT UTILITIES
// ============================================================================

/**
 * Get Tenant ID from Request
 * Primary utility for extracting tenant ID from any request
 * 
 * @param req - Medusa request object
 * @returns Tenant ID string
 */
export function getTenantId(req: MedusaRequest): string {
  // Priority: middleware-set > header > query > default
  const middlewareTenantId = (req as any).tenantId;
  if (middlewareTenantId) {
    return middlewareTenantId;
  }

  const headerTenantId = req.headers['x-tenant-id'] as string;
  if (headerTenantId) {
    return headerTenantId;
  }

  const queryTenantId = req.query.tenant as string;
  if (queryTenantId) {
    return queryTenantId;
  }

  return 'default';
}

/**
 * Get Tenant Configuration from Request
 * Primary utility for accessing complete tenant configuration
 * 
 * @param req - Medusa request object
 * @returns Tenant configuration or null if not available
 */
export function getTenantConfig(req: MedusaRequest): TenantConfig | null {
  return (req as any).tenant || null;
}

/**
 * Get Complete Tenant Context
 * Comprehensive tenant context with all available information
 * 
 * @param req - Medusa request object
 * @returns Complete tenant context
 */
export function getTenantContext(req: MedusaRequest): TenantContext | null {
  const tenantId = getTenantId(req);
  const config = getTenantConfig(req);

  if (!config) {
    return null;
  }

  // Determine extraction source
  let extractedFrom: 'header' | 'query' | 'subdomain' | 'default' = 'default';
  if (req.headers['x-tenant-id']) {
    extractedFrom = 'header';
  } else if (req.query.tenant) {
    extractedFrom = 'query';
  } else if (req.headers.host && req.headers.host.includes('.')) {
    extractedFrom = 'subdomain';
  }

  return {
    id: tenantId,
    config,
    isValid: true, // If we have config, it's valid (middleware validated it)
    isActive: config.status === 'active',
    features: config.settings.features,
    ondcConfig: config.settings.ondcConfig,
    metadata: {
      extractedFrom,
      timestamp: new Date().toISOString(),
      requestPath: req.path,
      requestMethod: req.method,
    },
  };
}

/**
 * Check if Request has Tenant Context
 * Quick validation that tenant context is available
 * 
 * @param req - Medusa request object
 * @returns True if tenant context is available
 */
export function hasTenantContext(req: MedusaRequest): boolean {
  return !!(req as any).tenantId && !!(req as any).tenant;
}

/**
 * Validate Tenant Context
 * Comprehensive validation of tenant context completeness
 * 
 * @param req - Medusa request object
 * @returns Validation result with details
 */
export function validateTenantContext(req: MedusaRequest): TenantValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  const tenantId = getTenantId(req);
  const config = getTenantConfig(req);

  // Check if tenant ID exists
  if (!tenantId) {
    errors.push('Tenant ID is missing');
  }

  // Check if tenant configuration exists
  if (!config) {
    errors.push('Tenant configuration is missing');
  } else {
    // Validate configuration completeness
    if (!config.name) {
      warnings.push('Tenant name is not configured');
    }

    if (!config.domain) {
      warnings.push('Tenant domain is not configured');
    }

    if (!config.settings.ondcConfig.participantId) {
      errors.push('ONDC participant ID is missing');
    }

    if (!config.settings.ondcConfig.subscriberId) {
      errors.push('ONDC subscriber ID is missing');
    }

    if (!config.settings.ondcConfig.bppId) {
      errors.push('ONDC BPP ID is missing');
    }

    if (config.status !== 'active') {
      warnings.push(`Tenant status is '${config.status}', not 'active'`);
    }
  }

  return {
    isValid: errors.length === 0,
    tenantId,
    errors,
    warnings,
  };
}

// ============================================================================
// TENANT-AWARE DATA UTILITIES
// ============================================================================

/**
 * Inject Tenant ID into Data Object
 * Automatically adds tenant_id to data objects for database operations
 * 
 * @param req - Medusa request object
 * @param data - Data object or array to inject tenant ID into
 * @returns Data with tenant_id injected
 */
export function injectTenantId<T extends Record<string, any>>(
  req: MedusaRequest, 
  data: T
): T & { tenant_id: string };
export function injectTenantId<T extends Record<string, any>>(
  req: MedusaRequest, 
  data: T[]
): (T & { tenant_id: string })[];
export function injectTenantId<T extends Record<string, any>>(
  req: MedusaRequest, 
  data: T | T[]
): (T & { tenant_id: string }) | (T & { tenant_id: string })[] {
  const tenantId = getTenantId(req);

  if (Array.isArray(data)) {
    return data.map(item => ({ ...item, tenant_id: tenantId }));
  }

  return { ...data, tenant_id: tenantId };
}

/**
 * Filter Data by Tenant ID
 * Filters array data to only include items belonging to current tenant
 * 
 * @param req - Medusa request object
 * @param data - Array of data to filter
 * @returns Filtered data array
 */
export function filterByTenantId<T extends { tenant_id?: string }>(
  req: MedusaRequest, 
  data: T[]
): T[] {
  const tenantId = getTenantId(req);
  return data.filter(item => !item.tenant_id || item.tenant_id === tenantId);
}

/**
 * Create Tenant-Scoped Query Parameters
 * Generates query parameters with tenant context for database queries
 * 
 * @param req - Medusa request object
 * @param additionalParams - Additional query parameters
 * @returns Query parameters with tenant context
 */
export function createTenantQuery(
  req: MedusaRequest, 
  additionalParams: Record<string, any> = {}
): Record<string, any> {
  const tenantId = getTenantId(req);
  
  return {
    tenant_id: tenantId,
    ...additionalParams,
  };
}

// ============================================================================
// RESPONSE UTILITIES
// ============================================================================

/**
 * Add Tenant Context to Response Headers
 * Adds tenant information to response headers for debugging and client use
 * 
 * @param req - Medusa request object
 * @param res - Medusa response object
 */
export function addTenantHeaders(req: MedusaRequest, res: MedusaResponse): void {
  const context = getTenantContext(req);
  
  if (context) {
    res.setHeader('X-Tenant-ID', context.id);
    res.setHeader('X-Tenant-Name', context.config.name);
    res.setHeader('X-Tenant-Status', context.config.status);
    res.setHeader('X-Tenant-Features', context.features.join(','));
    
    // Add debug headers in development
    if (process.env.NODE_ENV === 'development') {
      res.setHeader('X-Tenant-Extracted-From', context.metadata.extractedFrom);
      res.setHeader('X-Tenant-ONDC-BPP-ID', context.ondcConfig.bppId);
      res.setHeader('X-Tenant-Active', context.isActive.toString());
    }
  }
}

/**
 * Create Tenant-Aware API Response
 * Standardized API response format with tenant context
 * 
 * @param req - Medusa request object
 * @param data - Response data
 * @param message - Optional message
 * @returns Standardized API response
 */
export function createTenantResponse<T>(
  req: MedusaRequest,
  data: T,
  message?: string
): {
  success: boolean;
  data: T;
  tenant_context: {
    tenant_id: string;
    tenant_name: string;
    timestamp: string;
  };
  message?: string;
} {
  const context = getTenantContext(req);
  
  return {
    success: true,
    data,
    tenant_context: {
      tenant_id: context?.id || getTenantId(req),
      tenant_name: context?.config.name || 'Unknown Tenant',
      timestamp: new Date().toISOString(),
    },
    ...(message && { message }),
  };
}

// ============================================================================
// FEATURE CHECKING UTILITIES
// ============================================================================

/**
 * Check if Tenant has Feature
 * Validates if current tenant has access to a specific feature
 * 
 * @param req - Medusa request object
 * @param feature - Feature name to check
 * @returns True if tenant has the feature
 */
export function tenantHasFeature(req: MedusaRequest, feature: string): boolean {
  const context = getTenantContext(req);
  
  if (!context) {
    return false;
  }

  // Check for 'all' feature (grants access to everything)
  if (context.features.includes('all')) {
    return true;
  }

  return context.features.includes(feature);
}

/**
 * Require Tenant Feature
 * Throws error if tenant doesn't have required feature
 * 
 * @param req - Medusa request object
 * @param feature - Required feature name
 * @throws Error if feature is not available
 */
export function requireTenantFeature(req: MedusaRequest, feature: string): void {
  if (!tenantHasFeature(req, feature)) {
    const tenantId = getTenantId(req);
    throw new Error(`Tenant '${tenantId}' does not have access to feature '${feature}'`);
  }
}

// ============================================================================
// LOGGING UTILITIES
// ============================================================================

/**
 * Create Tenant-Aware Log Context
 * Generates structured logging context with tenant information
 * 
 * @param req - Medusa request object
 * @param additionalContext - Additional context data
 * @returns Structured log context
 */
export function createTenantLogContext(
  req: MedusaRequest,
  additionalContext: Record<string, any> = {}
): Record<string, any> {
  const context = getTenantContext(req);
  
  return {
    tenant_id: context?.id || getTenantId(req),
    tenant_name: context?.config.name || 'Unknown',
    tenant_status: context?.config.status || 'unknown',
    request_path: req.path,
    request_method: req.method,
    timestamp: new Date().toISOString(),
    ...additionalContext,
  };
}
