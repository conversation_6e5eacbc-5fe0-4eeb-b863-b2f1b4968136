/**
 * Service Discovery Utility for Medusa v2
 * 
 * This utility helps identify the correct service names and patterns
 * in Medusa v2's dependency injection container for multi-tenant integration.
 */

import { MedusaRequest } from "@medusajs/framework/http";

export interface ServiceInfo {
  name: string;
  type: 'service' | 'module' | 'repository' | 'unknown';
  methods?: string[];
  isCore?: boolean;
  description?: string;
}

export interface ServiceDiscoveryResult {
  coreServices: ServiceInfo[];
  modules: ServiceInfo[];
  repositories: ServiceInfo[];
  customServices: ServiceInfo[];
  totalServices: number;
  discoveredAt: string;
}

/**
 * Discover all available services in the Medusa container
 */
export function discoverServices(req: MedusaRequest): ServiceDiscoveryResult {
  const container = req.scope;
  const cradle = container.cradle || {};
  const serviceNames = Object.keys(cradle);
  
  console.log(`🔍 [SERVICE DISCOVERY] Found ${serviceNames.length} services in container`);
  
  const coreServices: ServiceInfo[] = [];
  const modules: ServiceInfo[] = [];
  const repositories: ServiceInfo[] = [];
  const customServices: ServiceInfo[] = [];
  
  // Known Medusa v2 core service patterns
  const coreServicePatterns = [
    'product', 'customer', 'order', 'cart', 'user', 'auth',
    'payment', 'inventory', 'fulfillment', 'region', 'currency',
    'shipping', 'tax', 'discount', 'promotion', 'price', 'sales'
  ];
  
  const modulePatterns = [
    'Module', 'module', 'Service', 'service'
  ];
  
  const repositoryPatterns = [
    'Repository', 'repository', 'repo'
  ];
  
  serviceNames.forEach(serviceName => {
    try {
      const service = container.resolve(serviceName);
      const serviceInfo: ServiceInfo = {
        name: serviceName,
        type: 'unknown',
        methods: getServiceMethods(service),
        isCore: false
      };
      
      // Categorize service
      if (coreServicePatterns.some(pattern => 
        serviceName.toLowerCase().includes(pattern.toLowerCase())
      )) {
        serviceInfo.type = 'service';
        serviceInfo.isCore = true;
        serviceInfo.description = `Core Medusa ${serviceName} service`;
        coreServices.push(serviceInfo);
      } else if (modulePatterns.some(pattern => 
        serviceName.includes(pattern)
      )) {
        serviceInfo.type = 'module';
        serviceInfo.description = `Medusa module: ${serviceName}`;
        modules.push(serviceInfo);
      } else if (repositoryPatterns.some(pattern => 
        serviceName.includes(pattern)
      )) {
        serviceInfo.type = 'repository';
        serviceInfo.description = `Data repository: ${serviceName}`;
        repositories.push(serviceInfo);
      } else {
        serviceInfo.type = 'service';
        serviceInfo.description = `Custom service: ${serviceName}`;
        customServices.push(serviceInfo);
      }
      
    } catch (error) {
      console.warn(`⚠️  [SERVICE DISCOVERY] Could not resolve service: ${serviceName}`);
    }
  });
  
  const result: ServiceDiscoveryResult = {
    coreServices: coreServices.sort((a, b) => a.name.localeCompare(b.name)),
    modules: modules.sort((a, b) => a.name.localeCompare(b.name)),
    repositories: repositories.sort((a, b) => a.name.localeCompare(b.name)),
    customServices: customServices.sort((a, b) => a.name.localeCompare(b.name)),
    totalServices: serviceNames.length,
    discoveredAt: new Date().toISOString()
  };
  
  console.log(`✅ [SERVICE DISCOVERY] Categorized services:
    - Core Services: ${coreServices.length}
    - Modules: ${modules.length}
    - Repositories: ${repositories.length}
    - Custom Services: ${customServices.length}
    - Total: ${serviceNames.length}`);
  
  return result;
}

/**
 * Get available methods from a service instance
 */
function getServiceMethods(service: any): string[] {
  if (!service || typeof service !== 'object') {
    return [];
  }
  
  const methods: string[] = [];
  const prototype = Object.getPrototypeOf(service);
  
  // Get methods from the service instance
  Object.getOwnPropertyNames(prototype).forEach(name => {
    if (name !== 'constructor' && typeof service[name] === 'function') {
      methods.push(name);
    }
  });
  
  // Get methods from the service object itself
  Object.getOwnPropertyNames(service).forEach(name => {
    if (typeof service[name] === 'function' && !methods.includes(name)) {
      methods.push(name);
    }
  });
  
  return methods.sort();
}

/**
 * Find services that match specific patterns (e.g., product-related services)
 */
export function findServicesByPattern(
  req: MedusaRequest, 
  pattern: string | RegExp
): ServiceInfo[] {
  const discovery = discoverServices(req);
  const allServices = [
    ...discovery.coreServices,
    ...discovery.modules,
    ...discovery.repositories,
    ...discovery.customServices
  ];
  
  const regex = typeof pattern === 'string' 
    ? new RegExp(pattern, 'i') 
    : pattern;
  
  return allServices.filter(service => regex.test(service.name));
}

/**
 * Attempt to resolve a service with multiple possible names
 */
export function resolveServiceWithFallbacks(
  req: MedusaRequest,
  possibleNames: string[]
): { service: any; resolvedName: string } | null {
  
  for (const name of possibleNames) {
    try {
      const service = req.scope.resolve(name);
      console.log(`✅ [SERVICE RESOLUTION] Successfully resolved: ${name}`);
      return { service, resolvedName: name };
    } catch (error) {
      console.log(`❌ [SERVICE RESOLUTION] Failed to resolve: ${name}`);
    }
  }
  
  console.error(`🚫 [SERVICE RESOLUTION] Could not resolve any of: ${possibleNames.join(', ')}`);
  return null;
}

/**
 * Common service name patterns for Medusa v2
 */
export const MEDUSA_V2_SERVICE_PATTERNS = {
  product: [
    'productModuleService',
    'productService', 
    'product',
    'productModule',
    'Product'
  ],
  customer: [
    'customerModuleService',
    'customerService',
    'customer',
    'customerModule',
    'Customer'
  ],
  order: [
    'orderModuleService',
    'orderService',
    'order',
    'orderModule',
    'Order'
  ],
  cart: [
    'cartModuleService',
    'cartService',
    'cart',
    'cartModule',
    'Cart'
  ],
  inventory: [
    'inventoryModuleService',
    'inventoryService',
    'inventory',
    'inventoryModule',
    'Inventory'
  ],
  pricing: [
    'pricingModuleService',
    'pricingService',
    'pricing',
    'pricingModule',
    'Pricing'
  ]
};

/**
 * Test service methods to understand their capabilities
 */
export function testServiceMethods(service: any, serviceName: string): {
  crudMethods: string[];
  queryMethods: string[];
  otherMethods: string[];
} {
  const methods = getServiceMethods(service);
  
  const crudMethods = methods.filter(method => 
    /^(create|update|delete|remove)/.test(method)
  );
  
  const queryMethods = methods.filter(method => 
    /^(find|get|list|retrieve|search|query)/.test(method)
  );
  
  const otherMethods = methods.filter(method => 
    !crudMethods.includes(method) && !queryMethods.includes(method)
  );
  
  console.log(`🔧 [SERVICE METHODS] ${serviceName}:
    - CRUD: ${crudMethods.join(', ')}
    - Query: ${queryMethods.join(', ')}
    - Other: ${otherMethods.join(', ')}`);
  
  return { crudMethods, queryMethods, otherMethods };
}
