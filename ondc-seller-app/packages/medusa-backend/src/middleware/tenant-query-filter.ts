import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http'

type NextFunction = (error?: any) => void

interface TenantFilterOptions {
  enabled?: boolean
  skipPaths?: string[]
  skipMethods?: string[]
  enforceStrict?: boolean
}

const defaultOptions: TenantFilterOptions = {
  enabled: true,
  skipPaths: ['/health', '/docs', '/auth', '/admin/auth', '/store/auth'],
  skipMethods: ['OPTIONS'],
  enforceStrict: true
}

/**
 * Database Connection Helper
 */
export const getDatabaseConnection = () => {
  const { Client } = require('pg')
  return new Client({
    connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
  })
}

/**
 * Universal Tenant Query Builder
 */
export class TenantQueryBuilder {
  private tenantId: string
  private client: any

  constructor(tenantId: string) {
    this.tenantId = tenantId
    this.client = getDatabaseConnection()
  }

  async connect() {
    await this.client.connect()
  }

  async disconnect() {
    await this.client.end().catch(() => {})
  }

  /**
   * Get paginated results for any table with tenant filtering
   */
  async getPaginatedResults(
    tableName: string,
    options: {
      limit?: number
      offset?: number
      orderBy?: string
      orderDirection?: 'ASC' | 'DESC'
      selectFields?: string[]
      additionalWhere?: string
      additionalParams?: any[]
    } = {}
  ) {
    const {
      limit = 50,
      offset = 0,
      orderBy = 'created_at',
      orderDirection = 'DESC',
      selectFields = ['*'],
      additionalWhere = '',
      additionalParams = []
    } = options

    try {
      // Build the WHERE clause
      let whereClause = 'tenant_id = $1'
      let params = [this.tenantId]
      
      if (additionalWhere) {
        whereClause += ` AND ${additionalWhere}`
        params = params.concat(additionalParams)
      }

      // Get total count
      const countQuery = `SELECT COUNT(*) as total FROM ${tableName} WHERE ${whereClause}`
      const countResult = await this.client.query(countQuery, params)
      const totalCount = parseInt(countResult.rows[0]?.total || 0)

      // Get paginated results
      const selectQuery = `
        SELECT ${selectFields.join(', ')}
        FROM ${tableName} 
        WHERE ${whereClause}
        ORDER BY ${orderBy} ${orderDirection}
        LIMIT $${params.length + 1} OFFSET $${params.length + 2}
      `
      
      const selectParams = [...params, limit, offset]
      const result = await this.client.query(selectQuery, selectParams)

      return {
        data: result.rows || [],
        count: result.rows.length,
        totalCount,
        offset,
        limit
      }
    } catch (error) {
      console.error(`❌ [TENANT FILTER] Error querying ${tableName}:`, error)
      throw error
    }
  }

  /**
   * Get single record by ID with tenant validation
   */
  async getById(tableName: string, id: string, selectFields: string[] = ['*']) {
    try {
      const query = `
        SELECT ${selectFields.join(', ')}
        FROM ${tableName} 
        WHERE id = $1 AND tenant_id = $2
      `
      
      const result = await this.client.query(query, [id, this.tenantId])
      return result.rows[0] || null
    } catch (error) {
      console.error(`❌ [TENANT FILTER] Error getting ${tableName} by ID:`, error)
      throw error
    }
  }

  /**
   * Create record with tenant ID injection
   */
  async create(tableName: string, data: any) {
    try {
      // Inject tenant_id into data
      const dataWithTenant = {
        ...data,
        tenant_id: this.tenantId
      }

      const fields = Object.keys(dataWithTenant)
      const values = Object.values(dataWithTenant)
      const placeholders = fields.map((_, index) => `$${index + 1}`).join(', ')

      const query = `
        INSERT INTO ${tableName} (${fields.join(', ')})
        VALUES (${placeholders})
        RETURNING *
      `

      const result = await this.client.query(query, values)
      return result.rows[0]
    } catch (error) {
      console.error(`❌ [TENANT FILTER] Error creating ${tableName}:`, error)
      throw error
    }
  }

  /**
   * Update record with tenant validation
   */
  async update(tableName: string, id: string, data: any) {
    try {
      // First validate the record belongs to this tenant
      const existing = await this.getById(tableName, id, ['id'])
      if (!existing) {
        throw new Error(`Record not found or access denied`)
      }

      // Remove tenant_id from update data to prevent modification
      const updateData = { ...data }
      delete updateData.tenant_id

      const fields = Object.keys(updateData)
      const values = Object.values(updateData)
      const setClause = fields.map((field, index) => `${field} = $${index + 1}`).join(', ')

      const query = `
        UPDATE ${tableName} 
        SET ${setClause}
        WHERE id = $${fields.length + 1} AND tenant_id = $${fields.length + 2}
        RETURNING *
      `

      const params = [...values, id, this.tenantId]
      const result = await this.client.query(query, params)
      return result.rows[0]
    } catch (error) {
      console.error(`❌ [TENANT FILTER] Error updating ${tableName}:`, error)
      throw error
    }
  }

  /**
   * Delete record with tenant validation
   */
  async delete(tableName: string, id: string) {
    try {
      // First validate the record belongs to this tenant
      const existing = await this.getById(tableName, id, ['id'])
      if (!existing) {
        throw new Error(`Record not found or access denied`)
      }

      const query = `
        DELETE FROM ${tableName} 
        WHERE id = $1 AND tenant_id = $2
        RETURNING *
      `

      const result = await this.client.query(query, [id, this.tenantId])
      return result.rows[0]
    } catch (error) {
      console.error(`❌ [TENANT FILTER] Error deleting ${tableName}:`, error)
      throw error
    }
  }
}

/**
 * Tenant-Aware Response Builder
 */
export const buildTenantResponse = (
  data: any[],
  totalCount: number,
  offset: number,
  limit: number,
  tenantId: string,
  resourceType: string
) => {
  return {
    [resourceType]: data,
    count: data.length,
    offset,
    limit,
    _tenant: {
      id: tenantId,
      filtered: true,
      method: 'tenant_query_builder',
      total_in_db: totalCount
    }
  }
}

/**
 * Universal Tenant Filter Middleware
 */
export const universalTenantFilter = (resourceType: string, tableName: string) => {
  return async (req: MedusaRequest, res: MedusaResponse, next: NextFunction) => {
    try {
      const tenantId = req.headers['x-tenant-id'] as string || 'default'
      console.log(`🔍 [UNIVERSAL TENANT FILTER] Processing ${resourceType} for tenant: ${tenantId}`)

      const queryBuilder = new TenantQueryBuilder(tenantId)
      await queryBuilder.connect()

      // Attach query builder to request for use in route handlers
      ;(req as any).tenantQueryBuilder = queryBuilder
      ;(req as any).tenantId = tenantId

      // Set response headers
      res.setHeader('X-Tenant-ID', tenantId)
      res.setHeader('X-Tenant-Filtered', 'true')

      next()
    } catch (error) {
      console.error(`❌ [UNIVERSAL TENANT FILTER] Error in ${resourceType} filter:`, error)
      next(error)
    }
  }
}

/**
 * Cleanup middleware to close database connections
 */
export const cleanupTenantFilter = (req: MedusaRequest, res: MedusaResponse, next: NextFunction) => {
  const originalSend = res.send
  const originalJson = res.json

  res.send = function(data) {
    cleanup()
    return originalSend.call(this, data)
  }

  res.json = function(data) {
    cleanup()
    return originalJson.call(this, data)
  }

  const cleanup = () => {
    if ((req as any).tenantQueryBuilder) {
      ;(req as any).tenantQueryBuilder.disconnect()
    }
  }

  next()
}

/**
 * Extract tenant context from request
 */
export const extractTenantContext = (req: MedusaRequest) => {
  return {
    tenantId: req.headers['x-tenant-id'] as string || 'default',
    queryBuilder: (req as any).tenantQueryBuilder,
    timestamp: new Date().toISOString()
  }
}