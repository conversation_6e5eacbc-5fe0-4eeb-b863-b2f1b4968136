import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';

type NextFunction = (error?: any) => void;

export interface TenantConfig {
  id: string;
  name: string;
  domain?: string;
  settings: {
    currency: string;
    timezone: string;
    features: string[];
    ondcConfig: {
      participantId: string;
      subscriberId: string;
      bppId: string;
    };
  };
  status: 'active' | 'inactive' | 'suspended';
  createdAt: Date;
  updatedAt: Date;
}

/**
 * RLS Integration Note:
 *
 * The RLS (Row-Level Security) system is properly configured and working.
 * However, PostgreSQL session variables only persist within a single database connection.
 * Since Medusa v2 manages its own database connections and connection pooling,
 * we cannot reliably set session variables from middleware that will persist
 * for the actual Medusa database operations.
 *
 * The RLS policies are in place and will enforce tenant isolation when:
 * 1. Using the non-superuser database role (medusa_app)
 * 2. Session variables are properly set on the same connection
 *
 * For now, we'll focus on the middleware functionality and tenant context management.
 * The RLS integration will be completed in the next phase when we can properly
 * integrate with Medusa's database connection management.
 */

/**
 * Log RLS status for debugging
 */
const logRLSStatus = (tenantId: string): void => {
  console.log(`🔒 [RLS] Tenant context prepared for: ${tenantId}`);
  console.log(
    `🔒 [RLS] Note: RLS policies are active and will enforce isolation when using non-superuser role`
  );
};

/**
 * Tenant Validation Error Types
 */
interface TenantValidationError {
  error: string;
  message: string;
  details?: any;
  tenant_id?: string;
}

/**
 * Enhanced Tenant ID Extraction with Validation
 * Task 2.3: Implement robust tenant header validation
 */
const extractAndValidateTenantId = (
  req: MedusaRequest
): { tenantId: string | null; error: TenantValidationError | null } => {
  // Priority order: header > query > subdomain > default
  let tenantId = req.headers['x-tenant-id'] as string;

  // Validate header format if present
  if (tenantId) {
    // Check for empty or whitespace-only values
    if (typeof tenantId !== 'string' || tenantId.trim() === '') {
      return {
        tenantId: null,
        error: {
          error: 'INVALID_TENANT_HEADER',
          message: 'x-tenant-id header cannot be empty or contain only whitespace',
          details: { provided_value: tenantId, source: 'header' },
        },
      };
    }

    // Validate tenant ID format (alphanumeric, hyphens, underscores only)
    const tenantIdRegex = /^[a-zA-Z0-9][a-zA-Z0-9_-]*[a-zA-Z0-9]$|^[a-zA-Z0-9]$/;
    if (!tenantIdRegex.test(tenantId.trim())) {
      return {
        tenantId: null,
        error: {
          error: 'MALFORMED_TENANT_ID',
          message:
            'Tenant ID must contain only alphanumeric characters, hyphens, and underscores. Must start and end with alphanumeric characters.',
          details: {
            provided_value: tenantId.trim(),
            source: 'header',
            valid_pattern:
              'alphanumeric, hyphens, underscores (e.g., tenant-001, default, electronics_store)',
          },
        },
      };
    }

    // Check length constraints
    const trimmedTenantId = tenantId.trim();
    if (trimmedTenantId.length < 1 || trimmedTenantId.length > 50) {
      return {
        tenantId: null,
        error: {
          error: 'TENANT_ID_LENGTH_INVALID',
          message: 'Tenant ID must be between 1 and 50 characters long',
          details: {
            provided_value: trimmedTenantId,
            length: trimmedTenantId.length,
            source: 'header',
          },
        },
      };
    }

    return { tenantId: trimmedTenantId, error: null };
  }

  // Fallback to query parameter
  if (!tenantId) {
    tenantId = req.query.tenant as string;

    if (tenantId && typeof tenantId === 'string' && tenantId.trim()) {
      const trimmedTenantId = tenantId.trim();
      const tenantIdRegex = /^[a-zA-Z0-9][a-zA-Z0-9_-]*[a-zA-Z0-9]$|^[a-zA-Z0-9]$/;

      if (!tenantIdRegex.test(trimmedTenantId)) {
        return {
          tenantId: null,
          error: {
            error: 'MALFORMED_TENANT_ID',
            message: 'Tenant ID in query parameter is malformed',
            details: { provided_value: trimmedTenantId, source: 'query' },
          },
        };
      }

      return { tenantId: trimmedTenantId, error: null };
    }
  }

  // Fallback to subdomain extraction
  if (!tenantId && req.headers.host) {
    const host = req.headers.host;
    const subdomain = host.split('.')[0];
    if (subdomain && subdomain !== 'www' && subdomain !== 'api' && subdomain !== 'localhost') {
      const tenantIdRegex = /^[a-zA-Z0-9][a-zA-Z0-9_-]*[a-zA-Z0-9]$|^[a-zA-Z0-9]$/;

      if (tenantIdRegex.test(subdomain)) {
        return { tenantId: subdomain, error: null };
      }
    }
  }

  // For development, allow default tenant
  if (process.env.NODE_ENV === 'development') {
    const defaultTenant = process.env.DEFAULT_TENANT_ID || 'default';
    return { tenantId: defaultTenant, error: null };
  }

  // In production, require explicit tenant ID
  return {
    tenantId: null,
    error: {
      error: 'MISSING_TENANT_ID',
      message:
        'Tenant ID is required. Provide it via x-tenant-id header, tenant query parameter, or subdomain.',
      details: {
        accepted_methods: [
          'Header: x-tenant-id',
          'Query parameter: ?tenant=your-tenant-id',
          'Subdomain: your-tenant.domain.com',
        ],
      },
    },
  };
};

/**
 * Validate Tenant Existence and Status
 */
const validateTenant = async (tenantId: string): Promise<TenantConfig | null> => {
  // For development mode, return mock tenant (skip VALID_TENANTS check)
  if (process.env.NODE_ENV === 'development') {
    return {
      id: tenantId,
      name: `Development Tenant ${tenantId}`,
      settings: {
        currency: 'INR',
        timezone: 'Asia/Kolkata',
        features: ['all'],
        ondcConfig: {
          participantId: process.env.ONDC_PARTICIPANT_ID || 'default-participant',
          subscriberId: process.env.ONDC_SUBSCRIBER_ID || 'default-subscriber',
          bppId: `${process.env.ONDC_BPP_ID || 'ondc-bpp'}-${tenantId}`,
        },
      },
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  // In production, validate against database or configuration
  const validTenants = process.env.VALID_TENANTS?.split(',') || ['default'];

  if (!validTenants.includes(tenantId)) {
    return null;
  }

  return {
    id: tenantId,
    name: `Tenant ${tenantId}`,
    domain: `${tenantId}.ondc-seller.com`,
    settings: {
      currency: 'INR',
      timezone: 'Asia/Kolkata',
      features: ['products', 'orders', 'customers', 'analytics'],
      ondcConfig: {
        participantId:
          process.env[`ONDC_PARTICIPANT_ID_${tenantId.toUpperCase()}`] ||
          process.env.ONDC_PARTICIPANT_ID ||
          'default-participant',
        subscriberId:
          process.env[`ONDC_SUBSCRIBER_ID_${tenantId.toUpperCase()}`] ||
          process.env.ONDC_SUBSCRIBER_ID ||
          'default-subscriber',
        bppId:
          process.env[`ONDC_BPP_ID_${tenantId.toUpperCase()}`] ||
          `${process.env.ONDC_BPP_ID || 'ondc-bpp'}-${tenantId}`,
      },
    },
    status: 'active',
    createdAt: new Date(),
    updatedAt: new Date(),
  };
};

/**
 * Main Tenant Middleware
 */
export const tenantMiddleware = async (
  req: MedusaRequest,
  res: MedusaResponse,
  next: NextFunction
): Promise<any> => {
  try {
    // Skip tenant validation for health checks and public endpoints
    const skipPaths = ['/health', '/docs', '/admin/auth', '/store/auth'];
    const shouldSkip = skipPaths.some(path => req.path.startsWith(path));

    if (shouldSkip) {
      return next();
    }

    // Extract and validate tenant ID with enhanced validation
    const { tenantId, error: validationError } = extractAndValidateTenantId(req);

    // Return HTTP 400 for validation errors
    if (validationError) {
      console.log(`❌ [TENANT] Validation error: ${validationError.message}`);
      return res.status(400).json(validationError);
    }

    // This should not happen due to validation logic, but add safety check
    if (!tenantId) {
      return res.status(400).json({
        error: 'TENANT_EXTRACTION_FAILED',
        message: 'Failed to extract tenant ID from request',
      });
    }

    // Set tenant ID in multiple formats for compatibility
    (req as any).tenantId = tenantId;
    (req as any).tenant_id = tenantId;

    // Validate tenant existence and status
    const tenant = await validateTenant(tenantId);
    if (!tenant) {
      return res.status(403).json({
        error: 'INVALID_TENANT',
        message: `Tenant '${tenantId}' not found or inactive`,
        tenant_id: tenantId,
      });
    }

    if (tenant.status !== 'active') {
      return res.status(403).json({
        error: 'TENANT_INACTIVE',
        message: `Tenant '${tenantId}' is ${tenant.status}`,
        tenant_id: tenantId,
        status: tenant.status,
      });
    }

    // Attach tenant to request
    (req as any).tenant = tenant;

    // Log RLS status for debugging
    logRLSStatus(tenantId);

    // Add tenant context to response headers (for debugging)
    if (process.env.NODE_ENV === 'development') {
      res.setHeader('X-Tenant-ID', tenantId);
      res.setHeader('X-Tenant-Name', tenant.name);
      res.setHeader('X-RLS-Enabled', 'true');
    }

    console.log(`🏢 [TENANT] Request for tenant: ${tenantId} on ${req.method} ${req.path}`);

    next();
  } catch (error) {
    console.error('❌ [TENANT] Middleware error:', error);
    next(error);
  }
};

/**
 * Get Tenant ID from Request (Utility Function)
 */
export const getTenantId = (req: MedusaRequest): string => {
  return (req as any).tenantId || (req as any).tenant?.id || 'default';
};

/**
 * Get Tenant Configuration (Utility Function)
 */
export const getTenantConfig = (req: MedusaRequest): TenantConfig | null => {
  return (req as any).tenant || null;
};

/**
 * Inject Tenant ID into Data
 */
export const injectTenantId = (req: MedusaRequest, data: any): any => {
  const tenantId = getTenantId(req);

  if (Array.isArray(data)) {
    return data.map(item => ({ ...item, tenant_id: tenantId }));
  }

  return { ...data, tenant_id: tenantId };
};

/**
 * Filter Data by Tenant ID
 */
export const filterByTenantId = (req: MedusaRequest, data: any[]): any[] => {
  const tenantId = getTenantId(req);

  return data.filter(item => !item.tenant_id || item.tenant_id === tenantId);
};

/**
 * Tenant Error Handler
 * Handles tenant-related errors
 */
export const tenantErrorHandler = async (
  error: any,
  req: MedusaRequest,
  res: MedusaResponse,
  next: NextFunction
): Promise<void> => {
  console.log('🔒 [RLS] Tenant error handler called');

  // Pass error to next handler
  next(error);
};
