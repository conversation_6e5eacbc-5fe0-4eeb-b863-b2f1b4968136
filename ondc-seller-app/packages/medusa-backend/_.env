MEDUSA_ADMIN_ONBOARDING_TYPE=default
STORE_CORS=http://localhost:3000,http://localhost:3001,http://localhost:8000,https://docs.medusajs.com
ADMIN_CORS=http://localhost:3000,http://localhost:3001,http://localhost:5173,http://localhost:9000,https://docs.medusajs.com
AUTH_CORS=http://localhost:3000,http://localhost:3001,http://localhost:5173,http://localhost:9000,http://localhost:8000,https://docs.medusajs.com
REDIS_URL=redis://localhost:6379
JWT_SECRET=supersecret
COOKIE_SECRET=supersecret
DATABASE_URL=postgresql://strapi:strapi_password@localhost:5432/medusa_backend
# Non-superuser database URL for RLS enforcement (used by middleware)
DATABASE_URL_APP=postgresql://medusa_app:medusa_app_password@localhost:5432/medusa_backend

# Multi-Tenancy Configuration
DEFAULT_TENANT_ID=default
VALID_TENANTS=default,tenant-electronics-001,tenant-fashion-002,tenant-books-003,zen-mart,my-kirana-store

# ONDC Configuration (Global)
ONDC_PARTICIPANT_ID=ondc-participant-001
ONDC_SUBSCRIBER_ID=ondc-subscriber-001
ONDC_BPP_ID=ondc-bpp

# Tenant-specific ONDC Configuration (Optional)
ONDC_PARTICIPANT_ID_TENANT_ELECTRONICS_001=electronics-participant-001
ONDC_SUBSCRIBER_ID_TENANT_ELECTRONICS_001=electronics-subscriber-001
ONDC_BPP_ID_TENANT_ELECTRONICS_001=ondc-bpp-electronics

ONDC_PARTICIPANT_ID_TENANT_FASHION_002=fashion-participant-002
ONDC_SUBSCRIBER_ID_TENANT_FASHION_002=fashion-subscriber-002
ONDC_BPP_ID_TENANT_FASHION_002=ondc-bpp-fashion

ONDC_PARTICIPANT_ID_TENANT_BOOKS_003=books-participant-003
ONDC_SUBSCRIBER_ID_TENANT_BOOKS_003=books-subscriber-003
ONDC_BPP_ID_TENANT_BOOKS_003=ondc-bpp-books
DB_NAME=medusa_backend

# Sales Channel Configuration
DEFAULT_SALES_CHANNEL_ID=sc_01K33ENQEWWEMQNT0WDXAHMCWD
DEFAULT_STOCK_LOCATION_ID=sloc_01K33E8B4MEHSRZBWT2RM11N6A
DEFAULT_SHIPPING_PROFILE_ID=sp_01JZ4VNZYVNWS3976TD64VQ423

# db config