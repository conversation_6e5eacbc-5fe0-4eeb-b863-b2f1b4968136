#!/usr/bin/env node

const axios = require('axios');

const BASE_URL = 'http://localhost:9000';
const ADMIN_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY3Rvcl9pZCI6InVzZXJfMDFKWjRWVkdFSlg2S1RRM1JaTUI3OFk1TTEiLCJhY3Rvcl90eXBlIjoidXNlciIsImF1dGhfaWRlbnRpdHlfaWQiOiJhdXRoaWRfMDFKWjRWVkdKSlRLVzNSWUdNTUUwRVg0NDMiLCJhcHBfbWV0YWRhdGEiOnsidXNlcl9pZCI6InVzZXJfMDFKWjRWVkdFSlg2S1RRM1JaTUI3OFk1TTEifSwiaWF0IjoxNzU0NDYzNTcxLCJleHAiOjE3NTQ1NDk5NzF9.8QuHH1MajzPppiHgz0yHRamCtzsgC0GIqc9sWcmlryo';

const TENANTS = [
  { id: 'default', name: 'Default Tenant' },
  { id: 'tenant-electronics-001', name: 'Electronics Tenant' },
  { id: 'tenant-fashion-002', name: 'Fashion Tenant' }
];

const ENDPOINTS = [
  { path: '/admin/products', name: 'Products', key: 'products' },
  { path: '/admin/customers', name: 'Customers', key: 'customers' },
  { path: '/admin/orders', name: 'Orders', key: 'orders' },
  { path: '/admin/product-categories', name: 'Categories', key: 'product_categories' }
];

async function testEndpoint(endpoint, tenant, limit = 10) {
  try {
    const response = await axios.get(`${BASE_URL}${endpoint.path}?limit=${limit}`, {
      headers: {
        'Authorization': `Bearer ${ADMIN_TOKEN}`,
        'Content-Type': 'application/json',
        'x-tenant-id': tenant.id
      }
    });

    const data = response.data[endpoint.key] || [];
    const tenantIds = [...new Set(data.map(item => item.tenant_id).filter(Boolean))];
    
    return {
      success: true,
      count: data.length,
      totalInDb: response.data._tenant?.total_in_db || 0,
      tenantIds,
      isolated: tenantIds.length <= 1 && (tenantIds.length === 0 || tenantIds[0] === tenant.id),
      headers: {
        tenantId: response.headers['x-tenant-id'],
        filtered: response.headers['x-tenant-filtered']
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.message || error.message,
      status: error.response?.status
    };
  }
}

async function testTenantIsolation() {
  console.log('🧪 Multi-Tenant API Testing Suite');
  console.log('==================================\n');

  const results = {};

  for (const tenant of TENANTS) {
    console.log(`📋 Testing data access for ${tenant.name} (${tenant.id})`);
    console.log('='.repeat(60));
    
    results[tenant.id] = {};

    for (const endpoint of ENDPOINTS) {
      const result = await testEndpoint(endpoint, tenant);
      results[tenant.id][endpoint.name] = result;

      if (result.success) {
        console.log(`  📦 ${endpoint.name}: ${result.count} found (${result.totalInDb} total in DB)`);
        if (result.tenantIds.length > 0) {
          console.log(`     Tenant IDs in results: ${result.tenantIds.join(', ')}`);
        }
        console.log(`     ${result.isolated ? '✅' : '❌'} Tenant isolation: ${result.isolated ? 'PASSED' : 'FAILED'}`);
      } else {
        console.log(`  ❌ ${endpoint.name}: Error - ${result.error} (Status: ${result.status})`);
      }
    }

    console.log(`  🏢 Response Headers:`);
    const firstResult = Object.values(results[tenant.id]).find(r => r.success);
    if (firstResult) {
      console.log(`     X-Tenant-ID: ${firstResult.headers.tenantId || 'Not Set'}`);
      console.log(`     X-Tenant-Filtered: ${firstResult.headers.filtered || 'Not Set'}`);
    }
    console.log('');
  }

  // Summary
  console.log('📊 Test Summary:');
  console.log('================');
  
  let totalTests = 0;
  let passedTests = 0;
  let failedTests = 0;

  for (const tenant of TENANTS) {
    for (const endpoint of ENDPOINTS) {
      const result = results[tenant.id][endpoint.name];
      totalTests++;
      
      if (result.success && result.isolated) {
        passedTests++;
      } else {
        failedTests++;
        console.log(`❌ ${tenant.name} - ${endpoint.name}: ${result.success ? 'Isolation Failed' : result.error}`);
      }
    }
  }

  console.log(`\n🎯 Results:`);
  console.log(`   Total Tests: ${totalTests}`);
  console.log(`   ✅ Passed: ${passedTests}`);
  console.log(`   ❌ Failed: ${failedTests}`);
  console.log(`   📊 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

  if (passedTests === totalTests) {
    console.log('\n🎉 All tenant isolation tests PASSED! 🎉');
  } else {
    console.log('\n⚠️  Some tenant isolation tests FAILED. Review the issues above.');
  }

  return {
    totalTests,
    passedTests,
    failedTests,
    successRate: (passedTests / totalTests) * 100
  };
}

// Test cross-tenant access prevention
async function testCrossTenantAccess() {
  console.log('\n🚫 Testing cross-tenant access prevention...');
  console.log('='.repeat(50));

  // Try to access electronics products from fashion tenant
  try {
    const electronicsResult = await testEndpoint(ENDPOINTS[0], TENANTS[1]); // Electronics products
    const fashionResult = await testEndpoint(ENDPOINTS[0], TENANTS[2]); // Fashion products

    if (electronicsResult.success && fashionResult.success) {
      const electronicsProducts = electronicsResult.tenantIds;
      const fashionProducts = fashionResult.tenantIds;
      
      const hasOverlap = electronicsProducts.some(id => fashionProducts.includes(id));
      
      console.log(`📦 Electronics tenant sees: ${electronicsProducts.join(', ') || 'No products'}`);
      console.log(`👗 Fashion tenant sees: ${fashionProducts.join(', ') || 'No products'}`);
      console.log(`${hasOverlap ? '❌' : '✅'} Cross-tenant isolation: ${hasOverlap ? 'FAILED' : 'PASSED'}`);
      
      return !hasOverlap;
    }
  } catch (error) {
    console.log(`❌ Error in cross-tenant test: ${error.message}`);
    return false;
  }
}

async function runAllTests() {
  const isolationResults = await testTenantIsolation();
  const crossTenantResults = await testCrossTenantAccess();

  console.log('\n🎯 Final Assessment:');
  console.log('===================');
  console.log('- ✅ Tenant middleware should inject tenant_id into created data');
  console.log('- ✅ Each tenant should only see their own data');
  console.log('- ✅ Cross-tenant access should be blocked');
  console.log('- ✅ All entities (products, customers, categories, orders) should be tenant-aware');

  if (isolationResults.successRate === 100 && crossTenantResults) {
    console.log('\n🏆 MULTI-TENANT SECURITY: FULLY IMPLEMENTED! 🏆');
  } else {
    console.log('\n⚠️  MULTI-TENANT SECURITY: NEEDS ATTENTION ⚠️');
  }
}

// Run the comprehensive test suite
runAllTests().catch(console.error);