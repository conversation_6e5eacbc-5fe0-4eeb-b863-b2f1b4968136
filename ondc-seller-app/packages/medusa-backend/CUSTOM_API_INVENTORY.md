# Custom API Inventory - Beyond Standard Medusa Commerce

## Overview

This document catalogs all custom APIs implemented beyond the standard Medusa Commerce APIs, providing detailed specifications for each endpoint.

## Admin APIs (Management Operations)

### 1. Tenant Management API

#### GET /admin/tenant
**Purpose:** Retrieve tenant configuration and ONDC settings  
**Authentication:** JWT Bearer token required  
**Tenant-Aware:** Yes (via `x-tenant-id` header)

**Request Headers:**
```http
Authorization: Bearer <jwt_token>
x-tenant-id: tenant-electronics-001
```

**Response Format:**
```json
{
  "success": true,
  "tenant": {
    "id": "tenant-electronics-001",
    "name": "Electronics Store",
    "domain": "electronics.ondc-seller.com",
    "settings": {
      "currency": "INR",
      "timezone": "Asia/Kolkata",
      "features": ["products", "orders", "customers", "analytics"],
      "ondcConfig": {
        "participantId": "electronics-participant-001",
        "subscriberId": "electronics-subscriber-001",
        "bppId": "ondc-bpp-electronics-001"
      }
    },
    "status": "active"
  },
  "multi_tenancy": {
    "enabled": true,
    "available_tenants": ["tenant-electronics-001", "tenant-fashion-002"],
    "database_isolation": {
      "products": "tenant_id column added",
      "customers": "tenant_id column added",
      "orders": "tenant_id column added"
    }
  }
}
```

#### POST /admin/tenant
**Purpose:** Update tenant configuration  
**Request Body:** Tenant configuration updates  
**Response:** Confirmation with updated configuration

### 2. Enhanced Product Management API

#### GET /admin/products
**Enhancements over Standard Medusa:**
- Tenant-aware filtering with ownership tracking
- Additional metadata fields for ONDC compliance
- Enhanced query parameters for multi-tenant scenarios

**Custom Features:**
```json
{
  "products": [...],
  "metadata": {
    "tenantId": "tenant-electronics-001",
    "filtering": {
      "totalBeforeFiltering": 100,
      "totalAfterFiltering": 25,
      "returned": 10
    }
  }
}
```

#### POST /admin/products
**Custom Fields:**
- `additional_data`: ONDC-specific product attributes
- `tenant_id`: Automatic tenant context injection
- Enhanced metadata with tenant ownership

#### PUT /admin/products
**Bulk Operations:**
- Bulk update multiple products with tenant validation
- Tenant ownership verification before updates
- Batch processing with error handling per product

### 3. Multi-Tenant Customer Management

#### GET /admin/customers
**Tenant Isolation Features:**
- Customer data scoped to tenant
- Email uniqueness per tenant (not global)
- Tenant-specific customer segmentation

#### POST /admin/customers
**Enhanced Validation:**
- Tenant-aware email validation
- Automatic tenant context injection
- Customer group assignment per tenant

### 4. Enhanced Order Management

#### GET /admin/orders
**Multi-Tenant Features:**
- Order filtering by tenant
- Tenant-specific order workflows
- ONDC compliance metadata

#### POST /admin/orders
**Custom Processing:**
- Tenant-aware order creation
- ONDC protocol integration
- Tenant-specific payment methods

### 5. Inventory Management with Tenant Context

#### GET /admin/stock-locations
**Tenant Features:**
- Warehouse management per tenant
- Tenant-specific inventory tracking
- Location-based tenant filtering

## Store APIs (Customer-Facing)

### 1. Tenant-Aware Product Catalog

#### GET /store/products
**Custom Filtering Logic:**
```javascript
const TENANT_PRODUCT_CONFIG = {
  'tenant-electronics-001': {
    categories: ['Electronics', 'Gadgets', 'Tech Accessories'],
    productFilter: (products) => {
      return products.filter(product =>
        product.title?.toLowerCase().includes('tech') ||
        product.categories?.some(cat =>
          cat.name?.toLowerCase().includes('electronics')
        )
      );
    }
  }
};
```

**Enhanced Response:**
```json
{
  "products": [...],
  "metadata": {
    "tenantId": "tenant-electronics-001",
    "tenantConfig": {
      "categories": ["Electronics", "Gadgets"],
      "collections": ["featured-products"],
      "tags": ["electronics", "tech"]
    },
    "filtering": {
      "totalBeforeFiltering": 200,
      "totalAfterFiltering": 50,
      "returned": 10
    }
  }
}
```

### 2. Enhanced Cart Management

#### GET/POST /store/carts
**Tenant Context Features:**
- Automatic tenant context preservation
- Tenant-specific pricing rules
- Currency handling per tenant

### 3. Simplified Order Creation

#### POST /store/orders/create
**Custom Features:**
- Cash-on-delivery support
- Tenant-specific order processing
- ONDC workflow integration

#### POST /store/orders/simple
**Simplified Retrieval:**
- Streamlined order data format
- Tenant-aware order filtering
- Optimized for frontend consumption

## Custom Utility APIs

### 1. Test Information API

#### GET /store/test-info
**Purpose:** Development and testing support  
**Features:**
- Tenant information display
- Database connection status
- API endpoint testing utilities

### 2. Multi-Tenant Testing API

#### GET /admin/test-multi-tenant
**Purpose:** Tenant isolation testing  
**Features:**
- Tenant data validation
- Cross-tenant access testing
- Database isolation verification

## API Enhancement Patterns

### Common Enhancements Across All APIs
1. **Tenant Context Injection:** Automatic `tenant_id` addition to all operations
2. **Enhanced Metadata:** Rich response metadata with tenant information
3. **Improved Error Handling:** Tenant-aware error messages and codes
4. **Performance Optimization:** Tenant-specific caching and query optimization
5. **ONDC Compliance:** Protocol-specific fields and validation

### Response Format Standardization
```json
{
  "success": boolean,
  "data": any,
  "metadata": {
    "tenantId": string,
    "timestamp": string,
    "pagination": object,
    "filtering": object
  },
  "error": string
}
```

---

*For implementation details and code examples, refer to the source files in `/src/api/` directory.*
