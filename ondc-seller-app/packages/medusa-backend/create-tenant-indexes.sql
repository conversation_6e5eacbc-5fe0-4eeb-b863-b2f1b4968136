-- Create indexes on tenant_id columns for optimal performance
-- These indexes are crucial for RLS policy performance

-- Core business tables
CREATE INDEX IF NOT EXISTS idx_cart_tenant_id ON cart(tenant_id);
CREATE INDEX IF NOT EXISTS idx_cart_address_tenant_id ON cart_address(tenant_id);
CREATE INDEX IF NOT EXISTS idx_cart_shipping_method_tenant_id ON cart_shipping_method(tenant_id);

-- Inventory and pricing tables
CREATE INDEX IF NOT EXISTS idx_inventory_item_tenant_id ON inventory_item(tenant_id);
CREATE INDEX IF NOT EXISTS idx_price_list_tenant_id ON price_list(tenant_id);
CREATE INDEX IF NOT EXISTS idx_price_set_tenant_id ON price_set(tenant_id);
CREATE INDEX IF NOT EXISTS idx_product_variant_price_set_tenant_id ON product_variant_price_set(tenant_id);

-- Product option tables
CREATE INDEX IF NOT EXISTS idx_product_option_tenant_id ON product_option(tenant_id);
CREATE INDEX IF NOT EXISTS idx_product_option_value_tenant_id ON product_option_value(tenant_id);

-- Order related tables
CREATE INDEX IF NOT EXISTS idx_order_address_tenant_id ON order_address(tenant_id);
CREATE INDEX IF NOT EXISTS idx_order_shipping_method_tenant_id ON order_shipping_method(tenant_id);

-- Promotion and marketing tables
CREATE INDEX IF NOT EXISTS idx_promotion_tenant_id ON promotion(tenant_id);

-- Logistics and fulfillment tables
CREATE INDEX IF NOT EXISTS idx_stock_location_tenant_id ON stock_location(tenant_id);
CREATE INDEX IF NOT EXISTS idx_shipping_option_tenant_id ON shipping_option(tenant_id);
CREATE INDEX IF NOT EXISTS idx_shipping_profile_tenant_id ON shipping_profile(tenant_id);

-- Tax tables
CREATE INDEX IF NOT EXISTS idx_tax_rate_tenant_id ON tax_rate(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tax_region_tenant_id ON tax_region(tenant_id);

-- Return and refund tables
CREATE INDEX IF NOT EXISTS idx_return_tenant_id ON "return"(tenant_id);
CREATE INDEX IF NOT EXISTS idx_return_item_tenant_id ON return_item(tenant_id);
CREATE INDEX IF NOT EXISTS idx_refund_tenant_id ON refund(tenant_id);

-- Notification table
CREATE INDEX IF NOT EXISTS idx_notification_tenant_id ON notification(tenant_id);

SELECT 'Created indexes on all tenant_id columns for optimal performance' as result;
