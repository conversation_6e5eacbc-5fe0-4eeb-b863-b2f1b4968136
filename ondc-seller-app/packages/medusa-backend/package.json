{"name": "@ondc-seller/medusa-backend", "version": "0.0.1", "description": "ONDC Seller App - Medusa Commerce Backend with Multi-tenancy", "author": "ONDC Team", "license": "MIT", "keywords": ["sqlite", "postgres", "typescript", "ecommerce", "headless", "medusa"], "scripts": {"build": "medusa build", "seed": "medusa exec ./src/scripts/seed.ts", "start": "medusa start", "dev": "medusa develop", "test:integration:http": "TEST_TYPE=integration:http NODE_OPTIONS=--experimental-vm-modules jest --silent=false --runInBand --forceExit", "test:integration:modules": "TEST_TYPE=integration:modules NODE_OPTIONS=--experimental-vm-modules jest --silent --runInBand --forceExit", "test:unit": "TEST_TYPE=unit NODE_OPTIONS=--experimental-vm-modules jest --silent --runInBand --forceExit"}, "dependencies": {"@medusajs/admin": "^7.1.18", "@medusajs/admin-sdk": "2.10.1", "@medusajs/cart": "^2.10.1", "@medusajs/cli": "2.10.1", "@medusajs/customer": "^2.10.1", "@medusajs/draft-order": "^2.10.1", "@medusajs/framework": "2.10.1", "@medusajs/inventory": "^2.10.1", "@medusajs/medusa": "2.10.1", "@medusajs/order": "^2.10.1", "@medusajs/payment": "^2.10.1", "@medusajs/stock-location": "^2.10.1", "@medusajs/utils": "^2.10.1", "@mikro-orm/core": "6.4.3", "@mikro-orm/knex": "6.4.3", "@mikro-orm/migrations": "6.4.3", "@mikro-orm/postgresql": "6.4.3", "@types/multer": "^2.0.0", "@types/uuid": "^10.0.0", "awilix": "^8.0.1", "formidable": "^3.5.4", "medusa-payment-manual": "^1.0.25", "multer": "^2.0.2", "pg": "^8.13.0", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@medusajs/test-utils": "2.10.1", "@mikro-orm/cli": "6.4.3", "@swc/core": "1.5.7", "@swc/jest": "^0.2.36", "@types/busboy": "^1.5.4", "@types/formidable": "^3.4.5", "@types/jest": "^29.5.13", "@types/node": "^20.0.0", "@types/react": "^18.3.2", "@types/react-dom": "^18.2.25", "jest": "^29.7.0", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "ts-node": "^10.9.2", "typescript": "^5.6.2", "vite": "^5.2.11", "yalc": "^1.0.0-pre.53"}, "engines": {"node": ">=20"}}