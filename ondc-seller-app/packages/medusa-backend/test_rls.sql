-- Test RLS policies for tenant isolation
-- This script tests that Row Level Security is working correctly

-- Test 1: Set tenant context and query products
SELECT set_tenant_context('default');
SELECT 'Testing tenant: default' as test_info;
SELECT COUNT(*) as product_count_default FROM product;

-- Test 2: Switch to different tenant and query products
SELECT set_tenant_context('tenant-electronics-001');
SELECT 'Testing tenant: tenant-electronics-001' as test_info;
SELECT COUNT(*) as product_count_electronics FROM product;

-- Test 3: Switch to another tenant and query products
SELECT set_tenant_context('tenant-fashion-002');
SELECT 'Testing tenant: tenant-fashion-002' as test_info;
SELECT COUNT(*) as product_count_fashion FROM product;

-- Test 4: Test customers with different tenants
SELECT set_tenant_context('default');
SELECT COUNT(*) as customer_count_default FROM customer;

SELECT set_tenant_context('tenant-electronics-001');
SELECT COUNT(*) as customer_count_electronics FROM customer;

-- Test 5: Test orders with different tenants
SELECT set_tenant_context('default');
SELECT COUNT(*) as order_count_default FROM "order";

SELECT set_tenant_context('tenant-electronics-001');
SELECT COUNT(*) as order_count_electronics FROM "order";

-- Test 6: Test carts with different tenants
SELECT set_tenant_context('default');
SELECT COUNT(*) as cart_count_default FROM cart;

SELECT set_tenant_context('tenant-electronics-001');
SELECT COUNT(*) as cart_count_electronics FROM cart;

-- Test 7: Show current tenant context
SELECT get_tenant_context() as current_tenant;

-- Test 8: Show sample products with tenant_id for verification
SELECT set_tenant_context('default');
SELECT id, title, tenant_id FROM product LIMIT 3;

SELECT set_tenant_context('tenant-electronics-001');
SELECT id, title, tenant_id FROM product LIMIT 3;
