# Dashboard Analytics API Documentation

## Overview

The Dashboard Analytics API provides comprehensive analytics data for the ONDC Seller App admin dashboard. All APIs are built on Medusa v2 and support multi-tenant architecture.

## Base URL
```
http://localhost:9000
```

## Authentication

Admin endpoints require authentication:
```
Authorization: Bearer <token>
```

Test endpoints do not require authentication for development purposes.

## Endpoints

### 1. Test Dashboard API

**Endpoint:** `GET /test/dashboard`

**Description:** Test endpoint that returns real dashboard data without authentication.

**Parameters:**
- `period` (optional): Time period for data filtering
  - Values: `7d`, `30d`, `90d`, `1y`
  - Default: `30d`
- `currency` (optional): Currency code
  - Default: `INR`
- `tenant_id` (optional): Tenant ID for multi-tenant filtering
- `sales_channel_id` (optional): Sales channel filtering

**Example Request:**
```bash
curl -X GET "http://localhost:9000/test/dashboard?period=30d"
```

**Example Response:**
```json
{
  "success": true,
  "message": "Test dashboard API working (real data)",
  "data": {
    "stats": {
      "totalRevenue": 0,
      "totalOrders": 19,
      "totalCustomers": 1,
      "totalProducts": 21,
      "averageOrderValue": 0,
      "conversionRate": 3.2,
      "revenueGrowth": 0,
      "orderGrowth": 0,
      "customerGrowth": 0
    },
    "revenueTrend": [],
    "topProducts": [],
    "topOrders": [
      {
        "order_id": "order_01K0PMQEA5Y1SZTWSWA6XS91EJ",
        "order_display_id": 19,
        "customer_name": "Guest",
        "customer_email": "<EMAIL>",
        "total_order_amount": 0,
        "order_status": "pending",
        "created_at": "2025-07-21T14:03:14.118Z"
      }
    ],
    "refundRate": [
      {"name": "Successful", "value": 95, "color": "#10B981"},
      {"name": "Refunded", "value": 5, "color": "#EF4444"}
    ],
    "customerSplit": [
      {"segment": "New", "count": 0, "percentage": 60},
      {"segment": "Returning", "count": 0, "percentage": 40}
    ]
  },
  "meta": {
    "period": "30d",
    "currency": "INR",
    "timestamp": "2025-07-23T14:25:01.988Z",
    "isTestEndpoint": true,
    "dataSource": "real"
  }
}
```

### 2. Admin KPI API

**Endpoint:** `GET /admin/analytics/kpi`

**Description:** Returns key performance indicators for the dashboard.

**Authentication:** Required

**Parameters:**
- `period` (optional): Time period (`7d`, `30d`, `90d`, `1y`)
- `tenant_id` (optional): Tenant filtering
- `sales_channel_id` (optional): Sales channel filtering
- `compare_previous` (optional): Include previous period comparison

**Response Structure:**
```json
{
  "kpis": {
    "totalRevenue": 0,
    "totalOrders": 19,
    "totalCustomers": 1,
    "totalProducts": 21,
    "averageOrderValue": 0,
    "conversionRate": 3.2
  },
  "growth": {
    "revenueGrowth": 0,
    "orderGrowth": 0,
    "customerGrowth": 0
  }
}
```

### 3. Admin Dashboard API

**Endpoint:** `GET /admin/analytics/dashboard`

**Description:** Main dashboard analytics endpoint with comprehensive data.

**Authentication:** Required

**Parameters:** Same as KPI API

### 4. Admin Sales API

**Endpoint:** `GET /admin/analytics/sales`

**Description:** Sales analytics and trends.

**Authentication:** Required

### 5. Admin Products API

**Endpoint:** `GET /admin/analytics/products`

**Description:** Product performance analytics.

**Authentication:** Required

### 6. Admin Inventory API

**Endpoint:** `GET /admin/analytics/inventory`

**Description:** Inventory analytics and stock levels.

**Authentication:** Required

### 7. Debug API

**Endpoint:** `GET /test/debug`

**Description:** Debug endpoint for inspecting available service methods.

**Authentication:** Not required

## Error Handling

All endpoints return standardized error responses:

```json
{
  "success": false,
  "error": "Error message",
  "details": "Detailed error information"
}
```

Common HTTP status codes:
- `200`: Success
- `401`: Unauthorized (missing or invalid authentication)
- `400`: Bad Request (invalid parameters)
- `500`: Internal Server Error

## Multi-tenant Support

All analytics APIs support multi-tenant filtering through the `tenant_id` parameter. When provided, data is automatically filtered to show only results for that specific tenant.

## Performance Notes

- Date filtering is implemented in-memory due to Medusa v2 limitations
- Customer counting is done from order data (no direct customer relations)
- All APIs are optimized for real-time dashboard usage
- Caching can be implemented at the application level if needed

## Testing

Use the provided test script to verify all endpoints:

```bash
node test-dashboard-apis.js
```

This will test all endpoints and provide a comprehensive status report.
