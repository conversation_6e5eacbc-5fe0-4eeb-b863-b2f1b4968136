/**
 * Test RLS Middleware Integration
 * This script tests the enhanced tenant middleware with RLS integration
 */

const { Client } = require('pg');

// Test the RLS helper functions directly
async function testRLSIntegration() {
  console.log('🧪 Testing RLS Middleware Integration...\n');

  const client = new Client({
    connectionString: 'postgresql://medusa_app:medusa_app_password@localhost:5432/medusa_backend'
  });

  try {
    await client.connect();
    console.log('✅ Connected to database with non-superuser role');

    // Test 1: Clear any existing context
    console.log('\n📋 Test 1: Clear tenant context');
    await client.query('SELECT clear_tenant_context()');
    
    // Try to query products without context (should return 0)
    const noContextResult = await client.query('SELECT COUNT(*) as count FROM product');
    console.log(`   Products without context: ${noContextResult.rows[0].count}`);

    // Test 2: Set tenant context via middleware function
    console.log('\n📋 Test 2: Set tenant context');
    await client.query('SELECT set_tenant_context($1)', ['default']);
    
    // Query products with context (should return tenant-specific data)
    const withContextResult = await client.query('SELECT COUNT(*) as count FROM product');
    console.log(`   Products with default tenant: ${withContextResult.rows[0].count}`);

    // Test 3: Switch tenant context
    console.log('\n📋 Test 3: Switch tenant context');
    await client.query('SELECT set_tenant_context($1)', ['tenant-electronics-001']);
    
    const electronicsResult = await client.query('SELECT COUNT(*) as count FROM product');
    console.log(`   Products with electronics tenant: ${electronicsResult.rows[0].count}`);

    // Test 4: Verify current tenant context
    console.log('\n📋 Test 4: Verify current context');
    const currentContext = await client.query('SELECT get_current_tenant_context() as tenant');
    console.log(`   Current tenant context: ${currentContext.rows[0].tenant}`);

    // Test 5: Clear context
    console.log('\n📋 Test 5: Clear context');
    await client.query('SELECT clear_tenant_context()');
    const clearedResult = await client.query('SELECT COUNT(*) as count FROM product');
    console.log(`   Products after clearing context: ${clearedResult.rows[0].count}`);

    console.log('\n🎉 RLS Middleware Integration Test Completed Successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await client.end();
  }
}

// Run the test
testRLSIntegration().catch(console.error);
