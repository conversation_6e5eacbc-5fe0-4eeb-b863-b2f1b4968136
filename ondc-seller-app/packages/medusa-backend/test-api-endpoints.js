#!/usr/bin/env node

/**
 * Test API Endpoints
 * This script tests the key API endpoints to verify they're working correctly
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:9000';
const TENANT_ID = 'tenant-electronics-001';
const PUBLISHABLE_API_KEY = 'pk_test_123';

// Test configuration
const config = {
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'x-tenant-id': TENANT_ID,
    'x-publishable-api-key': PUBLISHABLE_API_KEY,
  },
};

async function testEndpoint(method, url, data = null, description = '') {
  try {
    console.log(`\n🧪 Testing: ${description || `${method.toUpperCase()} ${url}`}`);

    const response = await axios({
      method,
      url,
      data,
      ...config,
    });

    console.log(`✅ Success: ${response.status} ${response.statusText}`);
    console.log(`📊 Response:`, JSON.stringify(response.data, null, 2).substring(0, 500) + '...');

    return response.data;
  } catch (error) {
    console.log(
      `❌ Error: ${error.response?.status || 'Network'} ${error.response?.statusText || error.message}`
    );
    if (error.response?.data) {
      console.log(`📊 Error Response:`, JSON.stringify(error.response.data, null, 2));
    }
    return null;
  }
}

async function runTests() {
  console.log('🚀 Starting API Endpoint Tests');
  console.log(`📍 Base URL: ${BASE_URL}`);
  console.log(`🏢 Tenant ID: ${TENANT_ID}`);
  console.log('='.repeat(60));

  // Test 1: Store Information
  await testEndpoint('GET', '/store/test-info', null, 'Get tenant-specific store information');

  // Test 2: Admin Tenant Information
  await testEndpoint('GET', '/admin/tenant', null, 'Get admin tenant configuration');

  // Test 3: Analytics (might require auth)
  await testEndpoint(
    'GET',
    '/admin/analytics/sales?from=2024-01-01&to=2024-01-31',
    null,
    'Get sales analytics'
  );

  // Test 4: Orders (simple endpoint)
  await testEndpoint(
    'GET',
    '/store/orders/simple?email=<EMAIL>',
    null,
    'Get orders by email'
  );

  // Test 5: Create Order (example)
  const orderData = {
    cart_id: 'cart_test_123',
    email: '<EMAIL>',
    shipping_address: {
      first_name: 'John',
      last_name: 'Doe',
      address_1: '123 Tech Street',
      city: 'Mumbai',
      postal_code: '400001',
      country_code: 'IN',
      phone: '+91-9876543210',
    },
    payment_method: 'cod',
  };

  await testEndpoint('POST', '/store/orders/create', orderData, 'Create order from cart');

  // Test 6: Authentication (admin login)
  const loginData = {
    email: '<EMAIL>',
    password: 'supersecret',
  };

  await testEndpoint('POST', '/auth/user/emailpass', loginData, 'Admin user login');

  console.log('\n' + '='.repeat(60));
  console.log('🏁 API Endpoint Tests Completed');
}

// Run tests
runTests().catch(error => {
  console.error('💥 Test suite failed:', error);
  process.exit(1);
});
