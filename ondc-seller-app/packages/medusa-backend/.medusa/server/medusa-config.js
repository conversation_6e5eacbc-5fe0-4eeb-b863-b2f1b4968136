"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const utils_1 = require("@medusajs/framework/utils");
(0, utils_1.loadEnv)(process.env.NODE_ENV || 'development', process.cwd());
exports.default = (0, utils_1.defineConfig)({
    projectConfig: {
        databaseUrl: process.env.DATABASE_URL,
        http: {
            storeCors: process.env.STORE_CORS,
            adminCors: process.env.ADMIN_CORS,
            authCors: process.env.AUTH_CORS,
            jwtSecret: process.env.JWT_SECRET || 'supersecret',
            cookieSecret: process.env.COOKIE_SECRET || 'supersecret',
        },
    },
    // ✅ REGISTER CORE MODULES HERE
    modules: {
        // Authentication module - required for user signup/login
        auth: {
            resolve: '@medusajs/medusa/auth',
            options: {
                providers: [
                    {
                        resolve: '@medusajs/medusa/auth-emailpass',
                        id: 'emailpass',
                        options: {},
                    },
                ],
            },
        },
        // User module - required for user management
        user: {
            resolve: '@medusajs/medusa/user',
            options: {
                jwt_secret: process.env.JWT_SECRET || 'supersecret',
            },
        },
        cart: {
            resolve: '@medusajs/cart',
            options: {},
        },
        order: {
            resolve: '@medusajs/order',
            options: {},
        },
        customer: {
            resolve: '@medusajs/customer',
            options: {},
        },
        payment: {
            resolve: '@medusajs/payment',
            options: {},
        },
        inventory: {
            resolve: '@medusajs/inventory',
            options: {},
        },
        promotion: { resolve: '@medusajs/medusa/promotion' },
        analytics: {
            resolve: '@medusajs/medusa/analytics',
            options: {
                providers: [
                    /* Logs events to the Medusa console – great for dev */
                    { resolve: '@medusajs/medusa/analytics-local', id: 'local' },
                    /* Switch to PostHog later:
                    {
                      resolve: "@medusajs/analytics-posthog",
                      id: "posthog",
                      options: {
                        posthogEventsKey: process.env.POSTHOG_EVENTS_API_KEY,
                        posthogHost:      process.env.POSTHOG_HOST
                      }
                    }
                    */
                ],
            },
        },
    },
    // ✅ REGISTER PAYMENT PROVIDERS
    plugins: [
        {
            resolve: 'medusa-payment-manual',
            options: {
                // Cash on Delivery configuration
                name: 'Cash on Delivery',
                description: 'Pay with cash when your order is delivered',
                id: 'manual',
            },
        },
        {
            resolve: '@rsc-labs/medusa-store-analytics-v2',
            options: {},
        },
    ],
});
//# sourceMappingURL=data:application/json;base64,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