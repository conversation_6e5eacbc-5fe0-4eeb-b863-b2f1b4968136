"use strict";
/**
 * Store API Endpoint Configurations
 *
 * Defines query parameter configurations for each store API endpoint
 * including allowed fields for filtering, sorting, selection, and expansion.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TAGS_CONFIG = exports.COLLECTIONS_CONFIG = exports.PRODUCTS_CONFIG = exports.PRODUCT_CATEGORIES_CONFIG = void 0;
exports.getEndpointConfig = getEndpointConfig;
exports.validateQueryParams = validateQueryParams;
/**
 * Product Categories API Configuration
 */
exports.PRODUCT_CATEGORIES_CONFIG = {
    sortableFields: [
        'id',
        'name',
        'handle',
        'created_at',
        'updated_at',
        'rank',
        'is_active',
    ],
    filterableFields: [
        'id',
        'name',
        'handle',
        'parent_category_id',
        'is_active',
        'is_internal',
        'created_at',
        'updated_at',
        'rank',
    ],
    selectableFields: [
        'id',
        'name',
        'description',
        'handle',
        'is_active',
        'is_internal',
        'parent_category_id',
        'rank',
        'created_at',
        'updated_at',
        'metadata',
    ],
    expandableRelations: [
        'parent_category',
        'category_children',
        'products',
    ],
    searchableFields: [
        'name',
        'description',
        'handle',
    ],
    defaultSort: { field: 'rank', direction: 'ASC' },
    maxLimit: 100,
    defaultLimit: 50,
};
/**
 * Products API Configuration
 */
exports.PRODUCTS_CONFIG = {
    sortableFields: [
        'id',
        'title',
        'handle',
        'status',
        'created_at',
        'updated_at',
        'weight',
        'length',
        'height',
        'width',
    ],
    filterableFields: [
        'id',
        'title',
        'handle',
        'status',
        'category_id',
        'subcategory_id',
        'parent_category_id',
        'collection_id',
        'type_id',
        'tags',
        'is_giftcard',
        'weight',
        'length',
        'height',
        'width',
        'created_at',
        'updated_at',
    ],
    selectableFields: [
        'id',
        'title',
        'subtitle',
        'description',
        'handle',
        'status',
        'thumbnail',
        'weight',
        'length',
        'height',
        'width',
        'hs_code',
        'origin_country',
        'mid_code',
        'material',
        'created_at',
        'updated_at',
        'metadata',
    ],
    expandableRelations: [
        'variants',
        'options',
        'images',
        'tags',
        'type',
        'collection',
        'categories',
        'profiles',
    ],
    searchableFields: [
        'title',
        'subtitle',
        'description',
        'handle',
    ],
    defaultSort: { field: 'created_at', direction: 'DESC' },
    maxLimit: 100,
    defaultLimit: 20,
};
/**
 * Collections API Configuration
 */
exports.COLLECTIONS_CONFIG = {
    sortableFields: [
        'id',
        'title',
        'handle',
        'created_at',
        'updated_at',
    ],
    filterableFields: [
        'id',
        'title',
        'handle',
        'created_at',
        'updated_at',
    ],
    selectableFields: [
        'id',
        'title',
        'handle',
        'created_at',
        'updated_at',
        'metadata',
    ],
    expandableRelations: [
        'products',
    ],
    searchableFields: [
        'title',
        'handle',
    ],
    defaultSort: { field: 'title', direction: 'ASC' },
    maxLimit: 100,
    defaultLimit: 50,
};
/**
 * Tags API Configuration
 */
exports.TAGS_CONFIG = {
    sortableFields: [
        'id',
        'value',
        'created_at',
        'updated_at',
    ],
    filterableFields: [
        'id',
        'value',
        'created_at',
        'updated_at',
    ],
    selectableFields: [
        'id',
        'value',
        'created_at',
        'updated_at',
        'metadata',
    ],
    expandableRelations: [
        'products',
    ],
    searchableFields: [
        'value',
    ],
    defaultSort: { field: 'value', direction: 'ASC' },
    maxLimit: 100,
    defaultLimit: 50,
};
/**
 * Get configuration for a specific endpoint
 */
function getEndpointConfig(endpoint) {
    switch (endpoint) {
        case 'product-categories':
            return exports.PRODUCT_CATEGORIES_CONFIG;
        case 'products':
            return exports.PRODUCTS_CONFIG;
        case 'collections':
            return exports.COLLECTIONS_CONFIG;
        case 'tags':
            return exports.TAGS_CONFIG;
        default:
            throw new Error(`Unknown endpoint configuration: ${endpoint}`);
    }
}
/**
 * Validate query parameters against endpoint configuration
 */
function validateQueryParams(query, config) {
    const errors = [];
    // Validate sort fields
    if (query.sort) {
        const sortFields = Array.isArray(query.sort) ? query.sort : [query.sort];
        for (const sortField of sortFields) {
            const [field] = sortField.split(':');
            if (!config.sortableFields.includes(field)) {
                errors.push(`Invalid sort field: ${field}. Allowed: ${config.sortableFields.join(', ')}`);
            }
        }
    }
    // Validate expand relations
    if (query.expand) {
        const expandFields = Array.isArray(query.expand) ? query.expand : query.expand.split(',');
        for (const relation of expandFields) {
            if (!config.expandableRelations.includes(relation.trim())) {
                errors.push(`Invalid expand relation: ${relation}. Allowed: ${config.expandableRelations.join(', ')}`);
            }
        }
    }
    // Validate field selection
    if (query.fields) {
        const fields = Array.isArray(query.fields) ? query.fields : query.fields.split(',');
        for (const field of fields) {
            if (!config.selectableFields.includes(field.trim())) {
                errors.push(`Invalid field selection: ${field}. Allowed: ${config.selectableFields.join(', ')}`);
            }
        }
    }
    // Validate pagination limits
    if (query.limit) {
        const limit = parseInt(query.limit);
        if (isNaN(limit) || limit < 1 || limit > config.maxLimit) {
            errors.push(`Invalid limit: ${query.limit}. Must be between 1 and ${config.maxLimit}`);
        }
    }
    return {
        valid: errors.length === 0,
        errors,
    };
}
//# sourceMappingURL=data:application/json;base64,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