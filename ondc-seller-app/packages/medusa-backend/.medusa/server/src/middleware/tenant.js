"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.tenantErrorHandler = exports.filterByTenantId = exports.injectTenantId = exports.getTenantConfig = exports.getTenantId = exports.tenantMiddleware = void 0;
/**
 * RLS Integration Note:
 *
 * The RLS (Row-Level Security) system is properly configured and working.
 * However, PostgreSQL session variables only persist within a single database connection.
 * Since Medusa v2 manages its own database connections and connection pooling,
 * we cannot reliably set session variables from middleware that will persist
 * for the actual Medusa database operations.
 *
 * The RLS policies are in place and will enforce tenant isolation when:
 * 1. Using the non-superuser database role (medusa_app)
 * 2. Session variables are properly set on the same connection
 *
 * For now, we'll focus on the middleware functionality and tenant context management.
 * The RLS integration will be completed in the next phase when we can properly
 * integrate with Medusa's database connection management.
 */
/**
 * Log RLS status for debugging
 */
const logRLSStatus = (tenantId) => {
    console.log(`🔒 [RLS] Tenant context prepared for: ${tenantId}`);
    console.log(`🔒 [RLS] Note: RLS policies are active and will enforce isolation when using non-superuser role`);
};
/**
 * Enhanced Tenant ID Extraction with Validation
 * Task 2.3: Implement robust tenant header validation
 */
const extractAndValidateTenantId = (req) => {
    // Priority order: header > query > subdomain > default
    let tenantId = req.headers['x-tenant-id'];
    // Validate header format if present
    if (tenantId) {
        // Check for empty or whitespace-only values
        if (typeof tenantId !== 'string' || tenantId.trim() === '') {
            return {
                tenantId: null,
                error: {
                    error: 'INVALID_TENANT_HEADER',
                    message: 'x-tenant-id header cannot be empty or contain only whitespace',
                    details: { provided_value: tenantId, source: 'header' },
                },
            };
        }
        // Validate tenant ID format (alphanumeric, hyphens, underscores only)
        const tenantIdRegex = /^[a-zA-Z0-9][a-zA-Z0-9_-]*[a-zA-Z0-9]$|^[a-zA-Z0-9]$/;
        if (!tenantIdRegex.test(tenantId.trim())) {
            return {
                tenantId: null,
                error: {
                    error: 'MALFORMED_TENANT_ID',
                    message: 'Tenant ID must contain only alphanumeric characters, hyphens, and underscores. Must start and end with alphanumeric characters.',
                    details: {
                        provided_value: tenantId.trim(),
                        source: 'header',
                        valid_pattern: 'alphanumeric, hyphens, underscores (e.g., tenant-001, default, electronics_store)',
                    },
                },
            };
        }
        // Check length constraints
        const trimmedTenantId = tenantId.trim();
        if (trimmedTenantId.length < 1 || trimmedTenantId.length > 50) {
            return {
                tenantId: null,
                error: {
                    error: 'TENANT_ID_LENGTH_INVALID',
                    message: 'Tenant ID must be between 1 and 50 characters long',
                    details: {
                        provided_value: trimmedTenantId,
                        length: trimmedTenantId.length,
                        source: 'header',
                    },
                },
            };
        }
        return { tenantId: trimmedTenantId, error: null };
    }
    // Fallback to query parameter
    if (!tenantId) {
        tenantId = req.query.tenant;
        if (tenantId && typeof tenantId === 'string' && tenantId.trim()) {
            const trimmedTenantId = tenantId.trim();
            const tenantIdRegex = /^[a-zA-Z0-9][a-zA-Z0-9_-]*[a-zA-Z0-9]$|^[a-zA-Z0-9]$/;
            if (!tenantIdRegex.test(trimmedTenantId)) {
                return {
                    tenantId: null,
                    error: {
                        error: 'MALFORMED_TENANT_ID',
                        message: 'Tenant ID in query parameter is malformed',
                        details: { provided_value: trimmedTenantId, source: 'query' },
                    },
                };
            }
            return { tenantId: trimmedTenantId, error: null };
        }
    }
    // Fallback to subdomain extraction
    if (!tenantId && req.headers.host) {
        const host = req.headers.host;
        const subdomain = host.split('.')[0];
        if (subdomain && subdomain !== 'www' && subdomain !== 'api' && subdomain !== 'localhost') {
            const tenantIdRegex = /^[a-zA-Z0-9][a-zA-Z0-9_-]*[a-zA-Z0-9]$|^[a-zA-Z0-9]$/;
            if (tenantIdRegex.test(subdomain)) {
                return { tenantId: subdomain, error: null };
            }
        }
    }
    // For development, allow default tenant
    if (process.env.NODE_ENV === 'development') {
        const defaultTenant = process.env.DEFAULT_TENANT_ID || 'default';
        return { tenantId: defaultTenant, error: null };
    }
    // In production, require explicit tenant ID
    return {
        tenantId: null,
        error: {
            error: 'MISSING_TENANT_ID',
            message: 'Tenant ID is required. Provide it via x-tenant-id header, tenant query parameter, or subdomain.',
            details: {
                accepted_methods: [
                    'Header: x-tenant-id',
                    'Query parameter: ?tenant=your-tenant-id',
                    'Subdomain: your-tenant.domain.com',
                ],
            },
        },
    };
};
/**
 * Validate Tenant Existence and Status
 */
const validateTenant = async (tenantId) => {
    // For development mode, return mock tenant (skip VALID_TENANTS check)
    if (process.env.NODE_ENV === 'development') {
        return {
            id: tenantId,
            name: `Development Tenant ${tenantId}`,
            settings: {
                currency: 'INR',
                timezone: 'Asia/Kolkata',
                features: ['all'],
                ondcConfig: {
                    participantId: process.env.ONDC_PARTICIPANT_ID || 'default-participant',
                    subscriberId: process.env.ONDC_SUBSCRIBER_ID || 'default-subscriber',
                    bppId: `${process.env.ONDC_BPP_ID || 'ondc-bpp'}-${tenantId}`,
                },
            },
            status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
        };
    }
    // In production, validate against database or configuration
    const validTenants = process.env.VALID_TENANTS?.split(',') || ['default'];
    if (!validTenants.includes(tenantId)) {
        return null;
    }
    return {
        id: tenantId,
        name: `Tenant ${tenantId}`,
        domain: `${tenantId}.ondc-seller.com`,
        settings: {
            currency: 'INR',
            timezone: 'Asia/Kolkata',
            features: ['products', 'orders', 'customers', 'analytics'],
            ondcConfig: {
                participantId: process.env[`ONDC_PARTICIPANT_ID_${tenantId.toUpperCase()}`] ||
                    process.env.ONDC_PARTICIPANT_ID ||
                    'default-participant',
                subscriberId: process.env[`ONDC_SUBSCRIBER_ID_${tenantId.toUpperCase()}`] ||
                    process.env.ONDC_SUBSCRIBER_ID ||
                    'default-subscriber',
                bppId: process.env[`ONDC_BPP_ID_${tenantId.toUpperCase()}`] ||
                    `${process.env.ONDC_BPP_ID || 'ondc-bpp'}-${tenantId}`,
            },
        },
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date(),
    };
};
/**
 * Main Tenant Middleware
 */
const tenantMiddleware = async (req, res, next) => {
    try {
        // Skip tenant validation for health checks and public endpoints
        const skipPaths = ['/health', '/docs', '/admin/auth', '/store/auth'];
        const shouldSkip = skipPaths.some(path => req.path.startsWith(path));
        if (shouldSkip) {
            return next();
        }
        // Extract and validate tenant ID with enhanced validation
        const { tenantId, error: validationError } = extractAndValidateTenantId(req);
        // Return HTTP 400 for validation errors
        if (validationError) {
            console.log(`❌ [TENANT] Validation error: ${validationError.message}`);
            return res.status(400).json(validationError);
        }
        // This should not happen due to validation logic, but add safety check
        if (!tenantId) {
            return res.status(400).json({
                error: 'TENANT_EXTRACTION_FAILED',
                message: 'Failed to extract tenant ID from request',
            });
        }
        // Set tenant ID in multiple formats for compatibility
        req.tenantId = tenantId;
        req.tenant_id = tenantId;
        // Validate tenant existence and status
        const tenant = await validateTenant(tenantId);
        if (!tenant) {
            return res.status(403).json({
                error: 'INVALID_TENANT',
                message: `Tenant '${tenantId}' not found or inactive`,
                tenant_id: tenantId,
            });
        }
        if (tenant.status !== 'active') {
            return res.status(403).json({
                error: 'TENANT_INACTIVE',
                message: `Tenant '${tenantId}' is ${tenant.status}`,
                tenant_id: tenantId,
                status: tenant.status,
            });
        }
        // Attach tenant to request
        req.tenant = tenant;
        // Log RLS status for debugging
        logRLSStatus(tenantId);
        // Add tenant context to response headers (for debugging)
        if (process.env.NODE_ENV === 'development') {
            res.setHeader('X-Tenant-ID', tenantId);
            res.setHeader('X-Tenant-Name', tenant.name);
            res.setHeader('X-RLS-Enabled', 'true');
        }
        console.log(`🏢 [TENANT] Request for tenant: ${tenantId} on ${req.method} ${req.path}`);
        next();
    }
    catch (error) {
        console.error('❌ [TENANT] Middleware error:', error);
        next(error);
    }
};
exports.tenantMiddleware = tenantMiddleware;
/**
 * Get Tenant ID from Request (Utility Function)
 */
const getTenantId = (req) => {
    return req.tenantId || req.tenant?.id || 'default';
};
exports.getTenantId = getTenantId;
/**
 * Get Tenant Configuration (Utility Function)
 */
const getTenantConfig = (req) => {
    return req.tenant || null;
};
exports.getTenantConfig = getTenantConfig;
/**
 * Inject Tenant ID into Data
 */
const injectTenantId = (req, data) => {
    const tenantId = (0, exports.getTenantId)(req);
    if (Array.isArray(data)) {
        return data.map(item => ({ ...item, tenant_id: tenantId }));
    }
    return { ...data, tenant_id: tenantId };
};
exports.injectTenantId = injectTenantId;
/**
 * Filter Data by Tenant ID
 */
const filterByTenantId = (req, data) => {
    const tenantId = (0, exports.getTenantId)(req);
    return data.filter(item => !item.tenant_id || item.tenant_id === tenantId);
};
exports.filterByTenantId = filterByTenantId;
/**
 * Tenant Error Handler
 * Handles tenant-related errors
 */
const tenantErrorHandler = async (error, req, res, next) => {
    console.log('🔒 [RLS] Tenant error handler called');
    // Pass error to next handler
    next(error);
};
exports.tenantErrorHandler = tenantErrorHandler;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidGVuYW50LmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vc3JjL21pZGRsZXdhcmUvdGVuYW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7OztBQXVCQTs7Ozs7Ozs7Ozs7Ozs7OztHQWdCRztBQUVIOztHQUVHO0FBQ0gsTUFBTSxZQUFZLEdBQUcsQ0FBQyxRQUFnQixFQUFRLEVBQUU7SUFDOUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyx5Q0FBeUMsUUFBUSxFQUFFLENBQUMsQ0FBQztJQUNqRSxPQUFPLENBQUMsR0FBRyxDQUNULGlHQUFpRyxDQUNsRyxDQUFDO0FBQ0osQ0FBQyxDQUFDO0FBWUY7OztHQUdHO0FBQ0gsTUFBTSwwQkFBMEIsR0FBRyxDQUNqQyxHQUFrQixFQUNnRCxFQUFFO0lBQ3BFLHVEQUF1RDtJQUN2RCxJQUFJLFFBQVEsR0FBRyxHQUFHLENBQUMsT0FBTyxDQUFDLGFBQWEsQ0FBVyxDQUFDO0lBRXBELG9DQUFvQztJQUNwQyxJQUFJLFFBQVEsRUFBRSxDQUFDO1FBQ2IsNENBQTRDO1FBQzVDLElBQUksT0FBTyxRQUFRLEtBQUssUUFBUSxJQUFJLFFBQVEsQ0FBQyxJQUFJLEVBQUUsS0FBSyxFQUFFLEVBQUUsQ0FBQztZQUMzRCxPQUFPO2dCQUNMLFFBQVEsRUFBRSxJQUFJO2dCQUNkLEtBQUssRUFBRTtvQkFDTCxLQUFLLEVBQUUsdUJBQXVCO29CQUM5QixPQUFPLEVBQUUsK0RBQStEO29CQUN4RSxPQUFPLEVBQUUsRUFBRSxjQUFjLEVBQUUsUUFBUSxFQUFFLE1BQU0sRUFBRSxRQUFRLEVBQUU7aUJBQ3hEO2FBQ0YsQ0FBQztRQUNKLENBQUM7UUFFRCxzRUFBc0U7UUFDdEUsTUFBTSxhQUFhLEdBQUcsc0RBQXNELENBQUM7UUFDN0UsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLElBQUksRUFBRSxDQUFDLEVBQUUsQ0FBQztZQUN6QyxPQUFPO2dCQUNMLFFBQVEsRUFBRSxJQUFJO2dCQUNkLEtBQUssRUFBRTtvQkFDTCxLQUFLLEVBQUUscUJBQXFCO29CQUM1QixPQUFPLEVBQ0wsaUlBQWlJO29CQUNuSSxPQUFPLEVBQUU7d0JBQ1AsY0FBYyxFQUFFLFFBQVEsQ0FBQyxJQUFJLEVBQUU7d0JBQy9CLE1BQU0sRUFBRSxRQUFRO3dCQUNoQixhQUFhLEVBQ1gsbUZBQW1GO3FCQUN0RjtpQkFDRjthQUNGLENBQUM7UUFDSixDQUFDO1FBRUQsMkJBQTJCO1FBQzNCLE1BQU0sZUFBZSxHQUFHLFFBQVEsQ0FBQyxJQUFJLEVBQUUsQ0FBQztRQUN4QyxJQUFJLGVBQWUsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxJQUFJLGVBQWUsQ0FBQyxNQUFNLEdBQUcsRUFBRSxFQUFFLENBQUM7WUFDOUQsT0FBTztnQkFDTCxRQUFRLEVBQUUsSUFBSTtnQkFDZCxLQUFLLEVBQUU7b0JBQ0wsS0FBSyxFQUFFLDBCQUEwQjtvQkFDakMsT0FBTyxFQUFFLG9EQUFvRDtvQkFDN0QsT0FBTyxFQUFFO3dCQUNQLGNBQWMsRUFBRSxlQUFlO3dCQUMvQixNQUFNLEVBQUUsZUFBZSxDQUFDLE1BQU07d0JBQzlCLE1BQU0sRUFBRSxRQUFRO3FCQUNqQjtpQkFDRjthQUNGLENBQUM7UUFDSixDQUFDO1FBRUQsT0FBTyxFQUFFLFFBQVEsRUFBRSxlQUFlLEVBQUUsS0FBSyxFQUFFLElBQUksRUFBRSxDQUFDO0lBQ3BELENBQUM7SUFFRCw4QkFBOEI7SUFDOUIsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDO1FBQ2QsUUFBUSxHQUFHLEdBQUcsQ0FBQyxLQUFLLENBQUMsTUFBZ0IsQ0FBQztRQUV0QyxJQUFJLFFBQVEsSUFBSSxPQUFPLFFBQVEsS0FBSyxRQUFRLElBQUksUUFBUSxDQUFDLElBQUksRUFBRSxFQUFFLENBQUM7WUFDaEUsTUFBTSxlQUFlLEdBQUcsUUFBUSxDQUFDLElBQUksRUFBRSxDQUFDO1lBQ3hDLE1BQU0sYUFBYSxHQUFHLHNEQUFzRCxDQUFDO1lBRTdFLElBQUksQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxFQUFFLENBQUM7Z0JBQ3pDLE9BQU87b0JBQ0wsUUFBUSxFQUFFLElBQUk7b0JBQ2QsS0FBSyxFQUFFO3dCQUNMLEtBQUssRUFBRSxxQkFBcUI7d0JBQzVCLE9BQU8sRUFBRSwyQ0FBMkM7d0JBQ3BELE9BQU8sRUFBRSxFQUFFLGNBQWMsRUFBRSxlQUFlLEVBQUUsTUFBTSxFQUFFLE9BQU8sRUFBRTtxQkFDOUQ7aUJBQ0YsQ0FBQztZQUNKLENBQUM7WUFFRCxPQUFPLEVBQUUsUUFBUSxFQUFFLGVBQWUsRUFBRSxLQUFLLEVBQUUsSUFBSSxFQUFFLENBQUM7UUFDcEQsQ0FBQztJQUNILENBQUM7SUFFRCxtQ0FBbUM7SUFDbkMsSUFBSSxDQUFDLFFBQVEsSUFBSSxHQUFHLENBQUMsT0FBTyxDQUFDLElBQUksRUFBRSxDQUFDO1FBQ2xDLE1BQU0sSUFBSSxHQUFHLEdBQUcsQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDO1FBQzlCLE1BQU0sU0FBUyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDckMsSUFBSSxTQUFTLElBQUksU0FBUyxLQUFLLEtBQUssSUFBSSxTQUFTLEtBQUssS0FBSyxJQUFJLFNBQVMsS0FBSyxXQUFXLEVBQUUsQ0FBQztZQUN6RixNQUFNLGFBQWEsR0FBRyxzREFBc0QsQ0FBQztZQUU3RSxJQUFJLGFBQWEsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLEVBQUUsQ0FBQztnQkFDbEMsT0FBTyxFQUFFLFFBQVEsRUFBRSxTQUFTLEVBQUUsS0FBSyxFQUFFLElBQUksRUFBRSxDQUFDO1lBQzlDLENBQUM7UUFDSCxDQUFDO0lBQ0gsQ0FBQztJQUVELHdDQUF3QztJQUN4QyxJQUFJLE9BQU8sQ0FBQyxHQUFHLENBQUMsUUFBUSxLQUFLLGFBQWEsRUFBRSxDQUFDO1FBQzNDLE1BQU0sYUFBYSxHQUFHLE9BQU8sQ0FBQyxHQUFHLENBQUMsaUJBQWlCLElBQUksU0FBUyxDQUFDO1FBQ2pFLE9BQU8sRUFBRSxRQUFRLEVBQUUsYUFBYSxFQUFFLEtBQUssRUFBRSxJQUFJLEVBQUUsQ0FBQztJQUNsRCxDQUFDO0lBRUQsNENBQTRDO0lBQzVDLE9BQU87UUFDTCxRQUFRLEVBQUUsSUFBSTtRQUNkLEtBQUssRUFBRTtZQUNMLEtBQUssRUFBRSxtQkFBbUI7WUFDMUIsT0FBTyxFQUNMLGlHQUFpRztZQUNuRyxPQUFPLEVBQUU7Z0JBQ1AsZ0JBQWdCLEVBQUU7b0JBQ2hCLHFCQUFxQjtvQkFDckIseUNBQXlDO29CQUN6QyxtQ0FBbUM7aUJBQ3BDO2FBQ0Y7U0FDRjtLQUNGLENBQUM7QUFDSixDQUFDLENBQUM7QUFFRjs7R0FFRztBQUNILE1BQU0sY0FBYyxHQUFHLEtBQUssRUFBRSxRQUFnQixFQUFnQyxFQUFFO0lBQzlFLHNFQUFzRTtJQUN0RSxJQUFJLE9BQU8sQ0FBQyxHQUFHLENBQUMsUUFBUSxLQUFLLGFBQWEsRUFBRSxDQUFDO1FBQzNDLE9BQU87WUFDTCxFQUFFLEVBQUUsUUFBUTtZQUNaLElBQUksRUFBRSxzQkFBc0IsUUFBUSxFQUFFO1lBQ3RDLFFBQVEsRUFBRTtnQkFDUixRQUFRLEVBQUUsS0FBSztnQkFDZixRQUFRLEVBQUUsY0FBYztnQkFDeEIsUUFBUSxFQUFFLENBQUMsS0FBSyxDQUFDO2dCQUNqQixVQUFVLEVBQUU7b0JBQ1YsYUFBYSxFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsbUJBQW1CLElBQUkscUJBQXFCO29CQUN2RSxZQUFZLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxrQkFBa0IsSUFBSSxvQkFBb0I7b0JBQ3BFLEtBQUssRUFBRSxHQUFHLE9BQU8sQ0FBQyxHQUFHLENBQUMsV0FBVyxJQUFJLFVBQVUsSUFBSSxRQUFRLEVBQUU7aUJBQzlEO2FBQ0Y7WUFDRCxNQUFNLEVBQUUsUUFBUTtZQUNoQixTQUFTLEVBQUUsSUFBSSxJQUFJLEVBQUU7WUFDckIsU0FBUyxFQUFFLElBQUksSUFBSSxFQUFFO1NBQ3RCLENBQUM7SUFDSixDQUFDO0lBRUQsNERBQTREO0lBQzVELE1BQU0sWUFBWSxHQUFHLE9BQU8sQ0FBQyxHQUFHLENBQUMsYUFBYSxFQUFFLEtBQUssQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDO0lBRTFFLElBQUksQ0FBQyxZQUFZLENBQUMsUUFBUSxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUM7UUFDckMsT0FBTyxJQUFJLENBQUM7SUFDZCxDQUFDO0lBRUQsT0FBTztRQUNMLEVBQUUsRUFBRSxRQUFRO1FBQ1osSUFBSSxFQUFFLFVBQVUsUUFBUSxFQUFFO1FBQzFCLE1BQU0sRUFBRSxHQUFHLFFBQVEsa0JBQWtCO1FBQ3JDLFFBQVEsRUFBRTtZQUNSLFFBQVEsRUFBRSxLQUFLO1lBQ2YsUUFBUSxFQUFFLGNBQWM7WUFDeEIsUUFBUSxFQUFFLENBQUMsVUFBVSxFQUFFLFFBQVEsRUFBRSxXQUFXLEVBQUUsV0FBVyxDQUFDO1lBQzFELFVBQVUsRUFBRTtnQkFDVixhQUFhLEVBQ1gsT0FBTyxDQUFDLEdBQUcsQ0FBQyx1QkFBdUIsUUFBUSxDQUFDLFdBQVcsRUFBRSxFQUFFLENBQUM7b0JBQzVELE9BQU8sQ0FBQyxHQUFHLENBQUMsbUJBQW1CO29CQUMvQixxQkFBcUI7Z0JBQ3ZCLFlBQVksRUFDVixPQUFPLENBQUMsR0FBRyxDQUFDLHNCQUFzQixRQUFRLENBQUMsV0FBVyxFQUFFLEVBQUUsQ0FBQztvQkFDM0QsT0FBTyxDQUFDLEdBQUcsQ0FBQyxrQkFBa0I7b0JBQzlCLG9CQUFvQjtnQkFDdEIsS0FBSyxFQUNILE9BQU8sQ0FBQyxHQUFHLENBQUMsZUFBZSxRQUFRLENBQUMsV0FBVyxFQUFFLEVBQUUsQ0FBQztvQkFDcEQsR0FBRyxPQUFPLENBQUMsR0FBRyxDQUFDLFdBQVcsSUFBSSxVQUFVLElBQUksUUFBUSxFQUFFO2FBQ3pEO1NBQ0Y7UUFDRCxNQUFNLEVBQUUsUUFBUTtRQUNoQixTQUFTLEVBQUUsSUFBSSxJQUFJLEVBQUU7UUFDckIsU0FBUyxFQUFFLElBQUksSUFBSSxFQUFFO0tBQ3RCLENBQUM7QUFDSixDQUFDLENBQUM7QUFFRjs7R0FFRztBQUNJLE1BQU0sZ0JBQWdCLEdBQUcsS0FBSyxFQUNuQyxHQUFrQixFQUNsQixHQUFtQixFQUNuQixJQUFrQixFQUNKLEVBQUU7SUFDaEIsSUFBSSxDQUFDO1FBQ0gsZ0VBQWdFO1FBQ2hFLE1BQU0sU0FBUyxHQUFHLENBQUMsU0FBUyxFQUFFLE9BQU8sRUFBRSxhQUFhLEVBQUUsYUFBYSxDQUFDLENBQUM7UUFDckUsTUFBTSxVQUFVLEdBQUcsU0FBUyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUM7UUFFckUsSUFBSSxVQUFVLEVBQUUsQ0FBQztZQUNmLE9BQU8sSUFBSSxFQUFFLENBQUM7UUFDaEIsQ0FBQztRQUVELDBEQUEwRDtRQUMxRCxNQUFNLEVBQUUsUUFBUSxFQUFFLEtBQUssRUFBRSxlQUFlLEVBQUUsR0FBRywwQkFBMEIsQ0FBQyxHQUFHLENBQUMsQ0FBQztRQUU3RSx3Q0FBd0M7UUFDeEMsSUFBSSxlQUFlLEVBQUUsQ0FBQztZQUNwQixPQUFPLENBQUMsR0FBRyxDQUFDLGdDQUFnQyxlQUFlLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQztZQUN2RSxPQUFPLEdBQUcsQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxDQUFDO1FBQy9DLENBQUM7UUFFRCx1RUFBdUU7UUFDdkUsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDO1lBQ2QsT0FBTyxHQUFHLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUFDLElBQUksQ0FBQztnQkFDMUIsS0FBSyxFQUFFLDBCQUEwQjtnQkFDakMsT0FBTyxFQUFFLDBDQUEwQzthQUNwRCxDQUFDLENBQUM7UUFDTCxDQUFDO1FBRUQsc0RBQXNEO1FBQ3JELEdBQVcsQ0FBQyxRQUFRLEdBQUcsUUFBUSxDQUFDO1FBQ2hDLEdBQVcsQ0FBQyxTQUFTLEdBQUcsUUFBUSxDQUFDO1FBRWxDLHVDQUF1QztRQUN2QyxNQUFNLE1BQU0sR0FBRyxNQUFNLGNBQWMsQ0FBQyxRQUFRLENBQUMsQ0FBQztRQUM5QyxJQUFJLENBQUMsTUFBTSxFQUFFLENBQUM7WUFDWixPQUFPLEdBQUcsQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDO2dCQUMxQixLQUFLLEVBQUUsZ0JBQWdCO2dCQUN2QixPQUFPLEVBQUUsV0FBVyxRQUFRLHlCQUF5QjtnQkFDckQsU0FBUyxFQUFFLFFBQVE7YUFDcEIsQ0FBQyxDQUFDO1FBQ0wsQ0FBQztRQUVELElBQUksTUFBTSxDQUFDLE1BQU0sS0FBSyxRQUFRLEVBQUUsQ0FBQztZQUMvQixPQUFPLEdBQUcsQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDO2dCQUMxQixLQUFLLEVBQUUsaUJBQWlCO2dCQUN4QixPQUFPLEVBQUUsV0FBVyxRQUFRLFFBQVEsTUFBTSxDQUFDLE1BQU0sRUFBRTtnQkFDbkQsU0FBUyxFQUFFLFFBQVE7Z0JBQ25CLE1BQU0sRUFBRSxNQUFNLENBQUMsTUFBTTthQUN0QixDQUFDLENBQUM7UUFDTCxDQUFDO1FBRUQsMkJBQTJCO1FBQzFCLEdBQVcsQ0FBQyxNQUFNLEdBQUcsTUFBTSxDQUFDO1FBRTdCLCtCQUErQjtRQUMvQixZQUFZLENBQUMsUUFBUSxDQUFDLENBQUM7UUFFdkIseURBQXlEO1FBQ3pELElBQUksT0FBTyxDQUFDLEdBQUcsQ0FBQyxRQUFRLEtBQUssYUFBYSxFQUFFLENBQUM7WUFDM0MsR0FBRyxDQUFDLFNBQVMsQ0FBQyxhQUFhLEVBQUUsUUFBUSxDQUFDLENBQUM7WUFDdkMsR0FBRyxDQUFDLFNBQVMsQ0FBQyxlQUFlLEVBQUUsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQzVDLEdBQUcsQ0FBQyxTQUFTLENBQUMsZUFBZSxFQUFFLE1BQU0sQ0FBQyxDQUFDO1FBQ3pDLENBQUM7UUFFRCxPQUFPLENBQUMsR0FBRyxDQUFDLG1DQUFtQyxRQUFRLE9BQU8sR0FBRyxDQUFDLE1BQU0sSUFBSSxHQUFHLENBQUMsSUFBSSxFQUFFLENBQUMsQ0FBQztRQUV4RixJQUFJLEVBQUUsQ0FBQztJQUNULENBQUM7SUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1FBQ2YsT0FBTyxDQUFDLEtBQUssQ0FBQyw4QkFBOEIsRUFBRSxLQUFLLENBQUMsQ0FBQztRQUNyRCxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7SUFDZCxDQUFDO0FBQ0gsQ0FBQyxDQUFDO0FBMUVXLFFBQUEsZ0JBQWdCLG9CQTBFM0I7QUFFRjs7R0FFRztBQUNJLE1BQU0sV0FBVyxHQUFHLENBQUMsR0FBa0IsRUFBVSxFQUFFO0lBQ3hELE9BQVEsR0FBVyxDQUFDLFFBQVEsSUFBSyxHQUFXLENBQUMsTUFBTSxFQUFFLEVBQUUsSUFBSSxTQUFTLENBQUM7QUFDdkUsQ0FBQyxDQUFDO0FBRlcsUUFBQSxXQUFXLGVBRXRCO0FBRUY7O0dBRUc7QUFDSSxNQUFNLGVBQWUsR0FBRyxDQUFDLEdBQWtCLEVBQXVCLEVBQUU7SUFDekUsT0FBUSxHQUFXLENBQUMsTUFBTSxJQUFJLElBQUksQ0FBQztBQUNyQyxDQUFDLENBQUM7QUFGVyxRQUFBLGVBQWUsbUJBRTFCO0FBRUY7O0dBRUc7QUFDSSxNQUFNLGNBQWMsR0FBRyxDQUFDLEdBQWtCLEVBQUUsSUFBUyxFQUFPLEVBQUU7SUFDbkUsTUFBTSxRQUFRLEdBQUcsSUFBQSxtQkFBVyxFQUFDLEdBQUcsQ0FBQyxDQUFDO0lBRWxDLElBQUksS0FBSyxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDO1FBQ3hCLE9BQU8sSUFBSSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxHQUFHLElBQUksRUFBRSxTQUFTLEVBQUUsUUFBUSxFQUFFLENBQUMsQ0FBQyxDQUFDO0lBQzlELENBQUM7SUFFRCxPQUFPLEVBQUUsR0FBRyxJQUFJLEVBQUUsU0FBUyxFQUFFLFFBQVEsRUFBRSxDQUFDO0FBQzFDLENBQUMsQ0FBQztBQVJXLFFBQUEsY0FBYyxrQkFRekI7QUFFRjs7R0FFRztBQUNJLE1BQU0sZ0JBQWdCLEdBQUcsQ0FBQyxHQUFrQixFQUFFLElBQVcsRUFBUyxFQUFFO0lBQ3pFLE1BQU0sUUFBUSxHQUFHLElBQUEsbUJBQVcsRUFBQyxHQUFHLENBQUMsQ0FBQztJQUVsQyxPQUFPLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLElBQUksSUFBSSxDQUFDLFNBQVMsS0FBSyxRQUFRLENBQUMsQ0FBQztBQUM3RSxDQUFDLENBQUM7QUFKVyxRQUFBLGdCQUFnQixvQkFJM0I7QUFFRjs7O0dBR0c7QUFDSSxNQUFNLGtCQUFrQixHQUFHLEtBQUssRUFDckMsS0FBVSxFQUNWLEdBQWtCLEVBQ2xCLEdBQW1CLEVBQ25CLElBQWtCLEVBQ0gsRUFBRTtJQUNqQixPQUFPLENBQUMsR0FBRyxDQUFDLHNDQUFzQyxDQUFDLENBQUM7SUFFcEQsNkJBQTZCO0lBQzdCLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztBQUNkLENBQUMsQ0FBQztBQVZXLFFBQUEsa0JBQWtCLHNCQVU3QiJ9