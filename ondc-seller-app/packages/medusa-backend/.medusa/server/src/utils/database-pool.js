"use strict";
/**
 * Centralized Database Connection Pool Manager
 *
 * Provides a single, shared connection pool for all API endpoints to prevent
 * connection exhaustion and ensure proper resource management.
 *
 * ENHANCED VERSION - Fixes connection pool exhaustion issues
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabasePoolManager = exports.dbPool = void 0;
const pg_1 = require("pg");
class DatabasePoolManager {
    constructor() {
        this.pool = null;
        this.isInitialized = false;
        this.connectionCount = 0;
        this.maxConnectionsReached = false;
        // Private constructor to enforce singleton pattern
    }
    /**
     * Get the singleton instance
     */
    static getInstance() {
        if (!DatabasePoolManager.instance) {
            DatabasePoolManager.instance = new DatabasePoolManager();
        }
        return DatabasePoolManager.instance;
    }
    /**
     * Initialize the connection pool with enhanced configuration
     */
    initialize() {
        if (this.isInitialized && this.pool) {
            return;
        }
        const databaseUrl = process.env.DATABASE_URL ||
            process.env.POSTGRES_URL ||
            'postgresql://strapi:strapi_password@localhost:5432/medusa_backend';
        // Enhanced pool configuration for high-frequency requests
        this.pool = new pg_1.Pool({
            connectionString: databaseUrl,
            max: 50, // Increased from 20 to handle more concurrent requests
            min: 5, // Increased minimum to ensure availability
            idleTimeoutMillis: 60000, // Increased to 60 seconds for better reuse
            connectionTimeoutMillis: 10000, // Increased timeout for connection establishment
            application_name: 'medusa-backend-api-enhanced', // Updated name for monitoring
            // Additional PostgreSQL-specific optimizations
            statement_timeout: 30000, // 30 second statement timeout
            query_timeout: 30000, // 30 second query timeout
            keepAlive: true, // Enable TCP keep-alive
            keepAliveInitialDelayMillis: 10000, // 10 second initial delay
        });
        // Enhanced error handling
        this.pool.on('error', (err, client) => {
            console.error('❌ [DB-POOL] Unexpected error on idle client:', err);
            console.error('❌ [DB-POOL] Client info:', {
                connected: client ? 'present' : 'missing',
                error: err.message,
            });
            // Attempt to recover by reinitializing if needed
            this.handlePoolError(err);
        });
        // Enhanced connection tracking
        this.pool.on('connect', client => {
            this.connectionCount++;
            const metrics = this.getConnectionMetrics();
            console.log(`🔗 [DB-POOL] New client connected. Metrics:`, metrics);
            // Set up client-specific error handling
            client.on('error', err => {
                console.error('❌ [DB-POOL] Client error:', err);
            });
        });
        // Enhanced removal tracking
        this.pool.on('remove', client => {
            this.connectionCount--;
            const metrics = this.getConnectionMetrics();
            console.log(`🔌 [DB-POOL] Client removed. Metrics:`, metrics);
        });
        // Monitor for connection pool exhaustion
        this.pool.on('acquire', client => {
            const metrics = this.getConnectionMetrics();
            if (metrics.totalConnections >= (this.pool?.options.max || 50) * 0.9) {
                if (!this.maxConnectionsReached) {
                    console.warn('⚠️ [DB-POOL] Approaching maximum connections:', metrics);
                    this.maxConnectionsReached = true;
                }
            }
            else {
                this.maxConnectionsReached = false;
            }
        });
        this.isInitialized = true;
        console.log('✅ [DB-POOL] Enhanced database connection pool initialized successfully');
        console.log(`📊 [DB-POOL] Pool configuration: max=${this.pool.options.max}, min=${this.pool.options.min}`);
    }
    /**
     * Get a connection from the pool with enhanced error handling
     */
    async getConnection() {
        if (!this.pool) {
            this.initialize();
        }
        if (!this.pool) {
            throw new Error('Database pool is not initialized');
        }
        const startTime = Date.now();
        try {
            const client = await this.pool.connect();
            const acquireTime = Date.now() - startTime;
            const metrics = this.getConnectionMetrics();
            console.log(`🔗 [DB-POOL] Connection acquired in ${acquireTime}ms. Metrics:`, metrics);
            // Set up connection-specific timeout handling
            const originalQuery = client.query.bind(client);
            client.query = async (...args) => {
                const queryStart = Date.now();
                try {
                    const result = await originalQuery(...args);
                    const queryTime = Date.now() - queryStart;
                    if (queryTime > 5000) {
                        // Log slow queries
                        console.warn(`⚠️ [DB-POOL] Slow query detected: ${queryTime}ms`);
                    }
                    return result;
                }
                catch (error) {
                    console.error(`❌ [DB-POOL] Query error after ${Date.now() - queryStart}ms:`, error);
                    throw error;
                }
            };
            return client;
        }
        catch (error) {
            const acquireTime = Date.now() - startTime;
            console.error(`❌ [DB-POOL] Failed to acquire connection after ${acquireTime}ms:`, error);
            // Log current pool state for debugging
            const metrics = this.getConnectionMetrics();
            console.error(`❌ [DB-POOL] Pool state during failure:`, metrics);
            throw error;
        }
    }
    /**
     * Execute a query with automatic connection management and enhanced error handling
     */
    async query(text, params) {
        const client = await this.getConnection();
        try {
            const start = Date.now();
            const result = await client.query(text, params);
            const duration = Date.now() - start;
            // Enhanced logging with performance metrics
            const logData = {
                text: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
                duration: `${duration}ms`,
                rows: result.rowCount,
                params: params ? params.length : 0,
            };
            if (duration > 1000) {
                console.warn('⚠️ [DB-POOL] Slow query detected:', logData);
            }
            else {
                console.log('📊 [DB-POOL] Query executed:', logData);
            }
            return result.rows;
        }
        catch (error) {
            console.error('❌ [DB-POOL] Query execution failed:', {
                error: error instanceof Error ? error.message : error,
                query: text.substring(0, 100),
                params: params ? params.length : 0,
            });
            throw error;
        }
        finally {
            // Always release the connection back to the pool
            try {
                client.release();
                console.log('🔌 [DB-POOL] Connection released back to pool');
            }
            catch (releaseError) {
                console.error('❌ [DB-POOL] Error releasing connection:', releaseError);
                // Don't throw here as it would mask the original error
            }
        }
    }
    /**
     * Execute a transaction with automatic connection management
     */
    async transaction(callback) {
        const client = await this.getConnection();
        try {
            await client.query('BEGIN');
            console.log('🔄 [DB-POOL] Transaction started');
            const result = await callback(client);
            await client.query('COMMIT');
            console.log('✅ [DB-POOL] Transaction committed');
            return result;
        }
        catch (error) {
            await client.query('ROLLBACK');
            console.error('❌ [DB-POOL] Transaction rolled back:', error);
            throw error;
        }
        finally {
            client.release();
            console.log('🔌 [DB-POOL] Transaction connection released');
        }
    }
    /**
     * Get connection metrics for monitoring
     */
    getConnectionMetrics() {
        if (!this.pool) {
            return {
                totalConnections: 0,
                idleConnections: 0,
                waitingRequests: 0,
                activeConnections: 0,
            };
        }
        return {
            totalConnections: this.pool.totalCount,
            idleConnections: this.pool.idleCount,
            waitingRequests: this.pool.waitingCount,
            activeConnections: this.pool.totalCount - this.pool.idleCount,
        };
    }
    /**
     * Handle pool errors and attempt recovery
     */
    handlePoolError(error) {
        console.error('❌ [DB-POOL] Pool error detected, attempting recovery:', error);
        // Log current metrics
        const metrics = this.getConnectionMetrics();
        console.error('❌ [DB-POOL] Pool metrics during error:', metrics);
        // If we have too many failed connections, consider reinitializing
        if (error.message.includes('connection') || error.message.includes('timeout')) {
            console.warn('⚠️ [DB-POOL] Connection-related error detected, monitoring for recovery');
            // Set a flag to reinitialize if needed
            setTimeout(() => {
                if (this.pool && this.pool.totalCount === 0) {
                    console.warn('⚠️ [DB-POOL] No active connections, considering reinitialization');
                }
            }, 5000);
        }
    }
    /**
     * Get pool statistics
     */
    getStats() {
        if (!this.pool) {
            return null;
        }
        return {
            totalCount: this.pool.totalCount,
            idleCount: this.pool.idleCount,
            waitingCount: this.pool.waitingCount,
            maxConnections: this.pool.options.max,
            minConnections: this.pool.options.min,
        };
    }
    /**
     * Close the pool (for graceful shutdown)
     */
    async close() {
        if (this.pool) {
            await this.pool.end();
            console.log('🔒 [DB-POOL] Database connection pool closed');
            this.pool = null;
            this.isInitialized = false;
        }
    }
    /**
     * Health check for the database connection
     */
    async healthCheck() {
        try {
            const result = await this.query('SELECT 1 as health_check');
            return result.length > 0 && result[0].health_check === 1;
        }
        catch (error) {
            console.error('❌ [DB-POOL] Health check failed:', error);
            return false;
        }
    }
}
exports.DatabasePoolManager = DatabasePoolManager;
// Export singleton instance
exports.dbPool = DatabasePoolManager.getInstance();
// Initialize the pool when the module is loaded
exports.dbPool.initialize();
//# sourceMappingURL=data:application/json;base64,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