"use strict";
/**
 * Query Builder Utilities for Tenant-Aware APIs
 *
 * Provides reusable functions for building database queries with:
 * - Tenant isolation
 * - Filtering
 * - Sorting
 * - Pagination
 * - Search functionality
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ENTITY_CONFIGS = void 0;
exports.buildWhereClause = buildWhereClause;
exports.buildOrderClause = buildOrderClause;
exports.parsePaginationParams = parsePaginationParams;
exports.buildListQuery = buildListQuery;
exports.buildResponseMetadata = buildResponseMetadata;
exports.getEntityConfig = getEntityConfig;
/**
 * Build WHERE clause with tenant isolation and filters
 */
function buildWhereClause(tenantId, queryParams, config) {
    const tenantField = config.tenantIdField || 'tenant_id';
    let whereConditions = [`${tenantField} = $1`, 'deleted_at IS NULL'];
    let values = [tenantId];
    let paramIndex = 2;
    // Search functionality
    if (queryParams.q && config.searchFields && config.searchFields.length > 0) {
        const searchConditions = config.searchFields.map(field => `${field} ILIKE $${paramIndex}`).join(' OR ');
        whereConditions.push(`(${searchConditions})`);
        // Add the search term for each search field
        config.searchFields.forEach(() => {
            values.push(`%${queryParams.q}%`);
            paramIndex++;
        });
    }
    // Date range filters
    if (queryParams.created_after) {
        whereConditions.push(`created_at >= $${paramIndex}`);
        values.push(queryParams.created_after);
        paramIndex++;
    }
    if (queryParams.created_before) {
        whereConditions.push(`created_at <= $${paramIndex}`);
        values.push(queryParams.created_before);
        paramIndex++;
    }
    if (queryParams.updated_after) {
        whereConditions.push(`updated_at >= $${paramIndex}`);
        values.push(queryParams.updated_after);
        paramIndex++;
    }
    if (queryParams.updated_before) {
        whereConditions.push(`updated_at <= $${paramIndex}`);
        values.push(queryParams.updated_before);
        paramIndex++;
    }
    // Custom filters
    if (config.customFilters) {
        Object.entries(config.customFilters).forEach(([paramKey, sqlCondition]) => {
            if (queryParams[paramKey] !== undefined) {
                // Replace {value} placeholder with parameter
                const condition = sqlCondition.replace('{value}', `$${paramIndex}`);
                whereConditions.push(condition);
                values.push(queryParams[paramKey]);
                paramIndex++;
            }
        });
    }
    return {
        whereClause: whereConditions.join(' AND '),
        values,
        nextParamIndex: paramIndex
    };
}
/**
 * Build ORDER BY clause with validation
 */
function buildOrderClause(queryParams, validSortFields) {
    const order = queryParams.order || queryParams.sort || 'created_at:desc';
    if (typeof order === 'string') {
        const [field, direction = 'asc'] = order.split(':');
        if (validSortFields.includes(field)) {
            const dir = direction.toLowerCase() === 'desc' ? 'DESC' : 'ASC';
            return `ORDER BY ${field} ${dir}`;
        }
    }
    // Default sorting
    return 'ORDER BY created_at DESC';
}
/**
 * Parse and validate pagination parameters
 */
function parsePaginationParams(queryParams) {
    const limit = Math.min(parseInt(queryParams.limit) || 20, 100); // Max 100 items
    const offset = Math.max(parseInt(queryParams.offset) || 0, 0);
    return { limit, offset };
}
/**
 * Build complete query for listing entities with all features
 */
function buildListQuery(tenantId, queryParams, config) {
    const { whereClause, values, nextParamIndex } = buildWhereClause(tenantId, queryParams, config);
    const orderClause = buildOrderClause(queryParams, config.validSortFields || []);
    const pagination = parsePaginationParams(queryParams);
    const countQuery = `
    SELECT COUNT(*) as total
    FROM ${config.tableName}
    WHERE ${whereClause}
  `;
    const listQuery = `
    SELECT *
    FROM ${config.tableName}
    WHERE ${whereClause}
    ${orderClause}
    LIMIT $${nextParamIndex} OFFSET $${nextParamIndex + 1}
  `;
    return {
        countQuery,
        listQuery,
        values: [...values, pagination.limit, pagination.offset],
        pagination
    };
}
/**
 * Build response metadata
 */
function buildResponseMetadata(tenantId, queryParams, totalCount, currentCount, pagination) {
    const appliedFilters = Object.keys(queryParams).filter(key => !['limit', 'offset', 'order', 'sort'].includes(key) && queryParams[key] !== undefined);
    return {
        count: currentCount,
        total: totalCount,
        offset: pagination.offset,
        limit: pagination.limit,
        has_more: (pagination.offset + pagination.limit) < totalCount,
        _tenant: {
            id: tenantId,
            filtered: true,
            method: 'enhanced_db_connection'
        },
        _query: {
            applied_filters: appliedFilters,
            sort_by: queryParams.order || queryParams.sort || 'created_at:desc',
            pagination: {
                current_page: Math.floor(pagination.offset / pagination.limit) + 1,
                total_pages: Math.ceil(totalCount / pagination.limit),
                per_page: pagination.limit
            }
        }
    };
}
/**
 * Predefined configurations for common entities
 */
exports.ENTITY_CONFIGS = {
    promotions: {
        tableName: 'promotion',
        searchFields: ['code', 'campaign_id'],
        validSortFields: ['created_at', 'updated_at', 'code', 'status', 'type'],
        customFilters: {
            status: 'status = {value}',
            code: 'code = {value}',
            type: 'type = {value}',
            is_automatic: 'is_automatic = {value}'
        }
    },
    products: {
        tableName: 'product',
        searchFields: ['title', 'description', 'handle'],
        validSortFields: ['created_at', 'updated_at', 'title', 'status', 'handle'],
        customFilters: {
            status: 'status = {value}',
            title: 'title ILIKE {value}',
            handle: 'handle = {value}',
            type_id: 'type_id = {value}',
            collection_id: 'collection_id = {value}'
        }
    },
    orders: {
        tableName: 'order',
        searchFields: ['display_id', 'email'],
        validSortFields: ['created_at', 'updated_at', 'total', 'status'],
        customFilters: {
            status: 'status = {value}',
            email: 'email ILIKE {value}',
            payment_status: 'payment_status = {value}',
            fulfillment_status: 'fulfillment_status = {value}'
        }
    },
    customers: {
        tableName: 'customer',
        searchFields: ['email', 'first_name', 'last_name'],
        validSortFields: ['created_at', 'updated_at', 'email', 'first_name', 'last_name'],
        customFilters: {
            email: 'email ILIKE {value}',
            first_name: 'first_name ILIKE {value}',
            last_name: 'last_name ILIKE {value}',
            has_account: 'has_account = {value}'
        }
    }
};
/**
 * Quick helper to get entity config
 */
function getEntityConfig(entityName) {
    return exports.ENTITY_CONFIGS[entityName] || {
        tableName: entityName,
        searchFields: [],
        validSortFields: ['created_at', 'updated_at'],
        customFilters: {}
    };
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicXVlcnktYnVpbGRlci5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uL3NyYy91dGlscy9xdWVyeS1idWlsZGVyLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFBQTs7Ozs7Ozs7O0dBU0c7OztBQXlDSCw0Q0FvRUM7QUFLRCw0Q0FjQztBQUtELHNEQUtDO0FBS0Qsd0NBa0NDO0FBS0Qsc0RBZ0NDO0FBMkRELDBDQU9DO0FBbFBEOztHQUVHO0FBQ0gsU0FBZ0IsZ0JBQWdCLENBQzlCLFFBQWdCLEVBQ2hCLFdBQXdCLEVBQ3hCLE1BQTBCO0lBRTFCLE1BQU0sV0FBVyxHQUFHLE1BQU0sQ0FBQyxhQUFhLElBQUksV0FBVyxDQUFBO0lBQ3ZELElBQUksZUFBZSxHQUFHLENBQUMsR0FBRyxXQUFXLE9BQU8sRUFBRSxvQkFBb0IsQ0FBQyxDQUFBO0lBQ25FLElBQUksTUFBTSxHQUFHLENBQUMsUUFBUSxDQUFDLENBQUE7SUFDdkIsSUFBSSxVQUFVLEdBQUcsQ0FBQyxDQUFBO0lBRWxCLHVCQUF1QjtJQUN2QixJQUFJLFdBQVcsQ0FBQyxDQUFDLElBQUksTUFBTSxDQUFDLFlBQVksSUFBSSxNQUFNLENBQUMsWUFBWSxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUUsQ0FBQztRQUMzRSxNQUFNLGdCQUFnQixHQUFHLE1BQU0sQ0FBQyxZQUFZLENBQUMsR0FBRyxDQUFDLEtBQUssQ0FBQyxFQUFFLENBQ3ZELEdBQUcsS0FBSyxXQUFXLFVBQVUsRUFBRSxDQUNoQyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQTtRQUVkLGVBQWUsQ0FBQyxJQUFJLENBQUMsSUFBSSxnQkFBZ0IsR0FBRyxDQUFDLENBQUE7UUFFN0MsNENBQTRDO1FBQzVDLE1BQU0sQ0FBQyxZQUFZLENBQUMsT0FBTyxDQUFDLEdBQUcsRUFBRTtZQUMvQixNQUFNLENBQUMsSUFBSSxDQUFDLElBQUksV0FBVyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUE7WUFDakMsVUFBVSxFQUFFLENBQUE7UUFDZCxDQUFDLENBQUMsQ0FBQTtJQUNKLENBQUM7SUFFRCxxQkFBcUI7SUFDckIsSUFBSSxXQUFXLENBQUMsYUFBYSxFQUFFLENBQUM7UUFDOUIsZUFBZSxDQUFDLElBQUksQ0FBQyxrQkFBa0IsVUFBVSxFQUFFLENBQUMsQ0FBQTtRQUNwRCxNQUFNLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxhQUFhLENBQUMsQ0FBQTtRQUN0QyxVQUFVLEVBQUUsQ0FBQTtJQUNkLENBQUM7SUFFRCxJQUFJLFdBQVcsQ0FBQyxjQUFjLEVBQUUsQ0FBQztRQUMvQixlQUFlLENBQUMsSUFBSSxDQUFDLGtCQUFrQixVQUFVLEVBQUUsQ0FBQyxDQUFBO1FBQ3BELE1BQU0sQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLGNBQWMsQ0FBQyxDQUFBO1FBQ3ZDLFVBQVUsRUFBRSxDQUFBO0lBQ2QsQ0FBQztJQUVELElBQUksV0FBVyxDQUFDLGFBQWEsRUFBRSxDQUFDO1FBQzlCLGVBQWUsQ0FBQyxJQUFJLENBQUMsa0JBQWtCLFVBQVUsRUFBRSxDQUFDLENBQUE7UUFDcEQsTUFBTSxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsYUFBYSxDQUFDLENBQUE7UUFDdEMsVUFBVSxFQUFFLENBQUE7SUFDZCxDQUFDO0lBRUQsSUFBSSxXQUFXLENBQUMsY0FBYyxFQUFFLENBQUM7UUFDL0IsZUFBZSxDQUFDLElBQUksQ0FBQyxrQkFBa0IsVUFBVSxFQUFFLENBQUMsQ0FBQTtRQUNwRCxNQUFNLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxjQUFjLENBQUMsQ0FBQTtRQUN2QyxVQUFVLEVBQUUsQ0FBQTtJQUNkLENBQUM7SUFFRCxpQkFBaUI7SUFDakIsSUFBSSxNQUFNLENBQUMsYUFBYSxFQUFFLENBQUM7UUFDekIsTUFBTSxDQUFDLE9BQU8sQ0FBQyxNQUFNLENBQUMsYUFBYSxDQUFDLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxRQUFRLEVBQUUsWUFBWSxDQUFDLEVBQUUsRUFBRTtZQUN4RSxJQUFJLFdBQVcsQ0FBQyxRQUFRLENBQUMsS0FBSyxTQUFTLEVBQUUsQ0FBQztnQkFDeEMsNkNBQTZDO2dCQUM3QyxNQUFNLFNBQVMsR0FBRyxZQUFZLENBQUMsT0FBTyxDQUFDLFNBQVMsRUFBRSxJQUFJLFVBQVUsRUFBRSxDQUFDLENBQUE7Z0JBQ25FLGVBQWUsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUE7Z0JBQy9CLE1BQU0sQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUE7Z0JBQ2xDLFVBQVUsRUFBRSxDQUFBO1lBQ2QsQ0FBQztRQUNILENBQUMsQ0FBQyxDQUFBO0lBQ0osQ0FBQztJQUVELE9BQU87UUFDTCxXQUFXLEVBQUUsZUFBZSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUM7UUFDMUMsTUFBTTtRQUNOLGNBQWMsRUFBRSxVQUFVO0tBQzNCLENBQUE7QUFDSCxDQUFDO0FBRUQ7O0dBRUc7QUFDSCxTQUFnQixnQkFBZ0IsQ0FBQyxXQUF3QixFQUFFLGVBQXlCO0lBQ2xGLE1BQU0sS0FBSyxHQUFHLFdBQVcsQ0FBQyxLQUFLLElBQUksV0FBVyxDQUFDLElBQUksSUFBSSxpQkFBaUIsQ0FBQTtJQUV4RSxJQUFJLE9BQU8sS0FBSyxLQUFLLFFBQVEsRUFBRSxDQUFDO1FBQzlCLE1BQU0sQ0FBQyxLQUFLLEVBQUUsU0FBUyxHQUFHLEtBQUssQ0FBQyxHQUFHLEtBQUssQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUE7UUFFbkQsSUFBSSxlQUFlLENBQUMsUUFBUSxDQUFDLEtBQUssQ0FBQyxFQUFFLENBQUM7WUFDcEMsTUFBTSxHQUFHLEdBQUcsU0FBUyxDQUFDLFdBQVcsRUFBRSxLQUFLLE1BQU0sQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUE7WUFDL0QsT0FBTyxZQUFZLEtBQUssSUFBSSxHQUFHLEVBQUUsQ0FBQTtRQUNuQyxDQUFDO0lBQ0gsQ0FBQztJQUVELGtCQUFrQjtJQUNsQixPQUFPLDBCQUEwQixDQUFBO0FBQ25DLENBQUM7QUFFRDs7R0FFRztBQUNILFNBQWdCLHFCQUFxQixDQUFDLFdBQXdCO0lBQzVELE1BQU0sS0FBSyxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUMsUUFBUSxDQUFDLFdBQVcsQ0FBQyxLQUFlLENBQUMsSUFBSSxFQUFFLEVBQUUsR0FBRyxDQUFDLENBQUEsQ0FBQyxnQkFBZ0I7SUFDekYsTUFBTSxNQUFNLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxRQUFRLENBQUMsV0FBVyxDQUFDLE1BQWdCLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUE7SUFFdkUsT0FBTyxFQUFFLEtBQUssRUFBRSxNQUFNLEVBQUUsQ0FBQTtBQUMxQixDQUFDO0FBRUQ7O0dBRUc7QUFDSCxTQUFnQixjQUFjLENBQzVCLFFBQWdCLEVBQ2hCLFdBQXdCLEVBQ3hCLE1BQTBCO0lBTzFCLE1BQU0sRUFBRSxXQUFXLEVBQUUsTUFBTSxFQUFFLGNBQWMsRUFBRSxHQUFHLGdCQUFnQixDQUFDLFFBQVEsRUFBRSxXQUFXLEVBQUUsTUFBTSxDQUFDLENBQUE7SUFDL0YsTUFBTSxXQUFXLEdBQUcsZ0JBQWdCLENBQUMsV0FBVyxFQUFFLE1BQU0sQ0FBQyxlQUFlLElBQUksRUFBRSxDQUFDLENBQUE7SUFDL0UsTUFBTSxVQUFVLEdBQUcscUJBQXFCLENBQUMsV0FBVyxDQUFDLENBQUE7SUFFckQsTUFBTSxVQUFVLEdBQUc7O1dBRVYsTUFBTSxDQUFDLFNBQVM7WUFDZixXQUFXO0dBQ3BCLENBQUE7SUFFRCxNQUFNLFNBQVMsR0FBRzs7V0FFVCxNQUFNLENBQUMsU0FBUztZQUNmLFdBQVc7TUFDakIsV0FBVzthQUNKLGNBQWMsWUFBWSxjQUFjLEdBQUcsQ0FBQztHQUN0RCxDQUFBO0lBRUQsT0FBTztRQUNMLFVBQVU7UUFDVixTQUFTO1FBQ1QsTUFBTSxFQUFFLENBQUMsR0FBRyxNQUFNLEVBQUUsVUFBVSxDQUFDLEtBQUssRUFBRSxVQUFVLENBQUMsTUFBTSxDQUFDO1FBQ3hELFVBQVU7S0FDWCxDQUFBO0FBQ0gsQ0FBQztBQUVEOztHQUVHO0FBQ0gsU0FBZ0IscUJBQXFCLENBQ25DLFFBQWdCLEVBQ2hCLFdBQXdCLEVBQ3hCLFVBQWtCLEVBQ2xCLFlBQW9CLEVBQ3BCLFVBQTZDO0lBRTdDLE1BQU0sY0FBYyxHQUFHLE1BQU0sQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQzNELENBQUMsQ0FBQyxPQUFPLEVBQUUsUUFBUSxFQUFFLE9BQU8sRUFBRSxNQUFNLENBQUMsQ0FBQyxRQUFRLENBQUMsR0FBRyxDQUFDLElBQUksV0FBVyxDQUFDLEdBQUcsQ0FBQyxLQUFLLFNBQVMsQ0FDdEYsQ0FBQTtJQUVELE9BQU87UUFDTCxLQUFLLEVBQUUsWUFBWTtRQUNuQixLQUFLLEVBQUUsVUFBVTtRQUNqQixNQUFNLEVBQUUsVUFBVSxDQUFDLE1BQU07UUFDekIsS0FBSyxFQUFFLFVBQVUsQ0FBQyxLQUFLO1FBQ3ZCLFFBQVEsRUFBRSxDQUFDLFVBQVUsQ0FBQyxNQUFNLEdBQUcsVUFBVSxDQUFDLEtBQUssQ0FBQyxHQUFHLFVBQVU7UUFDN0QsT0FBTyxFQUFFO1lBQ1AsRUFBRSxFQUFFLFFBQVE7WUFDWixRQUFRLEVBQUUsSUFBSTtZQUNkLE1BQU0sRUFBRSx3QkFBd0I7U0FDakM7UUFDRCxNQUFNLEVBQUU7WUFDTixlQUFlLEVBQUUsY0FBYztZQUMvQixPQUFPLEVBQUUsV0FBVyxDQUFDLEtBQUssSUFBSSxXQUFXLENBQUMsSUFBSSxJQUFJLGlCQUFpQjtZQUNuRSxVQUFVLEVBQUU7Z0JBQ1YsWUFBWSxFQUFFLElBQUksQ0FBQyxLQUFLLENBQUMsVUFBVSxDQUFDLE1BQU0sR0FBRyxVQUFVLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQztnQkFDbEUsV0FBVyxFQUFFLElBQUksQ0FBQyxJQUFJLENBQUMsVUFBVSxHQUFHLFVBQVUsQ0FBQyxLQUFLLENBQUM7Z0JBQ3JELFFBQVEsRUFBRSxVQUFVLENBQUMsS0FBSzthQUMzQjtTQUNGO0tBQ0YsQ0FBQTtBQUNILENBQUM7QUFFRDs7R0FFRztBQUNVLFFBQUEsY0FBYyxHQUEwQztJQUNuRSxVQUFVLEVBQUU7UUFDVixTQUFTLEVBQUUsV0FBVztRQUN0QixZQUFZLEVBQUUsQ0FBQyxNQUFNLEVBQUUsYUFBYSxDQUFDO1FBQ3JDLGVBQWUsRUFBRSxDQUFDLFlBQVksRUFBRSxZQUFZLEVBQUUsTUFBTSxFQUFFLFFBQVEsRUFBRSxNQUFNLENBQUM7UUFDdkUsYUFBYSxFQUFFO1lBQ2IsTUFBTSxFQUFFLGtCQUFrQjtZQUMxQixJQUFJLEVBQUUsZ0JBQWdCO1lBQ3RCLElBQUksRUFBRSxnQkFBZ0I7WUFDdEIsWUFBWSxFQUFFLHdCQUF3QjtTQUN2QztLQUNGO0lBRUQsUUFBUSxFQUFFO1FBQ1IsU0FBUyxFQUFFLFNBQVM7UUFDcEIsWUFBWSxFQUFFLENBQUMsT0FBTyxFQUFFLGFBQWEsRUFBRSxRQUFRLENBQUM7UUFDaEQsZUFBZSxFQUFFLENBQUMsWUFBWSxFQUFFLFlBQVksRUFBRSxPQUFPLEVBQUUsUUFBUSxFQUFFLFFBQVEsQ0FBQztRQUMxRSxhQUFhLEVBQUU7WUFDYixNQUFNLEVBQUUsa0JBQWtCO1lBQzFCLEtBQUssRUFBRSxxQkFBcUI7WUFDNUIsTUFBTSxFQUFFLGtCQUFrQjtZQUMxQixPQUFPLEVBQUUsbUJBQW1CO1lBQzVCLGFBQWEsRUFBRSx5QkFBeUI7U0FDekM7S0FDRjtJQUVELE1BQU0sRUFBRTtRQUNOLFNBQVMsRUFBRSxPQUFPO1FBQ2xCLFlBQVksRUFBRSxDQUFDLFlBQVksRUFBRSxPQUFPLENBQUM7UUFDckMsZUFBZSxFQUFFLENBQUMsWUFBWSxFQUFFLFlBQVksRUFBRSxPQUFPLEVBQUUsUUFBUSxDQUFDO1FBQ2hFLGFBQWEsRUFBRTtZQUNiLE1BQU0sRUFBRSxrQkFBa0I7WUFDMUIsS0FBSyxFQUFFLHFCQUFxQjtZQUM1QixjQUFjLEVBQUUsMEJBQTBCO1lBQzFDLGtCQUFrQixFQUFFLDhCQUE4QjtTQUNuRDtLQUNGO0lBRUQsU0FBUyxFQUFFO1FBQ1QsU0FBUyxFQUFFLFVBQVU7UUFDckIsWUFBWSxFQUFFLENBQUMsT0FBTyxFQUFFLFlBQVksRUFBRSxXQUFXLENBQUM7UUFDbEQsZUFBZSxFQUFFLENBQUMsWUFBWSxFQUFFLFlBQVksRUFBRSxPQUFPLEVBQUUsWUFBWSxFQUFFLFdBQVcsQ0FBQztRQUNqRixhQUFhLEVBQUU7WUFDYixLQUFLLEVBQUUscUJBQXFCO1lBQzVCLFVBQVUsRUFBRSwwQkFBMEI7WUFDdEMsU0FBUyxFQUFFLHlCQUF5QjtZQUNwQyxXQUFXLEVBQUUsdUJBQXVCO1NBQ3JDO0tBQ0Y7Q0FDRixDQUFBO0FBRUQ7O0dBRUc7QUFDSCxTQUFnQixlQUFlLENBQUMsVUFBa0I7SUFDaEQsT0FBTyxzQkFBYyxDQUFDLFVBQVUsQ0FBQyxJQUFJO1FBQ25DLFNBQVMsRUFBRSxVQUFVO1FBQ3JCLFlBQVksRUFBRSxFQUFFO1FBQ2hCLGVBQWUsRUFBRSxDQUFDLFlBQVksRUFBRSxZQUFZLENBQUM7UUFDN0MsYUFBYSxFQUFFLEVBQUU7S0FDbEIsQ0FBQTtBQUNILENBQUMifQ==