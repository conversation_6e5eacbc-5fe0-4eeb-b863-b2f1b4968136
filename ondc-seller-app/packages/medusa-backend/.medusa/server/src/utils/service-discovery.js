"use strict";
/**
 * Service Discovery Utility for Medusa v2
 *
 * This utility helps identify the correct service names and patterns
 * in Medusa v2's dependency injection container for multi-tenant integration.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.MEDUSA_V2_SERVICE_PATTERNS = void 0;
exports.discoverServices = discoverServices;
exports.findServicesByPattern = findServicesByPattern;
exports.resolveServiceWithFallbacks = resolveServiceWithFallbacks;
exports.testServiceMethods = testServiceMethods;
/**
 * Discover all available services in the Medusa container
 */
function discoverServices(req) {
    const container = req.scope;
    const cradle = container.cradle || {};
    const serviceNames = Object.keys(cradle);
    console.log(`🔍 [SERVICE DISCOVERY] Found ${serviceNames.length} services in container`);
    const coreServices = [];
    const modules = [];
    const repositories = [];
    const customServices = [];
    // Known Medusa v2 core service patterns
    const coreServicePatterns = [
        'product', 'customer', 'order', 'cart', 'user', 'auth',
        'payment', 'inventory', 'fulfillment', 'region', 'currency',
        'shipping', 'tax', 'discount', 'promotion', 'price', 'sales'
    ];
    const modulePatterns = [
        'Module', 'module', 'Service', 'service'
    ];
    const repositoryPatterns = [
        'Repository', 'repository', 'repo'
    ];
    serviceNames.forEach(serviceName => {
        try {
            const service = container.resolve(serviceName);
            const serviceInfo = {
                name: serviceName,
                type: 'unknown',
                methods: getServiceMethods(service),
                isCore: false
            };
            // Categorize service
            if (coreServicePatterns.some(pattern => serviceName.toLowerCase().includes(pattern.toLowerCase()))) {
                serviceInfo.type = 'service';
                serviceInfo.isCore = true;
                serviceInfo.description = `Core Medusa ${serviceName} service`;
                coreServices.push(serviceInfo);
            }
            else if (modulePatterns.some(pattern => serviceName.includes(pattern))) {
                serviceInfo.type = 'module';
                serviceInfo.description = `Medusa module: ${serviceName}`;
                modules.push(serviceInfo);
            }
            else if (repositoryPatterns.some(pattern => serviceName.includes(pattern))) {
                serviceInfo.type = 'repository';
                serviceInfo.description = `Data repository: ${serviceName}`;
                repositories.push(serviceInfo);
            }
            else {
                serviceInfo.type = 'service';
                serviceInfo.description = `Custom service: ${serviceName}`;
                customServices.push(serviceInfo);
            }
        }
        catch (error) {
            console.warn(`⚠️  [SERVICE DISCOVERY] Could not resolve service: ${serviceName}`);
        }
    });
    const result = {
        coreServices: coreServices.sort((a, b) => a.name.localeCompare(b.name)),
        modules: modules.sort((a, b) => a.name.localeCompare(b.name)),
        repositories: repositories.sort((a, b) => a.name.localeCompare(b.name)),
        customServices: customServices.sort((a, b) => a.name.localeCompare(b.name)),
        totalServices: serviceNames.length,
        discoveredAt: new Date().toISOString()
    };
    console.log(`✅ [SERVICE DISCOVERY] Categorized services:
    - Core Services: ${coreServices.length}
    - Modules: ${modules.length}
    - Repositories: ${repositories.length}
    - Custom Services: ${customServices.length}
    - Total: ${serviceNames.length}`);
    return result;
}
/**
 * Get available methods from a service instance
 */
function getServiceMethods(service) {
    if (!service || typeof service !== 'object') {
        return [];
    }
    const methods = [];
    const prototype = Object.getPrototypeOf(service);
    // Get methods from the service instance
    Object.getOwnPropertyNames(prototype).forEach(name => {
        if (name !== 'constructor' && typeof service[name] === 'function') {
            methods.push(name);
        }
    });
    // Get methods from the service object itself
    Object.getOwnPropertyNames(service).forEach(name => {
        if (typeof service[name] === 'function' && !methods.includes(name)) {
            methods.push(name);
        }
    });
    return methods.sort();
}
/**
 * Find services that match specific patterns (e.g., product-related services)
 */
function findServicesByPattern(req, pattern) {
    const discovery = discoverServices(req);
    const allServices = [
        ...discovery.coreServices,
        ...discovery.modules,
        ...discovery.repositories,
        ...discovery.customServices
    ];
    const regex = typeof pattern === 'string'
        ? new RegExp(pattern, 'i')
        : pattern;
    return allServices.filter(service => regex.test(service.name));
}
/**
 * Attempt to resolve a service with multiple possible names
 */
function resolveServiceWithFallbacks(req, possibleNames) {
    for (const name of possibleNames) {
        try {
            const service = req.scope.resolve(name);
            console.log(`✅ [SERVICE RESOLUTION] Successfully resolved: ${name}`);
            return { service, resolvedName: name };
        }
        catch (error) {
            console.log(`❌ [SERVICE RESOLUTION] Failed to resolve: ${name}`);
        }
    }
    console.error(`🚫 [SERVICE RESOLUTION] Could not resolve any of: ${possibleNames.join(', ')}`);
    return null;
}
/**
 * Common service name patterns for Medusa v2
 */
exports.MEDUSA_V2_SERVICE_PATTERNS = {
    product: [
        'productModuleService',
        'productService',
        'product',
        'productModule',
        'Product'
    ],
    customer: [
        'customerModuleService',
        'customerService',
        'customer',
        'customerModule',
        'Customer'
    ],
    order: [
        'orderModuleService',
        'orderService',
        'order',
        'orderModule',
        'Order'
    ],
    cart: [
        'cartModuleService',
        'cartService',
        'cart',
        'cartModule',
        'Cart'
    ],
    inventory: [
        'inventoryModuleService',
        'inventoryService',
        'inventory',
        'inventoryModule',
        'Inventory'
    ],
    pricing: [
        'pricingModuleService',
        'pricingService',
        'pricing',
        'pricingModule',
        'Pricing'
    ]
};
/**
 * Test service methods to understand their capabilities
 */
function testServiceMethods(service, serviceName) {
    const methods = getServiceMethods(service);
    const crudMethods = methods.filter(method => /^(create|update|delete|remove)/.test(method));
    const queryMethods = methods.filter(method => /^(find|get|list|retrieve|search|query)/.test(method));
    const otherMethods = methods.filter(method => !crudMethods.includes(method) && !queryMethods.includes(method));
    console.log(`🔧 [SERVICE METHODS] ${serviceName}:
    - CRUD: ${crudMethods.join(', ')}
    - Query: ${queryMethods.join(', ')}
    - Other: ${otherMethods.join(', ')}`);
    return { crudMethods, queryMethods, otherMethods };
}
//# sourceMappingURL=data:application/json;base64,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