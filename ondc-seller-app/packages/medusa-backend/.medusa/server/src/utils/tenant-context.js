"use strict";
/**
 * Task 2.5: Add Tenant Context to Request Objects
 *
 * Comprehensive tenant context utilities for Medusa v2 multi-tenancy.
 * Provides standardized access to tenant information across all request handlers.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTenantId = getTenantId;
exports.getTenantConfig = getTenantConfig;
exports.getTenantContext = getTenantContext;
exports.hasTenantContext = hasTenantContext;
exports.validateTenantContext = validateTenantContext;
exports.injectTenantId = injectTenantId;
exports.filterByTenantId = filterByTenantId;
exports.createTenantQuery = createTenantQuery;
exports.addTenantHeaders = addTenantHeaders;
exports.createTenantResponse = createTenantResponse;
exports.tenantHasFeature = tenantHasFeature;
exports.requireTenantFeature = requireTenantFeature;
exports.createTenantLogContext = createTenantLogContext;
// ============================================================================
// CORE TENANT CONTEXT UTILITIES
// ============================================================================
/**
 * Get Tenant ID from Request
 * Primary utility for extracting tenant ID from any request
 *
 * @param req - Medusa request object
 * @returns Tenant ID string
 */
function getTenantId(req) {
    // Priority: middleware-set > header > query > default
    const middlewareTenantId = req.tenantId;
    if (middlewareTenantId) {
        return middlewareTenantId;
    }
    const headerTenantId = req.headers['x-tenant-id'];
    if (headerTenantId) {
        return headerTenantId;
    }
    const queryTenantId = req.query.tenant;
    if (queryTenantId) {
        return queryTenantId;
    }
    return 'default';
}
/**
 * Get Tenant Configuration from Request
 * Primary utility for accessing complete tenant configuration
 *
 * @param req - Medusa request object
 * @returns Tenant configuration or null if not available
 */
function getTenantConfig(req) {
    return req.tenant || null;
}
/**
 * Get Complete Tenant Context
 * Comprehensive tenant context with all available information
 *
 * @param req - Medusa request object
 * @returns Complete tenant context
 */
function getTenantContext(req) {
    const tenantId = getTenantId(req);
    const config = getTenantConfig(req);
    if (!config) {
        return null;
    }
    // Determine extraction source
    let extractedFrom = 'default';
    if (req.headers['x-tenant-id']) {
        extractedFrom = 'header';
    }
    else if (req.query.tenant) {
        extractedFrom = 'query';
    }
    else if (req.headers.host && req.headers.host.includes('.')) {
        extractedFrom = 'subdomain';
    }
    return {
        id: tenantId,
        config,
        isValid: true, // If we have config, it's valid (middleware validated it)
        isActive: config.status === 'active',
        features: config.settings.features,
        ondcConfig: config.settings.ondcConfig,
        metadata: {
            extractedFrom,
            timestamp: new Date().toISOString(),
            requestPath: req.path,
            requestMethod: req.method,
        },
    };
}
/**
 * Check if Request has Tenant Context
 * Quick validation that tenant context is available
 *
 * @param req - Medusa request object
 * @returns True if tenant context is available
 */
function hasTenantContext(req) {
    return !!req.tenantId && !!req.tenant;
}
/**
 * Validate Tenant Context
 * Comprehensive validation of tenant context completeness
 *
 * @param req - Medusa request object
 * @returns Validation result with details
 */
function validateTenantContext(req) {
    const errors = [];
    const warnings = [];
    const tenantId = getTenantId(req);
    const config = getTenantConfig(req);
    // Check if tenant ID exists
    if (!tenantId) {
        errors.push('Tenant ID is missing');
    }
    // Check if tenant configuration exists
    if (!config) {
        errors.push('Tenant configuration is missing');
    }
    else {
        // Validate configuration completeness
        if (!config.name) {
            warnings.push('Tenant name is not configured');
        }
        if (!config.domain) {
            warnings.push('Tenant domain is not configured');
        }
        if (!config.settings.ondcConfig.participantId) {
            errors.push('ONDC participant ID is missing');
        }
        if (!config.settings.ondcConfig.subscriberId) {
            errors.push('ONDC subscriber ID is missing');
        }
        if (!config.settings.ondcConfig.bppId) {
            errors.push('ONDC BPP ID is missing');
        }
        if (config.status !== 'active') {
            warnings.push(`Tenant status is '${config.status}', not 'active'`);
        }
    }
    return {
        isValid: errors.length === 0,
        tenantId,
        errors,
        warnings,
    };
}
function injectTenantId(req, data) {
    const tenantId = getTenantId(req);
    if (Array.isArray(data)) {
        return data.map(item => ({ ...item, tenant_id: tenantId }));
    }
    return { ...data, tenant_id: tenantId };
}
/**
 * Filter Data by Tenant ID
 * Filters array data to only include items belonging to current tenant
 *
 * @param req - Medusa request object
 * @param data - Array of data to filter
 * @returns Filtered data array
 */
function filterByTenantId(req, data) {
    const tenantId = getTenantId(req);
    return data.filter(item => !item.tenant_id || item.tenant_id === tenantId);
}
/**
 * Create Tenant-Scoped Query Parameters
 * Generates query parameters with tenant context for database queries
 *
 * @param req - Medusa request object
 * @param additionalParams - Additional query parameters
 * @returns Query parameters with tenant context
 */
function createTenantQuery(req, additionalParams = {}) {
    const tenantId = getTenantId(req);
    return {
        tenant_id: tenantId,
        ...additionalParams,
    };
}
// ============================================================================
// RESPONSE UTILITIES
// ============================================================================
/**
 * Add Tenant Context to Response Headers
 * Adds tenant information to response headers for debugging and client use
 *
 * @param req - Medusa request object
 * @param res - Medusa response object
 */
function addTenantHeaders(req, res) {
    const context = getTenantContext(req);
    if (context) {
        res.setHeader('X-Tenant-ID', context.id);
        res.setHeader('X-Tenant-Name', context.config.name);
        res.setHeader('X-Tenant-Status', context.config.status);
        res.setHeader('X-Tenant-Features', context.features.join(','));
        // Add debug headers in development
        if (process.env.NODE_ENV === 'development') {
            res.setHeader('X-Tenant-Extracted-From', context.metadata.extractedFrom);
            res.setHeader('X-Tenant-ONDC-BPP-ID', context.ondcConfig.bppId);
            res.setHeader('X-Tenant-Active', context.isActive.toString());
        }
    }
}
/**
 * Create Tenant-Aware API Response
 * Standardized API response format with tenant context
 *
 * @param req - Medusa request object
 * @param data - Response data
 * @param message - Optional message
 * @returns Standardized API response
 */
function createTenantResponse(req, data, message) {
    const context = getTenantContext(req);
    return {
        success: true,
        data,
        tenant_context: {
            tenant_id: context?.id || getTenantId(req),
            tenant_name: context?.config.name || 'Unknown Tenant',
            timestamp: new Date().toISOString(),
        },
        ...(message && { message }),
    };
}
// ============================================================================
// FEATURE CHECKING UTILITIES
// ============================================================================
/**
 * Check if Tenant has Feature
 * Validates if current tenant has access to a specific feature
 *
 * @param req - Medusa request object
 * @param feature - Feature name to check
 * @returns True if tenant has the feature
 */
function tenantHasFeature(req, feature) {
    const context = getTenantContext(req);
    if (!context) {
        return false;
    }
    // Check for 'all' feature (grants access to everything)
    if (context.features.includes('all')) {
        return true;
    }
    return context.features.includes(feature);
}
/**
 * Require Tenant Feature
 * Throws error if tenant doesn't have required feature
 *
 * @param req - Medusa request object
 * @param feature - Required feature name
 * @throws Error if feature is not available
 */
function requireTenantFeature(req, feature) {
    if (!tenantHasFeature(req, feature)) {
        const tenantId = getTenantId(req);
        throw new Error(`Tenant '${tenantId}' does not have access to feature '${feature}'`);
    }
}
// ============================================================================
// LOGGING UTILITIES
// ============================================================================
/**
 * Create Tenant-Aware Log Context
 * Generates structured logging context with tenant information
 *
 * @param req - Medusa request object
 * @param additionalContext - Additional context data
 * @returns Structured log context
 */
function createTenantLogContext(req, additionalContext = {}) {
    const context = getTenantContext(req);
    return {
        tenant_id: context?.id || getTenantId(req),
        tenant_name: context?.config.name || 'Unknown',
        tenant_status: context?.config.status || 'unknown',
        request_path: req.path,
        request_method: req.method,
        timestamp: new Date().toISOString(),
        ...additionalContext,
    };
}
//# sourceMappingURL=data:application/json;base64,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