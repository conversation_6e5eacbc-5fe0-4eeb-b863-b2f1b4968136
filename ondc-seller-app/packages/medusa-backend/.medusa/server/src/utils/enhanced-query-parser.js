"use strict";
/**
 * Enhanced Query Parameter Parser for Medusa v2 Store API
 *
 * Provides comprehensive query parameter parsing with support for:
 * - Advanced filtering with operators
 * - Sorting with multiple fields
 * - Pagination with validation
 * - Field selection and relation expansion
 * - Search functionality
 * - Date range filtering
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseQueryParameters = parseQueryParameters;
exports.buildSQLFilters = buildSQLFilters;
exports.buildSQLSorting = buildSQLSorting;
/**
 * Parse query parameters with comprehensive validation and transformation
 */
function parseQueryParameters(req, config) {
    const query = req.query || {};
    return {
        pagination: parsePagination(query, config),
        sorting: parseSorting(query, config),
        filters: parseFilters(query, config),
        fields: parseFields(query, config),
        expand: parseExpand(query, config),
        search: parseSearch(query, config),
        dateFilters: parseDateFilters(query),
    };
}
/**
 * Parse pagination parameters
 */
function parsePagination(query, config) {
    const limit = Math.min(Math.max(parseInt(query.limit) || config.defaultLimit, 1), config.maxLimit);
    const offset = Math.max(parseInt(query.offset) || 0, 0);
    const page = query.page ? Math.max(parseInt(query.page), 1) : undefined;
    return {
        limit,
        offset: page ? (page - 1) * limit : offset,
        page,
    };
}
/**
 * Parse sorting parameters
 * Supports: sort=name:asc,created_at:desc
 */
function parseSorting(query, config) {
    const sortParam = query.sort || query.order;
    const sorting = [];
    if (sortParam) {
        const sortFields = Array.isArray(sortParam) ? sortParam : [sortParam];
        for (const sortField of sortFields) {
            const [field, direction = 'asc'] = sortField.split(':');
            if (config.sortableFields.includes(field)) {
                sorting.push({
                    field,
                    direction: direction.toLowerCase() === 'desc' ? 'DESC' : 'ASC',
                });
            }
        }
    }
    // Add default sorting if none provided
    if (sorting.length === 0) {
        sorting.push(config.defaultSort);
    }
    return sorting;
}
/**
 * Parse filter parameters with operators
 * Supports: field=value, field__gt=value, field__lt=value, field__in=value1,value2
 */
function parseFilters(query, config) {
    const filters = {};
    for (const [key, value] of Object.entries(query)) {
        // Skip non-filter parameters
        if (['limit', 'offset', 'page', 'sort', 'order', 'fields', 'expand', 'q', 'search'].includes(key)) {
            continue;
        }
        // Parse field with operator
        const [field, operator] = key.split('__');
        if (!config.filterableFields.includes(field)) {
            continue;
        }
        // Apply operator-based filtering
        switch (operator) {
            case 'gt':
                filters[field] = { $gt: parseFilterValue(value) };
                break;
            case 'gte':
                filters[field] = { $gte: parseFilterValue(value) };
                break;
            case 'lt':
                filters[field] = { $lt: parseFilterValue(value) };
                break;
            case 'lte':
                filters[field] = { $lte: parseFilterValue(value) };
                break;
            case 'in':
                const values = Array.isArray(value) ? value : value.toString().split(',');
                filters[field] = { $in: values.map(parseFilterValue) };
                break;
            case 'not':
                filters[field] = { $ne: parseFilterValue(value) };
                break;
            case 'like':
                filters[field] = { $ilike: `%${value}%` };
                break;
            case 'startswith':
                filters[field] = { $ilike: `${value}%` };
                break;
            case 'endswith':
                filters[field] = { $ilike: `%${value}` };
                break;
            default:
                // Direct equality
                filters[field] = parseFilterValue(value);
                break;
        }
    }
    return filters;
}
/**
 * Parse field selection parameters
 * Supports: fields=id,name,handle
 */
function parseFields(query, config) {
    if (!query.fields)
        return undefined;
    const fields = Array.isArray(query.fields)
        ? query.fields
        : query.fields.split(',');
    return fields.filter((field) => config.selectableFields.includes(field.trim()));
}
/**
 * Parse relation expansion parameters
 * Supports: expand=category,variants,images
 */
function parseExpand(query, config) {
    if (!query.expand)
        return undefined;
    const expand = Array.isArray(query.expand)
        ? query.expand
        : query.expand.split(',');
    return expand.filter((relation) => config.expandableRelations.includes(relation.trim()));
}
/**
 * Parse search parameters
 * Supports: q=search term, search=search term
 */
function parseSearch(query, config) {
    const searchQuery = query.q || query.search;
    if (!searchQuery || config.searchableFields.length === 0) {
        return undefined;
    }
    return {
        query: searchQuery.toString(),
        fields: config.searchableFields,
    };
}
/**
 * Parse date filter parameters
 */
function parseDateFilters(query) {
    const dateFilters = {};
    if (query.created_after) {
        dateFilters.created_after = new Date(query.created_after);
    }
    if (query.created_before) {
        dateFilters.created_before = new Date(query.created_before);
    }
    if (query.updated_after) {
        dateFilters.updated_after = new Date(query.updated_after);
    }
    if (query.updated_before) {
        dateFilters.updated_before = new Date(query.updated_before);
    }
    return dateFilters;
}
/**
 * Parse and convert filter values to appropriate types
 */
function parseFilterValue(value) {
    if (typeof value !== 'string')
        return value;
    // Boolean conversion
    if (value === 'true')
        return true;
    if (value === 'false')
        return false;
    // Number conversion
    if (/^\d+$/.test(value))
        return parseInt(value);
    if (/^\d+\.\d+$/.test(value))
        return parseFloat(value);
    // Date conversion (ISO format)
    if (/^\d{4}-\d{2}-\d{2}/.test(value)) {
        const date = new Date(value);
        if (!isNaN(date.getTime()))
            return date;
    }
    return value;
}
/**
 * Build SQL WHERE clause from parsed filters
 */
function buildSQLFilters(filters, tenantId, startParamIndex = 1) {
    const conditions = [`tenant_id = $${startParamIndex}`];
    const values = [tenantId];
    let paramIndex = startParamIndex + 1;
    for (const [field, filter] of Object.entries(filters)) {
        if (typeof filter === 'object' && filter !== null) {
            // Handle operator-based filters
            for (const [operator, value] of Object.entries(filter)) {
                switch (operator) {
                    case '$gt':
                        conditions.push(`${field} > $${paramIndex}`);
                        values.push(value);
                        paramIndex++;
                        break;
                    case '$gte':
                        conditions.push(`${field} >= $${paramIndex}`);
                        values.push(value);
                        paramIndex++;
                        break;
                    case '$lt':
                        conditions.push(`${field} < $${paramIndex}`);
                        values.push(value);
                        paramIndex++;
                        break;
                    case '$lte':
                        conditions.push(`${field} <= $${paramIndex}`);
                        values.push(value);
                        paramIndex++;
                        break;
                    case '$in':
                        const placeholders = value.map(() => `$${paramIndex++}`).join(',');
                        conditions.push(`${field} IN (${placeholders})`);
                        values.push(...value);
                        break;
                    case '$ne':
                        conditions.push(`${field} != $${paramIndex}`);
                        values.push(value);
                        paramIndex++;
                        break;
                    case '$ilike':
                        conditions.push(`${field} ILIKE $${paramIndex}`);
                        values.push(value);
                        paramIndex++;
                        break;
                }
            }
        }
        else {
            // Direct equality
            conditions.push(`${field} = $${paramIndex}`);
            values.push(filter);
            paramIndex++;
        }
    }
    return {
        whereClause: conditions.join(' AND '),
        values,
        nextParamIndex: paramIndex,
    };
}
/**
 * Build SQL ORDER BY clause from parsed sorting
 */
function buildSQLSorting(sorting) {
    if (sorting.length === 0)
        return '';
    const orderClauses = sorting.map(sort => `${sort.field} ${sort.direction}`);
    return `ORDER BY ${orderClauses.join(', ')}`;
}
//# sourceMappingURL=data:application/json;base64,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