"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductCategory = void 0;
const medusa_1 = require("@medusajs/medusa");
const core_1 = require("@mikro-orm/core");
/**
 * Extends Medusa's default ProductCategory entity to include:
 * - `tenant_id` VARCHAR column for multi-tenant data isolation
 *
 * A migration is provided to add the column at the database level.
 */
let ProductCategory = class ProductCategory extends medusa_1.ProductCategory {
    constructor() {
        super(...arguments);
        this.tenant_id = "default";
    }
};
exports.ProductCategory = ProductCategory;
__decorate([
    (0, core_1.Property)({ type: "varchar", length: 255, nullable: false, default: "default" }),
    __metadata("design:type", String)
], ProductCategory.prototype, "tenant_id", void 0);
exports.ProductCategory = ProductCategory = __decorate([
    (0, core_1.Entity)({ tableName: "product_category" })
], ProductCategory);
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicHJvZHVjdC1jYXRlZ29yeS5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uL3NyYy9tb2RlbHMvcHJvZHVjdC1jYXRlZ29yeS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBQSw2Q0FBMkU7QUFDM0UsMENBQWtEO0FBRWxEOzs7OztHQUtHO0FBRUksSUFBTSxlQUFlLEdBQXJCLE1BQU0sZUFBZ0IsU0FBUSx3QkFBcUI7SUFBbkQ7O1FBRUwsY0FBUyxHQUFXLFNBQVMsQ0FBQTtJQUMvQixDQUFDO0NBQUEsQ0FBQTtBQUhZLDBDQUFlO0FBRTFCO0lBREMsSUFBQSxlQUFRLEVBQUMsRUFBRSxJQUFJLEVBQUUsU0FBUyxFQUFFLE1BQU0sRUFBRSxHQUFHLEVBQUUsUUFBUSxFQUFFLEtBQUssRUFBRSxPQUFPLEVBQUUsU0FBUyxFQUFFLENBQUM7O2tEQUNuRDswQkFGbEIsZUFBZTtJQUQzQixJQUFBLGFBQU0sRUFBQyxFQUFFLFNBQVMsRUFBRSxrQkFBa0IsRUFBRSxDQUFDO0dBQzdCLGVBQWUsQ0FHM0IifQ==