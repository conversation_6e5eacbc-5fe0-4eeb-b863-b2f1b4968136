"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductCollection = void 0;
const medusa_1 = require("@medusajs/medusa");
const core_1 = require("@mikro-orm/core");
/**
 * Extends Medusa's default ProductCollection entity to include:
 * - `tenant_id` VARCHAR column for multi-tenant data isolation
 *
 * A migration is provided to add the column at the database level.
 */
let ProductCollection = class ProductCollection extends medusa_1.ProductCollection {
    constructor() {
        super(...arguments);
        this.tenant_id = "default";
    }
};
exports.ProductCollection = ProductCollection;
__decorate([
    (0, core_1.Property)({ type: "varchar", length: 255, nullable: false, default: "default" }),
    __metadata("design:type", String)
], ProductCollection.prototype, "tenant_id", void 0);
exports.ProductCollection = ProductCollection = __decorate([
    (0, core_1.Entity)({ tableName: "product_collection" })
], ProductCollection);
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicHJvZHVjdC1jb2xsZWN0aW9uLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vc3JjL21vZGVscy9wcm9kdWN0LWNvbGxlY3Rpb24udHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQUEsNkNBQStFO0FBQy9FLDBDQUFrRDtBQUVsRDs7Ozs7R0FLRztBQUVJLElBQU0saUJBQWlCLEdBQXZCLE1BQU0saUJBQWtCLFNBQVEsMEJBQXVCO0lBQXZEOztRQUVMLGNBQVMsR0FBVyxTQUFTLENBQUE7SUFDL0IsQ0FBQztDQUFBLENBQUE7QUFIWSw4Q0FBaUI7QUFFNUI7SUFEQyxJQUFBLGVBQVEsRUFBQyxFQUFFLElBQUksRUFBRSxTQUFTLEVBQUUsTUFBTSxFQUFFLEdBQUcsRUFBRSxRQUFRLEVBQUUsS0FBSyxFQUFFLE9BQU8sRUFBRSxTQUFTLEVBQUUsQ0FBQzs7b0RBQ25EOzRCQUZsQixpQkFBaUI7SUFEN0IsSUFBQSxhQUFNLEVBQUMsRUFBRSxTQUFTLEVBQUUsb0JBQW9CLEVBQUUsQ0FBQztHQUMvQixpQkFBaUIsQ0FHN0IifQ==