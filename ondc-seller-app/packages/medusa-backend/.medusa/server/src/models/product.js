"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Product = void 0;
const medusa_1 = require("@medusajs/medusa");
const core_1 = require("@mikro-orm/core");
/**
 * Extends Medusa's default Product entity to include:
 * - `additional_data` JSONB column for storing arbitrary product-specific data
 * - `tenant_id` VARCHAR column for multi-tenant data isolation
 *
 * Migrations are provided to add the columns at the database level.
 */
let Product = class Product extends medusa_1.Product {
    constructor() {
        super(...arguments);
        this.tenant_id = 'default';
    }
};
exports.Product = Product;
__decorate([
    (0, core_1.Property)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Product.prototype, "additional_data", void 0);
__decorate([
    (0, core_1.Property)({ type: 'varchar', length: 255, nullable: false, default: 'default' }),
    __metadata("design:type", String)
], Product.prototype, "tenant_id", void 0);
exports.Product = Product = __decorate([
    (0, core_1.Entity)({ tableName: 'product' })
], Product);
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicHJvZHVjdC5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uL3NyYy9tb2RlbHMvcHJvZHVjdC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBQSw2Q0FBNEQ7QUFDNUQsMENBQW1EO0FBRW5EOzs7Ozs7R0FNRztBQUVJLElBQU0sT0FBTyxHQUFiLE1BQU0sT0FBUSxTQUFRLGdCQUFhO0lBQW5DOztRQUtMLGNBQVMsR0FBVyxTQUFTLENBQUM7SUFDaEMsQ0FBQztDQUFBLENBQUE7QUFOWSwwQkFBTztBQUVsQjtJQURDLElBQUEsZUFBUSxFQUFDLEVBQUUsSUFBSSxFQUFFLE9BQU8sRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7O2dEQUNGO0FBRzFDO0lBREMsSUFBQSxlQUFRLEVBQUMsRUFBRSxJQUFJLEVBQUUsU0FBUyxFQUFFLE1BQU0sRUFBRSxHQUFHLEVBQUUsUUFBUSxFQUFFLEtBQUssRUFBRSxPQUFPLEVBQUUsU0FBUyxFQUFFLENBQUM7OzBDQUNsRDtrQkFMbkIsT0FBTztJQURuQixJQUFBLGFBQU0sRUFBQyxFQUFFLFNBQVMsRUFBRSxTQUFTLEVBQUUsQ0FBQztHQUNwQixPQUFPLENBTW5CIn0=