"use strict";
/**
 * Medusa Native API Client
 *
 * Refactored API client that uses Medusa's native multi-tenancy
 * through Sales Channels and Publishable API Keys instead of
 * custom tenant implementations.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.MedusaNativeAPI = void 0;
exports.createMedusaAPI = createMedusaAPI;
/**
 * Medusa Native API Client
 *
 * Uses Medusa's built-in multi-tenancy through Sales Channels
 * and Publishable API Keys for proper tenant isolation.
 */
class MedusaNativeAPI {
    constructor(tenantId) {
        this.baseURL = process.env.NEXT_PUBLIC_MEDUSA_API_URL || 'http://localhost:9000';
        this.publishableKey = this.getTenantPublishableKey(tenantId);
        this.salesChannelId = this.getTenantSalesChannelId(tenantId);
    }
    /**
     * Map tenant ID to publishable API key
     * These keys are generated by the setup-tenant-sales-channels script
     */
    getTenantPublishableKey(tenantId) {
        const keyMap = {
            'tenant-electronics-001': process.env.NEXT_PUBLIC_TENANT_ELECTRONICS_001_API_KEY || '',
            'tenant-fashion-002': process.env.NEXT_PUBLIC_TENANT_FASHION_002_API_KEY || '',
            'default': process.env.NEXT_PUBLIC_DEFAULT_API_KEY || ''
        };
        const key = keyMap[tenantId] || keyMap['default'];
        if (!key) {
            throw new Error(`No publishable API key found for tenant: ${tenantId}. Please run setup-tenant-sales-channels script.`);
        }
        return key;
    }
    /**
     * Map tenant ID to sales channel ID (for admin operations)
     */
    getTenantSalesChannelId(tenantId) {
        const channelMap = {
            'tenant-electronics-001': process.env.TENANT_ELECTRONICS_001_SALES_CHANNEL_ID || '',
            'tenant-fashion-002': process.env.TENANT_FASHION_002_SALES_CHANNEL_ID || '',
            'default': process.env.DEFAULT_SALES_CHANNEL_ID || ''
        };
        return channelMap[tenantId] || channelMap['default'];
    }
    // ==========================================
    // STORE API METHODS (Customer-facing)
    // ==========================================
    /**
     * Get products (automatically filtered by sales channel via publishable key)
     */
    async getProducts(params) {
        const searchParams = new URLSearchParams();
        if (params) {
            Object.entries(params).forEach(([key, value]) => {
                if (value !== undefined && value !== null) {
                    if (Array.isArray(value)) {
                        value.forEach(v => searchParams.append(key, v.toString()));
                    }
                    else {
                        searchParams.append(key, value.toString());
                    }
                }
            });
        }
        return await this.request(`/store/products?${searchParams}`, {
            headers: {
                'x-publishable-api-key': this.publishableKey
            }
        });
    }
    /**
     * Get specific product
     */
    async getProduct(id) {
        return await this.request(`/store/products/${id}`, {
            headers: {
                'x-publishable-api-key': this.publishableKey
            }
        });
    }
    /**
     * Create cart (automatically scoped to sales channel)
     */
    async createCart(data) {
        return await this.request('/store/carts', {
            method: 'POST',
            headers: {
                'x-publishable-api-key': this.publishableKey
            },
            body: JSON.stringify(data || {})
        });
    }
    /**
     * Get cart
     */
    async getCart(cartId) {
        return await this.request(`/store/carts/${cartId}`, {
            headers: {
                'x-publishable-api-key': this.publishableKey
            }
        });
    }
    /**
     * Add item to cart
     */
    async addToCart(cartId, item) {
        return await this.request(`/store/carts/${cartId}/line-items`, {
            method: 'POST',
            headers: {
                'x-publishable-api-key': this.publishableKey
            },
            body: JSON.stringify(item)
        });
    }
    /**
     * Update cart item
     */
    async updateCartItem(cartId, itemId, quantity) {
        return await this.request(`/store/carts/${cartId}/line-items/${itemId}`, {
            method: 'POST',
            headers: {
                'x-publishable-api-key': this.publishableKey
            },
            body: JSON.stringify({ quantity })
        });
    }
    /**
     * Remove item from cart
     */
    async removeFromCart(cartId, itemId) {
        return await this.request(`/store/carts/${cartId}/line-items/${itemId}`, {
            method: 'DELETE',
            headers: {
                'x-publishable-api-key': this.publishableKey
            }
        });
    }
    /**
     * Create order from cart
     */
    async createOrder(cartId) {
        return await this.request('/store/orders', {
            method: 'POST',
            headers: {
                'x-publishable-api-key': this.publishableKey
            },
            body: JSON.stringify({ cart_id: cartId })
        });
    }
    /**
     * Get order
     */
    async getOrder(orderId) {
        return await this.request(`/store/orders/${orderId}`, {
            headers: {
                'x-publishable-api-key': this.publishableKey
            }
        });
    }
    // ==========================================
    // ADMIN API METHODS (Management)
    // ==========================================
    /**
     * Set admin authentication token
     */
    setAdminToken(token) {
        this.adminToken = token;
    }
    /**
     * Get all sales channels (tenants)
     */
    async getSalesChannels() {
        return await this.request('/admin/sales-channels', {
            headers: {
                'Authorization': `Bearer ${this.adminToken}`
            }
        });
    }
    /**
     * Get specific sales channel (tenant configuration)
     */
    async getSalesChannel(id) {
        const channelId = id || this.salesChannelId;
        if (!channelId) {
            throw new Error('No sales channel ID available');
        }
        return await this.request(`/admin/sales-channels/${channelId}`, {
            headers: {
                'Authorization': `Bearer ${this.adminToken}`
            }
        });
    }
    /**
     * Update sales channel (tenant configuration)
     */
    async updateSalesChannel(id, data) {
        return await this.request(`/admin/sales-channels/${id}`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.adminToken}`
            },
            body: JSON.stringify(data)
        });
    }
    /**
     * Get admin products (optionally filtered by sales channel)
     */
    async getAdminProducts(salesChannelId, params) {
        const searchParams = new URLSearchParams();
        if (salesChannelId) {
            searchParams.append('sales_channel_id', salesChannelId);
        }
        if (params) {
            Object.entries(params).forEach(([key, value]) => {
                if (value !== undefined && value !== null) {
                    if (Array.isArray(value)) {
                        value.forEach(v => searchParams.append(key, v.toString()));
                    }
                    else {
                        searchParams.append(key, value.toString());
                    }
                }
            });
        }
        return await this.request(`/admin/products?${searchParams}`, {
            headers: {
                'Authorization': `Bearer ${this.adminToken}`
            }
        });
    }
    /**
     * Get admin customers (optionally filtered by sales channel)
     */
    async getAdminCustomers(salesChannelId) {
        const params = salesChannelId ? `?sales_channel_id=${salesChannelId}` : '';
        return await this.request(`/admin/customers${params}`, {
            headers: {
                'Authorization': `Bearer ${this.adminToken}`
            }
        });
    }
    /**
     * Get admin orders (optionally filtered by sales channel)
     */
    async getAdminOrders(salesChannelId) {
        const params = salesChannelId ? `?sales_channel_id=${salesChannelId}` : '';
        return await this.request(`/admin/orders${params}`, {
            headers: {
                'Authorization': `Bearer ${this.adminToken}`
            }
        });
    }
    // ==========================================
    // UTILITY METHODS
    // ==========================================
    /**
     * Get tenant configuration from sales channel metadata
     */
    async getTenantConfig() {
        const response = await this.getSalesChannel();
        return {
            id: response.sales_channel.metadata?.tenantId,
            name: response.sales_channel.name,
            domain: response.sales_channel.metadata?.domain,
            settings: {
                currency: response.sales_channel.metadata?.currency,
                timezone: response.sales_channel.metadata?.timezone,
                features: response.sales_channel.metadata?.features,
                ondcConfig: response.sales_channel.metadata?.ondcConfig,
                branding: response.sales_channel.metadata?.branding
            },
            status: response.sales_channel.is_disabled ? 'inactive' : 'active'
        };
    }
    /**
     * Generic request method
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const response = await fetch(url, {
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        });
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
        }
        return await response.json();
    }
}
exports.MedusaNativeAPI = MedusaNativeAPI;
/**
 * Factory function to create API client for specific tenant
 */
function createMedusaAPI(tenantId) {
    return new MedusaNativeAPI(tenantId);
}
/**
 * Default export for backward compatibility
 */
exports.default = MedusaNativeAPI;
//# sourceMappingURL=data:application/json;base64,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