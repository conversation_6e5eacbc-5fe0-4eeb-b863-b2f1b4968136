"use strict";
/**
 * Tenant Query Interceptor
 *
 * Intercepts and modifies database queries to automatically inject tenant filtering.
 * Works at the database level to ensure tenant isolation without modifying service schemas.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TenantQueryInterceptor = void 0;
class TenantQueryInterceptor {
    constructor(config) {
        this.config = config;
    }
    /**
     * Create interceptor from request context
     */
    static fromRequest(req, options = {}) {
        const tenantId = req.tenant_id || 'default';
        const config = {
            tenantId,
            enableLogging: true,
            strictMode: false,
            ...options
        };
        if (config.enableLogging) {
            console.log(`🔍 [QUERY-INTERCEPTOR] Creating interceptor for tenant: ${tenantId}`);
        }
        return new TenantQueryInterceptor(config);
    }
    /**
     * Intercept and modify service queries to include tenant filtering
     */
    interceptServiceQuery(serviceName, method, filters = {}) {
        if (this.config.enableLogging) {
            console.log(`🔍 [QUERY-INTERCEPTOR] Intercepting ${serviceName}.${method} for tenant: ${this.config.tenantId}`);
        }
        // Don't modify filters if tenant_id is already present
        if (filters.tenant_id) {
            if (this.config.enableLogging) {
                console.log(`ℹ️  [QUERY-INTERCEPTOR] Tenant filter already present: ${filters.tenant_id}`);
            }
            return filters;
        }
        // Add tenant filter based on service type
        const modifiedFilters = this.addTenantFilterForService(serviceName, filters);
        if (this.config.enableLogging) {
            console.log(`✅ [QUERY-INTERCEPTOR] Modified filters for ${serviceName}:`, modifiedFilters);
        }
        return modifiedFilters;
    }
    /**
     * Add tenant filter based on service type
     */
    addTenantFilterForService(serviceName, filters) {
        // For services that support tenant_id directly
        const directTenantServices = ['product', 'customer', 'order', 'cart'];
        if (directTenantServices.includes(serviceName)) {
            return { ...filters, tenant_id: this.config.tenantId };
        }
        // For services that need custom tenant filtering logic
        switch (serviceName) {
            case 'inventory':
                // Inventory might be linked to products, so we need to join
                return this.addInventoryTenantFilter(filters);
            case 'pricing':
                // Pricing might be linked to products or price sets
                return this.addPricingTenantFilter(filters);
            case 'fulfillment':
                // Fulfillment might be linked to orders
                return this.addFulfillmentTenantFilter(filters);
            default:
                // For unknown services, just add tenant_id and hope for the best
                return { ...filters, tenant_id: this.config.tenantId };
        }
    }
    /**
     * Add tenant filter for inventory queries
     */
    addInventoryTenantFilter(filters) {
        // Inventory items might need to be filtered by product tenant_id
        // This would require a join or subquery in real implementation
        return {
            ...filters,
            // For now, we'll use a placeholder approach
            _tenant_context: this.config.tenantId
        };
    }
    /**
     * Add tenant filter for pricing queries
     */
    addPricingTenantFilter(filters) {
        // Pricing might need to be filtered by product or price set tenant_id
        return {
            ...filters,
            _tenant_context: this.config.tenantId
        };
    }
    /**
     * Add tenant filter for fulfillment queries
     */
    addFulfillmentTenantFilter(filters) {
        // Fulfillment might need to be filtered by order tenant_id
        return {
            ...filters,
            _tenant_context: this.config.tenantId
        };
    }
    /**
     * Intercept create operations to inject tenant_id
     */
    interceptCreateData(serviceName, data) {
        if (this.config.enableLogging) {
            console.log(`🔍 [QUERY-INTERCEPTOR] Intercepting create data for ${serviceName}, tenant: ${this.config.tenantId}`);
        }
        if (Array.isArray(data)) {
            return data.map(item => this.injectTenantId(item));
        }
        return this.injectTenantId(data);
    }
    /**
     * Inject tenant_id into data object
     */
    injectTenantId(data) {
        // Don't override existing tenant_id
        if (data.tenant_id) {
            if (this.config.strictMode && data.tenant_id !== this.config.tenantId) {
                throw new Error(`Tenant ID mismatch: expected ${this.config.tenantId}, got ${data.tenant_id}`);
            }
            return data;
        }
        return { ...data, tenant_id: this.config.tenantId };
    }
    /**
     * Validate that retrieved data belongs to the current tenant
     */
    validateTenantOwnership(serviceName, data) {
        if (!data)
            return true; // No data to validate
        if (Array.isArray(data)) {
            return data.every(item => this.validateSingleItemOwnership(item));
        }
        return this.validateSingleItemOwnership(data);
    }
    /**
     * Validate single item tenant ownership
     */
    validateSingleItemOwnership(item) {
        if (!item || typeof item !== 'object')
            return true;
        // If item has tenant_id, validate it matches
        if (item.tenant_id) {
            const isValid = item.tenant_id === this.config.tenantId;
            if (!isValid && this.config.enableLogging) {
                console.warn(`⚠️  [QUERY-INTERCEPTOR] Tenant ownership violation: expected ${this.config.tenantId}, got ${item.tenant_id}`);
            }
            return isValid;
        }
        // If no tenant_id, assume it's valid (might be a system entity)
        return true;
    }
    /**
     * Create a wrapped service that automatically applies tenant filtering
     */
    wrapService(service, serviceName) {
        const interceptor = this;
        return new Proxy(service, {
            get(target, prop, receiver) {
                const originalMethod = Reflect.get(target, prop, receiver);
                if (typeof originalMethod !== 'function') {
                    return originalMethod;
                }
                // Wrap query methods
                if (interceptor.isQueryMethod(prop)) {
                    return function (...args) {
                        // Intercept the first argument (usually filters)
                        if (args.length > 0 && typeof args[0] === 'object') {
                            args[0] = interceptor.interceptServiceQuery(serviceName, prop, args[0]);
                        }
                        return originalMethod.apply(this, args);
                    };
                }
                // Wrap create methods
                if (interceptor.isCreateMethod(prop)) {
                    return function (...args) {
                        // Intercept the first argument (usually data)
                        if (args.length > 0) {
                            args[0] = interceptor.interceptCreateData(serviceName, args[0]);
                        }
                        return originalMethod.apply(this, args);
                    };
                }
                // Return original method for other operations
                return originalMethod;
            }
        });
    }
    /**
     * Check if method is a query method
     */
    isQueryMethod(methodName) {
        const queryMethods = [
            'list', 'listAndCount', 'retrieve', 'find', 'get', 'search', 'query'
        ];
        return queryMethods.some(method => methodName.toLowerCase().includes(method.toLowerCase()));
    }
    /**
     * Check if method is a create method
     */
    isCreateMethod(methodName) {
        const createMethods = ['create', 'add', 'insert', 'upsert'];
        return createMethods.some(method => methodName.toLowerCase().includes(method.toLowerCase()));
    }
    /**
     * Get tenant ID
     */
    getTenantId() {
        return this.config.tenantId;
    }
    /**
     * Update configuration
     */
    updateConfig(updates) {
        this.config = { ...this.config, ...updates };
        if (this.config.enableLogging) {
            console.log(`🔧 [QUERY-INTERCEPTOR] Updated config for tenant: ${this.config.tenantId}`, updates);
        }
    }
}
exports.TenantQueryInterceptor = TenantQueryInterceptor;
//# sourceMappingURL=data:application/json;base64,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