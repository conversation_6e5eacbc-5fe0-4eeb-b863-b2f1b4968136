"use strict";
/**
 * Database Connection Monitor Service
 *
 * Monitors database connection health, tracks metrics, and provides
 * automatic recovery mechanisms for connection pool issues.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.dbMonitor = exports.DatabaseMonitorService = void 0;
const database_pool_1 = require("../utils/database-pool");
class DatabaseMonitorService {
    constructor() {
        this.isMonitoring = false;
        this.monitoringInterval = null;
        this.metricsHistory = [];
        this.maxHistorySize = 100; // Keep last 100 metrics snapshots
        // Default alert thresholds
        this.alertThresholds = {
            maxPoolUtilization: 80, // 80%
            maxResponseTime: 5000, // 5 seconds
            maxErrorRate: 10, // 10%
            minHealthyConnections: 2,
        };
        // Tracking variables
        this.requestCount = 0;
        this.errorCount = 0;
        this.totalResponseTime = 0;
        this.lastResetTime = Date.now();
    }
    static getInstance() {
        if (!DatabaseMonitorService.instance) {
            DatabaseMonitorService.instance = new DatabaseMonitorService();
        }
        return DatabaseMonitorService.instance;
    }
    /**
     * Start monitoring database connections
     */
    startMonitoring(intervalMs = 30000) {
        if (this.isMonitoring) {
            console.log('⚠️ [DB-MONITOR] Monitoring is already running');
            return;
        }
        console.log(`🔍 [DB-MONITOR] Starting database connection monitoring (interval: ${intervalMs}ms)`);
        this.isMonitoring = true;
        this.monitoringInterval = setInterval(() => {
            this.collectMetrics();
        }, intervalMs);
        // Initial metrics collection
        this.collectMetrics();
    }
    /**
     * Stop monitoring database connections
     */
    stopMonitoring() {
        if (!this.isMonitoring) {
            return;
        }
        console.log('🛑 [DB-MONITOR] Stopping database connection monitoring');
        this.isMonitoring = false;
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
    }
    /**
     * Collect current connection metrics
     */
    async collectMetrics() {
        try {
            const poolMetrics = database_pool_1.dbPool.getConnectionMetrics();
            const currentTime = Date.now();
            const timeSinceReset = currentTime - this.lastResetTime;
            // Calculate rates (per minute)
            const avgResponseTime = this.requestCount > 0 ? this.totalResponseTime / this.requestCount : 0;
            const errorRate = this.requestCount > 0 ? (this.errorCount / this.requestCount) * 100 : 0;
            const poolUtilization = poolMetrics.totalConnections > 0
                ? (poolMetrics.activeConnections / poolMetrics.totalConnections) * 100
                : 0;
            const metrics = {
                timestamp: new Date(),
                totalConnections: poolMetrics.totalConnections,
                idleConnections: poolMetrics.idleConnections,
                activeConnections: poolMetrics.activeConnections,
                waitingRequests: poolMetrics.waitingRequests,
                poolUtilization,
                avgResponseTime,
                errorRate,
                isHealthy: this.assessHealth(poolMetrics, poolUtilization, avgResponseTime, errorRate),
            };
            // Add to history
            this.metricsHistory.push(metrics);
            if (this.metricsHistory.length > this.maxHistorySize) {
                this.metricsHistory.shift();
            }
            // Log metrics
            this.logMetrics(metrics);
            // Check for alerts
            this.checkAlerts(metrics);
            // Reset counters every 5 minutes
            if (timeSinceReset > 300000) {
                // 5 minutes
                this.resetCounters();
            }
        }
        catch (error) {
            console.error('❌ [DB-MONITOR] Error collecting metrics:', error);
        }
    }
    /**
     * Assess overall database health
     */
    assessHealth(poolMetrics, poolUtilization, avgResponseTime, errorRate) {
        // Check various health indicators
        const checks = [
            poolMetrics.totalConnections >= this.alertThresholds.minHealthyConnections,
            poolUtilization < this.alertThresholds.maxPoolUtilization,
            avgResponseTime < this.alertThresholds.maxResponseTime,
            errorRate < this.alertThresholds.maxErrorRate,
            poolMetrics.waitingRequests < 10, // No more than 10 waiting requests
        ];
        return checks.every(check => check);
    }
    /**
     * Log current metrics
     */
    logMetrics(metrics) {
        const healthIcon = metrics.isHealthy ? '✅' : '❌';
        console.log(`${healthIcon} [DB-MONITOR] Connection Health:`, {
            total: metrics.totalConnections,
            active: metrics.activeConnections,
            idle: metrics.idleConnections,
            waiting: metrics.waitingRequests,
            utilization: `${metrics.poolUtilization.toFixed(1)}%`,
            avgResponseTime: `${metrics.avgResponseTime.toFixed(0)}ms`,
            errorRate: `${metrics.errorRate.toFixed(1)}%`,
        });
    }
    /**
     * Check for alert conditions
     */
    checkAlerts(metrics) {
        const alerts = [];
        if (metrics.poolUtilization > this.alertThresholds.maxPoolUtilization) {
            alerts.push(`High pool utilization: ${metrics.poolUtilization.toFixed(1)}%`);
        }
        if (metrics.avgResponseTime > this.alertThresholds.maxResponseTime) {
            alerts.push(`High response time: ${metrics.avgResponseTime.toFixed(0)}ms`);
        }
        if (metrics.errorRate > this.alertThresholds.maxErrorRate) {
            alerts.push(`High error rate: ${metrics.errorRate.toFixed(1)}%`);
        }
        if (metrics.totalConnections < this.alertThresholds.minHealthyConnections) {
            alerts.push(`Low connection count: ${metrics.totalConnections}`);
        }
        if (metrics.waitingRequests > 5) {
            alerts.push(`High waiting requests: ${metrics.waitingRequests}`);
        }
        if (alerts.length > 0) {
            console.warn('🚨 [DB-MONITOR] ALERTS:', alerts);
            // Trigger recovery actions if needed
            this.triggerRecoveryActions(metrics, alerts);
        }
    }
    /**
     * Trigger automatic recovery actions
     */
    async triggerRecoveryActions(metrics, alerts) {
        console.log('🔧 [DB-MONITOR] Triggering recovery actions...');
        // Action 1: Force garbage collection if memory might be an issue
        if (global.gc && (metrics.poolUtilization > 90 || metrics.errorRate > 20)) {
            console.log('🧹 [DB-MONITOR] Forcing garbage collection');
            global.gc();
        }
        // Action 2: Health check the database connection
        try {
            const isHealthy = await database_pool_1.dbPool.healthCheck();
            if (!isHealthy) {
                console.error('❌ [DB-MONITOR] Database health check failed');
            }
        }
        catch (error) {
            console.error('❌ [DB-MONITOR] Health check error:', error);
        }
        // Action 3: Log detailed pool statistics for debugging
        const stats = database_pool_1.dbPool.getStats();
        console.log('📊 [DB-MONITOR] Detailed pool stats:', stats);
    }
    /**
     * Reset tracking counters
     */
    resetCounters() {
        console.log('🔄 [DB-MONITOR] Resetting performance counters');
        this.requestCount = 0;
        this.errorCount = 0;
        this.totalResponseTime = 0;
        this.lastResetTime = Date.now();
    }
    /**
     * Record a database request for metrics
     */
    recordRequest(responseTime, isError = false) {
        this.requestCount++;
        this.totalResponseTime += responseTime;
        if (isError) {
            this.errorCount++;
        }
    }
    /**
     * Get current health status
     */
    getCurrentHealth() {
        return this.metricsHistory.length > 0
            ? this.metricsHistory[this.metricsHistory.length - 1]
            : null;
    }
    /**
     * Get metrics history
     */
    getMetricsHistory() {
        return [...this.metricsHistory];
    }
    /**
     * Update alert thresholds
     */
    updateAlertThresholds(thresholds) {
        this.alertThresholds = { ...this.alertThresholds, ...thresholds };
        console.log('⚙️ [DB-MONITOR] Updated alert thresholds:', this.alertThresholds);
    }
    /**
     * Get health summary for API endpoints
     */
    getHealthSummary() {
        const currentHealth = this.getCurrentHealth();
        return {
            isHealthy: currentHealth?.isHealthy ?? false,
            lastCheck: currentHealth?.timestamp ?? null,
            metrics: currentHealth
                ? {
                    totalConnections: currentHealth.totalConnections,
                    activeConnections: currentHealth.activeConnections,
                    poolUtilization: `${currentHealth.poolUtilization.toFixed(1)}%`,
                    avgResponseTime: `${currentHealth.avgResponseTime.toFixed(0)}ms`,
                    errorRate: `${currentHealth.errorRate.toFixed(1)}%`,
                }
                : null,
            alertThresholds: this.alertThresholds,
        };
    }
}
exports.DatabaseMonitorService = DatabaseMonitorService;
// Export singleton instance
exports.dbMonitor = DatabaseMonitorService.getInstance();
//# sourceMappingURL=data:application/json;base64,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