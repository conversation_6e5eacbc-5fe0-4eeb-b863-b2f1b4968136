"use strict";
/**
 * Tenant-Aware Customer Service
 *
 * Wraps the core Medusa CustomerModuleService to automatically inject
 * tenant context into all customer operations.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TenantAwareCustomerService = void 0;
class TenantAwareCustomerService {
    constructor(customerService, tenantId = 'default') {
        this.customerService = customerService;
        this.tenantId = tenantId;
    }
    /**
     * Create a tenant-aware customer service instance from request
     */
    static fromRequest(req) {
        const customerService = req.scope.resolve('customer');
        const tenantId = req.tenant_id || 'default';
        console.log(`🏢 [TENANT-CUSTOMER] Creating tenant-aware customer service for tenant: ${tenantId}`);
        return new TenantAwareCustomerService(customerService, tenantId);
    }
    /**
     * Inject tenant_id into customer data
     */
    injectTenantId(data) {
        if (Array.isArray(data)) {
            return data.map(item => ({ ...item, tenant_id: this.tenantId }));
        }
        return { ...data, tenant_id: this.tenantId };
    }
    /**
     * Add tenant filter to query filters
     */
    addTenantFilter(filters = {}) {
        return { ...filters, tenant_id: this.tenantId };
    }
    // ============================================================================
    // CUSTOMER CRUD OPERATIONS WITH TENANT CONTEXT
    // ============================================================================
    /**
     * Create customers with automatic tenant injection
     */
    async createCustomers(data, context) {
        console.log(`🏢 [TENANT-CUSTOMER] Creating ${data.length} customers for tenant: ${this.tenantId}`);
        const tenantData = this.injectTenantId(data);
        const result = await this.customerService.createCustomers(tenantData, context);
        console.log(`✅ [TENANT-CUSTOMER] Created ${result.length} customers for tenant: ${this.tenantId}`);
        return result;
    }
    /**
     * Update customers with tenant validation
     */
    async updateCustomers(selector, data, context) {
        console.log(`🏢 [TENANT-CUSTOMER] Updating customers for tenant: ${this.tenantId}`);
        // Add tenant filter to selector to ensure we only update tenant's customers
        const tenantSelector = this.addTenantFilter(selector);
        const result = await this.customerService.updateCustomers(tenantSelector, data, context);
        console.log(`✅ [TENANT-CUSTOMER] Updated customers for tenant: ${this.tenantId}`);
        return result;
    }
    /**
     * List customers with automatic tenant filtering (using direct query)
     */
    async listCustomers(filters = {}, config, context) {
        console.log(`🏢 [TENANT-CUSTOMER] Listing customers for tenant: ${this.tenantId}`);
        // Since customer service doesn't have listCustomers, we'll use a direct query approach
        // This would typically be handled by the query service or direct database access
        const tenantFilters = this.addTenantFilter(filters);
        try {
            // For now, we'll simulate this - in real implementation, you'd use the query service
            console.log(`📋 [TENANT-CUSTOMER] Would query customers with filters:`, tenantFilters);
            // Placeholder - in real implementation, use query service or repository
            const result = [];
            console.log(`✅ [TENANT-CUSTOMER] Found ${result.length} customers for tenant: ${this.tenantId}`);
            return result;
        }
        catch (error) {
            console.error(`❌ [TENANT-CUSTOMER] Error listing customers: ${error}`);
            throw error;
        }
    }
    /**
     * Retrieve single customer with tenant validation
     */
    async retrieveCustomer(id, config, context) {
        console.log(`🏢 [TENANT-CUSTOMER] Retrieving customer ${id} for tenant: ${this.tenantId}`);
        try {
            // First validate customer belongs to tenant
            const customers = await this.listCustomers({ id }, { take: 1 }, context);
            if (customers.length === 0) {
                console.warn(`⚠️  [TENANT-CUSTOMER] Customer ${id} not found for tenant: ${this.tenantId}`);
                throw new Error(`Customer ${id} not found for tenant ${this.tenantId}`);
            }
            // For now, return the first customer from our list
            // In real implementation, you'd use the customer service's retrieve method
            const result = customers[0];
            console.log(`✅ [TENANT-CUSTOMER] Retrieved customer ${id} for tenant: ${this.tenantId}`);
            return result;
        }
        catch (error) {
            console.error(`❌ [TENANT-CUSTOMER] Error retrieving customer: ${error}`);
            throw error;
        }
    }
    // ============================================================================
    // CUSTOMER GROUP OPERATIONS WITH TENANT CONTEXT
    // ============================================================================
    /**
     * Create customer groups with tenant injection
     */
    async createCustomerGroups(data, context) {
        console.log(`🏢 [TENANT-CUSTOMER] Creating ${data.length} customer groups for tenant: ${this.tenantId}`);
        const tenantData = this.injectTenantId(data);
        const result = await this.customerService.createCustomerGroups(tenantData, context);
        console.log(`✅ [TENANT-CUSTOMER] Created ${result.length} customer groups for tenant: ${this.tenantId}`);
        return result;
    }
    /**
     * Update customer groups with tenant validation
     */
    async updateCustomerGroups(selector, data, context) {
        console.log(`🏢 [TENANT-CUSTOMER] Updating customer groups for tenant: ${this.tenantId}`);
        const tenantSelector = this.addTenantFilter(selector);
        const result = await this.customerService.updateCustomerGroups(tenantSelector, data, context);
        console.log(`✅ [TENANT-CUSTOMER] Updated customer groups for tenant: ${this.tenantId}`);
        return result;
    }
    /**
     * Add customer to group with tenant validation
     */
    async addCustomerToGroup(customerId, groupId, context) {
        console.log(`🏢 [TENANT-CUSTOMER] Adding customer ${customerId} to group ${groupId} for tenant: ${this.tenantId}`);
        // Validate both customer and group belong to tenant
        const customerExists = await this.validateCustomerOwnership(customerId);
        if (!customerExists) {
            throw new Error(`Customer ${customerId} not found for tenant ${this.tenantId}`);
        }
        const result = await this.customerService.addCustomerToGroup(customerId, groupId, context);
        console.log(`✅ [TENANT-CUSTOMER] Added customer to group for tenant: ${this.tenantId}`);
        return result;
    }
    /**
     * Remove customer from group with tenant validation
     */
    async removeCustomerFromGroup(customerId, groupId, context) {
        console.log(`🏢 [TENANT-CUSTOMER] Removing customer ${customerId} from group ${groupId} for tenant: ${this.tenantId}`);
        // Validate both customer and group belong to tenant
        const customerExists = await this.validateCustomerOwnership(customerId);
        if (!customerExists) {
            throw new Error(`Customer ${customerId} not found for tenant ${this.tenantId}`);
        }
        const result = await this.customerService.removeCustomerFromGroup(customerId, groupId, context);
        console.log(`✅ [TENANT-CUSTOMER] Removed customer from group for tenant: ${this.tenantId}`);
        return result;
    }
    // ============================================================================
    // CUSTOMER ADDRESS OPERATIONS WITH TENANT CONTEXT
    // ============================================================================
    /**
     * Create customer addresses with tenant injection
     */
    async createCustomerAddresses(data, context) {
        console.log(`🏢 [TENANT-CUSTOMER] Creating ${data.length} customer addresses for tenant: ${this.tenantId}`);
        const tenantData = this.injectTenantId(data);
        const result = await this.customerService.createCustomerAddresses(tenantData, context);
        console.log(`✅ [TENANT-CUSTOMER] Created ${result.length} customer addresses for tenant: ${this.tenantId}`);
        return result;
    }
    /**
     * Update customer addresses with tenant validation
     */
    async updateCustomerAddresses(selector, data, context) {
        console.log(`🏢 [TENANT-CUSTOMER] Updating customer addresses for tenant: ${this.tenantId}`);
        const tenantSelector = this.addTenantFilter(selector);
        const result = await this.customerService.updateCustomerAddresses(tenantSelector, data, context);
        console.log(`✅ [TENANT-CUSTOMER] Updated customer addresses for tenant: ${this.tenantId}`);
        return result;
    }
    // ============================================================================
    // UTILITY METHODS
    // ============================================================================
    /**
     * Get tenant ID for this service instance
     */
    getTenantId() {
        return this.tenantId;
    }
    /**
     * Get underlying customer service
     */
    getCustomerService() {
        return this.customerService;
    }
    /**
     * Validate customer ownership by tenant
     */
    async validateCustomerOwnership(customerId) {
        try {
            const customers = await this.listCustomers({ id: customerId }, { take: 1 });
            return customers.length > 0;
        }
        catch (error) {
            console.error(`❌ [TENANT-CUSTOMER] Error validating customer ownership: ${error}`);
            return false;
        }
    }
    /**
     * Get customer statistics for tenant
     */
    async getCustomerStats() {
        console.log(`📊 [TENANT-CUSTOMER] Getting customer statistics for tenant: ${this.tenantId}`);
        try {
            const customers = await this.listCustomers();
            // Note: For groups and addresses, we'd need separate queries
            // This is a simplified version focusing on customers
            const stats = {
                totalCustomers: customers.length,
                totalGroups: 0, // Would need separate query
                totalAddresses: 0 // Would need separate query
            };
            console.log(`📊 [TENANT-CUSTOMER] Stats for tenant ${this.tenantId}:`, stats);
            return stats;
        }
        catch (error) {
            console.error(`❌ [TENANT-CUSTOMER] Error getting customer stats: ${error}`);
            throw error;
        }
    }
    /**
     * Flush customer cache (if applicable)
     */
    async flush() {
        console.log(`🔄 [TENANT-CUSTOMER] Flushing customer cache for tenant: ${this.tenantId}`);
        if (typeof this.customerService.flush === 'function') {
            await this.customerService.flush();
            console.log(`✅ [TENANT-CUSTOMER] Flushed customer cache for tenant: ${this.tenantId}`);
        }
        else {
            console.log(`ℹ️  [TENANT-CUSTOMER] No flush method available for tenant: ${this.tenantId}`);
        }
    }
}
exports.TenantAwareCustomerService = TenantAwareCustomerService;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidGVuYW50LWF3YXJlLWN1c3RvbWVyLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vc3JjL3NlcnZpY2VzL3RlbmFudC1hd2FyZS1jdXN0b21lci50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQUE7Ozs7O0dBS0c7OztBQUlILE1BQWEsMEJBQTBCO0lBSXJDLFlBQVksZUFBb0IsRUFBRSxXQUFtQixTQUFTO1FBQzVELElBQUksQ0FBQyxlQUFlLEdBQUcsZUFBZSxDQUFDO1FBQ3ZDLElBQUksQ0FBQyxRQUFRLEdBQUcsUUFBUSxDQUFDO0lBQzNCLENBQUM7SUFFRDs7T0FFRztJQUNILE1BQU0sQ0FBQyxXQUFXLENBQUMsR0FBa0I7UUFDbkMsTUFBTSxlQUFlLEdBQUcsR0FBRyxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsVUFBVSxDQUFDLENBQUM7UUFDdEQsTUFBTSxRQUFRLEdBQUcsR0FBRyxDQUFDLFNBQVMsSUFBSSxTQUFTLENBQUM7UUFFNUMsT0FBTyxDQUFDLEdBQUcsQ0FBQywyRUFBMkUsUUFBUSxFQUFFLENBQUMsQ0FBQztRQUVuRyxPQUFPLElBQUksMEJBQTBCLENBQUMsZUFBZSxFQUFFLFFBQVEsQ0FBQyxDQUFDO0lBQ25FLENBQUM7SUFFRDs7T0FFRztJQUNLLGNBQWMsQ0FBQyxJQUFTO1FBQzlCLElBQUksS0FBSyxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDO1lBQ3hCLE9BQU8sSUFBSSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxHQUFHLElBQUksRUFBRSxTQUFTLEVBQUUsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUNuRSxDQUFDO1FBQ0QsT0FBTyxFQUFFLEdBQUcsSUFBSSxFQUFFLFNBQVMsRUFBRSxJQUFJLENBQUMsUUFBUSxFQUFFLENBQUM7SUFDL0MsQ0FBQztJQUVEOztPQUVHO0lBQ0ssZUFBZSxDQUFDLFVBQWUsRUFBRTtRQUN2QyxPQUFPLEVBQUUsR0FBRyxPQUFPLEVBQUUsU0FBUyxFQUFFLElBQUksQ0FBQyxRQUFRLEVBQUUsQ0FBQztJQUNsRCxDQUFDO0lBRUQsK0VBQStFO0lBQy9FLCtDQUErQztJQUMvQywrRUFBK0U7SUFFL0U7O09BRUc7SUFDSCxLQUFLLENBQUMsZUFBZSxDQUFDLElBQVcsRUFBRSxPQUFhO1FBQzlDLE9BQU8sQ0FBQyxHQUFHLENBQUMsaUNBQWlDLElBQUksQ0FBQyxNQUFNLDBCQUEwQixJQUFJLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQztRQUVuRyxNQUFNLFVBQVUsR0FBRyxJQUFJLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxDQUFDO1FBQzdDLE1BQU0sTUFBTSxHQUFHLE1BQU0sSUFBSSxDQUFDLGVBQWUsQ0FBQyxlQUFlLENBQUMsVUFBVSxFQUFFLE9BQU8sQ0FBQyxDQUFDO1FBRS9FLE9BQU8sQ0FBQyxHQUFHLENBQUMsK0JBQStCLE1BQU0sQ0FBQyxNQUFNLDBCQUEwQixJQUFJLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQztRQUNuRyxPQUFPLE1BQU0sQ0FBQztJQUNoQixDQUFDO0lBRUQ7O09BRUc7SUFDSCxLQUFLLENBQUMsZUFBZSxDQUFDLFFBQWEsRUFBRSxJQUFTLEVBQUUsT0FBYTtRQUMzRCxPQUFPLENBQUMsR0FBRyxDQUFDLHVEQUF1RCxJQUFJLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQztRQUVwRiw0RUFBNEU7UUFDNUUsTUFBTSxjQUFjLEdBQUcsSUFBSSxDQUFDLGVBQWUsQ0FBQyxRQUFRLENBQUMsQ0FBQztRQUN0RCxNQUFNLE1BQU0sR0FBRyxNQUFNLElBQUksQ0FBQyxlQUFlLENBQUMsZUFBZSxDQUFDLGNBQWMsRUFBRSxJQUFJLEVBQUUsT0FBTyxDQUFDLENBQUM7UUFFekYsT0FBTyxDQUFDLEdBQUcsQ0FBQyxxREFBcUQsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUM7UUFDbEYsT0FBTyxNQUFNLENBQUM7SUFDaEIsQ0FBQztJQUVEOztPQUVHO0lBQ0gsS0FBSyxDQUFDLGFBQWEsQ0FBQyxVQUFlLEVBQUUsRUFBRSxNQUFZLEVBQUUsT0FBYTtRQUNoRSxPQUFPLENBQUMsR0FBRyxDQUFDLHNEQUFzRCxJQUFJLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQztRQUVuRix1RkFBdUY7UUFDdkYsaUZBQWlGO1FBQ2pGLE1BQU0sYUFBYSxHQUFHLElBQUksQ0FBQyxlQUFlLENBQUMsT0FBTyxDQUFDLENBQUM7UUFFcEQsSUFBSSxDQUFDO1lBQ0gscUZBQXFGO1lBQ3JGLE9BQU8sQ0FBQyxHQUFHLENBQUMsMERBQTBELEVBQUUsYUFBYSxDQUFDLENBQUM7WUFFdkYsd0VBQXdFO1lBQ3hFLE1BQU0sTUFBTSxHQUFHLEVBQUUsQ0FBQztZQUVsQixPQUFPLENBQUMsR0FBRyxDQUFDLDZCQUE2QixNQUFNLENBQUMsTUFBTSwwQkFBMEIsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUM7WUFDakcsT0FBTyxNQUFNLENBQUM7UUFFaEIsQ0FBQztRQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7WUFDZixPQUFPLENBQUMsS0FBSyxDQUFDLGdEQUFnRCxLQUFLLEVBQUUsQ0FBQyxDQUFDO1lBQ3ZFLE1BQU0sS0FBSyxDQUFDO1FBQ2QsQ0FBQztJQUNILENBQUM7SUFFRDs7T0FFRztJQUNILEtBQUssQ0FBQyxnQkFBZ0IsQ0FBQyxFQUFVLEVBQUUsTUFBWSxFQUFFLE9BQWE7UUFDNUQsT0FBTyxDQUFDLEdBQUcsQ0FBQyw0Q0FBNEMsRUFBRSxnQkFBZ0IsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUM7UUFFM0YsSUFBSSxDQUFDO1lBQ0gsNENBQTRDO1lBQzVDLE1BQU0sU0FBUyxHQUFHLE1BQU0sSUFBSSxDQUFDLGFBQWEsQ0FBQyxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsSUFBSSxFQUFFLENBQUMsRUFBRSxFQUFFLE9BQU8sQ0FBQyxDQUFDO1lBRXpFLElBQUksU0FBUyxDQUFDLE1BQU0sS0FBSyxDQUFDLEVBQUUsQ0FBQztnQkFDM0IsT0FBTyxDQUFDLElBQUksQ0FBQyxrQ0FBa0MsRUFBRSwwQkFBMEIsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUM7Z0JBQzVGLE1BQU0sSUFBSSxLQUFLLENBQUMsWUFBWSxFQUFFLHlCQUF5QixJQUFJLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQztZQUMxRSxDQUFDO1lBRUQsbURBQW1EO1lBQ25ELDJFQUEyRTtZQUMzRSxNQUFNLE1BQU0sR0FBRyxTQUFTLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFFNUIsT0FBTyxDQUFDLEdBQUcsQ0FBQywwQ0FBMEMsRUFBRSxnQkFBZ0IsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUM7WUFDekYsT0FBTyxNQUFNLENBQUM7UUFFaEIsQ0FBQztRQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7WUFDZixPQUFPLENBQUMsS0FBSyxDQUFDLGtEQUFrRCxLQUFLLEVBQUUsQ0FBQyxDQUFDO1lBQ3pFLE1BQU0sS0FBSyxDQUFDO1FBQ2QsQ0FBQztJQUNILENBQUM7SUFFRCwrRUFBK0U7SUFDL0UsZ0RBQWdEO0lBQ2hELCtFQUErRTtJQUUvRTs7T0FFRztJQUNILEtBQUssQ0FBQyxvQkFBb0IsQ0FBQyxJQUFXLEVBQUUsT0FBYTtRQUNuRCxPQUFPLENBQUMsR0FBRyxDQUFDLGlDQUFpQyxJQUFJLENBQUMsTUFBTSxnQ0FBZ0MsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUM7UUFFekcsTUFBTSxVQUFVLEdBQUcsSUFBSSxDQUFDLGNBQWMsQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUM3QyxNQUFNLE1BQU0sR0FBRyxNQUFNLElBQUksQ0FBQyxlQUFlLENBQUMsb0JBQW9CLENBQUMsVUFBVSxFQUFFLE9BQU8sQ0FBQyxDQUFDO1FBRXBGLE9BQU8sQ0FBQyxHQUFHLENBQUMsK0JBQStCLE1BQU0sQ0FBQyxNQUFNLGdDQUFnQyxJQUFJLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQztRQUN6RyxPQUFPLE1BQU0sQ0FBQztJQUNoQixDQUFDO0lBRUQ7O09BRUc7SUFDSCxLQUFLLENBQUMsb0JBQW9CLENBQUMsUUFBYSxFQUFFLElBQVMsRUFBRSxPQUFhO1FBQ2hFLE9BQU8sQ0FBQyxHQUFHLENBQUMsNkRBQTZELElBQUksQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDO1FBRTFGLE1BQU0sY0FBYyxHQUFHLElBQUksQ0FBQyxlQUFlLENBQUMsUUFBUSxDQUFDLENBQUM7UUFDdEQsTUFBTSxNQUFNLEdBQUcsTUFBTSxJQUFJLENBQUMsZUFBZSxDQUFDLG9CQUFvQixDQUFDLGNBQWMsRUFBRSxJQUFJLEVBQUUsT0FBTyxDQUFDLENBQUM7UUFFOUYsT0FBTyxDQUFDLEdBQUcsQ0FBQywyREFBMkQsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUM7UUFDeEYsT0FBTyxNQUFNLENBQUM7SUFDaEIsQ0FBQztJQUVEOztPQUVHO0lBQ0gsS0FBSyxDQUFDLGtCQUFrQixDQUFDLFVBQWtCLEVBQUUsT0FBZSxFQUFFLE9BQWE7UUFDekUsT0FBTyxDQUFDLEdBQUcsQ0FBQyx3Q0FBd0MsVUFBVSxhQUFhLE9BQU8sZ0JBQWdCLElBQUksQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDO1FBRW5ILG9EQUFvRDtRQUNwRCxNQUFNLGNBQWMsR0FBRyxNQUFNLElBQUksQ0FBQyx5QkFBeUIsQ0FBQyxVQUFVLENBQUMsQ0FBQztRQUN4RSxJQUFJLENBQUMsY0FBYyxFQUFFLENBQUM7WUFDcEIsTUFBTSxJQUFJLEtBQUssQ0FBQyxZQUFZLFVBQVUseUJBQXlCLElBQUksQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDO1FBQ2xGLENBQUM7UUFFRCxNQUFNLE1BQU0sR0FBRyxNQUFNLElBQUksQ0FBQyxlQUFlLENBQUMsa0JBQWtCLENBQUMsVUFBVSxFQUFFLE9BQU8sRUFBRSxPQUFPLENBQUMsQ0FBQztRQUUzRixPQUFPLENBQUMsR0FBRyxDQUFDLDJEQUEyRCxJQUFJLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQztRQUN4RixPQUFPLE1BQU0sQ0FBQztJQUNoQixDQUFDO0lBRUQ7O09BRUc7SUFDSCxLQUFLLENBQUMsdUJBQXVCLENBQUMsVUFBa0IsRUFBRSxPQUFlLEVBQUUsT0FBYTtRQUM5RSxPQUFPLENBQUMsR0FBRyxDQUFDLDBDQUEwQyxVQUFVLGVBQWUsT0FBTyxnQkFBZ0IsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUM7UUFFdkgsb0RBQW9EO1FBQ3BELE1BQU0sY0FBYyxHQUFHLE1BQU0sSUFBSSxDQUFDLHlCQUF5QixDQUFDLFVBQVUsQ0FBQyxDQUFDO1FBQ3hFLElBQUksQ0FBQyxjQUFjLEVBQUUsQ0FBQztZQUNwQixNQUFNLElBQUksS0FBSyxDQUFDLFlBQVksVUFBVSx5QkFBeUIsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUM7UUFDbEYsQ0FBQztRQUVELE1BQU0sTUFBTSxHQUFHLE1BQU0sSUFBSSxDQUFDLGVBQWUsQ0FBQyx1QkFBdUIsQ0FBQyxVQUFVLEVBQUUsT0FBTyxFQUFFLE9BQU8sQ0FBQyxDQUFDO1FBRWhHLE9BQU8sQ0FBQyxHQUFHLENBQUMsK0RBQStELElBQUksQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDO1FBQzVGLE9BQU8sTUFBTSxDQUFDO0lBQ2hCLENBQUM7SUFFRCwrRUFBK0U7SUFDL0Usa0RBQWtEO0lBQ2xELCtFQUErRTtJQUUvRTs7T0FFRztJQUNILEtBQUssQ0FBQyx1QkFBdUIsQ0FBQyxJQUFXLEVBQUUsT0FBYTtRQUN0RCxPQUFPLENBQUMsR0FBRyxDQUFDLGlDQUFpQyxJQUFJLENBQUMsTUFBTSxtQ0FBbUMsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUM7UUFFNUcsTUFBTSxVQUFVLEdBQUcsSUFBSSxDQUFDLGNBQWMsQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUM3QyxNQUFNLE1BQU0sR0FBRyxNQUFNLElBQUksQ0FBQyxlQUFlLENBQUMsdUJBQXVCLENBQUMsVUFBVSxFQUFFLE9BQU8sQ0FBQyxDQUFDO1FBRXZGLE9BQU8sQ0FBQyxHQUFHLENBQUMsK0JBQStCLE1BQU0sQ0FBQyxNQUFNLG1DQUFtQyxJQUFJLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQztRQUM1RyxPQUFPLE1BQU0sQ0FBQztJQUNoQixDQUFDO0lBRUQ7O09BRUc7SUFDSCxLQUFLLENBQUMsdUJBQXVCLENBQUMsUUFBYSxFQUFFLElBQVMsRUFBRSxPQUFhO1FBQ25FLE9BQU8sQ0FBQyxHQUFHLENBQUMsZ0VBQWdFLElBQUksQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDO1FBRTdGLE1BQU0sY0FBYyxHQUFHLElBQUksQ0FBQyxlQUFlLENBQUMsUUFBUSxDQUFDLENBQUM7UUFDdEQsTUFBTSxNQUFNLEdBQUcsTUFBTSxJQUFJLENBQUMsZUFBZSxDQUFDLHVCQUF1QixDQUFDLGNBQWMsRUFBRSxJQUFJLEVBQUUsT0FBTyxDQUFDLENBQUM7UUFFakcsT0FBTyxDQUFDLEdBQUcsQ0FBQyw4REFBOEQsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUM7UUFDM0YsT0FBTyxNQUFNLENBQUM7SUFDaEIsQ0FBQztJQUVELCtFQUErRTtJQUMvRSxrQkFBa0I7SUFDbEIsK0VBQStFO0lBRS9FOztPQUVHO0lBQ0gsV0FBVztRQUNULE9BQU8sSUFBSSxDQUFDLFFBQVEsQ0FBQztJQUN2QixDQUFDO0lBRUQ7O09BRUc7SUFDSCxrQkFBa0I7UUFDaEIsT0FBTyxJQUFJLENBQUMsZUFBZSxDQUFDO0lBQzlCLENBQUM7SUFFRDs7T0FFRztJQUNILEtBQUssQ0FBQyx5QkFBeUIsQ0FBQyxVQUFrQjtRQUNoRCxJQUFJLENBQUM7WUFDSCxNQUFNLFNBQVMsR0FBRyxNQUFNLElBQUksQ0FBQyxhQUFhLENBQUMsRUFBRSxFQUFFLEVBQUUsVUFBVSxFQUFFLEVBQUUsRUFBRSxJQUFJLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQztZQUM1RSxPQUFPLFNBQVMsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDO1FBQzlCLENBQUM7UUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1lBQ2YsT0FBTyxDQUFDLEtBQUssQ0FBQyw0REFBNEQsS0FBSyxFQUFFLENBQUMsQ0FBQztZQUNuRixPQUFPLEtBQUssQ0FBQztRQUNmLENBQUM7SUFDSCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxLQUFLLENBQUMsZ0JBQWdCO1FBS3BCLE9BQU8sQ0FBQyxHQUFHLENBQUMsZ0VBQWdFLElBQUksQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDO1FBRTdGLElBQUksQ0FBQztZQUNILE1BQU0sU0FBUyxHQUFHLE1BQU0sSUFBSSxDQUFDLGFBQWEsRUFBRSxDQUFDO1lBRTdDLDZEQUE2RDtZQUM3RCxxREFBcUQ7WUFFckQsTUFBTSxLQUFLLEdBQUc7Z0JBQ1osY0FBYyxFQUFFLFNBQVMsQ0FBQyxNQUFNO2dCQUNoQyxXQUFXLEVBQUUsQ0FBQyxFQUFFLDRCQUE0QjtnQkFDNUMsY0FBYyxFQUFFLENBQUMsQ0FBQyw0QkFBNEI7YUFDL0MsQ0FBQztZQUVGLE9BQU8sQ0FBQyxHQUFHLENBQUMseUNBQXlDLElBQUksQ0FBQyxRQUFRLEdBQUcsRUFBRSxLQUFLLENBQUMsQ0FBQztZQUM5RSxPQUFPLEtBQUssQ0FBQztRQUVmLENBQUM7UUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1lBQ2YsT0FBTyxDQUFDLEtBQUssQ0FBQyxxREFBcUQsS0FBSyxFQUFFLENBQUMsQ0FBQztZQUM1RSxNQUFNLEtBQUssQ0FBQztRQUNkLENBQUM7SUFDSCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxLQUFLLENBQUMsS0FBSztRQUNULE9BQU8sQ0FBQyxHQUFHLENBQUMsNERBQTRELElBQUksQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDO1FBRXpGLElBQUksT0FBTyxJQUFJLENBQUMsZUFBZSxDQUFDLEtBQUssS0FBSyxVQUFVLEVBQUUsQ0FBQztZQUNyRCxNQUFNLElBQUksQ0FBQyxlQUFlLENBQUMsS0FBSyxFQUFFLENBQUM7WUFDbkMsT0FBTyxDQUFDLEdBQUcsQ0FBQywwREFBMEQsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUM7UUFDekYsQ0FBQzthQUFNLENBQUM7WUFDTixPQUFPLENBQUMsR0FBRyxDQUFDLCtEQUErRCxJQUFJLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQztRQUM5RixDQUFDO0lBQ0gsQ0FBQztDQUNGO0FBdFNELGdFQXNTQyJ9