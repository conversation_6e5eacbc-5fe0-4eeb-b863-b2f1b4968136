"use strict";
/**
 * Tenant-Aware Collection Service
 *
 * Provides complete CRUD operations for collections with automatic tenant isolation.
 * Integrates with Row Level Security (RLS) for database-level tenant filtering.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TenantAwareCollectionService = void 0;
const tenant_query_interceptor_1 = require("./tenant-query-interceptor");
class TenantAwareCollectionService {
    constructor(collectionService, tenantId) {
        this.collectionService = collectionService;
        this.tenantId = tenantId;
        this.queryInterceptor = new tenant_query_interceptor_1.TenantQueryInterceptor(tenantId);
        console.log(`🏢 [TENANT-COLLECTION] Initialized for tenant: ${tenantId}`);
    }
    /**
     * Set database tenant context for RLS
     */
    async setDatabaseTenantContext() {
        try {
            if (!this.pgConnection) {
                const { Client } = require('pg');
                this.pgConnection = new Client({
                    connectionString: process.env.DATABASE_URL ||
                        process.env.POSTGRES_URL ||
                        'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
                });
                await this.pgConnection.connect();
            }
            // Set tenant context for RLS
            await this.pgConnection.query('SELECT set_config($1, $2, true)', [
                'app.tenant_context.tenant_id',
                this.tenantId,
            ]);
            console.log(`🔒 [TENANT-COLLECTION] Set database tenant context: ${this.tenantId}`);
        }
        catch (error) {
            console.error(`❌ [TENANT-COLLECTION] Error setting database context: ${error}`);
        }
    }
    /**
     * Create a tenant-aware collection service instance from request
     */
    static fromRequest(req) {
        let collectionService = null;
        const tenantId = req.tenant_id || 'default';
        // Try different service names for collections
        const possibleServiceNames = ['collection', 'productCollection', 'product-collection'];
        for (const serviceName of possibleServiceNames) {
            try {
                collectionService = req.scope.resolve(serviceName);
                console.log(`✅ [TENANT-COLLECTION] Found collection service: ${serviceName}`);
                break;
            }
            catch (error) {
                // Continue trying other service names
            }
        }
        if (!collectionService) {
            console.warn(`⚠️ [TENANT-COLLECTION] No collection service found, creating placeholder for tenant: ${tenantId}`);
            // Create a placeholder service for now
            collectionService = {
                listCollections: async () => ({ collections: [], count: 0 }),
                retrieveCollection: async () => null,
                createCollections: async (data) => data,
                updateCollections: async (data) => data,
                deleteCollections: async () => true,
            };
        }
        return new TenantAwareCollectionService(collectionService, tenantId);
    }
    /**
     * Create collections with automatic tenant_id injection
     */
    async createCollections(collectionsData) {
        console.log(`📂 [TENANT-COLLECTION] Creating ${collectionsData.length} collections for tenant: ${this.tenantId}`);
        try {
            // Inject tenant_id into all collection data
            const tenantCollectionsData = collectionsData.map(collectionData => ({
                ...collectionData,
                tenant_id: this.tenantId,
            }));
            const result = await this.collectionService.createCollections(tenantCollectionsData);
            console.log(`✅ [TENANT-COLLECTION] Created ${result.length} collections for tenant: ${this.tenantId}`);
            return result;
        }
        catch (error) {
            console.error(`❌ [TENANT-COLLECTION] Error creating collections: ${error}`);
            throw error;
        }
    }
    /**
     * List collections with tenant awareness
     */
    async listCollections(filters = {}, config, context) {
        console.log(`📂 [TENANT-COLLECTION] Listing collections for tenant: ${this.tenantId}`);
        try {
            // Set database tenant context for RLS
            await this.setDatabaseTenantContext();
            // Use the service method directly - RLS will handle filtering
            const result = await this.collectionService.listCollections(filters, config, context);
            const collectionCount = Array.isArray(result)
                ? result.length
                : result?.collections?.length || 0;
            console.log(`✅ [TENANT-COLLECTION] Found ${collectionCount} collections for tenant: ${this.tenantId}`);
            return result;
        }
        catch (error) {
            console.error(`❌ [TENANT-COLLECTION] Error listing collections: ${error}`);
            // Return empty result structure
            return {
                collections: [],
                count: 0,
                offset: config?.skip || 0,
                limit: config?.take || 20,
            };
        }
    }
    /**
     * List and count collections with tenant awareness
     */
    async listAndCountCollections(filters = {}, config, context) {
        console.log(`📂 [TENANT-COLLECTION] Listing and counting collections for tenant: ${this.tenantId}`);
        try {
            // Use direct database query to ensure proper tenant filtering
            console.log(`🔍 [TENANT-COLLECTION] Using direct database query for tenant: ${this.tenantId}`);
            const [collections, count] = await this.listCollectionsDirectQuery(filters, config);
            console.log(`✅ [TENANT-COLLECTION] Found ${count} total collections for tenant: ${this.tenantId}`);
            return [collections, count];
        }
        catch (error) {
            console.error(`❌ [TENANT-COLLECTION] Error listing and counting collections: ${error}`);
            return [[], 0];
        }
    }
    /**
     * Retrieve a single collection by ID (with tenant validation)
     */
    async retrieveCollection(collectionId, config, context) {
        console.log(`📂 [TENANT-COLLECTION] Retrieving collection ${collectionId} for tenant: ${this.tenantId}`);
        try {
            // Use direct database query to get the collection
            const [collections] = await this.listCollectionsDirectQuery({ id: collectionId }, config);
            if (!collections || collections.length === 0) {
                console.log(`❌ [TENANT-COLLECTION] Collection ${collectionId} not found for tenant: ${this.tenantId}`);
                return null;
            }
            const collection = collections[0];
            console.log(`✅ [TENANT-COLLECTION] Retrieved collection ${collectionId} (${collection.title}) for tenant: ${this.tenantId}`);
            return collection;
        }
        catch (error) {
            console.error(`❌ [TENANT-COLLECTION] Error retrieving collection ${collectionId}: ${error}`);
            throw error;
        }
    }
    /**
     * Update collections with tenant validation
     */
    async updateCollections(collectionsData) {
        console.log(`📂 [TENANT-COLLECTION] Updating ${collectionsData.length} collections for tenant: ${this.tenantId}`);
        try {
            // Set database tenant context for RLS
            await this.setDatabaseTenantContext();
            // Ensure tenant_id is preserved in updates
            const tenantCollectionsData = collectionsData.map(collectionData => ({
                ...collectionData,
                tenant_id: this.tenantId, // Ensure tenant_id is not changed
            }));
            const result = await this.collectionService.updateCollections(tenantCollectionsData);
            console.log(`✅ [TENANT-COLLECTION] Updated ${result.length} collections for tenant: ${this.tenantId}`);
            return result;
        }
        catch (error) {
            console.error(`❌ [TENANT-COLLECTION] Error updating collections: ${error}`);
            throw error;
        }
    }
    /**
     * Delete collections with tenant validation
     */
    async deleteCollections(collectionIds) {
        console.log(`📂 [TENANT-COLLECTION] Deleting ${collectionIds.length} collections for tenant: ${this.tenantId}`);
        try {
            // Set database tenant context for RLS
            await this.setDatabaseTenantContext();
            const result = await this.collectionService.deleteCollections(collectionIds);
            console.log(`✅ [TENANT-COLLECTION] Deleted ${collectionIds.length} collections for tenant: ${this.tenantId}`);
            return result;
        }
        catch (error) {
            console.error(`❌ [TENANT-COLLECTION] Error deleting collections: ${error}`);
            throw error;
        }
    }
    /**
     * Get collection statistics for tenant
     */
    async getCollectionStats() {
        console.log(`📊 [TENANT-COLLECTION] Getting collection statistics for tenant: ${this.tenantId}`);
        try {
            // Set database tenant context for RLS
            await this.setDatabaseTenantContext();
            const collections = await this.listCollections({}, { take: 1000 });
            const collectionArray = Array.isArray(collections)
                ? collections
                : collections?.collections || [];
            const stats = {
                total_collections: collectionArray.length,
                tenant_id: this.tenantId,
                timestamp: new Date().toISOString(),
            };
            console.log(`✅ [TENANT-COLLECTION] Collection stats for tenant ${this.tenantId}:`, stats);
            return stats;
        }
        catch (error) {
            console.error(`❌ [TENANT-COLLECTION] Error getting collection stats: ${error}`);
            return {
                total_collections: 0,
                tenant_id: this.tenantId,
                error: error.message,
                timestamp: new Date().toISOString(),
            };
        }
    }
    /**
     * Direct database query for collections with tenant filtering
     */
    async listCollectionsDirectQuery(filters = {}, config) {
        // Ensure database connection is available
        if (!this.pgConnection) {
            console.log(`🔗 [TENANT-COLLECTION] Creating database connection for direct query`);
            try {
                const { Client } = require('pg');
                this.pgConnection = new Client({
                    connectionString: process.env.DATABASE_URL ||
                        process.env.POSTGRES_URL ||
                        'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
                });
                await this.pgConnection.connect();
                console.log(`✅ [TENANT-COLLECTION] Database connection established for tenant: ${this.tenantId}`);
            }
            catch (error) {
                console.error(`❌ [TENANT-COLLECTION] Failed to create database connection: ${error}`);
                return [[], 0];
            }
        }
        try {
            // Build WHERE clause from filters
            const whereConditions = [];
            const queryParams = [];
            let paramIndex = 1;
            // ALWAYS add tenant_id filter for security (manual fallback if RLS fails)
            whereConditions.push(`tenant_id = $${paramIndex++}`);
            queryParams.push(this.tenantId);
            // ALWAYS filter out soft-deleted records (using string interpolation to avoid prepared statement issues)
            whereConditions.push(`deleted_at IS NULL`);
            // Add basic filters
            if (filters.id) {
                whereConditions.push(`id = $${paramIndex++}`);
                queryParams.push(filters.id);
            }
            if (filters.title) {
                whereConditions.push(`title ILIKE $${paramIndex++}`);
                queryParams.push(`%${filters.title}%`);
            }
            if (filters.handle) {
                whereConditions.push(`handle = $${paramIndex++}`);
                queryParams.push(filters.handle);
            }
            // Build query to get collections
            let query = 'SELECT * FROM product_collection';
            if (whereConditions.length > 0) {
                query += ' WHERE ' + whereConditions.join(' AND ');
            }
            // Add ordering and limits
            query += ' ORDER BY created_at DESC';
            if (config?.take) {
                query += ` LIMIT $${paramIndex++}`;
                queryParams.push(config.take);
            }
            if (config?.skip) {
                query += ` OFFSET $${paramIndex++}`;
                queryParams.push(config.skip);
            }
            console.log(`🔍 [TENANT-COLLECTION] Executing direct query for tenant ${this.tenantId}:`, query);
            console.log(`🔍 [TENANT-COLLECTION] Query parameters:`, queryParams);
            // Try using a raw query to avoid prepared statement caching issues
            const rawQuery = query.replace(/\$(\d+)/g, (match, num) => {
                const paramIndex = parseInt(num) - 1;
                const value = queryParams[paramIndex];
                return typeof value === 'string' ? `'${value.replace(/'/g, "''")}'` : value;
            });
            console.log(`🔍 [TENANT-COLLECTION] Raw query:`, rawQuery);
            const result = await this.pgConnection.query(rawQuery);
            const collections = result.rows;
            // Get total count
            let countQuery = 'SELECT COUNT(*) as total FROM product_collection';
            if (whereConditions.length > 0) {
                countQuery += ' WHERE ' + whereConditions.join(' AND ');
            }
            // Use raw query for count as well to avoid prepared statement issues
            const rawCountQuery = countQuery.replace(/\$(\d+)/g, (match, num) => {
                const paramIndex = parseInt(num) - 1;
                const value = queryParams[paramIndex];
                return typeof value === 'string' ? `'${value.replace(/'/g, "''")}'` : value;
            });
            console.log(`🔍 [TENANT-COLLECTION] Raw count query:`, rawCountQuery);
            const countResult = await this.pgConnection.query(rawCountQuery);
            const total = parseInt(countResult.rows[0].total);
            console.log(`✅ [TENANT-COLLECTION] Direct query found ${collections.length} collections (${total} total) for tenant: ${this.tenantId}`);
            return [collections, total];
        }
        catch (error) {
            console.error(`❌ [TENANT-COLLECTION] Direct query failed: ${error}`);
            throw error;
        }
    }
    /**
     * Cleanup resources
     */
    async cleanup() {
        try {
            if (this.pgConnection) {
                await this.pgConnection.end();
                this.pgConnection = null;
            }
            console.log(`🧹 [TENANT-COLLECTION] Cleaned up resources for tenant: ${this.tenantId}`);
        }
        catch (error) {
            console.error(`❌ [TENANT-COLLECTION] Error during cleanup: ${error}`);
        }
    }
}
exports.TenantAwareCollectionService = TenantAwareCollectionService;
//# sourceMappingURL=data:application/json;base64,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