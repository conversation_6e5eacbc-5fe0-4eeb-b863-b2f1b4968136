"use strict";
/**
 * Tenant Service Factory
 *
 * Provides easy access to all tenant-aware service wrappers.
 * Automatically creates tenant-aware instances from request context.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TenantServiceFactory = void 0;
const tenant_aware_product_1 = require("./tenant-aware-product");
const tenant_aware_customer_1 = require("./tenant-aware-customer");
const tenant_aware_order_1 = require("./tenant-aware-order");
const tenant_aware_cart_1 = require("./tenant-aware-cart");
const tenant_aware_product_category_1 = require("./tenant-aware-product-category");
const tenant_aware_collection_1 = require("./tenant-aware-collection");
const tenant_aware_tag_1 = require("./tenant-aware-tag");
class TenantServiceFactory {
    /**
     * Create all tenant-aware services from request context
     */
    static fromRequest(req) {
        const tenantId = req.tenant_id || 'default';
        console.log(`🏭 [TENANT-FACTORY] Creating tenant services for tenant: ${tenantId}`);
        const services = {
            product: tenant_aware_product_1.TenantAwareProductService.fromRequest(req),
            customer: tenant_aware_customer_1.TenantAwareCustomerService.fromRequest(req),
            order: tenant_aware_order_1.TenantAwareOrderService.fromRequest(req),
            cart: tenant_aware_cart_1.TenantAwareCartService.fromRequest(req),
            productCategory: tenant_aware_product_category_1.TenantAwareProductCategoryService.fromRequest(req),
            collection: tenant_aware_collection_1.TenantAwareCollectionService.fromRequest(req),
            tagService: new tenant_aware_tag_1.TenantAwareTagService(null, tenantId),
            tenantId,
            getTenantId: () => tenantId,
        };
        console.log(`✅ [TENANT-FACTORY] Created tenant services for tenant: ${tenantId}`);
        return services;
    }
    /**
     * Create tenant-aware services with explicit tenant ID
     */
    static create(req, tenantId) {
        console.log(`🏭 [TENANT-FACTORY] Creating tenant services for explicit tenant: ${tenantId}`);
        // Temporarily set tenant_id on request for service creation
        const originalTenantId = req.tenant_id;
        req.tenant_id = tenantId;
        const services = this.fromRequest(req);
        // Restore original tenant_id
        req.tenant_id = originalTenantId;
        console.log(`✅ [TENANT-FACTORY] Created tenant services for explicit tenant: ${tenantId}`);
        return services;
    }
    /**
     * Get individual tenant-aware service
     */
    static getProductService(req) {
        return tenant_aware_product_1.TenantAwareProductService.fromRequest(req);
    }
    static getCustomerService(req) {
        return tenant_aware_customer_1.TenantAwareCustomerService.fromRequest(req);
    }
    static getOrderService(req) {
        return tenant_aware_order_1.TenantAwareOrderService.fromRequest(req);
    }
    /**
     * Validate tenant context in request
     */
    static validateTenantContext(req) {
        const errors = [];
        const tenantId = req.tenant_id || 'default';
        // Basic tenant ID validation
        if (!tenantId) {
            errors.push('Tenant ID is missing');
        }
        else if (typeof tenantId !== 'string') {
            errors.push('Tenant ID must be a string');
        }
        else if (tenantId.length === 0) {
            errors.push('Tenant ID cannot be empty');
        }
        else if (tenantId.length > 50) {
            errors.push('Tenant ID cannot exceed 50 characters');
        }
        else if (!/^[a-zA-Z0-9_-]+$/.test(tenantId)) {
            errors.push('Tenant ID can only contain alphanumeric characters, hyphens, and underscores');
        }
        // Check if container is available
        if (!req.scope) {
            errors.push('Request scope/container is not available');
        }
        const isValid = errors.length === 0;
        if (!isValid) {
            console.warn(`⚠️  [TENANT-FACTORY] Tenant context validation failed for ${tenantId}:`, errors);
        }
        else {
            console.log(`✅ [TENANT-FACTORY] Tenant context validation passed for ${tenantId}`);
        }
        return { isValid, tenantId, errors };
    }
    /**
     * Get tenant statistics across all services
     */
    static async getTenantStatistics(req) {
        const tenantId = req.tenant_id || 'default';
        console.log(`📊 [TENANT-FACTORY] Getting comprehensive statistics for tenant: ${tenantId}`);
        try {
            const services = this.fromRequest(req);
            // Get statistics from all services
            const [productStats, customerStats, orderStats] = await Promise.all([
                services.product.getProductStats().catch(error => {
                    console.error(`❌ [TENANT-FACTORY] Error getting product stats: ${error}`);
                    return { error: error.message };
                }),
                services.customer.getCustomerStats().catch(error => {
                    console.error(`❌ [TENANT-FACTORY] Error getting customer stats: ${error}`);
                    return { error: error.message };
                }),
                services.order.getOrderStats().catch(error => {
                    console.error(`❌ [TENANT-FACTORY] Error getting order stats: ${error}`);
                    return { error: error.message };
                }),
            ]);
            const statistics = {
                tenantId,
                products: productStats,
                customers: customerStats,
                orders: orderStats,
                timestamp: new Date().toISOString(),
            };
            console.log(`📊 [TENANT-FACTORY] Statistics for tenant ${tenantId}:`, statistics);
            return statistics;
        }
        catch (error) {
            console.error(`❌ [TENANT-FACTORY] Error getting tenant statistics: ${error}`);
            throw error;
        }
    }
    /**
     * Test all tenant services connectivity
     */
    static async testTenantServices(req) {
        const tenantId = req.tenant_id || 'default';
        console.log(`🧪 [TENANT-FACTORY] Testing tenant services for tenant: ${tenantId}`);
        const results = {
            tenantId,
            services: {
                product: { available: false },
                customer: { available: false },
                order: { available: false },
            },
            overall: { healthy: false, errors: [] },
        };
        try {
            // Test product service
            try {
                const productService = this.getProductService(req);
                await productService.listProducts({}, { take: 1 });
                results.services.product.available = true;
                console.log(`✅ [TENANT-FACTORY] Product service test passed for tenant: ${tenantId}`);
            }
            catch (error) {
                results.services.product.error = error instanceof Error ? error.message : 'Unknown error';
                results.overall.errors.push(`Product service: ${results.services.product.error}`);
                console.error(`❌ [TENANT-FACTORY] Product service test failed: ${error}`);
            }
            // Test customer service
            try {
                const customerService = this.getCustomerService(req);
                await customerService.listCustomers({}, { take: 1 });
                results.services.customer.available = true;
                console.log(`✅ [TENANT-FACTORY] Customer service test passed for tenant: ${tenantId}`);
            }
            catch (error) {
                results.services.customer.error = error instanceof Error ? error.message : 'Unknown error';
                results.overall.errors.push(`Customer service: ${results.services.customer.error}`);
                console.error(`❌ [TENANT-FACTORY] Customer service test failed: ${error}`);
            }
            // Test order service
            try {
                const orderService = this.getOrderService(req);
                await orderService.listOrders({}, { take: 1 });
                results.services.order.available = true;
                console.log(`✅ [TENANT-FACTORY] Order service test passed for tenant: ${tenantId}`);
            }
            catch (error) {
                results.services.order.error = error instanceof Error ? error.message : 'Unknown error';
                results.overall.errors.push(`Order service: ${results.services.order.error}`);
                console.error(`❌ [TENANT-FACTORY] Order service test failed: ${error}`);
            }
            // Determine overall health
            const availableServices = Object.values(results.services).filter(service => service.available).length;
            results.overall.healthy = availableServices === 3; // All services must be available
            if (results.overall.healthy) {
                console.log(`🎉 [TENANT-FACTORY] All tenant services healthy for tenant: ${tenantId}`);
            }
            else {
                console.warn(`⚠️  [TENANT-FACTORY] Some tenant services unhealthy for tenant: ${tenantId}`);
            }
            return results;
        }
        catch (error) {
            console.error(`❌ [TENANT-FACTORY] Error testing tenant services: ${error}`);
            results.overall.errors.push(`General error: ${error instanceof Error ? error.message : 'Unknown error'}`);
            return results;
        }
    }
    /**
     * Create tenant-aware service with custom configuration
     */
    static createCustomService(req, serviceCreator, tenantId) {
        const effectiveTenantId = tenantId || req.tenant_id || 'default';
        console.log(`🔧 [TENANT-FACTORY] Creating custom tenant service for tenant: ${effectiveTenantId}`);
        // Temporarily set tenant_id if provided
        const originalTenantId = req.tenant_id;
        if (tenantId) {
            req.tenant_id = tenantId;
        }
        try {
            const service = serviceCreator(req);
            console.log(`✅ [TENANT-FACTORY] Created custom tenant service for tenant: ${effectiveTenantId}`);
            return service;
        }
        finally {
            // Restore original tenant_id
            req.tenant_id = originalTenantId;
        }
    }
}
exports.TenantServiceFactory = TenantServiceFactory;
//# sourceMappingURL=data:application/json;base64,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