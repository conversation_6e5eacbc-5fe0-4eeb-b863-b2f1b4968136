"use strict";
/**
 * Tenant-Aware Order Service
 *
 * Wraps the core Medusa OrderModuleService to automatically inject
 * tenant context into all order operations.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TenantAwareOrderService = void 0;
class TenantAwareOrderService {
    constructor(orderService, tenantId = 'default') {
        this.orderService = orderService;
        this.tenantId = tenantId;
    }
    /**
     * Create a tenant-aware order service instance from request
     */
    static fromRequest(req) {
        const orderService = req.scope.resolve('order');
        const tenantId = req.tenant_id || 'default';
        console.log(`🏢 [TENANT-ORDER] Creating tenant-aware order service for tenant: ${tenantId}`);
        return new TenantAwareOrderService(orderService, tenantId);
    }
    /**
     * Inject tenant_id into order data
     */
    injectTenantId(data) {
        if (Array.isArray(data)) {
            return data.map(item => ({ ...item, tenant_id: this.tenantId }));
        }
        return { ...data, tenant_id: this.tenantId };
    }
    /**
     * Add tenant filter to query filters
     */
    addTenantFilter(filters = {}) {
        return { ...filters, tenant_id: this.tenantId };
    }
    // ============================================================================
    // ORDER CRUD OPERATIONS WITH TENANT CONTEXT
    // ============================================================================
    /**
     * Create orders with automatic tenant injection
     */
    async createOrders(data, context) {
        console.log(`🏢 [TENANT-ORDER] Creating ${data.length} orders for tenant: ${this.tenantId}`);
        const tenantData = this.injectTenantId(data);
        const result = await this.orderService.createOrders(tenantData, context);
        console.log(`✅ [TENANT-ORDER] Created ${result.length} orders for tenant: ${this.tenantId}`);
        return result;
    }
    /**
     * Update orders with tenant validation
     */
    async updateOrders(selector, data, context) {
        console.log(`🏢 [TENANT-ORDER] Updating orders for tenant: ${this.tenantId}`);
        // Add tenant filter to selector to ensure we only update tenant's orders
        const tenantSelector = this.addTenantFilter(selector);
        const result = await this.orderService.updateOrders(tenantSelector, data, context);
        console.log(`✅ [TENANT-ORDER] Updated orders for tenant: ${this.tenantId}`);
        return result;
    }
    /**
     * List orders with automatic tenant filtering
     */
    async listOrders(filters = {}, config, context) {
        console.log(`🏢 [TENANT-ORDER] Listing orders for tenant: ${this.tenantId}`);
        const tenantFilters = this.addTenantFilter(filters);
        const result = await this.orderService.listOrders(tenantFilters, config, context);
        console.log(`✅ [TENANT-ORDER] Found ${result.length} orders for tenant: ${this.tenantId}`);
        return result;
    }
    /**
     * List and count orders with automatic tenant filtering
     */
    async listAndCountOrders(filters = {}, config, context) {
        console.log(`🏢 [TENANT-ORDER] Listing and counting orders for tenant: ${this.tenantId}`);
        const tenantFilters = this.addTenantFilter(filters);
        const result = await this.orderService.listAndCountOrders(tenantFilters, config, context);
        console.log(`✅ [TENANT-ORDER] Found ${result[1]} total orders for tenant: ${this.tenantId}`);
        return result;
    }
    /**
     * Retrieve single order with tenant validation
     */
    async retrieveOrder(id, config, context) {
        console.log(`🏢 [TENANT-ORDER] Retrieving order ${id} for tenant: ${this.tenantId}`);
        // First check if order belongs to tenant
        const orders = await this.listOrders({ id }, { take: 1 }, context);
        if (orders.length === 0) {
            console.warn(`⚠️  [TENANT-ORDER] Order ${id} not found for tenant: ${this.tenantId}`);
            throw new Error(`Order ${id} not found for tenant ${this.tenantId}`);
        }
        const result = await this.orderService.retrieveOrder(id, config, context);
        console.log(`✅ [TENANT-ORDER] Retrieved order ${id} for tenant: ${this.tenantId}`);
        return result;
    }
    // ============================================================================
    // ORDER BUSINESS OPERATIONS WITH TENANT CONTEXT
    // ============================================================================
    /**
     * Complete order with tenant validation
     */
    async completeOrder(orderId, context) {
        console.log(`🏢 [TENANT-ORDER] Completing order ${orderId} for tenant: ${this.tenantId}`);
        // Validate order belongs to tenant
        const orderExists = await this.validateOrderOwnership(orderId);
        if (!orderExists) {
            throw new Error(`Order ${orderId} not found for tenant ${this.tenantId}`);
        }
        const result = await this.orderService.completeOrder(orderId, context);
        console.log(`✅ [TENANT-ORDER] Completed order ${orderId} for tenant: ${this.tenantId}`);
        return result;
    }
    /**
     * Cancel order with tenant validation
     */
    async cancel(orderId, context) {
        console.log(`🏢 [TENANT-ORDER] Canceling order ${orderId} for tenant: ${this.tenantId}`);
        // Validate order belongs to tenant
        const orderExists = await this.validateOrderOwnership(orderId);
        if (!orderExists) {
            throw new Error(`Order ${orderId} not found for tenant ${this.tenantId}`);
        }
        const result = await this.orderService.cancel(orderId, context);
        console.log(`✅ [TENANT-ORDER] Canceled order ${orderId} for tenant: ${this.tenantId}`);
        return result;
    }
    /**
     * Archive order with tenant validation
     */
    async archive(orderId, context) {
        console.log(`🏢 [TENANT-ORDER] Archiving order ${orderId} for tenant: ${this.tenantId}`);
        // Validate order belongs to tenant
        const orderExists = await this.validateOrderOwnership(orderId);
        if (!orderExists) {
            throw new Error(`Order ${orderId} not found for tenant ${this.tenantId}`);
        }
        const result = await this.orderService.archive(orderId, context);
        console.log(`✅ [TENANT-ORDER] Archived order ${orderId} for tenant: ${this.tenantId}`);
        return result;
    }
    // ============================================================================
    // UTILITY METHODS
    // ============================================================================
    /**
     * Get tenant ID for this service instance
     */
    getTenantId() {
        return this.tenantId;
    }
    /**
     * Get underlying order service
     */
    getOrderService() {
        return this.orderService;
    }
    /**
     * Validate order ownership by tenant
     */
    async validateOrderOwnership(orderId) {
        try {
            const orders = await this.listOrders({ id: orderId }, { take: 1 });
            return orders.length > 0;
        }
        catch (error) {
            console.error(`❌ [TENANT-ORDER] Error validating order ownership: ${error}`);
            return false;
        }
    }
    /**
     * Get order statistics for tenant
     */
    async getOrderStats() {
        console.log(`📊 [TENANT-ORDER] Getting order statistics for tenant: ${this.tenantId}`);
        try {
            const [orders, totalCount] = await this.listAndCountOrders();
            // Count orders by status
            const completedOrders = orders.filter((order) => order.status === 'completed').length;
            const canceledOrders = orders.filter((order) => order.status === 'canceled').length;
            const pendingOrders = orders.filter((order) => order.status === 'pending').length;
            const stats = {
                totalOrders: totalCount,
                completedOrders,
                canceledOrders,
                pendingOrders
            };
            console.log(`📊 [TENANT-ORDER] Stats for tenant ${this.tenantId}:`, stats);
            return stats;
        }
        catch (error) {
            console.error(`❌ [TENANT-ORDER] Error getting order stats: ${error}`);
            throw error;
        }
    }
}
exports.TenantAwareOrderService = TenantAwareOrderService;
//# sourceMappingURL=data:application/json;base64,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