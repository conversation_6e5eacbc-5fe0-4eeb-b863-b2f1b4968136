"use strict";
/**
 * Centralized Database Service
 *
 * This service provides a unified interface for all database operations
 * across the application. It ensures proper connection management,
 * tenant isolation, and resource cleanup.
 *
 * USAGE: All API endpoints should use this service instead of creating
 * their own database connections.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.centralizedDb = exports.CentralizedDatabaseService = void 0;
const database_pool_1 = require("../utils/database-pool");
const database_monitor_1 = require("./database-monitor");
class CentralizedDatabaseService {
    constructor() { }
    static getInstance() {
        if (!CentralizedDatabaseService.instance) {
            CentralizedDatabaseService.instance = new CentralizedDatabaseService();
        }
        return CentralizedDatabaseService.instance;
    }
    /**
     * Execute a query with tenant context and proper error handling
     */
    async query(sql, params = [], options = {}) {
        const { tenantId, timeout = 30000, retries = 2 } = options;
        let attempt = 0;
        let lastError = null;
        while (attempt <= retries) {
            try {
                const startTime = Date.now();
                // Get connection from the centralized pool
                const client = await database_pool_1.dbPool.getConnection();
                try {
                    // Set tenant context if provided
                    if (tenantId) {
                        await this.setTenantContext(client, tenantId);
                    }
                    // Execute the query with timeout
                    const result = await Promise.race([
                        client.query(sql, params),
                        this.createTimeoutPromise(timeout),
                    ]);
                    const duration = Date.now() - startTime;
                    console.log(`✅ [CENTRALIZED-DB] Query executed successfully`, {
                        tenantId,
                        duration: `${duration}ms`,
                        rows: result.rowCount,
                        attempt: attempt + 1,
                    });
                    // Record successful request for monitoring
                    database_monitor_1.dbMonitor.recordRequest(duration, false);
                    return {
                        rows: result.rows,
                        rowCount: result.rowCount || 0,
                        duration,
                    };
                }
                finally {
                    // Always release the connection
                    client.release();
                }
            }
            catch (error) {
                attempt++;
                lastError = error instanceof Error ? error : new Error(String(error));
                const errorDuration = Date.now() - startTime;
                console.error(`❌ [CENTRALIZED-DB] Query failed (attempt ${attempt}/${retries + 1}):`, {
                    error: lastError.message,
                    tenantId,
                    sql: sql.substring(0, 100),
                });
                // Record failed request for monitoring
                database_monitor_1.dbMonitor.recordRequest(errorDuration, true);
                // If this was the last attempt, throw the error
                if (attempt > retries) {
                    break;
                }
                // Wait before retrying (exponential backoff)
                await this.delay(Math.pow(2, attempt) * 1000);
            }
        }
        throw lastError || new Error('Query failed after all retries');
    }
    /**
     * Execute a transaction with proper error handling and cleanup
     */
    async transaction(callback, options = {}) {
        const { tenantId, timeout = 60000 } = options;
        const client = await database_pool_1.dbPool.getConnection();
        try {
            // Set tenant context if provided
            if (tenantId) {
                await this.setTenantContext(client, tenantId);
            }
            // Start transaction
            await client.query('BEGIN');
            console.log(`🔄 [CENTRALIZED-DB] Transaction started for tenant: ${tenantId || 'default'}`);
            // Execute callback with timeout
            const result = await Promise.race([callback(client), this.createTimeoutPromise(timeout)]);
            // Commit transaction
            await client.query('COMMIT');
            console.log(`✅ [CENTRALIZED-DB] Transaction committed for tenant: ${tenantId || 'default'}`);
            return result;
        }
        catch (error) {
            // Rollback transaction
            try {
                await client.query('ROLLBACK');
                console.log(`🔄 [CENTRALIZED-DB] Transaction rolled back for tenant: ${tenantId || 'default'}`);
            }
            catch (rollbackError) {
                console.error('❌ [CENTRALIZED-DB] Error during rollback:', rollbackError);
            }
            console.error(`❌ [CENTRALIZED-DB] Transaction failed for tenant: ${tenantId || 'default'}:`, error);
            throw error;
        }
        finally {
            // Always release the connection
            client.release();
        }
    }
    /**
     * Get products with tenant filtering
     */
    async getProducts(tenantId, filters = {}, pagination = {}) {
        const { limit = 50, offset = 0 } = pagination;
        let whereClause = 'WHERE tenant_id = $1';
        let params = [tenantId];
        let paramIndex = 2;
        // Add additional filters
        if (filters.category_id) {
            whereClause += ` AND id IN (
        SELECT product_id FROM product_category 
        WHERE category_id = $${paramIndex}
      )`;
            params.push(filters.category_id);
            paramIndex++;
        }
        if (filters.status) {
            whereClause += ` AND status = $${paramIndex}`;
            params.push(filters.status);
            paramIndex++;
        }
        const sql = `
      SELECT 
        p.id, p.title, p.subtitle, p.description, p.handle,
        p.status, p.created_at, p.updated_at, p.tenant_id,
        p.metadata, p.weight, p.length, p.height, p.width,
        p.origin_country, p.material, p.mid_code, p.hs_code
      FROM product p
      ${whereClause}
      ORDER BY p.created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
        params.push(limit, offset);
        return this.query(sql, params, { tenantId });
    }
    /**
     * Get customers with tenant filtering
     */
    async getCustomers(tenantId, pagination = {}) {
        const { limit = 50, offset = 0 } = pagination;
        const sql = `
      SELECT 
        id, email, first_name, last_name, phone,
        created_at, updated_at, tenant_id, metadata
      FROM customer 
      WHERE tenant_id = $1 
      ORDER BY created_at DESC 
      LIMIT $2 OFFSET $3
    `;
        return this.query(sql, [tenantId, limit, offset], { tenantId });
    }
    /**
     * Get count of records for a table with tenant filtering
     */
    async getCount(table, tenantId, additionalWhere = '') {
        const whereClause = additionalWhere
            ? `WHERE tenant_id = $1 AND ${additionalWhere}`
            : 'WHERE tenant_id = $1';
        const sql = `SELECT COUNT(*) as total FROM ${table} ${whereClause}`;
        const result = await this.query(sql, [tenantId], { tenantId });
        return parseInt(result.rows[0]?.total || '0');
    }
    /**
     * Set tenant context for RLS policies
     */
    async setTenantContext(client, tenantId) {
        try {
            await client.query('SELECT set_config($1, $2, true)', ['app.current_tenant_id', tenantId]);
            console.log(`🔐 [CENTRALIZED-DB] Tenant context set: ${tenantId}`);
        }
        catch (error) {
            console.warn(`⚠️ [CENTRALIZED-DB] Failed to set tenant context: ${error}`);
            // Don't throw here as RLS might not be enabled
        }
    }
    /**
     * Create a timeout promise for query timeouts
     */
    createTimeoutPromise(timeoutMs) {
        return new Promise((_, reject) => {
            setTimeout(() => {
                reject(new Error(`Query timeout after ${timeoutMs}ms`));
            }, timeoutMs);
        });
    }
    /**
     * Delay utility for retries
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    /**
     * Health check for the database service
     */
    async healthCheck() {
        try {
            const result = await this.query('SELECT 1 as health_check');
            return result.rows.length > 0 && result.rows[0].health_check === 1;
        }
        catch (error) {
            console.error('❌ [CENTRALIZED-DB] Health check failed:', error);
            return false;
        }
    }
}
exports.CentralizedDatabaseService = CentralizedDatabaseService;
// Export singleton instance
exports.centralizedDb = CentralizedDatabaseService.getInstance();
//# sourceMappingURL=data:application/json;base64,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