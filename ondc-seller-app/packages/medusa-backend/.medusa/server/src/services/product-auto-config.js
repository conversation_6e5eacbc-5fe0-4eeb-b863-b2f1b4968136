"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductAutoConfigService = void 0;
/**
 * ProductAutoConfigService
 *
 * Handles automatic configuration for products including:
 * - Sales channel assignment
 * - Inventory management settings
 * - Multi-tenant compatibility
 */
class ProductAutoConfigService {
    constructor(container) {
        this.logger_ = container.resolve('logger');
        // Resolve services
        try {
            this.productService_ = container.resolve('productService');
            this.productVariantService_ = container.resolve('productVariantService');
            this.salesChannelService_ = container.resolve('salesChannelService');
            this.inventoryService_ = container.resolve('inventoryService');
            this.stockLocationService_ = container.resolve('stockLocationService');
        }
        catch (error) {
            this.logger_.warn(`🔧 [PRODUCT-AUTO-CONFIG] Some services not available: ${error.message}`);
        }
        // Set default stock location ID and shipping profile ID
        this.defaultStockLocationId_ = process.env.DEFAULT_STOCK_LOCATION_ID || 'sloc_01JZ85WAREHOUSE';
        this.defaultShippingProfileId_ = process.env.DEFAULT_SHIPPING_PROFILE_ID || 'sp_01JZ4VNZYVNWS3976TD64VQ423';
        // Get default sales channel ID from environment
        this.defaultSalesChannelId_ =
            process.env.DEFAULT_SALES_CHANNEL_ID || 'sc_01K33ENQEWWEMQNT0WDXAHMCWD';
        this.logger_.info(`🔧 [PRODUCT-AUTO-CONFIG] Initialized with defaults - Sales Channel: ${this.defaultSalesChannelId_}, Stock Location: ${this.defaultStockLocationId_}, Shipping Profile: ${this.defaultShippingProfileId_}`);
    }
    /**
     * Apply automatic configuration to a product
     * - Assigns to default sales channel
     * - Sets manage_inventory: false for all variants
     */
    async applyAutoConfiguration(productId, tenantId) {
        try {
            this.logger_.info(`🔧 [PRODUCT-AUTO-CONFIG] Applying auto-configuration to product: ${productId}`);
            // Step 1: Assign product to sales channel
            await this.assignToSalesChannel(productId);
            // Step 2: Configure inventory management for all variants
            await this.configureInventoryManagement(productId);
            // Step 3: Setup inventory items and stock levels
            await this.setupInventoryItems(productId);
            // Step 4: Assign shipping profile
            await this.assignShippingProfile(productId);
            this.logger_.info(`✅ [PRODUCT-AUTO-CONFIG] Auto-configuration completed for product: ${productId}`);
        }
        catch (error) {
            this.logger_.error(`❌ [PRODUCT-AUTO-CONFIG] Error applying auto-configuration to product ${productId}:`, error);
            throw error;
        }
    }
    /**
     * Assign product to the default sales channel
     */
    async assignToSalesChannel(productId) {
        try {
            this.logger_.info(`📺 [PRODUCT-AUTO-CONFIG] Assigning product ${productId} to sales channel: ${this.defaultSalesChannelId_}`);
            // Get the product to ensure it exists
            if (this.productService_) {
                const product = await this.productService_.retrieve(productId);
                if (!product) {
                    throw new Error(`Product not found: ${productId}`);
                }
            }
            // Check if sales channel exists and add product
            if (this.salesChannelService_) {
                try {
                    const salesChannel = await this.salesChannelService_.retrieve(this.defaultSalesChannelId_);
                    if (salesChannel) {
                        // Add product to sales channel
                        await this.salesChannelService_.addProducts(this.defaultSalesChannelId_, [productId]);
                    }
                }
                catch (error) {
                    this.logger_.warn(`⚠️ [PRODUCT-AUTO-CONFIG] Sales channel ${this.defaultSalesChannelId_} not found, skipping assignment`);
                    return;
                }
            }
            else {
                this.logger_.warn(`⚠️ [PRODUCT-AUTO-CONFIG] Sales channel service not available, skipping assignment`);
                return;
            }
            this.logger_.info(`✅ [PRODUCT-AUTO-CONFIG] Product ${productId} assigned to sales channel: ${this.defaultSalesChannelId_}`);
        }
        catch (error) {
            this.logger_.error(`❌ [PRODUCT-AUTO-CONFIG] Error assigning product to sales channel:`, error);
            throw error;
        }
    }
    /**
     * Configure inventory management for all product variants
     * Sets manage_inventory: false for all variants
     */
    async configureInventoryManagement(productId) {
        try {
            this.logger_.info(`📦 [PRODUCT-AUTO-CONFIG] Configuring inventory management for product: ${productId}`);
            // Get all variants for the product
            if (this.productVariantService_) {
                const variants = await this.productVariantService_.list({
                    product_id: productId,
                });
                if (!variants || variants.length === 0) {
                    this.logger_.warn(`⚠️ [PRODUCT-AUTO-CONFIG] No variants found for product: ${productId}`);
                    return;
                }
                // Update each variant to disable inventory management
                for (const variant of variants) {
                    await this.productVariantService_.update(variant.id, {
                        manage_inventory: false,
                    });
                    this.logger_.info(`✅ [PRODUCT-AUTO-CONFIG] Disabled inventory management for variant: ${variant.id}`);
                }
                this.logger_.info(`✅ [PRODUCT-AUTO-CONFIG] Inventory management configured for ${variants.length} variants`);
            }
            else {
                this.logger_.warn(`⚠️ [PRODUCT-AUTO-CONFIG] Product variant service not available, skipping inventory configuration`);
            }
            this.logger_.info(`✅ [PRODUCT-AUTO-CONFIG] Disabled inventory management for variant: ${variant.id}`);
        }
        finally {
        }
        this.logger_.info(`✅ [PRODUCT-AUTO-CONFIG] Inventory management configured for ${variants.length} variants`);
    }
    catch(error) {
        this.logger_.error(`❌ [PRODUCT-AUTO-CONFIG] Error configuring inventory management:`, error);
        throw error;
    }
}
exports.ProductAutoConfigService = ProductAutoConfigService;
/**
 * Apply auto-configuration to multiple products (for bulk operations like Excel import)
 */
async;
applyAutoConfigurationBulk(productIds, string[], tenantId ?  : string);
Promise < void  > {
    this: .logger_.info(`🔧 [PRODUCT-AUTO-CONFIG] Applying bulk auto-configuration to ${productIds.length} products`),
    const: results = {
        successful: 0,
        failed: 0,
        errors: [],
    },
    for(, productId, of, productIds) {
        try {
            await this.applyAutoConfiguration(productId, tenantId);
            results.successful++;
        }
        catch (error) {
            results.failed++;
            results.errors.push(`Product ${productId}: ${error.message}`);
            this.logger_.error(`❌ [PRODUCT-AUTO-CONFIG] Failed to configure product ${productId}:`, error);
        }
    },
    this: .logger_.info(`✅ [PRODUCT-AUTO-CONFIG] Bulk configuration completed: ${results.successful} successful, ${results.failed} failed`),
    if(results) { }, : .failed > 0
};
{
    this.logger_.warn(`⚠️ [PRODUCT-AUTO-CONFIG] Bulk configuration errors:`, results.errors);
}
/**
 * Get the default sales channel ID
 */
getDefaultSalesChannelId();
string;
{
    return this.defaultSalesChannelId_;
}
/**
 * Validate that the default sales channel exists
 */
async;
validateDefaultSalesChannel();
Promise < boolean > {
    try: {
        : .salesChannelService_
    }
};
{
    const salesChannel = await this.salesChannelService_.retrieve(this.defaultSalesChannelId_);
    return !!salesChannel;
}
{
    this.logger_.warn(`⚠️ [PRODUCT-AUTO-CONFIG] Sales channel service not available for validation`);
    return false;
}
try { }
catch (error) {
    this.logger_.error(`❌ [PRODUCT-AUTO-CONFIG] Default sales channel validation failed:`, error);
    return false;
}
async;
setupInventoryItems(productId, string);
Promise < void  > {
    try: {
        this: .logger_.info(`📦 [PRODUCT-AUTO-CONFIG] Setting up inventory items for product: ${productId}`),
        : .productVariantService_
    }
};
{
    const variants = await this.productVariantService_.list({
        product_id: productId,
    });
    if (!variants || variants.length === 0) {
        this.logger_.warn(`⚠️ [PRODUCT-AUTO-CONFIG] No variants found for product: ${productId}`);
        return;
    }
    for (const variant of variants) {
        await this.setupVariantInventory(variant);
    }
    this.logger_.info(`✅ [PRODUCT-AUTO-CONFIG] Inventory setup completed for ${variants.length} variants`);
}
{
    this.logger_.warn(`⚠️ [PRODUCT-AUTO-CONFIG] Product variant service not available, skipping inventory setup`);
}
try { }
catch (error) {
    this.logger_.error(`❌ [PRODUCT-AUTO-CONFIG] Error setting up inventory items:`, error);
    throw error;
}
async;
setupVariantInventory(variant, any);
Promise < void  > {
    try: {
        : .inventoryService_
    }
};
{
    this.logger_.warn(`⚠️ [PRODUCT-AUTO-CONFIG] Inventory service not available, using direct database approach`);
    await this.setupVariantInventoryDirect(variant);
    return;
}
// Create inventory item for the variant
const inventoryItemId = `inv_${variant.id.slice(8)}`;
try {
    // Check if inventory item already exists
    const existingItem = await this.inventoryService_.retrieveInventoryItem(inventoryItemId);
    if (existingItem) {
        this.logger_.info(`✅ [PRODUCT-AUTO-CONFIG] Inventory item already exists for variant: ${variant.id}`);
        return;
    }
}
catch (error) {
    // Item doesn't exist, create it
}
// Create inventory item
const inventoryItem = await this.inventoryService_.createInventoryItems([{
        id: inventoryItemId,
        sku: variant.sku || variant.id,
    }]);
// Link inventory item to variant
await this.inventoryService_.createInventoryLevels([{
        inventory_item_id: inventoryItemId,
        location_id: this.defaultStockLocationId_,
        stocked_quantity: 100,
        reserved_quantity: 0,
        incoming_quantity: 0,
    }]);
this.logger_.info(`✅ [PRODUCT-AUTO-CONFIG] Inventory setup completed for variant: ${variant.id}`);
try { }
catch (error) {
    this.logger_.warn(`⚠️ [PRODUCT-AUTO-CONFIG] Failed to setup inventory for variant ${variant.id}, trying direct approach:`, error.message);
    await this.setupVariantInventoryDirect(variant);
}
async;
setupVariantInventoryDirect(variant, any);
Promise < void  > {
    try: {
        const: { Client } = require('pg'),
        const: client = new Client({
            connectionString: process.env.DATABASE_URL
        }),
        await, client, : .connect(),
        const: inventoryItemId = `inv_${variant.id.slice(8)}`,
        // Check if inventory item already exists
        const: existingItem = await client.query('SELECT id FROM inventory_item WHERE id = $1', [inventoryItemId]),
        if(existingItem) { }, : .rows.length === 0
    }
};
{
    // Create inventory item
    await client.query(`
          INSERT INTO inventory_item (id, sku, created_at, updated_at)
          VALUES ($1, $2, NOW(), NOW());
        `, [inventoryItemId, variant.sku || variant.id]);
}
// Check if variant-inventory link already exists
const existingLink = await client.query('SELECT id FROM product_variant_inventory_item WHERE variant_id = $1 AND inventory_item_id = $2', [variant.id, inventoryItemId]);
if (existingLink.rows.length === 0) {
    // Link inventory item to product variant
    const linkId = `pvii_${variant.id.slice(8)}_${inventoryItemId.slice(4)}`;
    await client.query(`
          INSERT INTO product_variant_inventory_item (id, variant_id, inventory_item_id, required_quantity, created_at, updated_at)
          VALUES ($1, $2, $3, 1, NOW(), NOW());
        `, [linkId, variant.id, inventoryItemId]);
}
// Check if inventory level already exists
const existingLevel = await client.query('SELECT inventory_item_id FROM inventory_level WHERE inventory_item_id = $1 AND location_id = $2', [inventoryItemId, this.defaultStockLocationId_]);
if (existingLevel.rows.length === 0) {
    // Create inventory level (stock quantity)
    const levelId = `invlvl_${inventoryItemId.slice(4)}_${this.defaultStockLocationId_.slice(5)}`;
    await client.query(`
          INSERT INTO inventory_level (id, inventory_item_id, location_id, stocked_quantity, reserved_quantity, incoming_quantity, created_at, updated_at)
          VALUES ($1, $2, $3, 100, 0, 0, NOW(), NOW());
        `, [levelId, inventoryItemId, this.defaultStockLocationId_]);
}
await client.end();
this.logger_.info(`✅ [PRODUCT-AUTO-CONFIG] Direct inventory setup completed for variant: ${variant.id}`);
try { }
catch (error) {
    this.logger_.error(`❌ [PRODUCT-AUTO-CONFIG] Direct inventory setup failed for variant ${variant.id}:`, error);
    throw error;
}
async;
assignShippingProfile(productId, string);
Promise < void  > {
    try: {
        this: .logger_.info(`🚚 [PRODUCT-AUTO-CONFIG] Assigning shipping profile to product: ${productId}`),
        // Check if product already has a shipping profile
        const: { Client } = require('pg'),
        const: client = new Client({
            connectionString: process.env.DATABASE_URL
        }),
        await, client, : .connect(),
        const: existingResult = await client.query('SELECT * FROM product_shipping_profile WHERE product_id = $1;', [productId]),
        if(existingResult) { }, : .rows.length === 0
    }
};
{
    // Create the shipping profile association
    const associationId = `psp_${productId.slice(5)}_${this.defaultShippingProfileId_.slice(3)}`;
    await client.query('INSERT INTO product_shipping_profile (id, product_id, shipping_profile_id, created_at, updated_at) VALUES ($1, $2, $3, NOW(), NOW());', [associationId, productId, this.defaultShippingProfileId_]);
    this.logger_.info(`✅ [PRODUCT-AUTO-CONFIG] Shipping profile assigned to product: ${productId}`);
}
{
    this.logger_.info(`✅ [PRODUCT-AUTO-CONFIG] Product already has shipping profile: ${productId}`);
}
await client.end();
try { }
catch (error) {
    this.logger_.error(`❌ [PRODUCT-AUTO-CONFIG] Error assigning shipping profile to product ${productId}:`, error);
    throw error;
}
/**
 * Get the default shipping profile ID
 */
getDefaultShippingProfileId();
string;
{
    return this.defaultShippingProfileId_;
}
/**
 * Validate that the default shipping profile exists
 */
async;
validateDefaultShippingProfile();
Promise < boolean > {
    try: {
        const: { Client } = require('pg'),
        const: client = new Client({
            connectionString: process.env.DATABASE_URL
        }),
        await, client, : .connect(),
        const: result = await client.query('SELECT id FROM shipping_profile WHERE id = $1;', [this.defaultShippingProfileId_]),
        await, client, : .end(),
        return: result.rows.length > 0
    }, catch(error) {
        this.logger_.error(`❌ [PRODUCT-AUTO-CONFIG] Default shipping profile validation failed:`, error);
        return false;
    }
};
exports.default = ProductAutoConfigService;
//# sourceMappingURL=data:application/json;base64,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