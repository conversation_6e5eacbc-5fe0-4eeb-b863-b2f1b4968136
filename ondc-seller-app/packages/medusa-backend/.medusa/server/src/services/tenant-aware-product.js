"use strict";
/**
 * Tenant-Aware Product Service (Updated)
 *
 * Uses direct database queries with RLS instead of trying to modify service schemas.
 * This approach works with the existing Medusa v2 architecture.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TenantAwareProductService = void 0;
const tenant_query_interceptor_1 = require("./tenant-query-interceptor");
class TenantAwareProductService {
    constructor(productService, tenantId = 'default', pgConnection) {
        this.productService = productService;
        this.tenantId = tenantId;
        this.queryInterceptor = new tenant_query_interceptor_1.TenantQueryInterceptor({
            tenantId,
            enableLogging: true,
            strictMode: false,
        });
        this.pgConnection = pgConnection;
        // Set tenant context in database for RLS
        this.setDatabaseTenantContext();
    }
    /**
     * Set tenant context in database for Row Level Security
     */
    async setDatabaseTenantContext() {
        if (this.pgConnection) {
            try {
                await this.pgConnection.query('SELECT set_config($1, $2, true)', [
                    'app.tenant_context.tenant_id',
                    this.tenantId,
                ]);
                console.log(`🔒 [TENANT-PRODUCT] Set database tenant context: ${this.tenantId}`);
            }
            catch (error) {
                console.warn(`⚠️  [TENANT-PRODUCT] Failed to set database tenant context: ${error}`);
            }
        }
    }
    /**
     * Create a tenant-aware product service instance from request
     */
    static fromRequest(req) {
        const productService = req.scope.resolve('product');
        const tenantId = req.tenant_id || 'default';
        // Try to get database connection for RLS context setting
        let pgConnection = null;
        try {
            // Try to get the database manager/connection
            const manager = req.scope.resolve('manager');
            if (manager && typeof manager.query === 'function') {
                pgConnection = manager;
                console.log(`✅ [TENANT-PRODUCT] Found database connection via manager`);
            }
            else {
                // Try other connection names
                const possibleConnections = ['__pg_connection__', 'dbConnection', 'database'];
                for (const connectionName of possibleConnections) {
                    try {
                        const connection = req.scope.resolve(connectionName);
                        if (connection && typeof connection.query === 'function') {
                            pgConnection = connection;
                            console.log(`✅ [TENANT-PRODUCT] Found database connection: ${connectionName}`);
                            break;
                        }
                    }
                    catch (error) {
                        // Continue trying other connection names
                    }
                }
            }
            if (!pgConnection) {
                console.warn(`⚠️  [TENANT-PRODUCT] No database connection found, creating direct connection`);
                // Create direct database connection
                try {
                    const { Client } = require('pg');
                    pgConnection = new Client({
                        connectionString: process.env.DATABASE_URL ||
                            process.env.POSTGRES_URL ||
                            'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
                    });
                    // Connect asynchronously without blocking
                    pgConnection
                        .connect()
                        .then(() => {
                        console.log(`✅ [TENANT-PRODUCT] Created direct database connection for tenant: ${tenantId}`);
                    })
                        .catch((dbError) => {
                        console.error(`❌ [TENANT-PRODUCT] Failed to connect to database: ${dbError}`);
                    });
                }
                catch (dbError) {
                    console.error(`❌ [TENANT-PRODUCT] Failed to create direct database connection: ${dbError}`);
                    pgConnection = null;
                }
            }
        }
        catch (error) {
            console.warn(`⚠️  [TENANT-PRODUCT] Error resolving database connection: ${error}`);
        }
        console.log(`🏢 [TENANT-PRODUCT] Creating tenant-aware product service for tenant: ${tenantId}`);
        return new TenantAwareProductService(productService, tenantId, pgConnection);
    }
    /**
     * Inject tenant_id into product data
     */
    injectTenantId(data) {
        if (Array.isArray(data)) {
            return data.map(item => ({ ...item, tenant_id: this.tenantId }));
        }
        return { ...data, tenant_id: this.tenantId };
    }
    /**
     * Add tenant filter to query filters
     */
    addTenantFilter(filters = {}) {
        return { ...filters, tenant_id: this.tenantId };
    }
    // ============================================================================
    // PRODUCT CRUD OPERATIONS WITH TENANT CONTEXT
    // ============================================================================
    /**
     * Create products with automatic tenant injection
     */
    async createProducts(data, context) {
        console.log(`🏢 [TENANT-PRODUCT] Creating ${data.length} products for tenant: ${this.tenantId}`);
        const tenantData = this.injectTenantId(data);
        const result = await this.productService.createProducts(tenantData, context);
        console.log(`✅ [TENANT-PRODUCT] Created ${result.length} products for tenant: ${this.tenantId}`);
        return result;
    }
    /**
     * Update products with tenant validation
     */
    async updateProducts(selector, data, context) {
        console.log(`🏢 [TENANT-PRODUCT] Updating products for tenant: ${this.tenantId}`);
        // Add tenant filter to selector to ensure we only update tenant's products
        const tenantSelector = this.addTenantFilter(selector);
        const result = await this.productService.updateProducts(tenantSelector, data, context);
        console.log(`✅ [TENANT-PRODUCT] Updated products for tenant: ${this.tenantId}`);
        return result;
    }
    /**
     * List products with tenant awareness
     */
    async listProducts(filters = {}, config, context) {
        console.log(`🏢 [TENANT-PRODUCT] Listing products for tenant: ${this.tenantId}`);
        try {
            // Use the service method directly - Medusa v2 handles the filtering
            const result = await this.productService.listProducts(filters, config, context);
            // Log the result
            const productCount = Array.isArray(result) ? result.length : result?.products?.length || 0;
            console.log(`✅ [TENANT-PRODUCT] Found ${productCount} products for tenant: ${this.tenantId}`);
            return result;
        }
        catch (error) {
            console.error(`❌ [TENANT-PRODUCT] Error listing products: ${error}`);
            // Return empty result structure that matches Medusa's expected format
            return {
                products: [],
                count: 0,
                offset: config?.skip || 0,
                limit: config?.take || 20,
            };
        }
    }
    /**
     * List and count products with tenant awareness
     */
    async listAndCountProducts(filters = {}, config, context) {
        console.log(`🏢 [TENANT-PRODUCT] Listing and counting products for tenant: ${this.tenantId}`);
        try {
            // Use direct database query to bypass Medusa ORM issues
            console.log(`🔍 [TENANT-PRODUCT] Using direct database query for tenant: ${this.tenantId}`);
            return await this.listAndCountProductsDirectQuery(filters, config);
        }
        catch (error) {
            console.error(`❌ [TENANT-PRODUCT] Error listing and counting products: ${error}`);
            return [[], 0];
        }
    }
    /**
     * Retrieve single product with tenant validation
     */
    async retrieveProduct(id, config, context) {
        console.log(`🏢 [TENANT-PRODUCT] Retrieving product ${id} for tenant: ${this.tenantId}`);
        // First check if product belongs to tenant
        const products = await this.listProducts({ id }, { take: 1 }, context);
        if (products.length === 0) {
            console.warn(`⚠️  [TENANT-PRODUCT] Product ${id} not found for tenant: ${this.tenantId}`);
            throw new Error(`Product ${id} not found for tenant ${this.tenantId}`);
        }
        const result = await this.productService.retrieveProduct(id, config, context);
        console.log(`✅ [TENANT-PRODUCT] Retrieved product ${id} for tenant: ${this.tenantId}`);
        return result;
    }
    // ============================================================================
    // PRODUCT VARIANT OPERATIONS WITH TENANT CONTEXT
    // ============================================================================
    /**
     * Create product variants with tenant injection
     */
    async createProductVariants(data, context) {
        console.log(`🏢 [TENANT-PRODUCT] Creating ${data.length} product variants for tenant: ${this.tenantId}`);
        const tenantData = this.injectTenantId(data);
        const result = await this.productService.createProductVariants(tenantData, context);
        console.log(`✅ [TENANT-PRODUCT] Created ${result.length} product variants for tenant: ${this.tenantId}`);
        return result;
    }
    /**
     * Update product variants with tenant validation
     */
    async updateProductVariants(selector, data, context) {
        console.log(`🏢 [TENANT-PRODUCT] Updating product variants for tenant: ${this.tenantId}`);
        const tenantSelector = this.addTenantFilter(selector);
        const result = await this.productService.updateProductVariants(tenantSelector, data, context);
        console.log(`✅ [TENANT-PRODUCT] Updated product variants for tenant: ${this.tenantId}`);
        return result;
    }
    // ============================================================================
    // PRODUCT CATEGORY OPERATIONS WITH TENANT CONTEXT
    // ============================================================================
    /**
     * Create product categories with tenant injection
     */
    async createProductCategories(data, context) {
        console.log(`🏢 [TENANT-PRODUCT] Creating ${data.length} product categories for tenant: ${this.tenantId}`);
        const tenantData = this.injectTenantId(data);
        const result = await this.productService.createProductCategories(tenantData, context);
        console.log(`✅ [TENANT-PRODUCT] Created ${result.length} product categories for tenant: ${this.tenantId}`);
        return result;
    }
    /**
     * Update product categories with tenant validation
     */
    async updateProductCategories(selector, data, context) {
        console.log(`🏢 [TENANT-PRODUCT] Updating product categories for tenant: ${this.tenantId}`);
        const tenantSelector = this.addTenantFilter(selector);
        const result = await this.productService.updateProductCategories(tenantSelector, data, context);
        console.log(`✅ [TENANT-PRODUCT] Updated product categories for tenant: ${this.tenantId}`);
        return result;
    }
    // ============================================================================
    // PRODUCT COLLECTION OPERATIONS WITH TENANT CONTEXT
    // ============================================================================
    /**
     * Create product collections with tenant injection
     */
    async createProductCollections(data, context) {
        console.log(`🏢 [TENANT-PRODUCT] Creating ${data.length} product collections for tenant: ${this.tenantId}`);
        const tenantData = this.injectTenantId(data);
        const result = await this.productService.createProductCollections(tenantData, context);
        console.log(`✅ [TENANT-PRODUCT] Created ${result.length} product collections for tenant: ${this.tenantId}`);
        return result;
    }
    /**
     * Update product collections with tenant validation
     */
    async updateProductCollections(selector, data, context) {
        console.log(`🏢 [TENANT-PRODUCT] Updating product collections for tenant: ${this.tenantId}`);
        const tenantSelector = this.addTenantFilter(selector);
        const result = await this.productService.updateProductCollections(tenantSelector, data, context);
        console.log(`✅ [TENANT-PRODUCT] Updated product collections for tenant: ${this.tenantId}`);
        return result;
    }
    // ============================================================================
    // UTILITY METHODS
    // ============================================================================
    /**
     * Get tenant ID for this service instance
     */
    getTenantId() {
        return this.tenantId;
    }
    /**
     * Get underlying product service
     */
    getProductService() {
        return this.productService;
    }
    /**
     * Validate product ownership by tenant
     */
    async validateProductOwnership(productId) {
        try {
            const products = await this.listProducts({ id: productId }, { take: 1 });
            return products.length > 0;
        }
        catch (error) {
            console.error(`❌ [TENANT-PRODUCT] Error validating product ownership: ${error}`);
            return false;
        }
    }
    // ============================================================================
    // DIRECT DATABASE QUERY METHODS (Using RLS)
    // ============================================================================
    /**
     * List products using direct database query with RLS
     */
    async listProductsDirectQuery(filters = {}, config) {
        // Ensure database connection is available
        if (!this.pgConnection) {
            console.log(`🔗 [TENANT-PRODUCT] Creating database connection for direct query`);
            try {
                const { Client } = require('pg');
                this.pgConnection = new Client({
                    connectionString: process.env.DATABASE_URL ||
                        process.env.POSTGRES_URL ||
                        'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
                });
                await this.pgConnection.connect();
                console.log(`✅ [TENANT-PRODUCT] Database connection established for tenant: ${this.tenantId}`);
            }
            catch (error) {
                console.error(`❌ [TENANT-PRODUCT] Failed to create database connection: ${error}`);
                return [];
            }
        }
        try {
            // Set tenant context for RLS
            await this.pgConnection.query('SELECT set_config($1, $2, true)', [
                'app.tenant_context.tenant_id',
                this.tenantId,
            ]);
            // Build WHERE clause from filters
            const whereConditions = [];
            const queryParams = [];
            let paramIndex = 1;
            // ALWAYS add tenant_id filter for security (manual fallback if RLS fails)
            whereConditions.push(`tenant_id = $${paramIndex++}`);
            queryParams.push(this.tenantId);
            // ALWAYS filter out soft-deleted records
            whereConditions.push(`deleted_at IS NULL`);
            // Add basic filters
            if (filters.id) {
                whereConditions.push(`id = $${paramIndex++}`);
                queryParams.push(filters.id);
            }
            if (filters.title) {
                whereConditions.push(`title ILIKE $${paramIndex++}`);
                queryParams.push(`%${filters.title}%`);
            }
            if (filters.status) {
                whereConditions.push(`status = $${paramIndex++}`);
                queryParams.push(filters.status);
            }
            // Add category filtering
            if (filters.category_id) {
                whereConditions.push(`id IN (
          SELECT pcp.product_id
          FROM product_category_product pcp
          WHERE pcp.product_category_id = $${paramIndex++}
        )`);
                queryParams.push(filters.category_id);
            }
            // Build query
            let query = 'SELECT * FROM product';
            if (whereConditions.length > 0) {
                query += ' WHERE ' + whereConditions.join(' AND ');
            }
            // Add ordering and limits
            query += ' ORDER BY created_at DESC';
            if (config?.take) {
                query += ` LIMIT $${paramIndex++}`;
                queryParams.push(config.take);
            }
            if (config?.skip) {
                query += ` OFFSET $${paramIndex++}`;
                queryParams.push(config.skip);
            }
            console.log(`🔍 [TENANT-PRODUCT] Executing direct query for tenant ${this.tenantId}:`, query);
            const result = await this.pgConnection.query(query, queryParams);
            const products = result.rows;
            console.log(`✅ [TENANT-PRODUCT] Direct query found ${products.length} products for tenant: ${this.tenantId}`);
            // If no products found, return empty array
            if (products.length === 0) {
                return products;
            }
            // Load related data for each product
            const enrichedProducts = await this.loadProductRelations(products, config);
            return enrichedProducts;
        }
        catch (error) {
            console.error(`❌ [TENANT-PRODUCT] Direct query failed: ${error}`);
            throw error;
        }
    }
    /**
     * List and count products using direct database query with RLS
     */
    async listAndCountProductsDirectQuery(filters = {}, config) {
        // Ensure database connection is available
        if (!this.pgConnection) {
            console.log(`🔗 [TENANT-PRODUCT] Creating database connection for direct query`);
            try {
                const { Client } = require('pg');
                this.pgConnection = new Client({
                    connectionString: process.env.DATABASE_URL ||
                        process.env.POSTGRES_URL ||
                        'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
                });
                await this.pgConnection.connect();
                console.log(`✅ [TENANT-PRODUCT] Database connection established for tenant: ${this.tenantId}`);
            }
            catch (error) {
                console.error(`❌ [TENANT-PRODUCT] Failed to create database connection: ${error}`);
                return [[], 0];
            }
        }
        try {
            // Set tenant context for RLS
            await this.pgConnection.query('SELECT set_config($1, $2, true)', [
                'app.tenant_context.tenant_id',
                this.tenantId,
            ]);
            // Get products
            const products = await this.listProductsDirectQuery(filters, config);
            // Get count (without limit/offset)
            const whereConditions = [];
            const queryParams = [];
            let paramIndex = 1;
            // ALWAYS add tenant_id filter for security (manual fallback if RLS fails)
            whereConditions.push(`tenant_id = $${paramIndex++}`);
            queryParams.push(this.tenantId);
            // ALWAYS filter out soft-deleted records
            whereConditions.push(`deleted_at IS NULL`);
            if (filters.id) {
                whereConditions.push(`id = $${paramIndex++}`);
                queryParams.push(filters.id);
            }
            if (filters.title) {
                whereConditions.push(`title ILIKE $${paramIndex++}`);
                queryParams.push(`%${filters.title}%`);
            }
            if (filters.status) {
                whereConditions.push(`status = $${paramIndex++}`);
                queryParams.push(filters.status);
            }
            // Add category filtering for count query
            if (filters.category_id) {
                whereConditions.push(`id IN (
          SELECT pcp.product_id
          FROM product_category_product pcp
          WHERE pcp.product_category_id = $${paramIndex++}
        )`);
                queryParams.push(filters.category_id);
            }
            let countQuery = 'SELECT COUNT(*) FROM product';
            if (whereConditions.length > 0) {
                countQuery += ' WHERE ' + whereConditions.join(' AND ');
            }
            const countResult = await this.pgConnection.query(countQuery, queryParams);
            const totalCount = parseInt(countResult.rows[0].count);
            console.log(`✅ [TENANT-PRODUCT] Direct query found ${products.length} products (${totalCount} total) for tenant: ${this.tenantId}`);
            return [products, totalCount];
        }
        catch (error) {
            console.error(`❌ [TENANT-PRODUCT] Direct count query failed: ${error}`);
            throw error;
        }
    }
    /**
     * Get product statistics for tenant
     */
    async getProductStats() {
        console.log(`📊 [TENANT-PRODUCT] Getting product statistics for tenant: ${this.tenantId}`);
        try {
            // Use service-based approach (no direct database queries)
            const [products, count] = await this.listAndCountProducts();
            const stats = {
                totalProducts: count,
                totalVariants: 0, // Would need variant service
                totalCategories: 0, // Would need category service
                totalCollections: 0, // Would need collection service
            };
            console.log(`📊 [TENANT-PRODUCT] Stats for tenant ${this.tenantId}:`, stats);
            return stats;
        }
        catch (error) {
            console.error(`❌ [TENANT-PRODUCT] Error getting product stats: ${error}`);
            throw error;
        }
    }
    /**
     * Load related data for products (variants, images, categories, etc.)
     */
    async loadProductRelations(products, config) {
        if (!products || products.length === 0) {
            return products;
        }
        const productIds = products.map(p => p.id);
        const relations = config?.relations || [];
        console.log(`🔗 [TENANT-PRODUCT] Loading relations for ${products.length} products:`, relations);
        // Load variants if requested
        if (relations.includes('variants') || relations.includes('variants.prices')) {
            await this.loadProductVariants(products, relations.includes('variants.prices'));
        }
        // Load images if requested
        if (relations.includes('images')) {
            await this.loadProductImages(products);
        }
        // Load categories if requested
        if (relations.includes('categories')) {
            await this.loadProductCategories(products);
        }
        // Load tags if requested
        if (relations.includes('tags')) {
            await this.loadProductTags(products);
        }
        return products;
    }
    /**
     * Load variants for products
     */
    async loadProductVariants(products, includePrices = false) {
        const productIds = products.map(p => p.id);
        try {
            // Load variants
            const variantQuery = `
        SELECT * FROM product_variant
        WHERE product_id = ANY($1) AND tenant_id = $2 AND deleted_at IS NULL
        ORDER BY variant_rank ASC, created_at ASC
      `;
            const variantResult = await this.pgConnection.query(variantQuery, [
                productIds,
                this.tenantId,
            ]);
            const variants = variantResult.rows;
            console.log(`🔗 [TENANT-PRODUCT] Loaded ${variants.length} variants for ${products.length} products`);
            // Group variants by product_id
            const variantsByProduct = variants.reduce((acc, variant) => {
                if (!acc[variant.product_id]) {
                    acc[variant.product_id] = [];
                }
                acc[variant.product_id].push(variant);
                return acc;
            }, {});
            // Assign variants to products
            products.forEach(product => {
                product.variants = variantsByProduct[product.id] || [];
            });
            // Load prices if requested
            if (includePrices && variants.length > 0) {
                await this.loadVariantPrices(variants);
            }
        }
        catch (error) {
            console.error(`❌ [TENANT-PRODUCT] Error loading variants: ${error}`);
        }
    }
    /**
     * Load prices for variants
     */
    async loadVariantPrices(variants) {
        const variantIds = variants.map(v => v.id);
        try {
            const priceQuery = `
        SELECT p.*, pvps.variant_id
        FROM price p
        JOIN product_variant_price_set pvps ON p.price_set_id = pvps.price_set_id
        WHERE pvps.variant_id = ANY($1) AND p.tenant_id = $2 AND p.deleted_at IS NULL
        ORDER BY p.currency_code, p.amount
      `;
            const priceResult = await this.pgConnection.query(priceQuery, [variantIds, this.tenantId]);
            const prices = priceResult.rows;
            console.log(`🔗 [TENANT-PRODUCT] Loaded ${prices.length} prices for ${variants.length} variants`);
            // Group prices by variant_id
            const pricesByVariant = prices.reduce((acc, price) => {
                if (!acc[price.variant_id]) {
                    acc[price.variant_id] = [];
                }
                acc[price.variant_id].push(price);
                return acc;
            }, {});
            // Assign prices to variants
            variants.forEach(variant => {
                variant.prices = pricesByVariant[variant.id] || [];
            });
        }
        catch (error) {
            console.error(`❌ [TENANT-PRODUCT] Error loading variant prices: ${error}`);
        }
    }
    /**
     * Load images for products
     */
    async loadProductImages(products) {
        const productIds = products.map(p => p.id);
        try {
            const imageQuery = `
        SELECT * FROM image
        WHERE product_id = ANY($1) AND deleted_at IS NULL
        ORDER BY rank ASC, created_at ASC
      `;
            const imageResult = await this.pgConnection.query(imageQuery, [productIds]);
            const images = imageResult.rows;
            console.log(`🔗 [TENANT-PRODUCT] Loaded ${images.length} images for ${products.length} products`);
            // Group images by product_id
            const imagesByProduct = images.reduce((acc, image) => {
                if (!acc[image.product_id]) {
                    acc[image.product_id] = [];
                }
                acc[image.product_id].push(image);
                return acc;
            }, {});
            // Assign images to products
            products.forEach(product => {
                product.images = imagesByProduct[product.id] || [];
            });
        }
        catch (error) {
            console.error(`❌ [TENANT-PRODUCT] Error loading images: ${error}`);
        }
    }
    /**
     * Load categories for products
     */
    async loadProductCategories(products) {
        const productIds = products.map(p => p.id);
        try {
            const categoryQuery = `
        SELECT pc.*, pcp.product_id
        FROM product_category pc
        JOIN product_category_product pcp ON pc.id = pcp.product_category_id
        WHERE pcp.product_id = ANY($1) AND pc.tenant_id = $2 AND pc.deleted_at IS NULL
        ORDER BY pc.name ASC
      `;
            const categoryResult = await this.pgConnection.query(categoryQuery, [
                productIds,
                this.tenantId,
            ]);
            const categories = categoryResult.rows;
            console.log(`🔗 [TENANT-PRODUCT] Loaded ${categories.length} categories for ${products.length} products`);
            // Group categories by product_id
            const categoriesByProduct = categories.reduce((acc, category) => {
                if (!acc[category.product_id]) {
                    acc[category.product_id] = [];
                }
                acc[category.product_id].push(category);
                return acc;
            }, {});
            // Assign categories to products
            products.forEach(product => {
                product.categories = categoriesByProduct[product.id] || [];
            });
        }
        catch (error) {
            console.error(`❌ [TENANT-PRODUCT] Error loading categories: ${error}`);
        }
    }
    /**
     * Load tags for products
     */
    async loadProductTags(products) {
        const productIds = products.map(p => p.id);
        try {
            // Check if product_tags junction table exists and has the right structure
            const tagQuery = `
        SELECT pt.*, ptp.product_id
        FROM product_tag pt
        JOIN product_tags ptp ON pt.id = ptp.product_tag_id
        WHERE ptp.product_id = ANY($1) AND pt.deleted_at IS NULL
        ORDER BY pt.value ASC
      `;
            const tagResult = await this.pgConnection.query(tagQuery, [productIds]);
            const tags = tagResult.rows;
            console.log(`🔗 [TENANT-PRODUCT] Loaded ${tags.length} tags for ${products.length} products`);
            // Group tags by product_id
            const tagsByProduct = tags.reduce((acc, tag) => {
                if (!acc[tag.product_id]) {
                    acc[tag.product_id] = [];
                }
                acc[tag.product_id].push(tag);
                return acc;
            }, {});
            // Assign tags to products
            products.forEach(product => {
                product.tags = tagsByProduct[product.id] || [];
            });
        }
        catch (error) {
            console.error(`❌ [TENANT-PRODUCT] Error loading tags: ${error}`);
            // Tags are optional, so don't fail the whole request
            products.forEach(product => {
                product.tags = [];
            });
        }
    }
    /**
     * Retrieve a single product by ID with tenant filtering
     */
    async retrieveProduct(productId, config) {
        console.log(`🔍 [TENANT-PRODUCT] Retrieving product ${productId} for tenant: ${this.tenantId}`);
        try {
            // Use direct database query to get the product
            const products = await this.listProductsDirectQuery({ id: productId }, config);
            if (!products || products.length === 0) {
                console.log(`❌ [TENANT-PRODUCT] Product ${productId} not found for tenant: ${this.tenantId}`);
                return null;
            }
            const product = products[0];
            console.log(`✅ [TENANT-PRODUCT] Retrieved product ${productId} (${product.title}) for tenant: ${this.tenantId}`);
            return product;
        }
        catch (error) {
            console.error(`❌ [TENANT-PRODUCT] Error retrieving product ${productId}: ${error}`);
            throw error;
        }
    }
}
exports.TenantAwareProductService = TenantAwareProductService;
//# sourceMappingURL=data:application/json;base64,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