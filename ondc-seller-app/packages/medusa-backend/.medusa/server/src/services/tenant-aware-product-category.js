"use strict";
/**
 * Tenant-Aware Product Category Service
 *
 * Provides complete CRUD operations for product categories with automatic tenant isolation.
 * Integrates with Row Level Security (RLS) for database-level tenant filtering.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TenantAwareProductCategoryService = void 0;
const tenant_query_interceptor_1 = require("./tenant-query-interceptor");
class TenantAwareProductCategoryService {
    constructor(categoryService, tenantId = 'default', pgConnection) {
        this.categoryService = categoryService;
        this.tenantId = tenantId;
        this.queryInterceptor = new tenant_query_interceptor_1.TenantQueryInterceptor({
            tenantId,
            enableLogging: true,
            strictMode: false,
        });
        this.pgConnection = pgConnection;
        // Set tenant context in database for RLS
        this.setDatabaseTenantContext();
    }
    /**
     * Set tenant context in database for Row Level Security
     */
    async setDatabaseTenantContext() {
        if (this.pgConnection) {
            try {
                await this.pgConnection.query('SELECT set_config($1, $2, true)', [
                    'app.tenant_context.tenant_id',
                    this.tenantId,
                ]);
                console.log(`🔒 [TENANT-CATEGORY] Set database tenant context: ${this.tenantId}`);
            }
            catch (error) {
                console.warn(`⚠️  [TENANT-CATEGORY] Failed to set database tenant context: ${error}`);
            }
        }
    }
    /**
     * Create a tenant-aware product category service instance from request
     */
    static fromRequest(req) {
        let categoryService = null;
        const tenantId = req.tenant_id || req.headers['x-tenant-id'] || 'default';
        // Try different service names for product categories
        const possibleServiceNames = ['productCategory', 'product-category', 'category'];
        for (const serviceName of possibleServiceNames) {
            try {
                categoryService = req.scope.resolve(serviceName);
                console.log(`✅ [TENANT-CATEGORY] Found category service: ${serviceName}`);
                break;
            }
            catch (error) {
                // Continue trying other service names
            }
        }
        if (!categoryService) {
            console.warn(`⚠️  [TENANT-CATEGORY] No category service found, creating mock service`);
            categoryService = {
                listCategories: async () => [],
                createCategories: async (data) => data,
                updateCategories: async (data) => data,
                deleteCategories: async () => { },
                retrieveCategory: async () => null,
            };
        }
        // Try to get database connection
        let pgConnection = null;
        try {
            const manager = req.scope.resolve('manager');
            if (manager && typeof manager.query === 'function') {
                pgConnection = manager;
                console.log(`✅ [TENANT-CATEGORY] Found database connection via manager`);
            }
            else {
                console.warn(`⚠️  [TENANT-CATEGORY] No database connection found, creating direct connection`);
                console.warn(`⚠️  [TENANT-CATEGORY] No database connection found, will use manual filtering`);
            }
        }
        catch (error) {
            console.warn(`⚠️  [TENANT-CATEGORY] Error resolving database connection: ${error}`);
        }
        console.log(`📂 [TENANT-CATEGORY] Creating tenant-aware category service for tenant: ${tenantId}`);
        return new TenantAwareProductCategoryService(categoryService, tenantId, pgConnection);
    }
    // ============================================================================
    // CREATE OPERATIONS
    // ============================================================================
    /**
     * Create product categories with automatic tenant_id injection
     */
    async createCategories(categoriesData) {
        console.log(`📂 [TENANT-CATEGORY] Creating ${categoriesData.length} categories for tenant: ${this.tenantId}`);
        try {
            // Inject tenant_id into all category data
            const tenantCategoriesData = categoriesData.map(categoryData => ({
                ...categoryData,
                tenant_id: this.tenantId,
            }));
            const result = await this.categoryService.createCategories(tenantCategoriesData);
            console.log(`✅ [TENANT-CATEGORY] Created ${result.length} categories for tenant: ${this.tenantId}`);
            return result;
        }
        catch (error) {
            console.error(`❌ [TENANT-CATEGORY] Error creating categories: ${error}`);
            throw error;
        }
    }
    // ============================================================================
    // READ OPERATIONS
    // ============================================================================
    /**
     * List product categories with tenant filtering
     */
    async listCategories(filters = {}, config, context) {
        console.log(`📂 [TENANT-CATEGORY] Listing categories for tenant: ${this.tenantId}`);
        try {
            // Set database tenant context for RLS
            await this.setDatabaseTenantContext();
            // Use the service method directly - RLS will handle filtering
            const result = await this.categoryService.listCategories(filters, config, context);
            const categoryCount = Array.isArray(result) ? result.length : result?.categories?.length || 0;
            console.log(`✅ [TENANT-CATEGORY] Found ${categoryCount} categories for tenant: ${this.tenantId}`);
            return result;
        }
        catch (error) {
            console.error(`❌ [TENANT-CATEGORY] Error listing categories: ${error}`);
            // Return empty result structure
            return {
                categories: [],
                count: 0,
                offset: config?.skip || 0,
                limit: config?.take || 20,
            };
        }
    }
    /**
     * List and count product categories with tenant filtering
     */
    async listAndCountCategories(filters = {}, config, context) {
        console.log(`📂 [TENANT-CATEGORY] Listing and counting categories for tenant: ${this.tenantId}`);
        try {
            // Use direct database query to bypass Medusa ORM issues
            console.log(`� [TENANT-CATEGORY] Using direct database query for tenant: ${this.tenantId}`);
            return await this.listAndCountCategoriesDirectQuery(filters, config);
        }
        catch (error) {
            console.error(`❌ [TENANT-CATEGORY] Error listing and counting categories: ${error}`);
            return [[], 0];
        }
    }
    /**
     * Retrieve a single product category by ID (with tenant validation)
     */
    async retrieveCategory(categoryId, config, context) {
        console.log(`📂 [TENANT-CATEGORY] Retrieving category ${categoryId} for tenant: ${this.tenantId}`);
        try {
            // Use direct database query to get the category
            const categories = await this.listAndCountCategoriesDirectQuery({ id: categoryId }, config);
            if (!categories || categories[0].length === 0) {
                console.log(`❌ [TENANT-CATEGORY] Category ${categoryId} not found for tenant: ${this.tenantId}`);
                return null;
            }
            const category = categories[0][0]; // categories is [categories[], count]
            console.log(`✅ [TENANT-CATEGORY] Retrieved category ${categoryId} (${category.name}) for tenant: ${this.tenantId}`);
            return category;
        }
        catch (error) {
            console.error(`❌ [TENANT-CATEGORY] Error retrieving category ${categoryId}: ${error}`);
            throw error;
        }
    }
    // ============================================================================
    // UPDATE OPERATIONS
    // ============================================================================
    /**
     * Update product categories with tenant validation
     */
    async updateCategories(categoriesData) {
        console.log(`📂 [TENANT-CATEGORY] Updating ${categoriesData.length} categories for tenant: ${this.tenantId}`);
        try {
            // Set database tenant context for RLS
            await this.setDatabaseTenantContext();
            // Ensure tenant_id is preserved in updates
            const tenantCategoriesData = categoriesData.map(categoryData => ({
                ...categoryData,
                tenant_id: this.tenantId, // Ensure tenant_id is not changed
            }));
            const result = await this.categoryService.updateCategories(tenantCategoriesData);
            console.log(`✅ [TENANT-CATEGORY] Updated ${result.length} categories for tenant: ${this.tenantId}`);
            return result;
        }
        catch (error) {
            console.error(`❌ [TENANT-CATEGORY] Error updating categories: ${error}`);
            throw error;
        }
    }
    // ============================================================================
    // DELETE OPERATIONS
    // ============================================================================
    /**
     * Delete product categories with tenant validation
     */
    async deleteCategories(categoryIds) {
        console.log(`📂 [TENANT-CATEGORY] Deleting ${categoryIds.length} categories for tenant: ${this.tenantId}`);
        try {
            // Set database tenant context for RLS
            await this.setDatabaseTenantContext();
            await this.categoryService.deleteCategories(categoryIds);
            console.log(`✅ [TENANT-CATEGORY] Deleted ${categoryIds.length} categories for tenant: ${this.tenantId}`);
        }
        catch (error) {
            console.error(`❌ [TENANT-CATEGORY] Error deleting categories: ${error}`);
            throw error;
        }
    }
    // ============================================================================
    // UTILITY METHODS
    // ============================================================================
    /**
     * Get product category statistics for tenant
     */
    async getCategoryStats() {
        console.log(`📊 [TENANT-CATEGORY] Getting category statistics for tenant: ${this.tenantId}`);
        try {
            const [categories, count] = await this.listAndCountCategories();
            // Calculate statistics
            const activeCategories = categories.filter((cat) => cat.is_active !== false).length;
            const categoriesWithProducts = categories.filter((cat) => (cat.products?.length || 0) > 0).length;
            const stats = {
                totalCategories: count,
                activeCategories,
                categoriesWithProducts,
            };
            console.log(`📊 [TENANT-CATEGORY] Stats for tenant ${this.tenantId}:`, stats);
            return stats;
        }
        catch (error) {
            console.error(`❌ [TENANT-CATEGORY] Error getting category stats: ${error}`);
            throw error;
        }
    }
    /**
     * List and count categories using direct database query with RLS
     */
    async listAndCountCategoriesDirectQuery(filters = {}, config) {
        // Ensure database connection is available
        if (!this.pgConnection) {
            console.log(`🔗 [TENANT-CATEGORY] Creating database connection for direct query`);
            try {
                const { Client } = require('pg');
                this.pgConnection = new Client({
                    connectionString: process.env.DATABASE_URL ||
                        process.env.POSTGRES_URL ||
                        'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
                });
                await this.pgConnection.connect();
                console.log(`✅ [TENANT-CATEGORY] Database connection established for tenant: ${this.tenantId}`);
            }
            catch (error) {
                console.error(`❌ [TENANT-CATEGORY] Failed to create database connection: ${error}`);
                return [[], 0];
            }
        }
        try {
            // Set tenant context for RLS
            await this.pgConnection.query('SELECT set_config($1, $2, true)', [
                'app.tenant_context.tenant_id',
                this.tenantId,
            ]);
            // Build WHERE clause from filters
            const whereConditions = [];
            const queryParams = [];
            let paramIndex = 1;
            // ALWAYS add tenant_id filter for security (manual fallback if RLS fails)
            whereConditions.push(`tenant_id = $${paramIndex++}`);
            queryParams.push(this.tenantId);
            // ALWAYS filter out soft-deleted records
            whereConditions.push(`deleted_at IS NULL`);
            // Add basic filters
            if (filters.id) {
                whereConditions.push(`id = $${paramIndex++}`);
                queryParams.push(filters.id);
            }
            if (filters.name) {
                whereConditions.push(`name ILIKE $${paramIndex++}`);
                queryParams.push(`%${filters.name}%`);
            }
            if (filters.is_active !== undefined) {
                whereConditions.push(`is_active = $${paramIndex++}`);
                queryParams.push(filters.is_active);
            }
            if (filters.is_internal !== undefined) {
                whereConditions.push(`is_internal = $${paramIndex++}`);
                queryParams.push(filters.is_internal);
            }
            if (filters.parent_category_id) {
                whereConditions.push(`parent_category_id = $${paramIndex++}`);
                queryParams.push(filters.parent_category_id);
            }
            // Build query
            let query = 'SELECT * FROM product_category';
            if (whereConditions.length > 0) {
                query += ' WHERE ' + whereConditions.join(' AND ');
            }
            // Add ordering and limits
            query += ' ORDER BY created_at DESC';
            if (config?.take) {
                query += ` LIMIT $${paramIndex++}`;
                queryParams.push(config.take);
            }
            if (config?.skip) {
                query += ` OFFSET $${paramIndex++}`;
                queryParams.push(config.skip);
            }
            console.log(`🔍 [TENANT-CATEGORY] Executing direct query for tenant ${this.tenantId}:`, query);
            const result = await this.pgConnection.query(query, queryParams);
            // Get count (without limit/offset)
            let countQuery = 'SELECT COUNT(*) FROM product_category';
            if (whereConditions.length > 0) {
                countQuery += ' WHERE ' + whereConditions.join(' AND ');
            }
            const countParams = queryParams.slice(0, paramIndex - 1 - (config?.take ? 1 : 0) - (config?.skip ? 1 : 0));
            const countResult = await this.pgConnection.query(countQuery, countParams);
            const totalCount = parseInt(countResult.rows[0].count);
            const categories = result.rows;
            // Load relations if requested
            if (config?.relations && config.relations.length > 0) {
                console.log(`🔗 [TENANT-CATEGORY] Loading relations for ${categories.length} categories:`, config.relations);
                await this.loadCategoryRelations(categories, config.relations);
            }
            console.log(`✅ [TENANT-CATEGORY] Direct query found ${categories.length} categories (${totalCount} total) for tenant: ${this.tenantId}`);
            return [categories, totalCount];
        }
        catch (error) {
            console.error(`❌ [TENANT-CATEGORY] Direct query failed: ${error}`);
            throw error;
        }
    }
    /**
     * Load relations for categories
     */
    async loadCategoryRelations(categories, relations) {
        if (!categories.length)
            return;
        for (const relation of relations) {
            switch (relation) {
                case 'category_children':
                    await this.loadCategoryChildren(categories);
                    break;
                case 'parent_category':
                    await this.loadParentCategories(categories);
                    break;
                default:
                    console.log(`⚠️ [TENANT-CATEGORY] Unknown relation: ${relation}`);
            }
        }
    }
    /**
     * Load category children for categories
     */
    async loadCategoryChildren(categories) {
        if (!categories.length)
            return;
        try {
            const categoryIds = categories.map(cat => cat.id);
            // Query for all children of these categories
            const childrenQuery = `
        SELECT * FROM product_category
        WHERE parent_category_id = ANY($1)
        AND tenant_id = $2
        AND deleted_at IS NULL
        ORDER BY name ASC
      `;
            const result = await this.pgConnection.query(childrenQuery, [categoryIds, this.tenantId]);
            const children = result.rows;
            // Group children by parent_category_id
            const childrenByParent = children.reduce((acc, child) => {
                if (!acc[child.parent_category_id]) {
                    acc[child.parent_category_id] = [];
                }
                acc[child.parent_category_id].push(child);
                return acc;
            }, {});
            // Assign children to their parent categories
            categories.forEach(category => {
                category.category_children = childrenByParent[category.id] || [];
            });
            console.log(`🔗 [TENANT-CATEGORY] Loaded ${children.length} children for ${categories.length} categories`);
        }
        catch (error) {
            console.error(`❌ [TENANT-CATEGORY] Error loading category children: ${error}`);
            // Set empty arrays as fallback
            categories.forEach(category => {
                category.category_children = [];
            });
        }
    }
    /**
     * Load parent categories for categories
     */
    async loadParentCategories(categories) {
        if (!categories.length)
            return;
        try {
            const parentIds = categories
                .map(cat => cat.parent_category_id)
                .filter(id => id !== null && id !== undefined);
            if (!parentIds.length) {
                // No parent categories to load
                categories.forEach(category => {
                    category.parent_category = null;
                });
                return;
            }
            // Query for all parent categories
            const parentsQuery = `
        SELECT * FROM product_category
        WHERE id = ANY($1)
        AND tenant_id = $2
        AND deleted_at IS NULL
      `;
            const result = await this.pgConnection.query(parentsQuery, [parentIds, this.tenantId]);
            const parents = result.rows;
            // Create a map of parent categories by ID
            const parentsById = parents.reduce((acc, parent) => {
                acc[parent.id] = parent;
                return acc;
            }, {});
            // Assign parent categories
            categories.forEach(category => {
                category.parent_category = category.parent_category_id
                    ? parentsById[category.parent_category_id] || null
                    : null;
            });
            console.log(`🔗 [TENANT-CATEGORY] Loaded ${parents.length} parent categories for ${categories.length} categories`);
        }
        catch (error) {
            console.error(`❌ [TENANT-CATEGORY] Error loading parent categories: ${error}`);
            // Set null as fallback
            categories.forEach(category => {
                category.parent_category = null;
            });
        }
    }
}
exports.TenantAwareProductCategoryService = TenantAwareProductCategoryService;
//# sourceMappingURL=data:application/json;base64,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