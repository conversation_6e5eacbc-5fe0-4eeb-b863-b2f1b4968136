"use strict";
/**
 * Tenant-aware Product Tag Service
 * Provides complete CRUD operations for product tags with automatic tenant isolation.
 * Uses direct database queries to ensure proper tenant filtering.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TenantAwareTagService = void 0;
class TenantAwareTagService {
    constructor(tagService, tenantId) {
        this.pgConnection = null;
        this.tagService = tagService;
        this.tenantId = tenantId;
        console.log(`🏢 [TENANT-TAG] Initialized for tenant: ${tenantId}`);
    }
    /**
     * Set database tenant context for RLS
     */
    async setDatabaseTenantContext() {
        try {
            if (!this.pgConnection) {
                const { Client } = require('pg');
                this.pgConnection = new Client({
                    connectionString: process.env.DATABASE_URL ||
                        process.env.POSTGRES_URL ||
                        'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
                });
                await this.pgConnection.connect();
            }
            // Set tenant context for RLS
            await this.pgConnection.query('SET app.current_tenant_id = $1', [this.tenantId]);
            console.log(`🔒 [TENANT-TAG] Set database tenant context: ${this.tenantId}`);
        }
        catch (error) {
            console.error(`❌ [TENANT-TAG] Error setting database context: ${error}`);
        }
    }
    /**
     * List and count tags with tenant awareness
     */
    async listAndCountTags(filters = {}, config) {
        console.log(`📂 [TENANT-TAG] Listing and counting tags for tenant: ${this.tenantId}`);
        try {
            // Use direct database query to ensure proper tenant filtering
            console.log(`🔍 [TENANT-TAG] Using direct database query for tenant: ${this.tenantId}`);
            const [tags, count] = await this.listTagsDirectQuery(filters, config);
            console.log(`✅ [TENANT-TAG] Found ${count} total tags for tenant: ${this.tenantId}`);
            return [tags, count];
        }
        catch (error) {
            console.error(`❌ [TENANT-TAG] Error listing and counting tags: ${error}`);
            return [[], 0];
        }
    }
    /**
     * Retrieve a single tag by ID (with tenant validation)
     */
    async retrieveTag(tagId, config, context) {
        console.log(`📂 [TENANT-TAG] Retrieving tag ${tagId} for tenant: ${this.tenantId}`);
        try {
            // Use direct database query to get the tag
            const [tags] = await this.listTagsDirectQuery({ id: tagId }, config);
            if (!tags || tags.length === 0) {
                console.log(`❌ [TENANT-TAG] Tag ${tagId} not found for tenant: ${this.tenantId}`);
                return null;
            }
            const tag = tags[0];
            console.log(`✅ [TENANT-TAG] Retrieved tag ${tagId} (${tag.value}) for tenant: ${this.tenantId}`);
            return tag;
        }
        catch (error) {
            console.error(`❌ [TENANT-TAG] Error retrieving tag ${tagId}: ${error}`);
            throw error;
        }
    }
    /**
     * Direct database query for tags with tenant filtering
     */
    async listTagsDirectQuery(filters = {}, config) {
        // Ensure database connection is available
        if (!this.pgConnection) {
            console.log(`🔗 [TENANT-TAG] Creating database connection for direct query`);
            try {
                const { Client } = require('pg');
                this.pgConnection = new Client({
                    connectionString: process.env.DATABASE_URL ||
                        process.env.POSTGRES_URL ||
                        'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
                });
                await this.pgConnection.connect();
                console.log(`✅ [TENANT-TAG] Database connection established for tenant: ${this.tenantId}`);
            }
            catch (error) {
                console.error(`❌ [TENANT-TAG] Failed to create database connection: ${error}`);
                return [[], 0];
            }
        }
        try {
            // Build WHERE clause from filters
            const whereConditions = [];
            const queryParams = [];
            let paramIndex = 1;
            // ALWAYS add tenant_id filter for security (manual fallback if RLS fails)
            whereConditions.push(`tenant_id = $${paramIndex++}`);
            queryParams.push(this.tenantId);
            // ALWAYS filter out soft-deleted records
            whereConditions.push(`deleted_at IS NULL`);
            // Add basic filters
            if (filters.id) {
                whereConditions.push(`id = $${paramIndex++}`);
                queryParams.push(filters.id);
            }
            if (filters.value) {
                whereConditions.push(`value ILIKE $${paramIndex++}`);
                queryParams.push(`%${filters.value}%`);
            }
            // Build query to get tags
            let query = 'SELECT * FROM product_tag';
            if (whereConditions.length > 0) {
                query += ' WHERE ' + whereConditions.join(' AND ');
            }
            // Add ordering and limits
            query += ' ORDER BY value ASC';
            if (config?.take) {
                query += ` LIMIT $${paramIndex++}`;
                queryParams.push(config.take);
            }
            if (config?.skip) {
                query += ` OFFSET $${paramIndex++}`;
                queryParams.push(config.skip);
            }
            console.log(`🔍 [TENANT-TAG] Executing direct query for tenant ${this.tenantId}:`, query);
            // Use raw query to avoid prepared statement caching issues
            const rawQuery = query.replace(/\$(\d+)/g, (match, num) => {
                const paramIndex = parseInt(num) - 1;
                const value = queryParams[paramIndex];
                return typeof value === 'string' ? `'${value.replace(/'/g, "''")}'` : value;
            });
            console.log(`🔍 [TENANT-TAG] Raw query:`, rawQuery);
            const result = await this.pgConnection.query(rawQuery);
            const tags = result.rows;
            // Get total count
            let countQuery = 'SELECT COUNT(*) as total FROM product_tag';
            if (whereConditions.length > 0) {
                countQuery += ' WHERE ' + whereConditions.join(' AND ');
            }
            // Use raw query for count as well to avoid prepared statement issues
            const countParams = queryParams.slice(0, whereConditions.length);
            const rawCountQuery = countQuery.replace(/\$(\d+)/g, (match, num) => {
                const paramIndex = parseInt(num) - 1;
                const value = countParams[paramIndex];
                return typeof value === 'string' ? `'${value.replace(/'/g, "''")}'` : value;
            });
            console.log(`🔍 [TENANT-TAG] Raw count query:`, rawCountQuery);
            const countResult = await this.pgConnection.query(rawCountQuery);
            const total = parseInt(countResult.rows[0].total);
            console.log(`✅ [TENANT-TAG] Direct query found ${tags.length} tags (${total} total) for tenant: ${this.tenantId}`);
            return [tags, total];
        }
        catch (error) {
            console.error(`❌ [TENANT-TAG] Direct query failed: ${error}`);
            throw error;
        }
    }
    /**
     * Cleanup resources
     */
    async cleanup() {
        try {
            if (this.pgConnection) {
                await this.pgConnection.end();
                this.pgConnection = null;
            }
            console.log(`🧹 [TENANT-TAG] Cleaned up resources for tenant: ${this.tenantId}`);
        }
        catch (error) {
            console.error(`❌ [TENANT-TAG] Error during cleanup: ${error}`);
        }
    }
}
exports.TenantAwareTagService = TenantAwareTagService;
//# sourceMappingURL=data:application/json;base64,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