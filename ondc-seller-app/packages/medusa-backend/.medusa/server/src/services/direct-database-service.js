"use strict";
/**
 * Direct Database Service
 *
 * Provides direct database access with automatic tenant context setting.
 * Bypasses Medusa services to ensure RLS policies are properly applied.
 * This is the solution for true tenant isolation at the database level.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DirectDatabaseService = void 0;
const pg_1 = require("pg");
class DirectDatabaseService {
    constructor(tenantId = 'default') {
        this.tenantId = tenantId;
        // Create database connection pool using environment variables
        const databaseUrl = process.env.DATABASE_URL || process.env.POSTGRES_URL;
        if (databaseUrl) {
            // Use connection string from environment
            this.pool = new pg_1.Pool({
                connectionString: databaseUrl,
                max: 10,
                idleTimeoutMillis: 30000,
                connectionTimeoutMillis: 2000,
            });
        }
        else {
            // Fallback to individual environment variables
            this.pool = new pg_1.Pool({
                user: process.env.POSTGRES_USER || 'postgres',
                password: process.env.POSTGRES_PASSWORD || 'postgres',
                host: process.env.POSTGRES_HOST || 'localhost',
                port: parseInt(process.env.POSTGRES_PORT || '5432'),
                database: process.env.POSTGRES_DB || 'medusa_backend',
                max: 10,
                idleTimeoutMillis: 30000,
                connectionTimeoutMillis: 2000,
            });
        }
    }
    /**
     * Create service instance from request
     */
    static fromRequest(req) {
        const tenantId = req.tenant_id || 'default';
        console.log(`🗄️  [DIRECT-DB] Creating direct database service for tenant: ${tenantId}`);
        return new DirectDatabaseService(tenantId);
    }
    /**
     * Execute query with automatic tenant context setting
     */
    async query(sql, params = []) {
        const client = await this.pool.connect();
        try {
            // Start transaction to ensure tenant context persists
            await client.query('BEGIN');
            // Set tenant context for RLS using the correct function
            console.log(`🔒 [DIRECT-DB] Setting tenant context: ${this.tenantId}`);
            // Try different tenant context setting methods based on available RLS functions
            try {
                // Method 1: Try set_tenant_context function (from create-rls-functions.sql)
                await client.query('SELECT set_tenant_context($1)', [this.tenantId]);
                console.log(`✅ [DIRECT-DB] Set tenant context using set_tenant_context function`);
            }
            catch (error1) {
                try {
                    // Method 2: Try direct config setting (fallback)
                    await client.query('SELECT set_config($1, $2, false)', [
                        'app.current_tenant_id',
                        this.tenantId,
                    ]);
                    console.log(`✅ [DIRECT-DB] Set tenant context using set_config`);
                }
                catch (error2) {
                    console.warn(`⚠️ [DIRECT-DB] Failed to set tenant context: ${error2.message}`);
                }
            }
            // Verify tenant context was set
            try {
                const contextResult = await client.query('SELECT current_setting($1, true) as tenant_context', ['app.current_tenant_id']);
                console.log(`🔍 [DIRECT-DB] Current tenant context: ${contextResult.rows[0].tenant_context}`);
            }
            catch (verifyError) {
                console.log(`🔍 [DIRECT-DB] Could not verify tenant context: ${verifyError.message}`);
            }
            // Execute the actual query
            console.log(`📝 [DIRECT-DB] Executing query: ${sql.substring(0, 100)}...`);
            const result = await client.query(sql, params);
            console.log(`✅ [DIRECT-DB] Query returned ${result.rows.length} rows`);
            // Commit transaction
            await client.query('COMMIT');
            return result;
        }
        catch (error) {
            // Rollback on error
            await client.query('ROLLBACK');
            throw error;
        }
        finally {
            client.release();
        }
    }
    // ============================================================================
    // PRODUCT OPERATIONS
    // ============================================================================
    async getProducts(limit = 20, offset = 0) {
        console.log(`🛍️  [DIRECT-DB] Getting products for tenant: ${this.tenantId}`);
        try {
            // Get products with count
            const countResult = await this.query('SELECT COUNT(*) FROM product');
            const total = parseInt(countResult.rows[0].count);
            const productsResult = await this.query('SELECT * FROM product ORDER BY created_at DESC LIMIT $1 OFFSET $2', [limit, offset]);
            console.log(`✅ [DIRECT-DB] Found ${productsResult.rows.length}/${total} products for tenant: ${this.tenantId}`);
            return {
                products: productsResult.rows,
                total,
            };
        }
        catch (error) {
            console.error(`❌ [DIRECT-DB] Error getting products: ${error}`);
            throw error;
        }
    }
    async createProduct(productData) {
        console.log(`🛍️  [DIRECT-DB] Creating product for tenant: ${this.tenantId}`);
        try {
            const { title, description = '', handle, status = 'draft', ...otherData } = productData;
            const result = await this.query(`
        INSERT INTO product (title, description, handle, status, tenant_id, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
        RETURNING *
      `, [title, description, handle, status, this.tenantId]);
            console.log(`✅ [DIRECT-DB] Created product ${result.rows[0].id} for tenant: ${this.tenantId}`);
            return result.rows[0];
        }
        catch (error) {
            console.error(`❌ [DIRECT-DB] Error creating product: ${error}`);
            throw error;
        }
    }
    // ============================================================================
    // CUSTOMER OPERATIONS
    // ============================================================================
    async getCustomers(limit = 20, offset = 0) {
        console.log(`👥 [DIRECT-DB] Getting customers for tenant: ${this.tenantId}`);
        try {
            const countResult = await this.query('SELECT COUNT(*) FROM customer');
            const total = parseInt(countResult.rows[0].count);
            const customersResult = await this.query('SELECT * FROM customer ORDER BY created_at DESC LIMIT $1 OFFSET $2', [limit, offset]);
            console.log(`✅ [DIRECT-DB] Found ${customersResult.rows.length}/${total} customers for tenant: ${this.tenantId}`);
            return {
                customers: customersResult.rows,
                total,
            };
        }
        catch (error) {
            console.error(`❌ [DIRECT-DB] Error getting customers: ${error}`);
            throw error;
        }
    }
    async createCustomer(customerData) {
        console.log(`👥 [DIRECT-DB] Creating customer for tenant: ${this.tenantId}`);
        try {
            const { email, first_name = '', last_name = '', phone = null, ...otherData } = customerData;
            const result = await this.query(`
        INSERT INTO customer (email, first_name, last_name, phone, tenant_id, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
        RETURNING *
      `, [email, first_name, last_name, phone, this.tenantId]);
            console.log(`✅ [DIRECT-DB] Created customer ${result.rows[0].id} for tenant: ${this.tenantId}`);
            return result.rows[0];
        }
        catch (error) {
            console.error(`❌ [DIRECT-DB] Error creating customer: ${error}`);
            throw error;
        }
    }
    // ============================================================================
    // CART OPERATIONS
    // ============================================================================
    async getCarts(limit = 20, offset = 0) {
        console.log(`🛒 [DIRECT-DB] Getting carts for tenant: ${this.tenantId}`);
        try {
            const countResult = await this.query('SELECT COUNT(*) FROM cart');
            const total = parseInt(countResult.rows[0].count);
            const cartsResult = await this.query('SELECT * FROM cart ORDER BY created_at DESC LIMIT $1 OFFSET $2', [limit, offset]);
            console.log(`✅ [DIRECT-DB] Found ${cartsResult.rows.length}/${total} carts for tenant: ${this.tenantId}`);
            return {
                carts: cartsResult.rows,
                total,
            };
        }
        catch (error) {
            console.error(`❌ [DIRECT-DB] Error getting carts: ${error}`);
            throw error;
        }
    }
    async createCart(cartData) {
        console.log(`🛒 [DIRECT-DB] Creating cart for tenant: ${this.tenantId}`);
        try {
            const { region_id, customer_id = null, sales_channel_id, currency_code = 'usd', ...otherData } = cartData;
            const result = await this.query(`
        INSERT INTO cart (region_id, customer_id, sales_channel_id, currency_code, tenant_id, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
        RETURNING *
      `, [region_id, customer_id, sales_channel_id, currency_code, this.tenantId]);
            console.log(`✅ [DIRECT-DB] Created cart ${result.rows[0].id} for tenant: ${this.tenantId}`);
            return result.rows[0];
        }
        catch (error) {
            console.error(`❌ [DIRECT-DB] Error creating cart: ${error}`);
            throw error;
        }
    }
    // ============================================================================
    // ORDER OPERATIONS
    // ============================================================================
    async getOrders(limit = 20, offset = 0) {
        console.log(`📦 [DIRECT-DB] Getting orders for tenant: ${this.tenantId}`);
        try {
            const countResult = await this.query('SELECT COUNT(*) FROM "order"');
            const total = parseInt(countResult.rows[0].count);
            const ordersResult = await this.query('SELECT * FROM "order" ORDER BY created_at DESC LIMIT $1 OFFSET $2', [limit, offset]);
            console.log(`✅ [DIRECT-DB] Found ${ordersResult.rows.length}/${total} orders for tenant: ${this.tenantId}`);
            return {
                orders: ordersResult.rows,
                total,
            };
        }
        catch (error) {
            console.error(`❌ [DIRECT-DB] Error getting orders: ${error}`);
            throw error;
        }
    }
    // ============================================================================
    // UTILITY METHODS
    // ============================================================================
    async getStats() {
        console.log(`📊 [DIRECT-DB] Getting statistics for tenant: ${this.tenantId}`);
        try {
            const [productsResult, customersResult, ordersResult, cartsResult] = await Promise.all([
                this.query('SELECT COUNT(*) FROM product'),
                this.query('SELECT COUNT(*) FROM customer'),
                this.query('SELECT COUNT(*) FROM "order"'),
                this.query('SELECT COUNT(*) FROM cart'),
            ]);
            const stats = {
                products: parseInt(productsResult.rows[0].count),
                customers: parseInt(customersResult.rows[0].count),
                orders: parseInt(ordersResult.rows[0].count),
                carts: parseInt(cartsResult.rows[0].count),
            };
            console.log(`📊 [DIRECT-DB] Stats for tenant ${this.tenantId}:`, stats);
            return stats;
        }
        catch (error) {
            console.error(`❌ [DIRECT-DB] Error getting stats: ${error}`);
            throw error;
        }
    }
    /**
     * Close database connection pool
     */
    async close() {
        await this.pool.end();
    }
}
exports.DirectDatabaseService = DirectDatabaseService;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZGlyZWN0LWRhdGFiYXNlLXNlcnZpY2UuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi9zcmMvc2VydmljZXMvZGlyZWN0LWRhdGFiYXNlLXNlcnZpY2UudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUFBOzs7Ozs7R0FNRzs7O0FBR0gsMkJBQTBCO0FBRTFCLE1BQWEscUJBQXFCO0lBSWhDLFlBQVksV0FBbUIsU0FBUztRQUN0QyxJQUFJLENBQUMsUUFBUSxHQUFHLFFBQVEsQ0FBQztRQUV6Qiw4REFBOEQ7UUFDOUQsTUFBTSxXQUFXLEdBQUcsT0FBTyxDQUFDLEdBQUcsQ0FBQyxZQUFZLElBQUksT0FBTyxDQUFDLEdBQUcsQ0FBQyxZQUFZLENBQUM7UUFFekUsSUFBSSxXQUFXLEVBQUUsQ0FBQztZQUNoQix5Q0FBeUM7WUFDekMsSUFBSSxDQUFDLElBQUksR0FBRyxJQUFJLFNBQUksQ0FBQztnQkFDbkIsZ0JBQWdCLEVBQUUsV0FBVztnQkFDN0IsR0FBRyxFQUFFLEVBQUU7Z0JBQ1AsaUJBQWlCLEVBQUUsS0FBSztnQkFDeEIsdUJBQXVCLEVBQUUsSUFBSTthQUM5QixDQUFDLENBQUM7UUFDTCxDQUFDO2FBQU0sQ0FBQztZQUNOLCtDQUErQztZQUMvQyxJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksU0FBSSxDQUFDO2dCQUNuQixJQUFJLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxhQUFhLElBQUksVUFBVTtnQkFDN0MsUUFBUSxFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsaUJBQWlCLElBQUksVUFBVTtnQkFDckQsSUFBSSxFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsYUFBYSxJQUFJLFdBQVc7Z0JBQzlDLElBQUksRUFBRSxRQUFRLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxhQUFhLElBQUksTUFBTSxDQUFDO2dCQUNuRCxRQUFRLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxXQUFXLElBQUksZ0JBQWdCO2dCQUNyRCxHQUFHLEVBQUUsRUFBRTtnQkFDUCxpQkFBaUIsRUFBRSxLQUFLO2dCQUN4Qix1QkFBdUIsRUFBRSxJQUFJO2FBQzlCLENBQUMsQ0FBQztRQUNMLENBQUM7SUFDSCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxNQUFNLENBQUMsV0FBVyxDQUFDLEdBQWtCO1FBQ25DLE1BQU0sUUFBUSxHQUFHLEdBQUcsQ0FBQyxTQUFTLElBQUksU0FBUyxDQUFDO1FBQzVDLE9BQU8sQ0FBQyxHQUFHLENBQUMsaUVBQWlFLFFBQVEsRUFBRSxDQUFDLENBQUM7UUFDekYsT0FBTyxJQUFJLHFCQUFxQixDQUFDLFFBQVEsQ0FBQyxDQUFDO0lBQzdDLENBQUM7SUFFRDs7T0FFRztJQUNILEtBQUssQ0FBQyxLQUFLLENBQUMsR0FBVyxFQUFFLFNBQWdCLEVBQUU7UUFDekMsTUFBTSxNQUFNLEdBQUcsTUFBTSxJQUFJLENBQUMsSUFBSSxDQUFDLE9BQU8sRUFBRSxDQUFDO1FBRXpDLElBQUksQ0FBQztZQUNILHNEQUFzRDtZQUN0RCxNQUFNLE1BQU0sQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLENBQUM7WUFFNUIsd0RBQXdEO1lBQ3hELE9BQU8sQ0FBQyxHQUFHLENBQUMsMENBQTBDLElBQUksQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDO1lBRXZFLGdGQUFnRjtZQUNoRixJQUFJLENBQUM7Z0JBQ0gsNEVBQTRFO2dCQUM1RSxNQUFNLE1BQU0sQ0FBQyxLQUFLLENBQUMsK0JBQStCLEVBQUUsQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQztnQkFDckUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxvRUFBb0UsQ0FBQyxDQUFDO1lBQ3BGLENBQUM7WUFBQyxPQUFPLE1BQU0sRUFBRSxDQUFDO2dCQUNoQixJQUFJLENBQUM7b0JBQ0gsaURBQWlEO29CQUNqRCxNQUFNLE1BQU0sQ0FBQyxLQUFLLENBQUMsa0NBQWtDLEVBQUU7d0JBQ3JELHVCQUF1Qjt3QkFDdkIsSUFBSSxDQUFDLFFBQVE7cUJBQ2QsQ0FBQyxDQUFDO29CQUNILE9BQU8sQ0FBQyxHQUFHLENBQUMsbURBQW1ELENBQUMsQ0FBQztnQkFDbkUsQ0FBQztnQkFBQyxPQUFPLE1BQU0sRUFBRSxDQUFDO29CQUNoQixPQUFPLENBQUMsSUFBSSxDQUFDLGdEQUFnRCxNQUFNLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQztnQkFDakYsQ0FBQztZQUNILENBQUM7WUFFRCxnQ0FBZ0M7WUFDaEMsSUFBSSxDQUFDO2dCQUNILE1BQU0sYUFBYSxHQUFHLE1BQU0sTUFBTSxDQUFDLEtBQUssQ0FDdEMsb0RBQW9ELEVBQ3BELENBQUMsdUJBQXVCLENBQUMsQ0FDMUIsQ0FBQztnQkFDRixPQUFPLENBQUMsR0FBRyxDQUNULDBDQUEwQyxhQUFhLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLGNBQWMsRUFBRSxDQUNqRixDQUFDO1lBQ0osQ0FBQztZQUFDLE9BQU8sV0FBVyxFQUFFLENBQUM7Z0JBQ3JCLE9BQU8sQ0FBQyxHQUFHLENBQUMsbURBQW1ELFdBQVcsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDO1lBQ3hGLENBQUM7WUFFRCwyQkFBMkI7WUFDM0IsT0FBTyxDQUFDLEdBQUcsQ0FBQyxtQ0FBbUMsR0FBRyxDQUFDLFNBQVMsQ0FBQyxDQUFDLEVBQUUsR0FBRyxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQzNFLE1BQU0sTUFBTSxHQUFHLE1BQU0sTUFBTSxDQUFDLEtBQUssQ0FBQyxHQUFHLEVBQUUsTUFBTSxDQUFDLENBQUM7WUFDL0MsT0FBTyxDQUFDLEdBQUcsQ0FBQyxnQ0FBZ0MsTUFBTSxDQUFDLElBQUksQ0FBQyxNQUFNLE9BQU8sQ0FBQyxDQUFDO1lBRXZFLHFCQUFxQjtZQUNyQixNQUFNLE1BQU0sQ0FBQyxLQUFLLENBQUMsUUFBUSxDQUFDLENBQUM7WUFFN0IsT0FBTyxNQUFNLENBQUM7UUFDaEIsQ0FBQztRQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7WUFDZixvQkFBb0I7WUFDcEIsTUFBTSxNQUFNLENBQUMsS0FBSyxDQUFDLFVBQVUsQ0FBQyxDQUFDO1lBQy9CLE1BQU0sS0FBSyxDQUFDO1FBQ2QsQ0FBQztnQkFBUyxDQUFDO1lBQ1QsTUFBTSxDQUFDLE9BQU8sRUFBRSxDQUFDO1FBQ25CLENBQUM7SUFDSCxDQUFDO0lBRUQsK0VBQStFO0lBQy9FLHFCQUFxQjtJQUNyQiwrRUFBK0U7SUFFL0UsS0FBSyxDQUFDLFdBQVcsQ0FDZixRQUFnQixFQUFFLEVBQ2xCLFNBQWlCLENBQUM7UUFFbEIsT0FBTyxDQUFDLEdBQUcsQ0FBQyxpREFBaUQsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUM7UUFFOUUsSUFBSSxDQUFDO1lBQ0gsMEJBQTBCO1lBQzFCLE1BQU0sV0FBVyxHQUFHLE1BQU0sSUFBSSxDQUFDLEtBQUssQ0FBQyw4QkFBOEIsQ0FBQyxDQUFDO1lBQ3JFLE1BQU0sS0FBSyxHQUFHLFFBQVEsQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBRWxELE1BQU0sY0FBYyxHQUFHLE1BQU0sSUFBSSxDQUFDLEtBQUssQ0FDckMsbUVBQW1FLEVBQ25FLENBQUMsS0FBSyxFQUFFLE1BQU0sQ0FBQyxDQUNoQixDQUFDO1lBRUYsT0FBTyxDQUFDLEdBQUcsQ0FDVCx1QkFBdUIsY0FBYyxDQUFDLElBQUksQ0FBQyxNQUFNLElBQUksS0FBSyx5QkFBeUIsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUNuRyxDQUFDO1lBRUYsT0FBTztnQkFDTCxRQUFRLEVBQUUsY0FBYyxDQUFDLElBQUk7Z0JBQzdCLEtBQUs7YUFDTixDQUFDO1FBQ0osQ0FBQztRQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7WUFDZixPQUFPLENBQUMsS0FBSyxDQUFDLHlDQUF5QyxLQUFLLEVBQUUsQ0FBQyxDQUFDO1lBQ2hFLE1BQU0sS0FBSyxDQUFDO1FBQ2QsQ0FBQztJQUNILENBQUM7SUFFRCxLQUFLLENBQUMsYUFBYSxDQUFDLFdBQWdCO1FBQ2xDLE9BQU8sQ0FBQyxHQUFHLENBQUMsaURBQWlELElBQUksQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDO1FBRTlFLElBQUksQ0FBQztZQUNILE1BQU0sRUFBRSxLQUFLLEVBQUUsV0FBVyxHQUFHLEVBQUUsRUFBRSxNQUFNLEVBQUUsTUFBTSxHQUFHLE9BQU8sRUFBRSxHQUFHLFNBQVMsRUFBRSxHQUFHLFdBQVcsQ0FBQztZQUV4RixNQUFNLE1BQU0sR0FBRyxNQUFNLElBQUksQ0FBQyxLQUFLLENBQzdCOzs7O09BSUQsRUFDQyxDQUFDLEtBQUssRUFBRSxXQUFXLEVBQUUsTUFBTSxFQUFFLE1BQU0sRUFBRSxJQUFJLENBQUMsUUFBUSxDQUFDLENBQ3BELENBQUM7WUFFRixPQUFPLENBQUMsR0FBRyxDQUNULGlDQUFpQyxNQUFNLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsZ0JBQWdCLElBQUksQ0FBQyxRQUFRLEVBQUUsQ0FDbEYsQ0FBQztZQUNGLE9BQU8sTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUN4QixDQUFDO1FBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztZQUNmLE9BQU8sQ0FBQyxLQUFLLENBQUMseUNBQXlDLEtBQUssRUFBRSxDQUFDLENBQUM7WUFDaEUsTUFBTSxLQUFLLENBQUM7UUFDZCxDQUFDO0lBQ0gsQ0FBQztJQUVELCtFQUErRTtJQUMvRSxzQkFBc0I7SUFDdEIsK0VBQStFO0lBRS9FLEtBQUssQ0FBQyxZQUFZLENBQ2hCLFFBQWdCLEVBQUUsRUFDbEIsU0FBaUIsQ0FBQztRQUVsQixPQUFPLENBQUMsR0FBRyxDQUFDLGdEQUFnRCxJQUFJLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQztRQUU3RSxJQUFJLENBQUM7WUFDSCxNQUFNLFdBQVcsR0FBRyxNQUFNLElBQUksQ0FBQyxLQUFLLENBQUMsK0JBQStCLENBQUMsQ0FBQztZQUN0RSxNQUFNLEtBQUssR0FBRyxRQUFRLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUVsRCxNQUFNLGVBQWUsR0FBRyxNQUFNLElBQUksQ0FBQyxLQUFLLENBQ3RDLG9FQUFvRSxFQUNwRSxDQUFDLEtBQUssRUFBRSxNQUFNLENBQUMsQ0FDaEIsQ0FBQztZQUVGLE9BQU8sQ0FBQyxHQUFHLENBQ1QsdUJBQXVCLGVBQWUsQ0FBQyxJQUFJLENBQUMsTUFBTSxJQUFJLEtBQUssMEJBQTBCLElBQUksQ0FBQyxRQUFRLEVBQUUsQ0FDckcsQ0FBQztZQUVGLE9BQU87Z0JBQ0wsU0FBUyxFQUFFLGVBQWUsQ0FBQyxJQUFJO2dCQUMvQixLQUFLO2FBQ04sQ0FBQztRQUNKLENBQUM7UUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1lBQ2YsT0FBTyxDQUFDLEtBQUssQ0FBQywwQ0FBMEMsS0FBSyxFQUFFLENBQUMsQ0FBQztZQUNqRSxNQUFNLEtBQUssQ0FBQztRQUNkLENBQUM7SUFDSCxDQUFDO0lBRUQsS0FBSyxDQUFDLGNBQWMsQ0FBQyxZQUFpQjtRQUNwQyxPQUFPLENBQUMsR0FBRyxDQUFDLGdEQUFnRCxJQUFJLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQztRQUU3RSxJQUFJLENBQUM7WUFDSCxNQUFNLEVBQUUsS0FBSyxFQUFFLFVBQVUsR0FBRyxFQUFFLEVBQUUsU0FBUyxHQUFHLEVBQUUsRUFBRSxLQUFLLEdBQUcsSUFBSSxFQUFFLEdBQUcsU0FBUyxFQUFFLEdBQUcsWUFBWSxDQUFDO1lBRTVGLE1BQU0sTUFBTSxHQUFHLE1BQU0sSUFBSSxDQUFDLEtBQUssQ0FDN0I7Ozs7T0FJRCxFQUNDLENBQUMsS0FBSyxFQUFFLFVBQVUsRUFBRSxTQUFTLEVBQUUsS0FBSyxFQUFFLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FDckQsQ0FBQztZQUVGLE9BQU8sQ0FBQyxHQUFHLENBQ1Qsa0NBQWtDLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxnQkFBZ0IsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUNuRixDQUFDO1lBQ0YsT0FBTyxNQUFNLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3hCLENBQUM7UUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1lBQ2YsT0FBTyxDQUFDLEtBQUssQ0FBQywwQ0FBMEMsS0FBSyxFQUFFLENBQUMsQ0FBQztZQUNqRSxNQUFNLEtBQUssQ0FBQztRQUNkLENBQUM7SUFDSCxDQUFDO0lBRUQsK0VBQStFO0lBQy9FLGtCQUFrQjtJQUNsQiwrRUFBK0U7SUFFL0UsS0FBSyxDQUFDLFFBQVEsQ0FBQyxRQUFnQixFQUFFLEVBQUUsU0FBaUIsQ0FBQztRQUNuRCxPQUFPLENBQUMsR0FBRyxDQUFDLDRDQUE0QyxJQUFJLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQztRQUV6RSxJQUFJLENBQUM7WUFDSCxNQUFNLFdBQVcsR0FBRyxNQUFNLElBQUksQ0FBQyxLQUFLLENBQUMsMkJBQTJCLENBQUMsQ0FBQztZQUNsRSxNQUFNLEtBQUssR0FBRyxRQUFRLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUVsRCxNQUFNLFdBQVcsR0FBRyxNQUFNLElBQUksQ0FBQyxLQUFLLENBQ2xDLGdFQUFnRSxFQUNoRSxDQUFDLEtBQUssRUFBRSxNQUFNLENBQUMsQ0FDaEIsQ0FBQztZQUVGLE9BQU8sQ0FBQyxHQUFHLENBQ1QsdUJBQXVCLFdBQVcsQ0FBQyxJQUFJLENBQUMsTUFBTSxJQUFJLEtBQUssc0JBQXNCLElBQUksQ0FBQyxRQUFRLEVBQUUsQ0FDN0YsQ0FBQztZQUVGLE9BQU87Z0JBQ0wsS0FBSyxFQUFFLFdBQVcsQ0FBQyxJQUFJO2dCQUN2QixLQUFLO2FBQ04sQ0FBQztRQUNKLENBQUM7UUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1lBQ2YsT0FBTyxDQUFDLEtBQUssQ0FBQyxzQ0FBc0MsS0FBSyxFQUFFLENBQUMsQ0FBQztZQUM3RCxNQUFNLEtBQUssQ0FBQztRQUNkLENBQUM7SUFDSCxDQUFDO0lBRUQsS0FBSyxDQUFDLFVBQVUsQ0FBQyxRQUFhO1FBQzVCLE9BQU8sQ0FBQyxHQUFHLENBQUMsNENBQTRDLElBQUksQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDO1FBRXpFLElBQUksQ0FBQztZQUNILE1BQU0sRUFDSixTQUFTLEVBQ1QsV0FBVyxHQUFHLElBQUksRUFDbEIsZ0JBQWdCLEVBQ2hCLGFBQWEsR0FBRyxLQUFLLEVBQ3JCLEdBQUcsU0FBUyxFQUNiLEdBQUcsUUFBUSxDQUFDO1lBRWIsTUFBTSxNQUFNLEdBQUcsTUFBTSxJQUFJLENBQUMsS0FBSyxDQUM3Qjs7OztPQUlELEVBQ0MsQ0FBQyxTQUFTLEVBQUUsV0FBVyxFQUFFLGdCQUFnQixFQUFFLGFBQWEsRUFBRSxJQUFJLENBQUMsUUFBUSxDQUFDLENBQ3pFLENBQUM7WUFFRixPQUFPLENBQUMsR0FBRyxDQUFDLDhCQUE4QixNQUFNLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsZ0JBQWdCLElBQUksQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDO1lBQzVGLE9BQU8sTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUN4QixDQUFDO1FBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztZQUNmLE9BQU8sQ0FBQyxLQUFLLENBQUMsc0NBQXNDLEtBQUssRUFBRSxDQUFDLENBQUM7WUFDN0QsTUFBTSxLQUFLLENBQUM7UUFDZCxDQUFDO0lBQ0gsQ0FBQztJQUVELCtFQUErRTtJQUMvRSxtQkFBbUI7SUFDbkIsK0VBQStFO0lBRS9FLEtBQUssQ0FBQyxTQUFTLENBQ2IsUUFBZ0IsRUFBRSxFQUNsQixTQUFpQixDQUFDO1FBRWxCLE9BQU8sQ0FBQyxHQUFHLENBQUMsNkNBQTZDLElBQUksQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDO1FBRTFFLElBQUksQ0FBQztZQUNILE1BQU0sV0FBVyxHQUFHLE1BQU0sSUFBSSxDQUFDLEtBQUssQ0FBQyw4QkFBOEIsQ0FBQyxDQUFDO1lBQ3JFLE1BQU0sS0FBSyxHQUFHLFFBQVEsQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBRWxELE1BQU0sWUFBWSxHQUFHLE1BQU0sSUFBSSxDQUFDLEtBQUssQ0FDbkMsbUVBQW1FLEVBQ25FLENBQUMsS0FBSyxFQUFFLE1BQU0sQ0FBQyxDQUNoQixDQUFDO1lBRUYsT0FBTyxDQUFDLEdBQUcsQ0FDVCx1QkFBdUIsWUFBWSxDQUFDLElBQUksQ0FBQyxNQUFNLElBQUksS0FBSyx1QkFBdUIsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUMvRixDQUFDO1lBRUYsT0FBTztnQkFDTCxNQUFNLEVBQUUsWUFBWSxDQUFDLElBQUk7Z0JBQ3pCLEtBQUs7YUFDTixDQUFDO1FBQ0osQ0FBQztRQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7WUFDZixPQUFPLENBQUMsS0FBSyxDQUFDLHVDQUF1QyxLQUFLLEVBQUUsQ0FBQyxDQUFDO1lBQzlELE1BQU0sS0FBSyxDQUFDO1FBQ2QsQ0FBQztJQUNILENBQUM7SUFFRCwrRUFBK0U7SUFDL0Usa0JBQWtCO0lBQ2xCLCtFQUErRTtJQUUvRSxLQUFLLENBQUMsUUFBUTtRQU1aLE9BQU8sQ0FBQyxHQUFHLENBQUMsaURBQWlELElBQUksQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDO1FBRTlFLElBQUksQ0FBQztZQUNILE1BQU0sQ0FBQyxjQUFjLEVBQUUsZUFBZSxFQUFFLFlBQVksRUFBRSxXQUFXLENBQUMsR0FBRyxNQUFNLE9BQU8sQ0FBQyxHQUFHLENBQUM7Z0JBQ3JGLElBQUksQ0FBQyxLQUFLLENBQUMsOEJBQThCLENBQUM7Z0JBQzFDLElBQUksQ0FBQyxLQUFLLENBQUMsK0JBQStCLENBQUM7Z0JBQzNDLElBQUksQ0FBQyxLQUFLLENBQUMsOEJBQThCLENBQUM7Z0JBQzFDLElBQUksQ0FBQyxLQUFLLENBQUMsMkJBQTJCLENBQUM7YUFDeEMsQ0FBQyxDQUFDO1lBRUgsTUFBTSxLQUFLLEdBQUc7Z0JBQ1osUUFBUSxFQUFFLFFBQVEsQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQztnQkFDaEQsU0FBUyxFQUFFLFFBQVEsQ0FBQyxlQUFlLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQztnQkFDbEQsTUFBTSxFQUFFLFFBQVEsQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQztnQkFDNUMsS0FBSyxFQUFFLFFBQVEsQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQzthQUMzQyxDQUFDO1lBRUYsT0FBTyxDQUFDLEdBQUcsQ0FBQyxtQ0FBbUMsSUFBSSxDQUFDLFFBQVEsR0FBRyxFQUFFLEtBQUssQ0FBQyxDQUFDO1lBQ3hFLE9BQU8sS0FBSyxDQUFDO1FBQ2YsQ0FBQztRQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7WUFDZixPQUFPLENBQUMsS0FBSyxDQUFDLHNDQUFzQyxLQUFLLEVBQUUsQ0FBQyxDQUFDO1lBQzdELE1BQU0sS0FBSyxDQUFDO1FBQ2QsQ0FBQztJQUNILENBQUM7SUFFRDs7T0FFRztJQUNILEtBQUssQ0FBQyxLQUFLO1FBQ1QsTUFBTSxJQUFJLENBQUMsSUFBSSxDQUFDLEdBQUcsRUFBRSxDQUFDO0lBQ3hCLENBQUM7Q0FDRjtBQWxXRCxzREFrV0MifQ==