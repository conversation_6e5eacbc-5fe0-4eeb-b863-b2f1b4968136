"use strict";
/**
 * Tenant-Aware Cart Service
 *
 * Provides complete CRUD operations for carts with automatic tenant isolation.
 * Integrates with Row Level Security (RLS) for database-level tenant filtering.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TenantAwareCartService = void 0;
const tenant_query_interceptor_1 = require("./tenant-query-interceptor");
class TenantAwareCartService {
    constructor(cartService, tenantId = 'default', pgConnection) {
        this.cartService = cartService;
        this.tenantId = tenantId;
        this.queryInterceptor = new tenant_query_interceptor_1.TenantQueryInterceptor({
            tenantId,
            enableLogging: true,
            strictMode: false
        });
        this.pgConnection = pgConnection;
        // Set tenant context in database for RLS
        this.setDatabaseTenantContext();
    }
    /**
     * Set tenant context in database for Row Level Security
     */
    async setDatabaseTenantContext() {
        if (this.pgConnection) {
            try {
                await this.pgConnection.query('SELECT set_config($1, $2, true)', [
                    'app.tenant_context.tenant_id',
                    this.tenantId
                ]);
                console.log(`🔒 [TENANT-CART] Set database tenant context: ${this.tenantId}`);
            }
            catch (error) {
                console.warn(`⚠️  [TENANT-CART] Failed to set database tenant context: ${error}`);
            }
        }
    }
    /**
     * Create a tenant-aware cart service instance from request
     */
    static fromRequest(req) {
        const cartService = req.scope.resolve('cart');
        const tenantId = req.tenant_id || 'default';
        // Try to get database connection for RLS context setting
        let pgConnection = null;
        try {
            const manager = req.scope.resolve('manager');
            if (manager && typeof manager.query === 'function') {
                pgConnection = manager;
                console.log(`✅ [TENANT-CART] Found database connection via manager`);
            }
        }
        catch (error) {
            console.warn(`⚠️  [TENANT-CART] Error resolving database connection: ${error}`);
        }
        console.log(`🛒 [TENANT-CART] Creating tenant-aware cart service for tenant: ${tenantId}`);
        return new TenantAwareCartService(cartService, tenantId, pgConnection);
    }
    // ============================================================================
    // CREATE OPERATIONS
    // ============================================================================
    /**
     * Create carts with automatic tenant_id injection
     */
    async createCarts(cartsData) {
        console.log(`🛒 [TENANT-CART] Creating ${cartsData.length} carts for tenant: ${this.tenantId}`);
        try {
            // Inject tenant_id into all cart data
            const tenantCartsData = cartsData.map(cartData => ({
                ...cartData,
                tenant_id: this.tenantId
            }));
            const result = await this.cartService.createCarts(tenantCartsData);
            console.log(`✅ [TENANT-CART] Created ${result.length} carts for tenant: ${this.tenantId}`);
            return result;
        }
        catch (error) {
            console.error(`❌ [TENANT-CART] Error creating carts: ${error}`);
            throw error;
        }
    }
    // ============================================================================
    // READ OPERATIONS
    // ============================================================================
    /**
     * List carts with tenant filtering
     */
    async listCarts(filters = {}, config, context) {
        console.log(`🛒 [TENANT-CART] Listing carts for tenant: ${this.tenantId}`);
        try {
            // Set database tenant context for RLS
            await this.setDatabaseTenantContext();
            // Use the service method directly - RLS will handle filtering
            const result = await this.cartService.listCarts(filters, config, context);
            const cartCount = Array.isArray(result) ? result.length : (result?.carts?.length || 0);
            console.log(`✅ [TENANT-CART] Found ${cartCount} carts for tenant: ${this.tenantId}`);
            return result;
        }
        catch (error) {
            console.error(`❌ [TENANT-CART] Error listing carts: ${error}`);
            // Return empty result structure
            return {
                carts: [],
                count: 0,
                offset: config?.skip || 0,
                limit: config?.take || 20
            };
        }
    }
    /**
     * List and count carts with tenant filtering
     */
    async listAndCountCarts(filters = {}, config, context) {
        console.log(`🛒 [TENANT-CART] Listing and counting carts for tenant: ${this.tenantId}`);
        try {
            // Set database tenant context for RLS
            await this.setDatabaseTenantContext();
            const result = await this.cartService.listAndCountCarts(filters, config, context);
            // Handle different result formats
            let carts = [];
            let count = 0;
            if (Array.isArray(result) && result.length === 2) {
                carts = result[0] || [];
                count = result[1] || 0;
            }
            else if (result?.carts) {
                carts = result.carts || [];
                count = result.count || carts.length;
            }
            else if (Array.isArray(result)) {
                carts = result;
                count = carts.length;
            }
            console.log(`✅ [TENANT-CART] Found ${count} total carts for tenant: ${this.tenantId}`);
            return [carts, count];
        }
        catch (error) {
            console.error(`❌ [TENANT-CART] Error listing and counting carts: ${error}`);
            return [[], 0];
        }
    }
    /**
     * Retrieve a single cart by ID (with tenant validation)
     */
    async retrieveCart(cartId, config, context) {
        console.log(`🛒 [TENANT-CART] Retrieving cart ${cartId} for tenant: ${this.tenantId}`);
        try {
            // Set database tenant context for RLS
            await this.setDatabaseTenantContext();
            const cart = await this.cartService.retrieveCart(cartId, config, context);
            if (!cart) {
                throw new Error(`Cart ${cartId} not found or not accessible for tenant: ${this.tenantId}`);
            }
            console.log(`✅ [TENANT-CART] Retrieved cart ${cartId} for tenant: ${this.tenantId}`);
            return cart;
        }
        catch (error) {
            console.error(`❌ [TENANT-CART] Error retrieving cart ${cartId}: ${error}`);
            throw error;
        }
    }
    // ============================================================================
    // UPDATE OPERATIONS
    // ============================================================================
    /**
     * Update carts with tenant validation
     */
    async updateCarts(cartsData) {
        console.log(`🛒 [TENANT-CART] Updating ${cartsData.length} carts for tenant: ${this.tenantId}`);
        try {
            // Set database tenant context for RLS
            await this.setDatabaseTenantContext();
            // Ensure tenant_id is preserved in updates
            const tenantCartsData = cartsData.map(cartData => ({
                ...cartData,
                tenant_id: this.tenantId // Ensure tenant_id is not changed
            }));
            const result = await this.cartService.updateCarts(tenantCartsData);
            console.log(`✅ [TENANT-CART] Updated ${result.length} carts for tenant: ${this.tenantId}`);
            return result;
        }
        catch (error) {
            console.error(`❌ [TENANT-CART] Error updating carts: ${error}`);
            throw error;
        }
    }
    // ============================================================================
    // DELETE OPERATIONS
    // ============================================================================
    /**
     * Delete carts with tenant validation
     */
    async deleteCarts(cartIds) {
        console.log(`🛒 [TENANT-CART] Deleting ${cartIds.length} carts for tenant: ${this.tenantId}`);
        try {
            // Set database tenant context for RLS
            await this.setDatabaseTenantContext();
            await this.cartService.deleteCarts(cartIds);
            console.log(`✅ [TENANT-CART] Deleted ${cartIds.length} carts for tenant: ${this.tenantId}`);
        }
        catch (error) {
            console.error(`❌ [TENANT-CART] Error deleting carts: ${error}`);
            throw error;
        }
    }
    // ============================================================================
    // UTILITY METHODS
    // ============================================================================
    /**
     * Get cart statistics for tenant
     */
    async getCartStats() {
        console.log(`📊 [TENANT-CART] Getting cart statistics for tenant: ${this.tenantId}`);
        try {
            const [carts, count] = await this.listAndCountCarts();
            // Calculate statistics
            const activeCarts = carts.filter((cart) => cart.status === 'active' || !cart.completed_at).length;
            const completedCarts = carts.filter((cart) => cart.completed_at).length;
            const totalItems = carts.reduce((sum, cart) => sum + (cart.items?.length || 0), 0);
            const averageItemsPerCart = count > 0 ? Math.round(totalItems / count * 100) / 100 : 0;
            const stats = {
                totalCarts: count,
                activeCarts,
                completedCarts,
                averageItemsPerCart
            };
            console.log(`📊 [TENANT-CART] Stats for tenant ${this.tenantId}:`, stats);
            return stats;
        }
        catch (error) {
            console.error(`❌ [TENANT-CART] Error getting cart stats: ${error}`);
            throw error;
        }
    }
    /**
     * Add tenant filter to query filters
     */
    addTenantFilter(filters) {
        return {
            ...filters,
            tenant_id: this.tenantId
        };
    }
}
exports.TenantAwareCartService = TenantAwareCartService;
//# sourceMappingURL=data:application/json;base64,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