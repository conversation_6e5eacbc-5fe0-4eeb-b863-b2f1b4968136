"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupTenantSalesChannels = setupTenantSalesChannels;
const core_flows_1 = require("@medusajs/core-flows");
const utils_1 = require("@medusajs/framework/utils");
const TENANT_CONFIGURATIONS = [
    {
        id: 'tenant-electronics-001',
        name: 'Electronics Store',
        description: 'Electronics and gadgets marketplace for ONDC',
        domain: 'electronics.ondc-seller.com',
        metadata: {
            tenantId: 'tenant-electronics-001',
            ondcConfig: {
                participantId: 'electronics-participant-001',
                subscriberId: 'electronics-subscriber-001',
                bppId: 'ondc-bpp-electronics-001',
                domain: 'electronics',
                region: 'IND'
            },
            currency: 'INR',
            timezone: 'Asia/Kolkata',
            features: ['products', 'orders', 'customers', 'analytics', 'inventory'],
            branding: {
                primaryColor: '#2563eb',
                secondaryColor: '#1e40af',
                logo: '/logos/electronics-logo.png'
            }
        }
    },
    {
        id: 'tenant-fashion-002',
        name: 'Fashion Store',
        description: 'Fashion and clothing marketplace for ONDC',
        domain: 'fashion.ondc-seller.com',
        metadata: {
            tenantId: 'tenant-fashion-002',
            ondcConfig: {
                participantId: 'fashion-participant-002',
                subscriberId: 'fashion-subscriber-002',
                bppId: 'ondc-bpp-fashion-002',
                domain: 'fashion',
                region: 'IND'
            },
            currency: 'INR',
            timezone: 'Asia/Kolkata',
            features: ['products', 'orders', 'customers', 'analytics', 'inventory'],
            branding: {
                primaryColor: '#ec4899',
                secondaryColor: '#db2777',
                logo: '/logos/fashion-logo.png'
            }
        }
    },
    {
        id: 'default',
        name: 'Default Store',
        description: 'Default marketplace for ONDC',
        domain: 'localhost',
        metadata: {
            tenantId: 'default',
            ondcConfig: {
                participantId: 'default-participant',
                subscriberId: 'default-subscriber',
                bppId: 'ondc-bpp-default',
                domain: 'general',
                region: 'IND'
            },
            currency: 'INR',
            timezone: 'Asia/Kolkata',
            features: ['products', 'orders', 'customers', 'analytics', 'inventory'],
            branding: {
                primaryColor: '#059669',
                secondaryColor: '#047857',
                logo: '/logos/default-logo.png'
            }
        }
    }
];
async function setupTenantSalesChannels(container) {
    const logger = container.resolve(utils_1.ContainerRegistrationKeys.LOGGER);
    try {
        logger.info("🚀 Starting tenant sales channels setup...");
        // Step 1: Create India region if it doesn't exist
        logger.info("📍 Setting up India region...");
        const { result: regions } = await (0, core_flows_1.createRegionsWorkflow)(container).run({
            input: {
                regions: [{
                        name: "India",
                        currency_code: "inr",
                        countries: ["in"],
                        payment_providers: ["pp_system_default"],
                        metadata: {
                            timezone: "Asia/Kolkata",
                            ondcRegion: true
                        }
                    }]
            }
        });
        const indiaRegion = regions[0];
        logger.info(`✅ India region created: ${indiaRegion.id}`);
        // Step 2: Create sales channels for each tenant
        logger.info("🏪 Creating tenant sales channels...");
        const { result: salesChannels } = await (0, core_flows_1.createSalesChannelsWorkflow)(container).run({
            input: {
                salesChannelsData: TENANT_CONFIGURATIONS.map(config => ({
                    name: config.name,
                    description: config.description,
                    is_disabled: false,
                    metadata: {
                        ...config.metadata,
                        regionId: indiaRegion.id,
                        setupDate: new Date().toISOString()
                    }
                }))
            }
        });
        logger.info(`✅ Created ${salesChannels.length} sales channels`);
        // Step 3: Create publishable API keys for each sales channel
        logger.info("🔑 Creating publishable API keys...");
        const tenantApiKeys = {};
        for (let i = 0; i < salesChannels.length; i++) {
            const salesChannel = salesChannels[i];
            const config = TENANT_CONFIGURATIONS[i];
            // Create API key
            const { result: apiKeys } = await (0, core_flows_1.createApiKeysWorkflow)(container).run({
                input: {
                    api_keys: [{
                            title: `${config.name} Publishable Key`,
                            type: "publishable",
                            created_by: "system",
                            metadata: {
                                tenantId: config.id,
                                salesChannelId: salesChannel.id,
                                createdAt: new Date().toISOString()
                            }
                        }]
                }
            });
            const apiKey = apiKeys[0];
            // Link API key to sales channel
            await (0, core_flows_1.linkSalesChannelsToApiKeyWorkflow)(container).run({
                input: {
                    id: apiKey.id,
                    add: [salesChannel.id]
                }
            });
            tenantApiKeys[config.id] = apiKey.token;
            logger.info(`✅ Tenant: ${config.id}`);
            logger.info(`   Sales Channel ID: ${salesChannel.id}`);
            logger.info(`   API Key: ${apiKey.token}`);
            logger.info(`   Domain: ${config.domain}`);
        }
        // Step 4: Update store configuration
        logger.info("🏬 Updating store configuration...");
        const query = container.resolve(utils_1.ContainerRegistrationKeys.QUERY);
        const { data: stores } = await query.graph({
            entity: "store",
            fields: ["id"],
            filters: {}
        });
        if (stores.length > 0) {
            const store = stores[0];
            await (0, core_flows_1.updateStoresWorkflow)(container).run({
                input: {
                    selector: { id: store.id },
                    update: {
                        supported_currencies: [
                            {
                                currency_code: "inr",
                                is_default: true
                            }
                        ],
                        default_sales_channel_id: salesChannels[0].id, // Default to first sales channel
                        metadata: {
                            multiTenantEnabled: true,
                            ondcCompliant: true,
                            setupDate: new Date().toISOString()
                        }
                    }
                }
            });
            logger.info(`✅ Store updated with multi-tenant configuration`);
        }
        // Step 5: Generate environment variables
        logger.info("📝 Generating environment variables...");
        const envVars = [
            "# Tenant-specific Publishable API Keys",
            "# Add these to your frontend .env.local file",
            ""
        ];
        Object.entries(tenantApiKeys).forEach(([tenantId, apiKey]) => {
            const envVarName = `NEXT_PUBLIC_${tenantId.toUpperCase().replace(/-/g, '_')}_API_KEY`;
            envVars.push(`${envVarName}=${apiKey}`);
        });
        envVars.push("");
        envVars.push("# Sales Channel IDs for admin operations");
        salesChannels.forEach((channel, index) => {
            const config = TENANT_CONFIGURATIONS[index];
            const envVarName = `${config.id.toUpperCase().replace(/-/g, '_')}_SALES_CHANNEL_ID`;
            envVars.push(`${envVarName}=${channel.id}`);
        });
        logger.info("\n" + "=".repeat(60));
        logger.info("🎉 TENANT SALES CHANNELS SETUP COMPLETE!");
        logger.info("=".repeat(60));
        logger.info("\n📋 SUMMARY:");
        logger.info(`   • Created ${salesChannels.length} sales channels`);
        logger.info(`   • Generated ${Object.keys(tenantApiKeys).length} publishable API keys`);
        logger.info(`   • Configured India region with INR currency`);
        logger.info(`   • Updated store for multi-tenant support`);
        logger.info("\n🔧 NEXT STEPS:");
        logger.info("   1. Add the environment variables below to your frontend");
        logger.info("   2. Update your frontend API client to use tenant-specific keys");
        logger.info("   3. Remove custom tenant middleware and endpoints");
        logger.info("   4. Test the native Medusa multi-tenancy");
        logger.info("\n📝 ENVIRONMENT VARIABLES:");
        logger.info("-".repeat(40));
        envVars.forEach(line => logger.info(line));
        logger.info("-".repeat(40));
        return {
            salesChannels,
            apiKeys: tenantApiKeys,
            region: indiaRegion,
            success: true
        };
    }
    catch (error) {
        logger.error("❌ Error setting up tenant sales channels:", error);
        throw error;
    }
}
// CLI execution
if (require.main === module) {
    const { getContainer } = require("@medusajs/framework");
    async function run() {
        const container = getContainer();
        await setupTenantSalesChannels(container);
        process.exit(0);
    }
    run().catch(error => {
        console.error("Setup failed:", error);
        process.exit(1);
    });
}
//# sourceMappingURL=data:application/json;base64,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