"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.migrateTenantDataToSalesChannels = migrateTenantDataToSalesChannels;
exports.verifyMigration = verifyMigration;
const utils_1 = require("@medusajs/framework/utils");
const core_flows_1 = require("@medusajs/core-flows");
async function migrateTenantDataToSalesChannels(container) {
    const logger = container.resolve(utils_1.ContainerRegistrationKeys.LOGGER);
    const query = container.resolve(utils_1.ContainerRegistrationKeys.QUERY);
    const stats = {
        productsProcessed: 0,
        productsMigrated: 0,
        customersProcessed: 0,
        customersMigrated: 0,
        ordersProcessed: 0,
        ordersMigrated: 0,
        errors: []
    };
    try {
        logger.info("🔄 Starting tenant data migration to sales channels...");
        // Step 1: Get all sales channels and create tenant mapping
        logger.info("📋 Building tenant to sales channel mapping...");
        const { data: salesChannels } = await query.graph({
            entity: "sales_channel",
            fields: ["id", "name", "metadata"],
            filters: {}
        });
        const tenantToSalesChannel = new Map();
        const salesChannelToTenant = new Map();
        salesChannels.forEach((channel) => {
            const tenantId = channel.metadata?.tenantId;
            if (tenantId) {
                tenantToSalesChannel.set(tenantId, channel.id);
                salesChannelToTenant.set(channel.id, tenantId);
                logger.info(`   • ${tenantId} → ${channel.id} (${channel.name})`);
            }
        });
        if (tenantToSalesChannel.size === 0) {
            throw new Error("No sales channels with tenant metadata found. Please run setup-tenant-sales-channels first.");
        }
        // Step 2: Migrate Products
        logger.info("📦 Migrating products to sales channels...");
        const { data: products } = await query.graph({
            entity: "product",
            fields: ["id", "title", "metadata"],
            filters: {}
        });
        stats.productsProcessed = products.length;
        logger.info(`   Found ${products.length} products to process`);
        const productsBySalesChannel = new Map();
        for (const product of products) {
            try {
                const tenantId = product.metadata?.tenant_id;
                if (tenantId && tenantToSalesChannel.has(tenantId)) {
                    const salesChannelId = tenantToSalesChannel.get(tenantId);
                    if (!productsBySalesChannel.has(salesChannelId)) {
                        productsBySalesChannel.set(salesChannelId, []);
                    }
                    productsBySalesChannel.get(salesChannelId).push(product.id);
                    stats.productsMigrated++;
                }
                else if (tenantId) {
                    stats.errors.push(`Product ${product.id} has unknown tenant_id: ${tenantId}`);
                }
            }
            catch (error) {
                stats.errors.push(`Error processing product ${product.id}: ${error.message}`);
            }
        }
        // Link products to sales channels in batches
        for (const [salesChannelId, productIds] of productsBySalesChannel.entries()) {
            try {
                const tenantId = salesChannelToTenant.get(salesChannelId);
                logger.info(`   Linking ${productIds.length} products to ${tenantId} (${salesChannelId})`);
                // Process in batches of 50 to avoid overwhelming the system
                const batchSize = 50;
                for (let i = 0; i < productIds.length; i += batchSize) {
                    const batch = productIds.slice(i, i + batchSize);
                    await (0, core_flows_1.linkProductsToSalesChannelWorkflow)(container).run({
                        input: {
                            id: salesChannelId,
                            add: batch
                        }
                    });
                    logger.info(`     Processed batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(productIds.length / batchSize)}`);
                }
                logger.info(`   ✅ Linked ${productIds.length} products to ${tenantId}`);
            }
            catch (error) {
                const errorMsg = `Failed to link products to sales channel ${salesChannelId}: ${error.message}`;
                stats.errors.push(errorMsg);
                logger.error(`   ❌ ${errorMsg}`);
            }
        }
        // Step 3: Migrate Customers (Update metadata to include sales channel reference)
        logger.info("👥 Migrating customers to sales channels...");
        const { data: customers } = await query.graph({
            entity: "customer",
            fields: ["id", "email", "metadata"],
            filters: {}
        });
        stats.customersProcessed = customers.length;
        logger.info(`   Found ${customers.length} customers to process`);
        for (const customer of customers) {
            try {
                const tenantId = customer.metadata?.tenant_id;
                if (tenantId && tenantToSalesChannel.has(tenantId)) {
                    const salesChannelId = tenantToSalesChannel.get(tenantId);
                    // Update customer metadata to include sales channel reference
                    await query.graph({
                        entity: "customer",
                        fields: ["id"],
                        filters: { id: customer.id },
                        data: {
                            metadata: {
                                ...customer.metadata,
                                sales_channel_id: salesChannelId,
                                migrated_at: new Date().toISOString()
                            }
                        }
                    });
                    stats.customersMigrated++;
                }
                else if (tenantId) {
                    stats.errors.push(`Customer ${customer.id} has unknown tenant_id: ${tenantId}`);
                }
            }
            catch (error) {
                stats.errors.push(`Error processing customer ${customer.id}: ${error.message}`);
            }
        }
        logger.info(`   ✅ Migrated ${stats.customersMigrated} customers`);
        // Step 4: Migrate Orders (Update metadata to include sales channel reference)
        logger.info("📋 Migrating orders to sales channels...");
        const { data: orders } = await query.graph({
            entity: "order",
            fields: ["id", "display_id", "metadata"],
            filters: {}
        });
        stats.ordersProcessed = orders.length;
        logger.info(`   Found ${orders.length} orders to process`);
        for (const order of orders) {
            try {
                const tenantId = order.metadata?.tenant_id;
                if (tenantId && tenantToSalesChannel.has(tenantId)) {
                    const salesChannelId = tenantToSalesChannel.get(tenantId);
                    // Update order metadata to include sales channel reference
                    await query.graph({
                        entity: "order",
                        fields: ["id"],
                        filters: { id: order.id },
                        data: {
                            metadata: {
                                ...order.metadata,
                                sales_channel_id: salesChannelId,
                                migrated_at: new Date().toISOString()
                            }
                        }
                    });
                    stats.ordersMigrated++;
                }
                else if (tenantId) {
                    stats.errors.push(`Order ${order.id} has unknown tenant_id: ${tenantId}`);
                }
            }
            catch (error) {
                stats.errors.push(`Error processing order ${order.id}: ${error.message}`);
            }
        }
        logger.info(`   ✅ Migrated ${stats.ordersMigrated} orders`);
        // Step 5: Generate migration report
        logger.info("\n" + "=".repeat(60));
        logger.info("🎉 TENANT DATA MIGRATION COMPLETE!");
        logger.info("=".repeat(60));
        logger.info("\n📊 MIGRATION STATISTICS:");
        logger.info(`   Products: ${stats.productsMigrated}/${stats.productsProcessed} migrated`);
        logger.info(`   Customers: ${stats.customersMigrated}/${stats.customersProcessed} migrated`);
        logger.info(`   Orders: ${stats.ordersMigrated}/${stats.ordersProcessed} migrated`);
        logger.info(`   Errors: ${stats.errors.length}`);
        if (stats.errors.length > 0) {
            logger.info("\n❌ ERRORS ENCOUNTERED:");
            stats.errors.forEach((error, index) => {
                logger.info(`   ${index + 1}. ${error}`);
            });
        }
        logger.info("\n🔧 NEXT STEPS:");
        logger.info("   1. Verify that products appear correctly in store APIs with publishable keys");
        logger.info("   2. Test admin APIs with sales channel filtering");
        logger.info("   3. Remove custom tenant middleware and endpoints");
        logger.info("   4. Update frontend to use native Medusa API client");
        logger.info("   5. Consider removing tenant_id columns after verification");
        return stats;
    }
    catch (error) {
        logger.error("❌ Migration failed:", error);
        stats.errors.push(`Migration failed: ${error.message}`);
        throw error;
    }
}
/**
 * Verify migration by testing API endpoints
 */
async function verifyMigration(container) {
    const logger = container.resolve(utils_1.ContainerRegistrationKeys.LOGGER);
    const query = container.resolve(utils_1.ContainerRegistrationKeys.QUERY);
    try {
        logger.info("🔍 Verifying migration...");
        // Get sales channels
        const { data: salesChannels } = await query.graph({
            entity: "sales_channel",
            fields: ["id", "name", "metadata"],
            filters: {}
        });
        for (const channel of salesChannels) {
            const tenantId = channel.metadata?.tenantId;
            if (!tenantId)
                continue;
            // Check if products are linked to this sales channel
            const { data: products } = await query.graph({
                entity: "product",
                fields: ["id", "title"],
                filters: {
                    sales_channels: {
                        id: channel.id
                    }
                }
            });
            logger.info(`   ${tenantId}: ${products.length} products linked to sales channel`);
        }
        logger.info("✅ Migration verification complete");
        return true;
    }
    catch (error) {
        logger.error("❌ Migration verification failed:", error);
        return false;
    }
}
// CLI execution
if (require.main === module) {
    const { getContainer } = require("@medusajs/framework");
    async function run() {
        const container = getContainer();
        const stats = await migrateTenantDataToSalesChannels(container);
        if (stats.errors.length === 0) {
            await verifyMigration(container);
        }
        process.exit(0);
    }
    run().catch(error => {
        console.error("Migration failed:", error);
        process.exit(1);
    });
}
//# sourceMappingURL=data:application/json;base64,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