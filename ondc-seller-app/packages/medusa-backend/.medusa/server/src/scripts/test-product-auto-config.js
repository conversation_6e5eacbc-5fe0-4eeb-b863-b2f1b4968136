#!/usr/bin/env node
"use strict";
/**
 * Test Script for Product Auto-Configuration
 *
 * This script tests the automatic sales channel assignment and inventory management
 * configuration for products created via API and Excel import.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.testProductAutoConfig = testProductAutoConfig;
const framework_1 = require("@medusajs/framework");
const product_auto_config_1 = require("../workflows/product-auto-config");
async function testProductAutoConfig() {
    console.log("🧪 Starting Product Auto-Configuration Test");
    try {
        // Initialize Medusa app
        const { container } = await (0, framework_1.MedusaApp)({
            directory: process.cwd(),
        });
        console.log("✅ Medusa app initialized");
        // Test 1: Validate default sales channel exists
        console.log("\n📺 Test 1: Validating default sales channel...");
        const productAutoConfigService = container.resolve("productAutoConfigService");
        const isValidSalesChannel = await productAutoConfigService.validateDefaultSalesChannel();
        if (isValidSalesChannel) {
            console.log(`✅ Default sales channel exists: ${productAutoConfigService.getDefaultSalesChannelId()}`);
        }
        else {
            console.log(`❌ Default sales channel not found: ${productAutoConfigService.getDefaultSalesChannelId()}`);
            console.log("⚠️  Please ensure the sales channel is created before running this test");
        }
        // Test 2: Create a test product using the auto-config workflow
        console.log("\n🛍️  Test 2: Creating test product with auto-configuration...");
        const testProductData = {
            title: "Test Auto-Config Product",
            handle: "test-auto-config-product",
            description: "A test product to verify automatic sales channel assignment and inventory management configuration",
            status: "draft",
            variants: [
                {
                    title: "Default Variant",
                    sku: "TEST-AUTO-CONFIG-001",
                    prices: [
                        {
                            currency_code: "inr",
                            amount: 1000
                        }
                    ]
                }
            ]
        };
        const workflowResult = await (0, product_auto_config_1.createProductsWithAutoConfigWorkflow)(container).run({
            input: {
                products: [testProductData],
                tenantId: "default"
            }
        });
        const createdProduct = workflowResult.result.products[0];
        const configResults = workflowResult.result.configurationResults;
        console.log(`✅ Product created: ${createdProduct.id}`);
        console.log(`📊 Auto-configuration results:`, configResults);
        // Test 3: Verify the configuration was applied
        console.log("\n🔍 Test 3: Verifying auto-configuration...");
        // Check sales channel assignment
        const salesChannelService = container.resolve("salesChannelService");
        try {
            const salesChannels = await salesChannelService.listAndCount({
                id: [productAutoConfigService.getDefaultSalesChannelId()]
            });
            if (salesChannels[1] > 0) {
                console.log(`✅ Sales channel verified: ${salesChannels[0][0].name}`);
            }
            else {
                console.log(`❌ Sales channel not found`);
            }
        }
        catch (error) {
            console.log(`⚠️  Could not verify sales channel: ${error.message}`);
        }
        // Check variant inventory management
        const productVariantService = container.resolve("productVariantService");
        try {
            const variants = await productVariantService.list({
                product_id: createdProduct.id
            });
            for (const variant of variants) {
                if (variant.manage_inventory === false) {
                    console.log(`✅ Variant ${variant.id} has manage_inventory: false`);
                }
                else {
                    console.log(`❌ Variant ${variant.id} has manage_inventory: ${variant.manage_inventory}`);
                }
            }
        }
        catch (error) {
            console.log(`⚠️  Could not verify variant configuration: ${error.message}`);
        }
        console.log("\n🎉 Product Auto-Configuration Test Completed!");
        console.log("\n📋 Summary:");
        console.log(`- Default Sales Channel ID: ${productAutoConfigService.getDefaultSalesChannelId()}`);
        console.log(`- Sales Channel Valid: ${isValidSalesChannel ? 'Yes' : 'No'}`);
        console.log(`- Test Product Created: ${createdProduct.id}`);
        console.log(`- Auto-Config Successful: ${configResults.successful}`);
        console.log(`- Auto-Config Failed: ${configResults.failed}`);
        if (configResults.errors.length > 0) {
            console.log(`- Errors: ${configResults.errors.join(', ')}`);
        }
    }
    catch (error) {
        console.error("❌ Test failed:", error);
        process.exit(1);
    }
}
// Run the test
if (require.main === module) {
    testProductAutoConfig()
        .then(() => {
        console.log("\n✅ Test script completed successfully");
        process.exit(0);
    })
        .catch((error) => {
        console.error("\n❌ Test script failed:", error);
        process.exit(1);
    });
}
//# sourceMappingURL=data:application/json;base64,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