"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * Database Query Interceptor
 * Intercepts database queries to add tenant filtering
 */
exports.default = async (container) => {
    try {
        console.log('🔧 Setting up database query interceptor for tenant filtering...');
        // This will be called after the database connection is established
        setTimeout(() => {
            try {
                // Get the database manager
                const manager = container.resolve('manager');
                if (manager && manager.query) {
                    const originalQuery = manager.query.bind(manager);
                    // Intercept all database queries
                    manager.query = function (sql, parameters) {
                        // Add tenant filtering to product queries
                        if (sql.includes('SELECT') && sql.includes('product') && !sql.includes('tenant_id')) {
                            // This is a basic example - you'd need more sophisticated SQL parsing
                            console.log('🔍 Intercepted product query:', sql);
                        }
                        return originalQuery(sql, parameters);
                    };
                }
                console.log('✅ Database query interceptor set up successfully');
            }
            catch (error) {
                console.error('❌ Failed to set up database interceptor:', error);
            }
        }, 1000);
    }
    catch (error) {
        console.error('❌ Failed to register database interceptor:', error);
        throw error;
    }
};
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZGF0YWJhc2UtaW50ZXJjZXB0b3IuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi9zcmMvbG9hZGVycy9kYXRhYmFzZS1pbnRlcmNlcHRvci50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOztBQUVBOzs7R0FHRztBQUNILGtCQUFlLEtBQUssRUFBRSxTQUEwQixFQUFpQixFQUFFO0lBQ2pFLElBQUksQ0FBQztRQUNILE9BQU8sQ0FBQyxHQUFHLENBQUMsa0VBQWtFLENBQUMsQ0FBQztRQUVoRixtRUFBbUU7UUFDbkUsVUFBVSxDQUFDLEdBQUcsRUFBRTtZQUNkLElBQUksQ0FBQztnQkFDSCwyQkFBMkI7Z0JBQzNCLE1BQU0sT0FBTyxHQUFHLFNBQVMsQ0FBQyxPQUFPLENBQUMsU0FBUyxDQUFDLENBQUM7Z0JBRTdDLElBQUksT0FBTyxJQUFJLE9BQU8sQ0FBQyxLQUFLLEVBQUUsQ0FBQztvQkFDN0IsTUFBTSxhQUFhLEdBQUcsT0FBTyxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUM7b0JBRWxELGlDQUFpQztvQkFDakMsT0FBTyxDQUFDLEtBQUssR0FBRyxVQUFVLEdBQVcsRUFBRSxVQUFrQjt3QkFDdkQsMENBQTBDO3dCQUMxQyxJQUFJLEdBQUcsQ0FBQyxRQUFRLENBQUMsUUFBUSxDQUFDLElBQUksR0FBRyxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxRQUFRLENBQUMsV0FBVyxDQUFDLEVBQUUsQ0FBQzs0QkFDcEYsc0VBQXNFOzRCQUN0RSxPQUFPLENBQUMsR0FBRyxDQUFDLCtCQUErQixFQUFFLEdBQUcsQ0FBQyxDQUFDO3dCQUNwRCxDQUFDO3dCQUVELE9BQU8sYUFBYSxDQUFDLEdBQUcsRUFBRSxVQUFVLENBQUMsQ0FBQztvQkFDeEMsQ0FBQyxDQUFDO2dCQUNKLENBQUM7Z0JBRUQsT0FBTyxDQUFDLEdBQUcsQ0FBQyxrREFBa0QsQ0FBQyxDQUFDO1lBQ2xFLENBQUM7WUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO2dCQUNmLE9BQU8sQ0FBQyxLQUFLLENBQUMsMENBQTBDLEVBQUUsS0FBSyxDQUFDLENBQUM7WUFDbkUsQ0FBQztRQUNILENBQUMsRUFBRSxJQUFJLENBQUMsQ0FBQztJQUNYLENBQUM7SUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1FBQ2YsT0FBTyxDQUFDLEtBQUssQ0FBQyw0Q0FBNEMsRUFBRSxLQUFLLENBQUMsQ0FBQztRQUNuRSxNQUFNLEtBQUssQ0FBQztJQUNkLENBQUM7QUFDSCxDQUFDLENBQUMifQ==