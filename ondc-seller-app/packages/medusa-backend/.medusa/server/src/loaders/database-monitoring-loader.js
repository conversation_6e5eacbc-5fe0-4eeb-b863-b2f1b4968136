"use strict";
/**
 * Database Monitoring Loader
 *
 * Initializes database connection monitoring and health checks
 * when the Medusa backend starts up.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.dbPool = exports.dbMonitor = void 0;
exports.initializeDatabaseMonitoring = initializeDatabaseMonitoring;
exports.getMonitoringStatus = getMonitoringStatus;
exports.createHealthCheckData = createHealthCheckData;
const database_monitor_1 = require("../services/database-monitor");
Object.defineProperty(exports, "dbMonitor", { enumerable: true, get: function () { return database_monitor_1.dbMonitor; } });
const database_pool_1 = require("../utils/database-pool");
Object.defineProperty(exports, "dbPool", { enumerable: true, get: function () { return database_pool_1.dbPool; } });
/**
 * Initialize database monitoring
 */
async function initializeDatabaseMonitoring(config) {
    const defaultConfig = {
        enabled: process.env.NODE_ENV !== 'test', // Disable in test environment
        intervalMs: 30000, // 30 seconds
        alertThresholds: {
            maxPoolUtilization: 80,
            maxResponseTime: 5000,
            maxErrorRate: 10,
            minHealthyConnections: 2,
        },
    };
    const finalConfig = { ...defaultConfig, ...config };
    console.log('🔧 [DB-MONITORING-LOADER] Initializing database monitoring...');
    try {
        // Initialize the database pool first
        database_pool_1.dbPool.initialize();
        console.log('✅ [DB-MONITORING-LOADER] Database pool initialized');
        // Perform initial health check
        const isHealthy = await database_pool_1.dbPool.healthCheck();
        if (!isHealthy) {
            console.warn('⚠️ [DB-MONITORING-LOADER] Initial database health check failed');
        }
        else {
            console.log('✅ [DB-MONITORING-LOADER] Initial database health check passed');
        }
        // Configure alert thresholds if provided
        if (finalConfig.alertThresholds) {
            database_monitor_1.dbMonitor.updateAlertThresholds(finalConfig.alertThresholds);
        }
        // Start monitoring if enabled
        if (finalConfig.enabled) {
            database_monitor_1.dbMonitor.startMonitoring(finalConfig.intervalMs);
            console.log(`✅ [DB-MONITORING-LOADER] Database monitoring started (interval: ${finalConfig.intervalMs}ms)`);
        }
        else {
            console.log('ℹ️ [DB-MONITORING-LOADER] Database monitoring disabled');
        }
        // Set up graceful shutdown
        setupGracefulShutdown();
        console.log('✅ [DB-MONITORING-LOADER] Database monitoring initialization complete');
    }
    catch (error) {
        console.error('❌ [DB-MONITORING-LOADER] Failed to initialize database monitoring:', error);
        throw error;
    }
}
/**
 * Set up graceful shutdown handlers
 */
function setupGracefulShutdown() {
    const shutdownHandler = async (signal) => {
        console.log(`🛑 [DB-MONITORING-LOADER] Received ${signal}, shutting down gracefully...`);
        try {
            // Stop monitoring
            database_monitor_1.dbMonitor.stopMonitoring();
            console.log('✅ [DB-MONITORING-LOADER] Database monitoring stopped');
            // Close database pool
            await database_pool_1.dbPool.close();
            console.log('✅ [DB-MONITORING-LOADER] Database pool closed');
            console.log('✅ [DB-MONITORING-LOADER] Graceful shutdown complete');
            process.exit(0);
        }
        catch (error) {
            console.error('❌ [DB-MONITORING-LOADER] Error during graceful shutdown:', error);
            process.exit(1);
        }
    };
    // Handle various shutdown signals
    process.on('SIGTERM', () => shutdownHandler('SIGTERM'));
    process.on('SIGINT', () => shutdownHandler('SIGINT'));
    process.on('SIGUSR2', () => shutdownHandler('SIGUSR2')); // Nodemon restart
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
        console.error('❌ [DB-MONITORING-LOADER] Uncaught exception:', error);
        shutdownHandler('uncaughtException');
    });
    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
        console.error('❌ [DB-MONITORING-LOADER] Unhandled rejection at:', promise, 'reason:', reason);
        shutdownHandler('unhandledRejection');
    });
}
/**
 * Get current monitoring status
 */
function getMonitoringStatus() {
    return {
        isActive: database_monitor_1.dbMonitor.getCurrentHealth() !== null,
        healthSummary: database_monitor_1.dbMonitor.getHealthSummary(),
        poolStats: database_pool_1.dbPool.getStats(),
    };
}
/**
 * Create health check endpoint data
 */
async function createHealthCheckData() {
    try {
        const poolHealthy = await database_pool_1.dbPool.healthCheck();
        const monitoringHealth = database_monitor_1.dbMonitor.getHealthSummary();
        const poolStats = database_pool_1.dbPool.getStats();
        return {
            status: poolHealthy && monitoringHealth.isHealthy ? 'healthy' : 'unhealthy',
            timestamp: new Date().toISOString(),
            database: {
                pool: {
                    healthy: poolHealthy,
                    stats: poolStats,
                },
                monitoring: monitoringHealth,
            },
            uptime: process.uptime(),
            memory: process.memoryUsage(),
        };
    }
    catch (error) {
        return {
            status: 'error',
            timestamp: new Date().toISOString(),
            error: error instanceof Error ? error.message : String(error),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
        };
    }
}
//# sourceMappingURL=data:application/json;base64,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