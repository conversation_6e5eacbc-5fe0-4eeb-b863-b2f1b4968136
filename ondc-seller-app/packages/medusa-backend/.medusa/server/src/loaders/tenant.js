"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const service_1 = require("../modules/tenant/service");
/**
 * Tenant Service Loader
 * Registers the tenant service in the Medusa container
 */
exports.default = async (container) => {
    try {
        // Register tenant service in container
        container.register({
            tenantModuleService: {
                resolve: () => new service_1.TenantService(container),
                lifetime: "SINGLETON"
            }
        });
        console.log("✅ Tenant service registered successfully");
    }
    catch (error) {
        console.error("❌ Failed to register tenant service:", error);
        throw error;
    }
};
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidGVuYW50LmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vc3JjL2xvYWRlcnMvdGVuYW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7O0FBQ0EsdURBQXlEO0FBRXpEOzs7R0FHRztBQUNILGtCQUFlLEtBQUssRUFBRSxTQUEwQixFQUFpQixFQUFFO0lBQ2pFLElBQUksQ0FBQztRQUNILHVDQUF1QztRQUN2QyxTQUFTLENBQUMsUUFBUSxDQUFDO1lBQ2pCLG1CQUFtQixFQUFFO2dCQUNuQixPQUFPLEVBQUUsR0FBRyxFQUFFLENBQUMsSUFBSSx1QkFBYSxDQUFDLFNBQVMsQ0FBQztnQkFDM0MsUUFBUSxFQUFFLFdBQVc7YUFDdEI7U0FDRixDQUFDLENBQUE7UUFFRixPQUFPLENBQUMsR0FBRyxDQUFDLDBDQUEwQyxDQUFDLENBQUE7SUFDekQsQ0FBQztJQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7UUFDZixPQUFPLENBQUMsS0FBSyxDQUFDLHNDQUFzQyxFQUFFLEtBQUssQ0FBQyxDQUFBO1FBQzVELE1BQU0sS0FBSyxDQUFBO0lBQ2IsQ0FBQztBQUNILENBQUMsQ0FBQSJ9