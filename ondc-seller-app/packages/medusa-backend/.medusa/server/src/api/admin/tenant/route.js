"use strict";
/**
 * Task 2.4: Create Tenant Configuration API
 *
 * Enhanced /admin/tenant endpoint with tenant validation and configuration management
 * Provides comprehensive tenant management capabilities including status checking,
 * configuration updates, and ONDC-specific settings.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.POST = exports.GET = void 0;
/**
 * GET /admin/tenant
 * Retrieve current tenant configuration and validation status
 */
const GET = async (req, res) => {
    try {
        console.log('🏢 [TENANT-API] GET /admin/tenant called');
        // Get tenant information from middleware
        const tenantId = req.tenantId;
        const tenant = req.tenant;
        if (!tenantId || !tenant) {
            res.status(400).json({
                error: 'TENANT_CONTEXT_MISSING',
                message: 'Tenant context not found. Ensure x-tenant-id header is provided.',
            });
            return;
        }
        // Perform comprehensive tenant validation
        const validation = await validateTenantConfiguration(tenant);
        // Get additional tenant metrics
        const metrics = await getTenantMetrics(tenantId);
        // Prepare comprehensive response
        const response = {
            success: true,
            tenant: {
                id: tenant.id,
                name: tenant.name,
                domain: tenant.domain,
                status: tenant.status,
                settings: tenant.settings,
                created_at: tenant.createdAt,
                updated_at: tenant.updatedAt,
            },
            validation,
            metrics,
            api_info: {
                endpoint: '/admin/tenant',
                method: 'GET',
                timestamp: new Date().toISOString(),
                tenant_context: tenantId,
            }
        };
        console.log(`✅ [TENANT-API] Tenant configuration retrieved for: ${tenantId}`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT-API] Error retrieving tenant configuration:', error);
        res.status(500).json({
            error: 'TENANT_CONFIG_ERROR',
            message: 'Failed to retrieve tenant configuration',
            details: error.message,
        });
    }
};
exports.GET = GET;
/**
 * POST /admin/tenant
 * Update tenant configuration and settings
 */
const POST = async (req, res) => {
    try {
        console.log('🏢 [TENANT-API] POST /admin/tenant called');
        // Get tenant information from middleware
        const tenantId = req.tenantId;
        const currentTenant = req.tenant;
        if (!tenantId || !currentTenant) {
            res.status(400).json({
                error: 'TENANT_CONTEXT_MISSING',
                message: 'Tenant context not found. Ensure x-tenant-id header is provided.',
            });
            return;
        }
        // Parse and validate update request
        const updateRequest = req.body;
        if (!updateRequest || Object.keys(updateRequest).length === 0) {
            res.status(400).json({
                error: 'INVALID_UPDATE_REQUEST',
                message: 'Update request body is required and cannot be empty.',
                allowed_fields: ['name', 'domain', 'settings', 'status'],
            });
            return;
        }
        // Validate update request
        const validationErrors = validateUpdateRequest(updateRequest);
        if (validationErrors.length > 0) {
            res.status(400).json({
                error: 'VALIDATION_FAILED',
                message: 'Tenant update validation failed',
                errors: validationErrors,
            });
            return;
        }
        // Apply updates to tenant configuration
        const updatedTenant = await updateTenantConfiguration(currentTenant, updateRequest);
        // Validate updated configuration
        const validation = await validateTenantConfiguration(updatedTenant);
        // Prepare response
        const response = {
            success: true,
            message: 'Tenant configuration updated successfully',
            tenant: {
                id: updatedTenant.id,
                name: updatedTenant.name,
                domain: updatedTenant.domain,
                status: updatedTenant.status,
                settings: updatedTenant.settings,
                updated_at: updatedTenant.updatedAt,
            },
            validation,
            changes_applied: Object.keys(updateRequest),
            api_info: {
                endpoint: '/admin/tenant',
                method: 'POST',
                timestamp: new Date().toISOString(),
                tenant_context: tenantId,
            }
        };
        console.log(`✅ [TENANT-API] Tenant configuration updated for: ${tenantId}`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT-API] Error updating tenant configuration:', error);
        res.status(500).json({
            error: 'TENANT_UPDATE_ERROR',
            message: 'Failed to update tenant configuration',
            details: error.message,
        });
    }
};
exports.POST = POST;
/**
 * Validate tenant configuration comprehensively
 */
async function validateTenantConfiguration(tenant) {
    const issues = [];
    // Check basic configuration
    if (!tenant.name || tenant.name.trim() === '') {
        issues.push('Tenant name is missing or empty');
    }
    if (!tenant.domain) {
        issues.push('Tenant domain is not configured');
    }
    // Check ONDC configuration
    const ondcConfig = tenant.settings.ondcConfig;
    const ondcConfigured = ondcConfig &&
        ondcConfig.participantId &&
        ondcConfig.subscriberId &&
        ondcConfig.bppId;
    if (!ondcConfigured) {
        issues.push('ONDC configuration is incomplete (missing participantId, subscriberId, or bppId)');
    }
    // Check required features
    const requiredFeatures = ['products', 'orders', 'customers'];
    const missingFeatures = requiredFeatures.filter(feature => !tenant.settings.features.includes(feature));
    if (missingFeatures.length > 0) {
        issues.push(`Missing required features: ${missingFeatures.join(', ')}`);
    }
    return {
        tenant_id: tenant.id,
        is_valid: issues.length === 0 && tenant.status === 'active',
        status: tenant.status,
        validation_details: {
            exists: true,
            active: tenant.status === 'active',
            configuration_complete: issues.length === 0,
            ondc_configured: !!ondcConfigured,
            last_validated: new Date().toISOString(),
        },
        issues: issues.length > 0 ? issues : undefined,
    };
}
/**
 * Get tenant metrics and statistics
 */
async function getTenantMetrics(tenantId) {
    // In a real implementation, this would query the database for tenant-specific metrics
    return {
        total_products: 0, // Would be calculated from database
        total_customers: 0, // Would be calculated from database
        total_orders: 0, // Would be calculated from database
        active_sessions: 1, // Current request
        last_activity: new Date().toISOString(),
        storage_used: '0 MB', // Would be calculated
        api_calls_today: 1, // Would be tracked
    };
}
/**
 * Validate update request
 */
function validateUpdateRequest(request) {
    const errors = [];
    if (request.name !== undefined) {
        if (typeof request.name !== 'string' || request.name.trim().length === 0) {
            errors.push('Name must be a non-empty string');
        }
        if (request.name.length > 100) {
            errors.push('Name must be less than 100 characters');
        }
    }
    if (request.domain !== undefined) {
        if (typeof request.domain !== 'string') {
            errors.push('Domain must be a string');
        }
        // Basic domain validation
        const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\..*$/;
        if (request.domain && !domainRegex.test(request.domain)) {
            errors.push('Domain must be a valid domain format');
        }
    }
    if (request.status !== undefined) {
        const validStatuses = ['active', 'inactive', 'suspended', 'pending'];
        if (!validStatuses.includes(request.status)) {
            errors.push(`Status must be one of: ${validStatuses.join(', ')}`);
        }
    }
    return errors;
}
/**
 * Update tenant configuration
 */
async function updateTenantConfiguration(currentTenant, updates) {
    // Create updated tenant configuration
    const updatedTenant = {
        ...currentTenant,
        updatedAt: new Date(),
    };
    // Apply updates
    if (updates.name !== undefined) {
        updatedTenant.name = updates.name;
    }
    if (updates.domain !== undefined) {
        updatedTenant.domain = updates.domain;
    }
    if (updates.status !== undefined) {
        updatedTenant.status = updates.status;
    }
    if (updates.settings) {
        updatedTenant.settings = {
            ...updatedTenant.settings,
            ...updates.settings,
        };
        // Handle nested ONDC config updates
        if (updates.settings.ondcConfig) {
            updatedTenant.settings.ondcConfig = {
                ...updatedTenant.settings.ondcConfig,
                ...updates.settings.ondcConfig,
            };
        }
    }
    // In a real implementation, this would persist to database
    console.log(`📝 [TENANT-API] Configuration updated for tenant: ${updatedTenant.id}`);
    return updatedTenant;
}
//# sourceMappingURL=data:application/json;base64,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