"use strict";
/**
 * Debug RLS API
 *
 * Simple test to debug Row Level Security implementation
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = void 0;
const pg_1 = require("pg");
const GET = async (req, res) => {
    try {
        const tenantId = req.tenant_id || 'default';
        console.log(`🐛 [DEBUG-RLS] Testing RLS for tenant: ${tenantId}`);
        // Create database connection
        const pool = new pg_1.Pool({
            user: 'medusa_app',
            password: 'medusa_app_password',
            host: 'localhost',
            port: 5432,
            database: 'medusa_backend',
            max: 1,
            idleTimeoutMillis: 30000,
            connectionTimeoutMillis: 2000,
        });
        const client = await pool.connect();
        try {
            // Start a transaction to ensure all operations use the same connection
            await client.query('BEGIN');
            // Test 1: Check current tenant context (should be empty initially)
            const initialContext = await client.query('SELECT current_setting($1, true) as initial_context', [
                'app.tenant_context.tenant_id'
            ]);
            console.log(`🐛 [DEBUG-RLS] Initial tenant context: "${initialContext.rows[0].initial_context}"`);
            // Test 2: Set tenant context using different methods
            console.log(`🐛 [DEBUG-RLS] Setting tenant context to: ${tenantId}`);
            // Method 1: Using set_config function
            const setConfigResult = await client.query('SELECT set_config($1, $2, false) as set_result', [
                'app.tenant_context.tenant_id',
                tenantId
            ]);
            console.log(`🐛 [DEBUG-RLS] set_config result:`, setConfigResult.rows[0]);
            // Test 3: Verify tenant context was set
            const verifyContext = await client.query('SELECT current_setting($1, true) as current_context', [
                'app.tenant_context.tenant_id'
            ]);
            console.log(`🐛 [DEBUG-RLS] Verified tenant context: "${verifyContext.rows[0].current_context}"`);
            // Test 4: Try alternative method if first didn't work
            if (!verifyContext.rows[0].current_context || verifyContext.rows[0].current_context === '') {
                console.log(`🐛 [DEBUG-RLS] First method failed, trying alternative...`);
                // Method 2: Using SET command
                await client.query(`SET app.tenant_context.tenant_id = '${tenantId}'`);
                const verifyContext2 = await client.query('SELECT current_setting($1, true) as current_context', [
                    'app.tenant_context.tenant_id'
                ]);
                console.log(`🐛 [DEBUG-RLS] Alternative method result: "${verifyContext2.rows[0].current_context}"`);
            }
            // Test 5: Count products with RLS
            const productCount = await client.query('SELECT COUNT(*) as count FROM product');
            console.log(`🐛 [DEBUG-RLS] Product count with RLS: ${productCount.rows[0].count}`);
            // Test 6: Get sample products
            const sampleProducts = await client.query('SELECT id, title, tenant_id FROM product LIMIT 3');
            console.log(`🐛 [DEBUG-RLS] Sample products:`, sampleProducts.rows);
            // Test 7: Count all tenant data
            const allTenantCounts = await client.query(`
        SELECT
          (SELECT COUNT(*) FROM product) as products,
          (SELECT COUNT(*) FROM customer) as customers,
          (SELECT COUNT(*) FROM cart) as carts,
          (SELECT COUNT(*) FROM "order") as orders
      `);
            console.log(`🐛 [DEBUG-RLS] All counts:`, allTenantCounts.rows[0]);
            // Commit transaction
            await client.query('COMMIT');
            const response = {
                success: true,
                message: `RLS debug test completed for tenant: ${tenantId}`,
                debug_info: {
                    tenant_id: tenantId,
                    initial_context: initialContext.rows[0].initial_context,
                    set_context: tenantId,
                    verified_context: verifyContext.rows[0].current_context,
                    product_count: parseInt(productCount.rows[0].count),
                    sample_products: sampleProducts.rows,
                    all_counts: allTenantCounts.rows[0],
                    context_matches: verifyContext.rows[0].current_context === tenantId,
                    rls_working: parseInt(productCount.rows[0].count) > 0 || tenantId === 'default'
                },
                timestamp: new Date().toISOString()
            };
            client.release();
            await pool.end();
            return res.json(response);
        }
        catch (error) {
            client.release();
            await pool.end();
            throw error;
        }
    }
    catch (error) {
        console.error(`❌ [DEBUG-RLS] Test failed: ${error}`);
        return res.status(500).json({
            success: false,
            message: 'RLS debug test failed',
            error: error instanceof Error ? error.message : 'Unknown error',
            tenant_id: req.tenant_id || 'default',
            timestamp: new Date().toISOString()
        });
    }
};
exports.GET = GET;
//# sourceMappingURL=data:application/json;base64,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