"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
exports.POST = POST;
async function GET(req, res) {
    console.log(`🚀 [TENANT FILTER] === CUSTOM COLLECTIONS ENDPOINT CALLED ===`);
    console.log(`🚀 [TENANT FILTER] Headers:`, JSON.stringify(req.headers, null, 2));
    console.log(`🚀 [TENANT FILTER] Query:`, JSON.stringify(req.query, null, 2));
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        console.log(`🔍 [TENANT FILTER] Processing collections request for tenant: ${tenantId}`);
        // Get query parameters
        const { limit = 50, offset = 0 } = req.query;
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
        });
        let collections = [];
        let count = 0;
        try {
            await client.connect();
            console.log(`🔗 [TENANT FILTER] Connected to database directly`);
            // Get total count for this tenant (exclude soft-deleted)
            const countResult = await client.query('SELECT COUNT(*) as total FROM product_collection WHERE tenant_id = $1 AND deleted_at IS NULL', [tenantId]);
            count = parseInt(countResult.rows[0]?.total || 0);
            console.log(`📊 [TENANT FILTER] Total collections for tenant ${tenantId}: ${count}`);
            // Get collections for this tenant (exclude soft-deleted)
            const result = await client.query(`
        SELECT 
          id, title, handle, metadata, created_at, updated_at, 
          deleted_at, tenant_id
        FROM product_collection 
        WHERE tenant_id = $1 AND deleted_at IS NULL
        ORDER BY created_at DESC 
        LIMIT $2 OFFSET $3
      `, [tenantId, parseInt(limit), parseInt(offset)]);
            collections = result.rows || [];
            // Parse metadata for each collection
            collections.forEach(collection => {
                if (typeof collection.metadata === 'string') {
                    try {
                        collection.metadata = JSON.parse(collection.metadata);
                    }
                    catch (e) {
                        console.log(`⚠️ Could not parse metadata for collection ${collection.id}`);
                    }
                }
            });
            console.log(`📦 [TENANT FILTER] Retrieved ${collections.length} collections`);
            await client.end();
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            await client.end().catch(() => { });
        }
        // Return response in Medusa format
        const response = {
            collections,
            count: collections.length,
            offset: parseInt(offset),
            limit: parseInt(limit),
            // Add tenant info for debugging
            _tenant: {
                id: tenantId,
                filtered: true,
                method: 'direct_db_connection',
                total_in_db: count
            }
        };
        // Add tenant headers for debugging
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Filtered', 'true');
        res.setHeader('X-Collections-Count', collections.length.toString());
        console.log(`📤 [TENANT FILTER] Returning response:`, {
            collections_count: collections.length,
            total_count: count,
            tenant_id: tenantId
        });
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error getting collections:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to get collections',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_collections_get_error',
                timestamp: new Date().toISOString()
            }
        });
    }
}
async function POST(req, res) {
    console.log(`🚀 [TENANT FILTER] === CUSTOM COLLECTION CREATE ENDPOINT CALLED ===`);
    console.log(`🚀 [TENANT FILTER] Body:`, JSON.stringify(req.body, null, 2));
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        console.log(`🔄 [TENANT FILTER] Creating collection for tenant: ${tenantId}`);
        // Get collection data from request body
        const collectionData = req.body;
        // Ensure tenant_id is injected and cannot be modified
        const collectionWithTenant = {
            ...collectionData,
            tenant_id: tenantId
        };
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
        });
        let createdCollection = null;
        try {
            await client.connect();
            console.log(`🔗 [TENANT FILTER] Connected to database for collection creation`);
            // Generate collection ID
            const collectionId = `pcol_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
            // Check if handle already exists for this tenant
            if (collectionWithTenant.handle) {
                const existingResult = await client.query('SELECT id FROM product_collection WHERE handle = $1 AND tenant_id = $2', [collectionWithTenant.handle, tenantId]);
                if (existingResult.rows.length > 0) {
                    await client.end();
                    return res.status(400).json({
                        type: 'invalid_data',
                        message: `Collection with handle: ${collectionWithTenant.handle}, already exists for tenant: ${tenantId}.`,
                        tenant_id: tenantId
                    });
                }
            }
            // Insert collection
            const insertCollectionQuery = `
        INSERT INTO product_collection (
          id, title, handle, metadata, tenant_id, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
        RETURNING *
      `;
            const collectionValues = [
                collectionId,
                collectionWithTenant.title,
                collectionWithTenant.handle,
                collectionWithTenant.metadata ? JSON.stringify({
                    ...collectionWithTenant.metadata,
                    tenant_id: tenantId
                }) : JSON.stringify({ tenant_id: tenantId }),
                tenantId
            ];
            const result = await client.query(insertCollectionQuery, collectionValues);
            createdCollection = result.rows[0];
            // Parse metadata
            if (typeof createdCollection.metadata === 'string') {
                try {
                    createdCollection.metadata = JSON.parse(createdCollection.metadata);
                }
                catch (e) {
                    console.log(`⚠️ Could not parse metadata for created collection ${createdCollection.id}`);
                }
            }
            console.log(`✅ [TENANT FILTER] Created collection ${collectionId} for tenant: ${tenantId}`);
            await client.end();
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            await client.end().catch(() => { });
            throw dbError;
        }
        // Return response in Medusa format
        const response = {
            collection: createdCollection,
            _tenant: {
                id: tenantId,
                injected: true,
                method: 'direct_db_creation'
            }
        };
        // Add tenant headers
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Injected', 'true');
        console.log(`📤 [TENANT FILTER] Returning created collection for tenant ${tenantId}`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error creating collection:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to create collection',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_collection_create_error',
                timestamp: new Date().toISOString(),
                stack: error.stack
            }
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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