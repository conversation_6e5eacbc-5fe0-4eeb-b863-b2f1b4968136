"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
exports.POST = POST;
exports.PUT = PUT;
exports.DELETE = DELETE;
async function GET(req, res) {
    console.log(`🚀 [TENANT FILTER] === TENANT-AWARE PROMOTION GET BY ID ENDPOINT CALLED ===`);
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        // Get promotion ID from URL params
        const promotionId = req.params?.id;
        if (!promotionId) {
            return res.status(400).json({
                error: 'Promotion ID is required',
                tenant_id: tenantId
            });
        }
        console.log(`🔍 [TENANT FILTER] Getting promotion ${promotionId} for tenant: ${tenantId}`);
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
        });
        let promotion = null;
        try {
            await client.connect();
            console.log(`🔗 [TENANT FILTER] Connected to database directly`);
            // Get promotion with tenant validation
            const result = await client.query(`
        SELECT 
          id, code, campaign_id, is_automatic, type, status, is_tax_inclusive,
          created_at, updated_at, deleted_at, tenant_id
        FROM promotion 
        WHERE id = $1 AND tenant_id = $2 AND deleted_at IS NULL
      `, [promotionId, tenantId]);
            promotion = result.rows[0] || null;
            console.log(`📦 [TENANT FILTER] Retrieved promotion: ${promotion ? 'Found' : 'Not Found'}`);
            if (promotion) {
                // Get application method
                try {
                    const appMethodQuery = `
            SELECT 
              id, currency_code, max_quantity, apply_to_quantity, buy_rules_min_quantity,
              type, target_type, allocation, raw_value, created_at, updated_at, deleted_at,
              promotion_id
            FROM promotion_application_method 
            WHERE promotion_id = $1 AND deleted_at IS NULL
          `;
                    const appMethodResult = await client.query(appMethodQuery, [promotion.id]);
                    const appMethod = appMethodResult.rows[0];
                    if (appMethod) {
                        // Parse raw_value if it's a string
                        if (typeof appMethod.raw_value === 'string') {
                            try {
                                appMethod.raw_value = JSON.parse(appMethod.raw_value);
                            }
                            catch (e) {
                                console.log(`⚠️ Could not parse raw_value for application method ${appMethod.id}`);
                            }
                        }
                        // Calculate value from raw_value
                        if (appMethod.raw_value && appMethod.raw_value.value) {
                            appMethod.value = parseFloat(appMethod.raw_value.value);
                        }
                        // Get target rules and buy rules
                        appMethod.target_rules = [];
                        appMethod.buy_rules = [];
                        promotion.application_method = appMethod;
                    }
                    else {
                        promotion.application_method = null;
                    }
                }
                catch (e) {
                    console.log(`⚠️ Could not fetch application method for promotion ${promotion.id}:`, e.message);
                    promotion.application_method = null;
                }
                // Get promotion rules
                try {
                    const rulesQuery = `
            SELECT 
              pr.id, pr.operator, pr.description, pr.attribute, pr.values,
              pr.created_at, pr.updated_at, pr.deleted_at
            FROM promotion_rule pr
            JOIN promotion_promotion_rule ppr ON pr.id = ppr.promotion_rule_id
            WHERE ppr.promotion_id = $1 AND pr.deleted_at IS NULL
          `;
                    const rulesResult = await client.query(rulesQuery, [promotion.id]);
                    promotion.rules = rulesResult.rows || [];
                }
                catch (e) {
                    console.log(`⚠️ Could not fetch rules for promotion ${promotion.id}:`, e.message);
                    promotion.rules = [];
                }
                // Get campaign if exists
                if (promotion.campaign_id) {
                    try {
                        const campaignQuery = `
              SELECT 
                id, name, description, starts_at, ends_at, created_at, updated_at, deleted_at
              FROM promotion_campaign 
              WHERE id = $1 AND deleted_at IS NULL
            `;
                        const campaignResult = await client.query(campaignQuery, [promotion.campaign_id]);
                        promotion.campaign = campaignResult.rows[0] || null;
                    }
                    catch (e) {
                        console.log(`⚠️ Could not fetch campaign for promotion ${promotion.id}:`, e.message);
                        promotion.campaign = null;
                    }
                }
                else {
                    promotion.campaign = null;
                }
            }
            await client.end();
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            await client.end().catch(() => { });
            throw dbError;
        }
        if (!promotion) {
            return res.status(404).json({
                error: 'Promotion not found or access denied',
                promotion_id: promotionId,
                tenant_id: tenantId,
                _debug: {
                    message: 'Promotion either does not exist or belongs to a different tenant'
                }
            });
        }
        // Return response in Medusa format
        const response = {
            promotion,
            _tenant: {
                id: tenantId,
                filtered: true,
                method: 'direct_db_connection'
            }
        };
        // Add tenant headers
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Filtered', 'true');
        console.log(`📤 [TENANT FILTER] Returning promotion ${promotionId} for tenant ${tenantId}`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error getting promotion:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to get promotion',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_promotion_get_error',
                timestamp: new Date().toISOString()
            }
        });
    }
}
async function POST(req, res) {
    console.log(`🚀 [TENANT FILTER] === TENANT-AWARE PROMOTION UPDATE ENDPOINT CALLED ===`);
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        // Get promotion ID from URL params
        const promotionId = req.params?.id;
        if (!promotionId) {
            return res.status(400).json({
                error: 'Promotion ID is required for update',
                tenant_id: tenantId
            });
        }
        console.log(`🔄 [TENANT FILTER] Updating promotion ${promotionId} for tenant: ${tenantId}`);
        // Get update data from request body
        const updateData = req.body;
        // Ensure tenant_id is injected and cannot be modified
        const promotionWithTenant = {
            ...updateData,
            tenant_id: tenantId
        };
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
        });
        let updatedPromotion = null;
        try {
            await client.connect();
            console.log(`🔗 [TENANT FILTER] Connected to database for promotion update`);
            // Start transaction
            await client.query('BEGIN');
            // First, verify the promotion belongs to this tenant
            const checkQuery = 'SELECT * FROM promotion WHERE id = $1 AND tenant_id = $2 AND deleted_at IS NULL';
            const checkResult = await client.query(checkQuery, [promotionId, tenantId]);
            if (checkResult.rows.length === 0) {
                await client.query('ROLLBACK');
                await client.end();
                return res.status(404).json({
                    error: 'Promotion not found or access denied',
                    promotion_id: promotionId,
                    tenant_id: tenantId,
                    _debug: {
                        message: 'Promotion either does not exist or belongs to a different tenant'
                    }
                });
            }
            // Update promotion
            const updatePromotionQuery = `
        UPDATE promotion 
        SET 
          code = COALESCE($1, code),
          campaign_id = COALESCE($2, campaign_id),
          is_automatic = COALESCE($3, is_automatic),
          type = COALESCE($4, type),
          status = COALESCE($5, status),
          is_tax_inclusive = COALESCE($6, is_tax_inclusive),
          updated_at = NOW()
        WHERE id = $7 AND tenant_id = $8
        RETURNING *
      `;
            const promotionValues = [
                promotionWithTenant.code,
                promotionWithTenant.campaign_id,
                promotionWithTenant.is_automatic,
                promotionWithTenant.type,
                promotionWithTenant.status,
                promotionWithTenant.is_tax_inclusive,
                promotionId,
                tenantId
            ];
            const promotionResult = await client.query(updatePromotionQuery, promotionValues);
            updatedPromotion = promotionResult.rows[0];
            // Commit transaction
            await client.query('COMMIT');
            console.log(`✅ [TENANT FILTER] Updated promotion ${promotionId} for tenant ${tenantId}`);
            await client.end();
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error during promotion update:', dbError);
            await client.query('ROLLBACK').catch(() => { });
            await client.end().catch(() => { });
            throw dbError;
        }
        // Return response in Medusa format
        const response = {
            promotion: updatedPromotion,
            _tenant: {
                id: tenantId,
                validated: true,
                method: 'direct_db_update'
            }
        };
        // Add tenant headers
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Validated', 'true');
        console.log(`📤 [TENANT FILTER] Returning updated promotion for tenant ${tenantId}`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error updating promotion:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to update promotion',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_promotion_update_error',
                timestamp: new Date().toISOString()
            }
        });
    }
}
// PUT method - alias to POST for standard REST API compatibility
async function PUT(req, res) {
    console.log(`🔄 [TENANT FILTER] PUT request received, delegating to POST handler`);
    return await POST(req, res);
}
async function DELETE(req, res) {
    console.log(`🚀 [TENANT FILTER] === TENANT-AWARE PROMOTION DELETE ENDPOINT CALLED ===`);
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        // Get promotion ID from URL params
        const promotionId = req.params?.id;
        if (!promotionId) {
            return res.status(400).json({
                error: 'Promotion ID is required for deletion',
                tenant_id: tenantId
            });
        }
        console.log(`🗑️ [TENANT FILTER] Deleting promotion ${promotionId} for tenant: ${tenantId}`);
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
        });
        try {
            await client.connect();
            console.log(`🔗 [TENANT FILTER] Connected to database directly`);
            // First, verify the promotion belongs to this tenant and get it
            const checkQuery = 'SELECT * FROM promotion WHERE id = $1 AND tenant_id = $2 AND deleted_at IS NULL';
            const checkResult = await client.query(checkQuery, [promotionId, tenantId]);
            if (checkResult.rows.length === 0) {
                await client.end();
                return res.status(404).json({
                    error: 'Promotion not found or access denied',
                    promotion_id: promotionId,
                    tenant_id: tenantId,
                    _debug: {
                        message: 'Promotion either does not exist or belongs to a different tenant'
                    }
                });
            }
            // Soft delete the promotion
            const deleteQuery = 'UPDATE promotion SET deleted_at = NOW() WHERE id = $1 AND tenant_id = $2 RETURNING *';
            const result = await client.query(deleteQuery, [promotionId, tenantId]);
            console.log(`✅ [TENANT FILTER] Deleted promotion ${promotionId} for tenant: ${tenantId}`);
            await client.end();
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            await client.end().catch(() => { });
            throw dbError;
        }
        // Return response in Medusa format
        const response = {
            id: promotionId,
            object: 'promotion',
            deleted: true,
            _tenant: {
                id: tenantId,
                validated: true,
                method: 'direct_db_connection'
            }
        };
        // Add tenant headers
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Validated', 'true');
        console.log(`📤 [TENANT FILTER] Confirmed deletion of promotion ${promotionId} for tenant ${tenantId}`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error deleting promotion:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to delete promotion',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_promotion_delete_error',
                timestamp: new Date().toISOString()
            }
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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