"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
exports.POST = POST;
exports.PUT = PUT;
exports.DELETE = DELETE;
// Helper function to preprocess data and fix data types
function preprocessUpdateData(data) {
    if (!data)
        return data;
    const processed = { ...data };
    // Fix variants data types
    if (processed.variants && Array.isArray(processed.variants)) {
        processed.variants = processed.variants.map((variant) => {
            const processedVariant = { ...variant };
            // Fix prices data types
            if (processedVariant.prices && Array.isArray(processedVariant.prices)) {
                processedVariant.prices = processedVariant.prices.map((price) => ({
                    ...price,
                    amount: typeof price.amount === 'string' ? parseFloat(price.amount) : price.amount,
                    min_quantity: typeof price.min_quantity === 'string'
                        ? parseInt(price.min_quantity)
                        : price.min_quantity,
                    max_quantity: typeof price.max_quantity === 'string'
                        ? parseInt(price.max_quantity)
                        : price.max_quantity,
                }));
            }
            // Fix numeric fields in variant
            if (processedVariant.weight && typeof processedVariant.weight === 'string') {
                processedVariant.weight = parseFloat(processedVariant.weight);
            }
            if (processedVariant.width && typeof processedVariant.width === 'string') {
                processedVariant.width = parseFloat(processedVariant.width);
            }
            if (processedVariant.length && typeof processedVariant.length === 'string') {
                processedVariant.length = parseFloat(processedVariant.length);
            }
            if (processedVariant.height && typeof processedVariant.height === 'string') {
                processedVariant.height = parseFloat(processedVariant.height);
            }
            return processedVariant;
        });
    }
    // Fix product-level numeric fields
    if (processed.weight && typeof processed.weight === 'string') {
        processed.weight = parseFloat(processed.weight);
    }
    if (processed.width && typeof processed.width === 'string') {
        processed.width = parseFloat(processed.width);
    }
    if (processed.length && typeof processed.length === 'string') {
        processed.length = parseFloat(processed.length);
    }
    if (processed.height && typeof processed.height === 'string') {
        processed.height = parseFloat(processed.height);
    }
    return processed;
}
async function GET(req, res) {
    console.log(`🚀 [TENANT FILTER] === CUSTOM PRODUCT GET BY ID ENDPOINT CALLED ===`);
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        // Get product ID from URL params
        const productId = req.params?.id;
        if (!productId) {
            return res.status(400).json({
                error: 'Product ID is required',
                tenant_id: tenantId,
            });
        }
        console.log(`🔍 [TENANT FILTER] Getting product ${productId} for tenant: ${tenantId}`);
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: process.env.DATABASE_URL ||
                process.env.POSTGRES_URL ||
                'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
        });
        let product = null;
        try {
            await client.connect();
            console.log(`🔗 [TENANT FILTER] Connected to database directly`);
            // Get product with tenant validation
            const result = await client.query(`
        SELECT 
          id, title, handle, description, status, 
          created_at, updated_at, tenant_id, metadata,
          thumbnail, weight, length, height, width,
          origin_country, hs_code, mid_code, material,
          collection_id, type_id, discountable, external_id,
          is_giftcard, subtitle, deleted_at
        FROM product 
        WHERE id = $1 AND tenant_id = $2
      `, [productId, tenantId]);
            product = result.rows[0] || null;
            console.log(`📦 [TENANT FILTER] Retrieved product: ${product ? 'Found' : 'Not Found'}`);
            if (product) {
                // Parse metadata if it's a string
                if (typeof product.metadata === 'string') {
                    try {
                        product.metadata = JSON.parse(product.metadata);
                    }
                    catch (e) {
                        console.log(`⚠️ Could not parse metadata for product ${product.id}`);
                    }
                }
                // Get variants with tenant filtering
                try {
                    const variantsResult = await client.query(`
            SELECT id, title, sku, barcode, ean, upc, allow_backorder, 
                   manage_inventory, hs_code, origin_country, mid_code, 
                   material, weight, length, height, width, metadata,
                   variant_rank, product_id, created_at, updated_at, 
                   deleted_at, tenant_id
            FROM product_variant 
            WHERE product_id = $1 AND tenant_id = $2
          `, [product.id, tenantId]);
                    product.variants = variantsResult.rows.map(variant => {
                        if (typeof variant.metadata === 'string') {
                            try {
                                variant.metadata = JSON.parse(variant.metadata);
                            }
                            catch (e) {
                                console.log(`⚠️ Could not parse variant metadata for ${variant.id}`);
                            }
                        }
                        return variant;
                    });
                }
                catch (e) {
                    console.log(`⚠️ Could not fetch variants for product ${product.id}:`, e.message);
                    product.variants = [];
                }
                // Get images (no tenant_id in image table)
                try {
                    const imagesResult = await client.query(`
            SELECT id, url, metadata, rank, product_id, created_at, updated_at, deleted_at
            FROM image 
            WHERE product_id = $1
          `, [product.id]);
                    // Filter by tenant_id from metadata
                    product.images = imagesResult.rows.filter(img => {
                        try {
                            const metadata = typeof img.metadata === 'string' ? JSON.parse(img.metadata) : img.metadata;
                            return metadata && metadata.tenant_id === tenantId;
                        }
                        catch {
                            return false;
                        }
                    });
                }
                catch (e) {
                    console.log(`⚠️ Could not fetch images for product ${product.id}:`, e.message);
                    product.images = [];
                }
                // Get options (no tenant_id in product_option table)
                try {
                    const optionsResult = await client.query(`
            SELECT id, title, metadata, product_id, created_at, updated_at, deleted_at
            FROM product_option 
            WHERE product_id = $1
          `, [product.id]);
                    // Filter by tenant_id from metadata
                    product.options = optionsResult.rows.filter(opt => {
                        try {
                            const metadata = typeof opt.metadata === 'string' ? JSON.parse(opt.metadata) : opt.metadata;
                            return metadata && metadata.tenant_id === tenantId;
                        }
                        catch {
                            return false;
                        }
                    });
                    // Get option values for each option
                    for (const option of product.options) {
                        try {
                            const valuesResult = await client.query(`
                SELECT id, value, option_id, metadata, created_at, updated_at, deleted_at
                FROM product_option_value 
                WHERE option_id = $1
              `, [option.id]);
                            // Filter by tenant_id from metadata
                            option.values = valuesResult.rows.filter(val => {
                                try {
                                    const metadata = typeof val.metadata === 'string' ? JSON.parse(val.metadata) : val.metadata;
                                    return metadata && metadata.tenant_id === tenantId;
                                }
                                catch {
                                    return false;
                                }
                            });
                        }
                        catch (e) {
                            console.log(`⚠️ Could not fetch option values for option ${option.id}:`, e.message);
                            option.values = [];
                        }
                    }
                }
                catch (e) {
                    console.log(`⚠️ Could not fetch options for product ${product.id}:`, e.message);
                    product.options = [];
                }
                // Get tags
                try {
                    const tagsResult = await client.query(`
            SELECT pt.id, pt.value, pt.metadata, pt.created_at, pt.updated_at, pt.deleted_at
            FROM product_tag pt
            JOIN product_tags ptags ON pt.id = ptags.product_tag_id
            WHERE ptags.product_id = $1 AND pt.tenant_id = $2
          `, [product.id, tenantId]);
                    product.tags = tagsResult.rows || [];
                }
                catch (e) {
                    console.log(`⚠️ Could not fetch tags for product ${product.id}:`, e.message);
                    product.tags = [];
                }
                // Get categories
                try {
                    const categoriesResult = await client.query(`
            SELECT pc.id, pc.name, pc.description, pc.handle, pc.metadata, 
                   pc.created_at, pc.updated_at, pc.deleted_at
            FROM product_category pc
            JOIN product_category_product pcp ON pc.id = pcp.product_category_id
            WHERE pcp.product_id = $1 AND pc.tenant_id = $2
          `, [product.id, tenantId]);
                    product.categories = categoriesResult.rows || [];
                }
                catch (e) {
                    console.log(`⚠️ Could not fetch categories for product ${product.id}:`, e.message);
                    product.categories = [];
                }
                // Get collection if collection_id exists
                if (product.collection_id) {
                    try {
                        const collectionResult = await client.query(`
              SELECT id, title, handle, metadata, created_at, updated_at, deleted_at
              FROM product_collection 
              WHERE id = $1 AND tenant_id = $2
            `, [product.collection_id, tenantId]);
                        product.collection = collectionResult.rows[0] || null;
                    }
                    catch (e) {
                        console.log(`⚠️ Could not fetch collection for product ${product.id}:`, e.message);
                        product.collection = null;
                    }
                }
                else {
                    product.collection = null;
                }
                // Get type if type_id exists
                if (product.type_id) {
                    try {
                        const typeResult = await client.query(`
              SELECT id, value, metadata, created_at, updated_at, deleted_at
              FROM product_type 
              WHERE id = $1 AND tenant_id = $2
            `, [product.type_id, tenantId]);
                        product.type = typeResult.rows[0] || null;
                    }
                    catch (e) {
                        console.log(`⚠️ Could not fetch type for product ${product.id}:`, e.message);
                        product.type = null;
                    }
                }
                else {
                    product.type = null;
                }
                // Get profiles (sales channels)
                try {
                    const profilesResult = await client.query(`
            SELECT sp.id, sp.name, sp.description, sp.is_disabled, sp.metadata,
                   sp.created_at, sp.updated_at, sp.deleted_at
            FROM sales_channel sp
            JOIN product_sales_channel psc ON sp.id = psc.sales_channel_id
            WHERE psc.product_id = $1 AND sp.tenant_id = $2
          `, [product.id, tenantId]);
                    product.profiles = profilesResult.rows || [];
                }
                catch (e) {
                    console.log(`⚠️ Could not fetch profiles for product ${product.id}:`, e.message);
                    product.profiles = [];
                }
                // Get variant prices
                for (const variant of product.variants) {
                    try {
                        const pricesResult = await client.query(`
              SELECT p.id, p.currency_code, p.amount, p.raw_amount, p.min_quantity, p.max_quantity,
                     p.price_list_id, p.created_at, p.updated_at, p.deleted_at
              FROM price p
              JOIN price_set ps ON p.price_set_id = ps.id
              JOIN product_variant_price_set pvps ON ps.id = pvps.price_set_id
              WHERE pvps.variant_id = $1 AND p.tenant_id = $2
            `, [variant.id, tenantId]);
                        variant.prices = pricesResult.rows || [];
                    }
                    catch (e) {
                        console.log(`⚠️ Could not fetch prices for variant ${variant.id}:`, e.message);
                        variant.prices = [];
                    }
                }
            }
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            throw dbError;
        }
        finally {
            // Always close the database connection
            try {
                await client.end();
                console.log(`🔗 [TENANT FILTER] Database connection closed`);
            }
            catch (closeError) {
                console.error('❌ [TENANT FILTER] Error closing database connection:', closeError);
            }
        }
        if (!product) {
            return res.status(404).json({
                error: 'Product not found or access denied',
                product_id: productId,
                tenant_id: tenantId,
                _debug: {
                    message: 'Product either does not exist or belongs to a different tenant',
                },
            });
        }
        // Return response in Medusa format
        const response = {
            product,
            _tenant: {
                id: tenantId,
                filtered: true,
                method: 'direct_db_connection',
            },
        };
        // Add tenant headers
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Filtered', 'true');
        console.log(`📤 [TENANT FILTER] Returning product ${productId} for tenant ${tenantId}`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error getting product:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to get product',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_get_error',
                timestamp: new Date().toISOString(),
            },
        });
    }
}
async function POST(req, res) {
    // CRITICAL: Preprocess request body IMMEDIATELY to fix data types before any Medusa validation
    if (req.body) {
        req.body = preprocessUpdateData(req.body);
    }
    console.log(`🚀 [TENANT FILTER] === COMPREHENSIVE PRODUCT UPDATE ENDPOINT CALLED ===`);
    console.log(`🚀 [TENANT FILTER] Body (after preprocessing):`, JSON.stringify(req.body, null, 2));
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        // Get product ID from URL params
        const productId = req.params?.id;
        if (!productId) {
            return res.status(400).json({
                error: 'Product ID is required for update',
                tenant_id: tenantId,
            });
        }
        console.log(`🔄 [TENANT FILTER] Comprehensive update for product ${productId}, tenant: ${tenantId}`);
        // Get update data from request body (already preprocessed)
        const updateData = req.body;
        // Ensure tenant_id is injected and cannot be modified
        const productWithTenant = {
            ...updateData,
            tenant_id: tenantId,
        };
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: process.env.DATABASE_URL ||
                process.env.POSTGRES_URL ||
                'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
        });
        let updatedProduct = null;
        try {
            await client.connect();
            console.log(`🔗 [TENANT FILTER] Connected to database for comprehensive update`);
            // Start transaction
            await client.query('BEGIN');
            // First, verify the product belongs to this tenant
            const checkQuery = 'SELECT * FROM product WHERE id = $1 AND tenant_id = $2';
            const checkResult = await client.query(checkQuery, [productId, tenantId]);
            if (checkResult.rows.length === 0) {
                await client.query('ROLLBACK');
                throw new Error('Product not found or access denied');
            }
            // 1. Update main product fields
            const updateProductQuery = `
        UPDATE product
        SET
          title = COALESCE($1, title),
          handle = COALESCE($2, handle),
          description = COALESCE($3, description),
          status = COALESCE($4, status),
          thumbnail = COALESCE($5, thumbnail),
          collection_id = COALESCE($6, collection_id),
          metadata = COALESCE($7, metadata),
          updated_at = NOW()
        WHERE id = $8 AND tenant_id = $9
        RETURNING *
      `;
            const productValues = [
                productWithTenant.title,
                productWithTenant.handle,
                productWithTenant.description,
                productWithTenant.status,
                productWithTenant.thumbnail,
                productWithTenant.collection_id,
                productWithTenant.metadata
                    ? JSON.stringify({
                        ...productWithTenant.metadata,
                        tenant_id: tenantId,
                    })
                    : null,
                productId,
                tenantId,
            ];
            const productResult = await client.query(updateProductQuery, productValues);
            updatedProduct = productResult.rows[0];
            // 2. Handle variants update (both existing and new variants)
            if (productWithTenant.variants && productWithTenant.variants.length > 0) {
                for (const variant of productWithTenant.variants) {
                    if (variant.id) {
                        // Update existing variant
                        const updateVariantQuery = `
              UPDATE product_variant 
              SET 
                title = COALESCE($1, title),
                sku = COALESCE($2, sku),
                barcode = COALESCE($3, barcode),
                ean = COALESCE($4, ean),
                upc = COALESCE($5, upc),
                material = COALESCE($6, material),
                weight = COALESCE($7, weight),
                width = COALESCE($8, width),
                length = COALESCE($9, length),
                height = COALESCE($10, height),
                metadata = COALESCE($11, metadata),
                updated_at = NOW()
              WHERE id = $12 AND product_id = $13 AND tenant_id = $14
            `;
                        const variantValues = [
                            variant.title,
                            variant.sku,
                            variant.barcode,
                            variant.ean,
                            variant.upc,
                            variant.material,
                            variant.weight,
                            variant.width,
                            variant.length,
                            variant.height,
                            variant.metadata
                                ? JSON.stringify({
                                    ...variant.metadata,
                                    tenant_id: tenantId,
                                })
                                : null,
                            variant.id,
                            productId,
                            tenantId,
                        ];
                        await client.query(updateVariantQuery, variantValues);
                        // Update variant prices if provided
                        if (variant.prices && variant.prices.length > 0) {
                            // First, delete existing prices for this variant
                            await client.query(`
                DELETE FROM price 
                WHERE price_set_id IN (
                  SELECT price_set_id FROM product_variant_price_set 
                  WHERE variant_id = $1
                ) AND tenant_id = $2
              `, [variant.id, tenantId]);
                            // Get the price set for this variant
                            const priceSetResult = await client.query(`
                SELECT price_set_id FROM product_variant_price_set 
                WHERE variant_id = $1
              `, [variant.id]);
                            let priceSetId = priceSetResult.rows[0]?.price_set_id;
                            if (!priceSetId) {
                                // Create new price set if it doesn't exist
                                priceSetId = `pset_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                                await client.query(`
                  INSERT INTO price_set (id, created_at, updated_at) 
                  VALUES ($1, NOW(), NOW())
                `, [priceSetId]);
                                const linkId = `pvps_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                                await client.query(`
                  INSERT INTO product_variant_price_set (id, variant_id, price_set_id, created_at, updated_at) 
                  VALUES ($1, $2, $3, NOW(), NOW())
                `, [linkId, variant.id, priceSetId]);
                            }
                            // Add new prices
                            for (const price of variant.prices) {
                                const priceId = `price_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                                const amount = typeof price.amount === 'string' ? parseInt(price.amount) : price.amount;
                                await client.query(`
                  INSERT INTO price (
                    id, currency_code, amount, raw_amount, price_set_id, tenant_id, created_at, updated_at
                  ) VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
                `, [
                                    priceId,
                                    price.currency_code || 'inr',
                                    amount,
                                    JSON.stringify({ value: amount.toString(), precision: 20 }),
                                    priceSetId,
                                    tenantId,
                                ]);
                            }
                        }
                    }
                    else {
                        // Create new variant (no id provided)
                        console.log(`🆕 [TENANT FILTER] Creating new variant: ${variant.title}`);
                        const newVariantId = `variant_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                        const createVariantQuery = `
              INSERT INTO product_variant (
                id, title, sku, barcode, ean, upc, material, weight, width, length, height,
                metadata, product_id, tenant_id, created_at, updated_at
              ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, NOW(), NOW())
              RETURNING *
            `;
                        const newVariantValues = [
                            newVariantId,
                            variant.title,
                            variant.sku,
                            variant.barcode || null,
                            variant.ean || null,
                            variant.upc || null,
                            variant.material || null,
                            variant.weight || null,
                            variant.width || null,
                            variant.length || null,
                            variant.height || null,
                            variant.metadata
                                ? JSON.stringify({
                                    ...variant.metadata,
                                    tenant_id: tenantId,
                                })
                                : JSON.stringify({ tenant_id: tenantId }),
                            productId,
                            tenantId,
                        ];
                        const newVariantResult = await client.query(createVariantQuery, newVariantValues);
                        const createdVariant = newVariantResult.rows[0];
                        console.log(`✅ [TENANT FILTER] Created new variant: ${createdVariant.id}`);
                        // Create prices for new variant if provided
                        if (variant.prices && variant.prices.length > 0) {
                            // Create new price set for this variant
                            const priceSetId = `pset_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                            await client.query(`
                INSERT INTO price_set (id, created_at, updated_at)
                VALUES ($1, NOW(), NOW())
              `, [priceSetId]);
                            // Link variant to price set
                            const linkId = `pvps_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                            await client.query(`
                INSERT INTO product_variant_price_set (id, variant_id, price_set_id, created_at, updated_at)
                VALUES ($1, $2, $3, NOW(), NOW())
              `, [linkId, createdVariant.id, priceSetId]);
                            // Add prices
                            for (const price of variant.prices) {
                                const priceId = `price_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                                const amount = typeof price.amount === 'string' ? parseInt(price.amount) : price.amount;
                                await client.query(`
                  INSERT INTO price (
                    id, currency_code, amount, raw_amount, price_set_id, tenant_id, created_at, updated_at
                  ) VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
                `, [
                                    priceId,
                                    price.currency_code || 'inr',
                                    amount,
                                    JSON.stringify({ value: amount.toString(), precision: 20 }),
                                    priceSetId,
                                    tenantId,
                                ]);
                            }
                        }
                    }
                }
            }
            // 3. Handle category associations update
            if (productWithTenant.categories !== undefined) {
                console.log(`🏷️ [TENANT FILTER] Updating categories for product ${productId}`);
                // Delete existing category associations for this product and tenant
                await client.query(`
          DELETE FROM product_category_product
          WHERE product_id = $1 AND tenant_id = $2
        `, [productId, tenantId]);
                // Add new category associations if provided
                if (productWithTenant.categories && productWithTenant.categories.length > 0) {
                    for (const category of productWithTenant.categories) {
                        // Handle both string IDs and objects with id property
                        const categoryId = typeof category === 'string' ? category : category.id;
                        if (!categoryId) {
                            console.warn(`⚠️ [TENANT FILTER] Skipping invalid category:`, category);
                            continue;
                        }
                        // Verify category exists and belongs to this tenant
                        const categoryCheckResult = await client.query(`
              SELECT id FROM product_category
              WHERE id = $1 AND tenant_id = $2
            `, [categoryId, tenantId]);
                        if (categoryCheckResult.rows.length === 0) {
                            console.warn(`⚠️ [TENANT FILTER] Category ${categoryId} not found for tenant ${tenantId}, skipping`);
                            continue;
                        }
                        const insertCategoryLinkQuery = `
              INSERT INTO product_category_product (
                product_id, product_category_id, tenant_id, created_at, updated_at
              ) VALUES ($1, $2, $3, NOW(), NOW())
              ON CONFLICT (product_id, product_category_id) DO NOTHING
            `;
                        await client.query(insertCategoryLinkQuery, [productId, categoryId, tenantId]);
                    }
                    console.log(`✅ [TENANT FILTER] Updated ${productWithTenant.categories.length} category associations`);
                }
                else {
                    console.log(`✅ [TENANT FILTER] Removed all category associations (empty categories array)`);
                }
            }
            // 4. Handle tag associations update
            if (productWithTenant.tags !== undefined) {
                console.log(`🏷️ [TENANT FILTER] Updating tags for product ${productId}`);
                // Delete existing tag associations for this product
                await client.query(`
          DELETE FROM product_tags
          WHERE product_id = $1
        `, [productId]);
                // Add new tag associations if provided
                if (productWithTenant.tags && productWithTenant.tags.length > 0) {
                    for (const tag of productWithTenant.tags) {
                        // Handle both tag IDs and objects with id property
                        const tagId = typeof tag === 'string' ? tag : tag.id;
                        if (!tagId) {
                            console.warn(`⚠️ [TENANT FILTER] Skipping invalid tag:`, tag);
                            continue;
                        }
                        // Verify tag exists and belongs to this tenant
                        const tagCheckResult = await client.query(`
              SELECT id FROM product_tag
              WHERE id = $1 AND tenant_id = $2
            `, [tagId, tenantId]);
                        if (tagCheckResult.rows.length === 0) {
                            console.warn(`⚠️ [TENANT FILTER] Tag ${tagId} not found for tenant ${tenantId}, skipping`);
                            continue;
                        }
                        // Link tag to product
                        const insertTagLinkQuery = `
              INSERT INTO product_tags (
                product_id, product_tag_id
              ) VALUES ($1, $2)
              ON CONFLICT (product_id, product_tag_id) DO NOTHING
            `;
                        await client.query(insertTagLinkQuery, [productId, tagId]);
                    }
                    console.log(`✅ [TENANT FILTER] Updated ${productWithTenant.tags.length} tag associations`);
                }
                else {
                    console.log(`✅ [TENANT FILTER] Removed all tag associations (empty tags array)`);
                }
            }
            // 5. Handle images update
            if (productWithTenant.images && productWithTenant.images.length > 0) {
                // Delete existing images for this product and tenant
                await client.query(`
          DELETE FROM image
          WHERE product_id = $1 AND metadata::jsonb @> $2
        `, [productId, JSON.stringify({ tenant_id: tenantId })]);
                // Add new images
                for (const image of productWithTenant.images) {
                    const imageId = `img_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                    await client.query(`
            INSERT INTO image (
              id, url, product_id, metadata, rank, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
          `, [imageId, image.url, productId, JSON.stringify({ tenant_id: tenantId }), 0]);
                }
            }
            // Commit transaction
            await client.query('COMMIT');
            console.log(`✅ [TENANT FILTER] Comprehensive update completed for product ${productId}`);
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error during update:', dbError);
            await client.query('ROLLBACK').catch(() => { });
            // Handle specific error cases
            if (dbError.message === 'Product not found or access denied') {
                return res.status(404).json({
                    error: 'Product not found or access denied',
                    product_id: productId,
                    tenant_id: tenantId,
                    _debug: {
                        message: 'Product either does not exist or belongs to a different tenant',
                    },
                });
            }
            throw dbError;
        }
        finally {
            // Always close the database connection
            try {
                await client.end();
                console.log(`🔗 [TENANT FILTER] Database connection closed`);
            }
            catch (closeError) {
                console.error('❌ [TENANT FILTER] Error closing database connection:', closeError);
            }
        }
        // Return response in Medusa format
        const response = {
            product: updatedProduct,
            _tenant: {
                id: tenantId,
                validated: true,
                method: 'comprehensive_db_update',
                updated_entities: {
                    product: 1,
                    variants: productWithTenant.variants?.length || 0,
                    images: productWithTenant.images?.length || 0,
                    categories: productWithTenant.categories?.length || 0,
                    tags: productWithTenant.tags?.length || 0,
                    collection: productWithTenant.collection_id ? 1 : 0,
                },
            },
        };
        // Add tenant headers
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Validated', 'true');
        console.log(`📤 [TENANT FILTER] Returning comprehensively updated product for tenant ${tenantId}`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error in comprehensive product update:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to update comprehensive product',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_comprehensive_update_error',
                timestamp: new Date().toISOString(),
                stack: error.stack,
            },
        });
    }
}
// PUT method - alias to POST for standard REST API compatibility
async function PUT(req, res) {
    console.log(`🔄 [TENANT FILTER] PUT request received, delegating to POST handler`);
    return await POST(req, res);
}
async function DELETE(req, res) {
    console.log(`🚀 [TENANT FILTER] === CUSTOM PRODUCT DELETE ENDPOINT CALLED ===`);
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        // Get product ID from URL params
        const productId = req.params?.id;
        if (!productId) {
            return res.status(400).json({
                error: 'Product ID is required for deletion',
                tenant_id: tenantId,
            });
        }
        console.log(`🗑️ [TENANT FILTER] Deleting product ${productId} for tenant: ${tenantId}`);
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: process.env.DATABASE_URL ||
                process.env.POSTGRES_URL ||
                'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
        });
        let deletedProduct = null;
        try {
            await client.connect();
            console.log(`🔗 [TENANT FILTER] Connected to database directly`);
            // First, verify the product belongs to this tenant and get it
            const checkQuery = 'SELECT * FROM product WHERE id = $1 AND tenant_id = $2';
            const checkResult = await client.query(checkQuery, [productId, tenantId]);
            if (checkResult.rows.length === 0) {
                throw new Error('Product not found or access denied');
            }
            // Delete the product
            const deleteQuery = 'DELETE FROM product WHERE id = $1 AND tenant_id = $2 RETURNING *';
            const result = await client.query(deleteQuery, [productId, tenantId]);
            deletedProduct = result.rows[0];
            console.log(`✅ [TENANT FILTER] Deleted product ${productId} for tenant: ${tenantId}`);
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            // Handle specific error cases
            if (dbError.message === 'Product not found or access denied') {
                return res.status(404).json({
                    error: 'Product not found or access denied',
                    product_id: productId,
                    tenant_id: tenantId,
                    _debug: {
                        message: 'Product either does not exist or belongs to a different tenant',
                    },
                });
            }
            throw dbError;
        }
        finally {
            // Always close the database connection
            try {
                await client.end();
                console.log(`🔗 [TENANT FILTER] Database connection closed`);
            }
            catch (closeError) {
                console.error('❌ [TENANT FILTER] Error closing database connection:', closeError);
            }
        }
        // Return response in Medusa format
        const response = {
            id: productId,
            object: 'product',
            deleted: true,
            _tenant: {
                id: tenantId,
                validated: true,
                method: 'direct_db_connection',
            },
        };
        // Add tenant headers
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Validated', 'true');
        console.log(`📤 [TENANT FILTER] Confirmed deletion of product ${productId} for tenant ${tenantId}`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error deleting product:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to delete product',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_delete_error',
                timestamp: new Date().toISOString(),
            },
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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