"use strict";
/**
 * Comprehensive Tenant Isolation Analysis
 *
 * Provides detailed analysis of tenant isolation status across all Medusa entities
 * and generates actionable recommendations for complete multi-tenant implementation.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = void 0;
const GET = async (req, res) => {
    try {
        const tenantId = req.tenant_id || 'default';
        console.log(`📊 [TENANT-ISOLATION-ANALYSIS] Analyzing tenant isolation for: ${tenantId}`);
        const startTime = Date.now();
        const analysis = {
            timestamp: new Date().toISOString(),
            tenantId,
            summary: {
                totalEntities: 0,
                completeIsolation: 0,
                partialIsolation: 0,
                missingIsolation: 0,
                overallStatus: 'INCOMPLETE',
                readinessPercentage: 0
            },
            entityAnalyses: [],
            criticalGaps: [],
            implementationPlan: [],
            nextSteps: []
        };
        // Define all Medusa entities that need tenant isolation
        const entitiesToAnalyze = [
            {
                entityType: 'Products',
                tableName: 'product',
                serviceNames: ['product'],
                priority: 'HIGH'
            },
            {
                entityType: 'Product Variants',
                tableName: 'product_variant',
                serviceNames: ['product'],
                priority: 'HIGH'
            },
            {
                entityType: 'Customers',
                tableName: 'customer',
                serviceNames: ['customer'],
                priority: 'HIGH'
            },
            {
                entityType: 'Orders',
                tableName: 'order',
                serviceNames: ['order'],
                priority: 'HIGH'
            },
            {
                entityType: 'Carts',
                tableName: 'cart',
                serviceNames: ['cart'],
                priority: 'HIGH'
            },
            {
                entityType: 'Product Categories',
                tableName: 'product_category',
                serviceNames: ['productCategory', 'product-category'],
                priority: 'MEDIUM'
            },
            {
                entityType: 'Product Collections',
                tableName: 'product_collection',
                serviceNames: ['productCollection', 'collection'],
                priority: 'MEDIUM'
            },
            {
                entityType: 'Product Tags',
                tableName: 'product_tag',
                serviceNames: ['productTag', 'tag'],
                priority: 'MEDIUM'
            },
            {
                entityType: 'Regions',
                tableName: 'region',
                serviceNames: ['region'],
                priority: 'LOW'
            },
            {
                entityType: 'Sales Channels',
                tableName: 'sales_channel',
                serviceNames: ['salesChannel'],
                priority: 'MEDIUM'
            },
            {
                entityType: 'Discounts',
                tableName: 'discount',
                serviceNames: ['discount'],
                priority: 'MEDIUM'
            },
            {
                entityType: 'Gift Cards',
                tableName: 'gift_card',
                serviceNames: ['giftCard'],
                priority: 'LOW'
            }
        ];
        // Analyze each entity
        for (const entity of entitiesToAnalyze) {
            const entityAnalysis = await analyzeEntity(req, entity);
            analysis.entityAnalyses.push(entityAnalysis);
            analysis.summary.totalEntities++;
            switch (entityAnalysis.tenantIsolationStatus) {
                case 'COMPLETE':
                    analysis.summary.completeIsolation++;
                    break;
                case 'PARTIAL':
                    analysis.summary.partialIsolation++;
                    break;
                case 'MISSING':
                    analysis.summary.missingIsolation++;
                    break;
            }
        }
        // Calculate overall status
        analysis.summary.readinessPercentage = Math.round(((analysis.summary.completeIsolation + (analysis.summary.partialIsolation * 0.5)) / analysis.summary.totalEntities) * 100);
        if (analysis.summary.readinessPercentage >= 90) {
            analysis.summary.overallStatus = 'READY';
        }
        else if (analysis.summary.readinessPercentage >= 50) {
            analysis.summary.overallStatus = 'PARTIAL';
        }
        else {
            analysis.summary.overallStatus = 'INCOMPLETE';
        }
        // Generate critical gaps and implementation plan
        generateCriticalGaps(analysis);
        generateImplementationPlan(analysis);
        generateNextSteps(analysis);
        // Performance metrics
        analysis.performance = {
            analysisTime: Date.now() - startTime,
            entitiesAnalyzed: analysis.summary.totalEntities
        };
        console.log(`📊 [TENANT-ISOLATION-ANALYSIS] Analysis completed:`, analysis.summary);
        return res.json({
            success: true,
            message: 'Comprehensive tenant isolation analysis completed',
            analysis
        });
    }
    catch (error) {
        console.error(`❌ [TENANT-ISOLATION-ANALYSIS] Analysis failed:`, error);
        return res.status(500).json({
            success: false,
            message: 'Tenant isolation analysis failed',
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        });
    }
};
exports.GET = GET;
async function analyzeEntity(req, entity) {
    const analysis = {
        entityType: entity.entityType,
        tableName: entity.tableName,
        hasTenantColumn: false, // Would need database inspection
        hasRLSPolicy: false, // Would need database inspection
        serviceAvailable: false,
        crudOperations: {
            create: false,
            read: false,
            update: false,
            delete: false
        },
        tenantIsolationStatus: 'MISSING',
        recommendations: []
    };
    // Test service availability
    for (const serviceName of entity.serviceNames) {
        try {
            const service = req.scope.resolve(serviceName);
            if (service) {
                analysis.serviceAvailable = true;
                // Test CRUD operations
                if (typeof service.listProducts === 'function' ||
                    typeof service.listCustomers === 'function' ||
                    typeof service.listOrders === 'function') {
                    analysis.crudOperations.read = true;
                }
                if (typeof service.createProducts === 'function' ||
                    typeof service.createCustomers === 'function') {
                    analysis.crudOperations.create = true;
                }
                if (typeof service.updateProducts === 'function' ||
                    typeof service.updateCustomers === 'function') {
                    analysis.crudOperations.update = true;
                }
                if (typeof service.deleteProducts === 'function' ||
                    typeof service.deleteCustomers === 'function') {
                    analysis.crudOperations.delete = true;
                }
                break; // Found working service
            }
        }
        catch (error) {
            // Service not available, continue to next
        }
    }
    // Determine isolation status
    const crudCount = Object.values(analysis.crudOperations).filter(Boolean).length;
    if (analysis.serviceAvailable && crudCount >= 3) {
        analysis.tenantIsolationStatus = 'PARTIAL'; // Service available but no RLS
    }
    else if (analysis.serviceAvailable && crudCount >= 1) {
        analysis.tenantIsolationStatus = 'PARTIAL';
    }
    else {
        analysis.tenantIsolationStatus = 'MISSING';
    }
    // Generate recommendations
    generateEntityRecommendations(analysis, entity);
    return analysis;
}
function generateEntityRecommendations(analysis, entity) {
    const recommendations = [];
    if (!analysis.serviceAvailable) {
        recommendations.push(`Implement or fix ${entity.entityType} service resolution`);
    }
    if (!analysis.hasTenantColumn) {
        recommendations.push(`Add tenant_id column to ${analysis.tableName} table`);
    }
    if (!analysis.hasRLSPolicy) {
        recommendations.push(`Implement RLS policy for ${analysis.tableName} table`);
    }
    if (!analysis.crudOperations.create) {
        recommendations.push(`Implement CREATE operation with tenant_id injection`);
    }
    if (!analysis.crudOperations.read) {
        recommendations.push(`Implement READ operations with tenant filtering`);
    }
    if (!analysis.crudOperations.update) {
        recommendations.push(`Implement UPDATE operations with tenant validation`);
    }
    if (!analysis.crudOperations.delete) {
        recommendations.push(`Implement DELETE operations with tenant validation`);
    }
    if (entity.priority === 'HIGH') {
        recommendations.push(`HIGH PRIORITY: Critical for multi-tenant functionality`);
    }
    analysis.recommendations = recommendations;
}
function generateCriticalGaps(analysis) {
    const criticalGaps = [];
    const highPriorityMissing = analysis.entityAnalyses.filter((e) => e.tenantIsolationStatus === 'MISSING' &&
        ['Products', 'Customers', 'Orders', 'Carts'].includes(e.entityType));
    if (highPriorityMissing.length > 0) {
        criticalGaps.push({
            type: 'HIGH_PRIORITY_ENTITIES_MISSING',
            description: 'Critical entities lack tenant isolation',
            entities: highPriorityMissing.map((e) => e.entityType),
            impact: 'CRITICAL'
        });
    }
    const noRLSPolicies = analysis.entityAnalyses.filter((e) => !e.hasRLSPolicy);
    if (noRLSPolicies.length > 0) {
        criticalGaps.push({
            type: 'NO_RLS_POLICIES',
            description: 'Database-level tenant isolation not implemented',
            count: noRLSPolicies.length,
            impact: 'HIGH'
        });
    }
    const noTenantColumns = analysis.entityAnalyses.filter((e) => !e.hasTenantColumn);
    if (noTenantColumns.length > 0) {
        criticalGaps.push({
            type: 'NO_TENANT_COLUMNS',
            description: 'Database tables missing tenant_id columns',
            count: noTenantColumns.length,
            impact: 'HIGH'
        });
    }
    analysis.criticalGaps = criticalGaps;
}
function generateImplementationPlan(analysis) {
    const plan = [
        {
            phase: 'Phase 1: Database Schema',
            priority: 'CRITICAL',
            tasks: [
                'Add tenant_id columns to all entity tables',
                'Create database migration scripts',
                'Implement RLS policies for all tables',
                'Test RLS policies with sample data'
            ],
            estimatedTime: '2-3 days'
        },
        {
            phase: 'Phase 2: Service Layer',
            priority: 'HIGH',
            tasks: [
                'Complete tenant-aware service implementations',
                'Add missing CRUD operations',
                'Implement tenant validation in all services',
                'Add comprehensive error handling'
            ],
            estimatedTime: '3-4 days'
        },
        {
            phase: 'Phase 3: API Integration',
            priority: 'HIGH',
            tasks: [
                'Update all Medusa API endpoints',
                'Implement tenant-aware middleware for all routes',
                'Add tenant validation to all endpoints',
                'Test all CRUD operations'
            ],
            estimatedTime: '2-3 days'
        },
        {
            phase: 'Phase 4: Data Migration',
            priority: 'MEDIUM',
            tasks: [
                'Migrate existing data with tenant assignments',
                'Validate data integrity after migration',
                'Test cross-tenant isolation',
                'Performance testing'
            ],
            estimatedTime: '1-2 days'
        },
        {
            phase: 'Phase 5: Frontend Integration',
            priority: 'MEDIUM',
            tasks: [
                'Update frontend API clients',
                'Implement tenant switching UI',
                'Test end-to-end functionality',
                'User acceptance testing'
            ],
            estimatedTime: '3-4 days'
        }
    ];
    analysis.implementationPlan = plan;
}
function generateNextSteps(analysis) {
    const nextSteps = [];
    if (analysis.summary.readinessPercentage < 50) {
        nextSteps.push('URGENT: Implement database schema changes (tenant_id columns and RLS policies)');
        nextSteps.push('Complete service layer implementations for high-priority entities');
        nextSteps.push('Test basic CRUD operations with tenant isolation');
    }
    else if (analysis.summary.readinessPercentage < 90) {
        nextSteps.push('Complete remaining entity implementations');
        nextSteps.push('Implement comprehensive testing suite');
        nextSteps.push('Prepare for data migration');
    }
    else {
        nextSteps.push('Finalize testing and validation');
        nextSteps.push('Prepare for production deployment');
        nextSteps.push('Begin frontend integration');
    }
    nextSteps.push('Set up monitoring and logging for tenant operations');
    nextSteps.push('Create documentation for multi-tenant API usage');
    analysis.nextSteps = nextSteps;
}
//# sourceMappingURL=data:application/json;base64,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