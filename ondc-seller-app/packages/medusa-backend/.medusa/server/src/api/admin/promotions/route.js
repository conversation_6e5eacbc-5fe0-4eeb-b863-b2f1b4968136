"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
exports.POST = POST;
const query_builder_1 = require("../../../utils/query-builder");
async function GET(req, res) {
    console.log(`🚀 [TENANT FILTER] === ENHANCED TENANT-AWARE PROMOTIONS ENDPOINT CALLED ===`);
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        // Extract query parameters
        const queryParams = req.query || {};
        console.log(`🔍 [TENANT FILTER] Getting promotions for tenant: ${tenantId}`);
        console.log(`📋 [QUERY PARAMS] Filters:`, queryParams);
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
        });
        let promotions = [];
        let totalCount = 0;
        let pagination = { limit: 20, offset: 0 };
        try {
            await client.connect();
            console.log(`🔗 [TENANT FILTER] Connected to database directly`);
            // Build query using utility functions
            const config = (0, query_builder_1.getEntityConfig)('promotions');
            const queryResult = (0, query_builder_1.buildListQuery)(tenantId, queryParams, config);
            pagination = queryResult.pagination;
            // Get total count
            const countResult = await client.query(queryResult.countQuery, queryResult.values.slice(0, -2)); // Remove limit/offset for count
            totalCount = parseInt(countResult.rows[0].total);
            // Get promotions
            const promotionsResult = await client.query(queryResult.listQuery, queryResult.values);
            promotions = promotionsResult.rows;
            console.log(`📦 [TENANT FILTER] Retrieved ${promotions.length}/${totalCount} promotions for tenant ${tenantId}`);
            // Get application methods for each promotion
            for (const promotion of promotions) {
                try {
                    const appMethodQuery = `
            SELECT 
              id, currency_code, max_quantity, apply_to_quantity, buy_rules_min_quantity,
              type, target_type, allocation, raw_value, created_at, updated_at, deleted_at,
              promotion_id
            FROM promotion_application_method 
            WHERE promotion_id = $1 AND deleted_at IS NULL
          `;
                    const appMethodResult = await client.query(appMethodQuery, [promotion.id]);
                    const appMethod = appMethodResult.rows[0];
                    if (appMethod) {
                        // Parse raw_value if it's a string
                        if (typeof appMethod.raw_value === 'string') {
                            try {
                                appMethod.raw_value = JSON.parse(appMethod.raw_value);
                            }
                            catch (e) {
                                console.log(`⚠️ Could not parse raw_value for application method ${appMethod.id}`);
                            }
                        }
                        // Calculate value from raw_value
                        if (appMethod.raw_value && appMethod.raw_value.value) {
                            appMethod.value = parseFloat(appMethod.raw_value.value);
                        }
                        // Get target rules for this application method
                        const targetRulesQuery = `
              SELECT 
                id, operator, description, attribute, values, created_at, updated_at, deleted_at
              FROM promotion_rule 
              WHERE application_method_id = $1 AND deleted_at IS NULL
            `;
                        const targetRulesResult = await client.query(targetRulesQuery, [appMethod.id]);
                        appMethod.target_rules = targetRulesResult.rows || [];
                        // Get buy rules for this application method
                        const buyRulesQuery = `
              SELECT 
                id, operator, description, attribute, values, created_at, updated_at, deleted_at
              FROM promotion_rule 
              WHERE application_method_id = $1 AND deleted_at IS NULL
            `;
                        const buyRulesResult = await client.query(buyRulesQuery, [appMethod.id]);
                        appMethod.buy_rules = buyRulesResult.rows || [];
                        promotion.application_method = appMethod;
                    }
                    else {
                        promotion.application_method = null;
                    }
                }
                catch (e) {
                    console.log(`⚠️ Could not fetch application method for promotion ${promotion.id}:`, e.message);
                    promotion.application_method = null;
                }
            }
            // Get promotion rules for each promotion
            for (const promotion of promotions) {
                try {
                    const rulesQuery = `
            SELECT 
              pr.id, pr.operator, pr.description, pr.attribute, pr.values,
              pr.created_at, pr.updated_at, pr.deleted_at
            FROM promotion_rule pr
            JOIN promotion_promotion_rule ppr ON pr.id = ppr.promotion_rule_id
            WHERE ppr.promotion_id = $1 AND pr.deleted_at IS NULL
          `;
                    const rulesResult = await client.query(rulesQuery, [promotion.id]);
                    promotion.rules = rulesResult.rows || [];
                }
                catch (e) {
                    console.log(`⚠️ Could not fetch rules for promotion ${promotion.id}:`, e.message);
                    promotion.rules = [];
                }
            }
            // Get campaign information for promotions that have campaign_id
            for (const promotion of promotions) {
                if (promotion.campaign_id) {
                    try {
                        const campaignQuery = `
              SELECT 
                id, name, description, starts_at, ends_at, created_at, updated_at, deleted_at
              FROM promotion_campaign 
              WHERE id = $1 AND deleted_at IS NULL
            `;
                        const campaignResult = await client.query(campaignQuery, [promotion.campaign_id]);
                        promotion.campaign = campaignResult.rows[0] || null;
                    }
                    catch (e) {
                        console.log(`⚠️ Could not fetch campaign for promotion ${promotion.id}:`, e.message);
                        promotion.campaign = null;
                    }
                }
                else {
                    promotion.campaign = null;
                }
            }
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            throw dbError;
        }
        finally {
            // Always close the database connection
            try {
                await client.end();
                console.log(`🔗 [TENANT FILTER] Database connection closed`);
            }
            catch (closeError) {
                console.error('❌ [TENANT FILTER] Error closing database connection:', closeError);
            }
        }
        // Build response using utility function
        const responseMetadata = (0, query_builder_1.buildResponseMetadata)(tenantId, queryParams, totalCount, promotions.length, pagination);
        const response = {
            promotions,
            ...responseMetadata,
        };
        // Add tenant headers
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Filtered', 'true');
        console.log(`📤 [TENANT FILTER] Returning ${promotions.length} promotions for tenant ${tenantId}`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error getting promotions:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to get promotions',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_promotions_error',
                timestamp: new Date().toISOString(),
            },
        });
    }
}
async function POST(req, res) {
    console.log(`🚀 [TENANT FILTER] === TENANT-AWARE PROMOTION CREATE ENDPOINT CALLED ===`);
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        console.log(`🆕 [TENANT FILTER] Creating promotion for tenant: ${tenantId}`);
        // Get promotion data from request body
        const promotionData = req.body;
        // Ensure tenant_id is injected and cannot be modified
        const promotionWithTenant = {
            ...promotionData,
            tenant_id: tenantId,
        };
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
        });
        let createdPromotion = null;
        try {
            await client.connect();
            console.log(`🔗 [TENANT FILTER] Connected to database for promotion creation`);
            // Start transaction
            await client.query('BEGIN');
            // Generate promotion ID
            const promotionId = `promo_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
            // Create promotion
            const createPromotionQuery = `
        INSERT INTO promotion (
          id, code, campaign_id, is_automatic, type, status, is_tax_inclusive, tenant_id,
          created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())
        RETURNING *
      `;
            const promotionValues = [
                promotionId,
                promotionWithTenant.code,
                promotionWithTenant.campaign_id || null,
                promotionWithTenant.is_automatic || false,
                promotionWithTenant.type || 'standard',
                promotionWithTenant.status || 'draft',
                promotionWithTenant.is_tax_inclusive || false,
                tenantId,
            ];
            const promotionResult = await client.query(createPromotionQuery, promotionValues);
            createdPromotion = promotionResult.rows[0];
            // Create application method if provided
            if (promotionWithTenant.application_method) {
                const appMethod = promotionWithTenant.application_method;
                const appMethodId = `proappmet_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                const createAppMethodQuery = `
          INSERT INTO promotion_application_method (
            id, currency_code, max_quantity, apply_to_quantity, buy_rules_min_quantity,
            type, target_type, allocation, raw_value, promotion_id, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW())
        `;
                const appMethodValues = [
                    appMethodId,
                    appMethod.currency_code || null,
                    appMethod.max_quantity || null,
                    appMethod.apply_to_quantity || null,
                    appMethod.buy_rules_min_quantity || null,
                    appMethod.type || 'percentage',
                    appMethod.target_type || 'order',
                    appMethod.allocation || null,
                    appMethod.raw_value ? JSON.stringify(appMethod.raw_value) : null,
                    promotionId,
                ];
                await client.query(createAppMethodQuery, appMethodValues);
            }
            // Commit transaction
            await client.query('COMMIT');
            console.log(`✅ [TENANT FILTER] Created promotion ${promotionId} for tenant ${tenantId}`);
            await client.end();
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error during promotion creation:', dbError);
            await client.query('ROLLBACK').catch(() => { });
            await client.end().catch(() => { });
            throw dbError;
        }
        // Return response in Medusa format
        const response = {
            promotion: createdPromotion,
            _tenant: {
                id: tenantId,
                validated: true,
                method: 'direct_db_creation',
            },
        };
        // Add tenant headers
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Validated', 'true');
        console.log(`📤 [TENANT FILTER] Returning created promotion for tenant ${tenantId}`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error creating promotion:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to create promotion',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_promotion_create_error',
                timestamp: new Date().toISOString(),
            },
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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