"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
exports.POST = POST;
const centralized_database_1 = require("../../../services/centralized-database");
async function GET(req, res) {
    console.log(`🚀 [TENANT FILTER] === ENHANCED CUSTOMERS ENDPOINT CALLED ===`);
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        console.log(`🔍 [TENANT FILTER] Processing customers request for tenant: ${tenantId}`);
        // Get query parameters
        const { limit = 50, offset = 0,
        // fields, order, and filters are available but not used in direct DB query
         } = req.query;
        let customers = [];
        let count = 0;
        try {
            console.log(`🔗 [TENANT FILTER] Using centralized database service`);
            // Get total count for this tenant using centralized service
            count = await centralized_database_1.centralizedDb.getCount('customer', tenantId);
            console.log(`📊 [TENANT FILTER] Total customers for tenant ${tenantId}: ${count}`);
            // Get customers using centralized service
            const customersResult = await centralized_database_1.centralizedDb.getCustomers(tenantId, {
                limit: parseInt(limit),
                offset: parseInt(offset),
            });
            customers = customersResult.rows || [];
            console.log(`👥 [TENANT FILTER] Retrieved ${customers.length} customers`);
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            customers = [];
            count = 0;
        }
        // No finally block needed - centralized service handles connection cleanup
        // Return response in Medusa format
        const response = {
            customers,
            count: customers.length,
            offset: parseInt(offset),
            limit: parseInt(limit),
            _tenant: {
                id: tenantId,
                filtered: true,
                method: 'centralized_db_service',
                total_in_db: count,
            },
        };
        // Add tenant headers for debugging
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Filtered', 'true');
        res.setHeader('X-Customers-Count', customers.length.toString());
        console.log(`📤 [TENANT FILTER] Returning ${customers.length} customers for tenant ${tenantId}`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error in customers endpoint:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to fetch customers',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_filter_error',
                timestamp: new Date().toISOString(),
            },
        });
    }
}
async function POST(req, res) {
    console.log(`🚀 [TENANT FILTER] === CUSTOM CUSTOMERS POST ENDPOINT CALLED ===`);
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        console.log(`🔍 [TENANT FILTER] Creating customer for tenant: ${tenantId}`);
        // Get customer data from request body
        const customerData = req.body;
        // Inject tenant_id into the customer data
        const customerWithTenant = {
            ...customerData,
            tenant_id: tenantId,
            metadata: {
                ...customerData.metadata,
                tenant_id: tenantId,
            },
        };
        console.log(`🏷️ [TENANT FILTER] Injected tenant_id: ${tenantId} into customer data`);
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
        });
        let createdCustomer = null;
        try {
            await client.connect();
            // Generate a unique customer ID
            const customerId = `cus_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
            // Insert customer with tenant_id
            const insertQuery = `
        INSERT INTO customer (
          id, email, first_name, last_name, phone,
          tenant_id, metadata, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
        RETURNING *
      `;
            const values = [
                customerId,
                customerWithTenant.email,
                customerWithTenant.first_name || '',
                customerWithTenant.last_name || '',
                customerWithTenant.phone || null,
                tenantId,
                JSON.stringify(customerWithTenant.metadata || {}),
            ];
            const result = await client.query(insertQuery, values);
            createdCustomer = result.rows[0];
            console.log(`✅ [TENANT FILTER] Created customer with ID: ${createdCustomer.id} for tenant: ${tenantId}`);
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            throw dbError;
        }
        finally {
            // Always close the database connection
            try {
                await client.end();
                console.log(`🔗 [TENANT FILTER] Database connection closed`);
            }
            catch (closeError) {
                console.error('❌ [TENANT FILTER] Error closing database connection:', closeError);
            }
        }
        // Return response
        const response = {
            customer: createdCustomer,
            _tenant: {
                id: tenantId,
                injected: true,
                method: 'direct_db_connection',
            },
        };
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Injected', 'true');
        res.status(201).json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error creating customer:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to create customer',
            message: error.message,
            tenant_id: tenantId,
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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