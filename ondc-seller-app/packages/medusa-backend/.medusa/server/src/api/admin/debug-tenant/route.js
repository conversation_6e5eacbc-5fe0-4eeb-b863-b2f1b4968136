"use strict";
/**
 * Debug Tenant Context Endpoint
 *
 * Simple endpoint to debug tenant context extraction and middleware functionality.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = void 0;
const GET = async (req, res) => {
    try {
        console.log(`🔍 [DEBUG-TENANT] Debugging tenant context`);
        const debugInfo = {
            timestamp: new Date().toISOString(),
            request: {
                method: req.method,
                path: req.path,
                url: req.url
            },
            headers: {
                'x-tenant-id': req.headers['x-tenant-id'],
                'authorization': req.headers['authorization'] ? 'Bearer ***' : 'Not provided',
                'content-type': req.headers['content-type'],
                'user-agent': req.headers['user-agent']
            },
            tenantContext: {
                'req.tenant_id': req.tenant_id,
                'req.tenantId': req.tenantId,
                'req.tenant': req.tenant
            },
            query: req.query,
            environment: {
                NODE_ENV: process.env.NODE_ENV,
                DEFAULT_TENANT_ID: process.env.DEFAULT_TENANT_ID
            }
        };
        console.log(`🔍 [DEBUG-TENANT] Debug info:`, debugInfo);
        return res.json({
            success: true,
            message: 'Tenant context debug information',
            data: debugInfo
        });
    }
    catch (error) {
        console.error(`❌ [DEBUG-TENANT] Debug failed:`, error);
        return res.status(500).json({
            success: false,
            message: 'Tenant debug failed',
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        });
    }
};
exports.GET = GET;
//# sourceMappingURL=data:application/json;base64,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