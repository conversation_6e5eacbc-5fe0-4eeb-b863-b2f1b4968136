"use strict";
/**
 * Medusa API Endpoints Tenant Isolation Test
 *
 * Tests actual Medusa API endpoints to verify tenant isolation works
 * across all standard Medusa Commerce operations.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.POST = exports.GET = void 0;
const GET = async (req, res) => {
    try {
        const tenantId = req.tenant_id || 'default';
        console.log(`🧪 [MEDUSA-ENDPOINTS-TEST] Testing Medusa API endpoints for tenant: ${tenantId}`);
        const startTime = Date.now();
        const results = {
            timestamp: new Date().toISOString(),
            tenantId,
            testSummary: {
                totalEndpoints: 0,
                successfulEndpoints: 0,
                failedEndpoints: 0,
                averageResponseTime: 0
            },
            endpointResults: [],
            crossTenantTest: null
        };
        // Define Medusa API endpoints to test
        const endpointsToTest = [
            { path: '/admin/products', method: 'GET', description: 'List Products' },
            { path: '/admin/customers', method: 'GET', description: 'List Customers' },
            { path: '/admin/orders', method: 'GET', description: 'List Orders' },
            { path: '/admin/product-categories', method: 'GET', description: 'List Product Categories' },
            { path: '/admin/collections', method: 'GET', description: 'List Collections' },
            { path: '/admin/regions', method: 'GET', description: 'List Regions' },
            { path: '/admin/sales-channels', method: 'GET', description: 'List Sales Channels' },
            { path: '/admin/shipping-options', method: 'GET', description: 'List Shipping Options' },
            { path: '/admin/tax-rates', method: 'GET', description: 'List Tax Rates' },
            { path: '/admin/discounts', method: 'GET', description: 'List Discounts' }
        ];
        // Test each endpoint
        for (const endpoint of endpointsToTest) {
            const testResult = await testMedusaEndpoint(req, endpoint, tenantId);
            results.endpointResults.push(testResult);
            results.testSummary.totalEndpoints++;
            if (testResult.success) {
                results.testSummary.successfulEndpoints++;
            }
            else {
                results.testSummary.failedEndpoints++;
            }
        }
        // Test cross-tenant isolation
        results.crossTenantTest = await testCrossTenantIsolation(req);
        // Calculate summary statistics
        const totalResponseTime = results.endpointResults.reduce((sum, result) => sum + (result.responseTime || 0), 0);
        results.testSummary.averageResponseTime = totalResponseTime / results.testSummary.totalEndpoints;
        results.testSummary.successRate = `${((results.testSummary.successfulEndpoints / results.testSummary.totalEndpoints) * 100).toFixed(1)}%`;
        // Performance metrics
        results.performance = {
            totalTime: Date.now() - startTime,
            averageTestTime: (Date.now() - startTime) / results.testSummary.totalEndpoints
        };
        console.log(`🧪 [MEDUSA-ENDPOINTS-TEST] Test completed:`, results.testSummary);
        return res.json({
            success: true,
            message: 'Medusa API endpoints tenant isolation test completed',
            results
        });
    }
    catch (error) {
        console.error(`❌ [MEDUSA-ENDPOINTS-TEST] Test failed:`, error);
        return res.status(500).json({
            success: false,
            message: 'Medusa endpoints test failed',
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        });
    }
};
exports.GET = GET;
async function testMedusaEndpoint(req, endpoint, tenantId) {
    const testResult = {
        endpoint: endpoint.path,
        method: endpoint.method,
        success: false,
        tenantId
    };
    const startTime = Date.now();
    try {
        // Simulate internal API call by using the services directly
        let data = null;
        let count = 0;
        switch (endpoint.path) {
            case '/admin/products':
                const productService = req.scope.resolve('product');
                data = await productService.listProducts({}, { take: 10 });
                count = Array.isArray(data) ? data.length : (data?.products?.length || 0);
                break;
            case '/admin/customers':
                const customerService = req.scope.resolve('customer');
                data = await customerService.listCustomers({}, { take: 10 });
                count = Array.isArray(data) ? data.length : (data?.customers?.length || 0);
                break;
            case '/admin/orders':
                const orderService = req.scope.resolve('order');
                data = await orderService.listOrders({}, { take: 10 });
                count = Array.isArray(data) ? data.length : (data?.orders?.length || 0);
                break;
            case '/admin/product-categories':
                try {
                    const categoryService = req.scope.resolve('productCategory');
                    data = await categoryService.listCategories({}, { take: 10 });
                    count = Array.isArray(data) ? data.length : 0;
                }
                catch (error) {
                    // Try alternative service names
                    try {
                        const categoryService = req.scope.resolve('product-category');
                        data = await categoryService.listCategories({}, { take: 10 });
                        count = Array.isArray(data) ? data.length : 0;
                    }
                    catch (error2) {
                        throw new Error('Product category service not available');
                    }
                }
                break;
            case '/admin/collections':
                try {
                    const collectionService = req.scope.resolve('productCollection');
                    data = await collectionService.listCollections({}, { take: 10 });
                    count = Array.isArray(data) ? data.length : 0;
                }
                catch (error) {
                    try {
                        const collectionService = req.scope.resolve('collection');
                        data = await collectionService.listCollections({}, { take: 10 });
                        count = Array.isArray(data) ? data.length : 0;
                    }
                    catch (error2) {
                        throw new Error('Collection service not available');
                    }
                }
                break;
            case '/admin/regions':
                const regionService = req.scope.resolve('region');
                data = await regionService.listRegions({}, { take: 10 });
                count = Array.isArray(data) ? data.length : 0;
                break;
            default:
                throw new Error(`Endpoint ${endpoint.path} not implemented in test`);
        }
        testResult.success = true;
        testResult.statusCode = 200;
        testResult.dataCount = count;
        testResult.responseTime = Date.now() - startTime;
    }
    catch (error) {
        testResult.success = false;
        testResult.statusCode = 500;
        testResult.error = error instanceof Error ? error.message : 'Unknown error';
        testResult.responseTime = Date.now() - startTime;
    }
    return testResult;
}
async function testCrossTenantIsolation(req) {
    const currentTenantId = req.tenant_id || 'default';
    const testTenantId = 'test-tenant-' + Date.now();
    console.log(`🧪 [MEDUSA-ENDPOINTS-TEST] Testing cross-tenant isolation: ${currentTenantId} vs ${testTenantId}`);
    try {
        // Test with current tenant
        const productService1 = req.scope.resolve('product');
        const currentTenantProducts = await productService1.listProducts({}, { take: 5 });
        const currentCount = Array.isArray(currentTenantProducts) ? currentTenantProducts.length : 0;
        // Create mock request with different tenant
        const mockReq = {
            ...req,
            tenant_id: testTenantId,
            tenantId: testTenantId
        };
        // Test with different tenant (same service instance, but different context)
        const productService2 = mockReq.scope.resolve('product');
        const differentTenantProducts = await productService2.listProducts({}, { take: 5 });
        const differentCount = Array.isArray(differentTenantProducts) ? differentTenantProducts.length : 0;
        return {
            success: true,
            currentTenant: {
                id: currentTenantId,
                productCount: currentCount
            },
            testTenant: {
                id: testTenantId,
                productCount: differentCount
            },
            isolationWorking: currentCount === differentCount, // Same data = no isolation yet
            message: currentCount === differentCount
                ? 'Same data returned - RLS not yet implemented'
                : 'Different data returned - isolation working'
        };
    }
    catch (error) {
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            message: 'Cross-tenant isolation test failed'
        };
    }
}
const POST = async (req, res) => {
    try {
        const tenantId = req.tenant_id || 'default';
        const { endpoint, testData } = req.body;
        console.log(`🧪 [MEDUSA-ENDPOINTS-TEST] Testing CREATE operation for ${endpoint} with tenant: ${tenantId}`);
        let result = null;
        switch (endpoint) {
            case '/admin/products':
                const productService = req.scope.resolve('product');
                const productData = {
                    title: testData?.title || `Test Product ${Date.now()}`,
                    description: testData?.description || 'Test product for tenant verification',
                    handle: testData?.handle || `test-product-${Date.now()}`,
                    status: 'draft',
                    ...testData
                };
                result = await productService.createProducts([productData]);
                break;
            case '/admin/customers':
                const customerService = req.scope.resolve('customer');
                const customerData = {
                    email: testData?.email || `test-${Date.now()}@example.com`,
                    first_name: testData?.first_name || 'Test',
                    last_name: testData?.last_name || 'Customer',
                    ...testData
                };
                result = await customerService.createCustomers([customerData]);
                break;
            default:
                throw new Error(`CREATE test for ${endpoint} not implemented`);
        }
        return res.json({
            success: true,
            message: `CREATE operation tested for ${endpoint}`,
            data: {
                endpoint,
                tenantId,
                created: result,
                timestamp: new Date().toISOString()
            }
        });
    }
    catch (error) {
        console.error(`❌ [MEDUSA-ENDPOINTS-TEST] CREATE test failed:`, error);
        return res.status(500).json({
            success: false,
            message: 'CREATE operation test failed',
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        });
    }
};
exports.POST = POST;
//# sourceMappingURL=data:application/json;base64,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