"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
exports.POST = POST;
exports.DELETE = DELETE;
async function GET(req, res) {
    console.log(`🚀 [TENANT FILTER] === CUSTOM TAG GET BY ID ENDPOINT CALLED ===`);
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        // Get tag ID from URL params
        const tagId = req.params?.id;
        if (!tagId) {
            return res.status(400).json({
                error: 'Tag ID is required',
                tenant_id: tenantId
            });
        }
        console.log(`🔍 [TENANT FILTER] Getting tag ${tagId} for tenant: ${tenantId}`);
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
        });
        let tag = null;
        try {
            await client.connect();
            console.log(`🔗 [TENANT FILTER] Connected to database directly`);
            // Get tag with tenant validation
            const result = await client.query(`
        SELECT 
          id, value, metadata, created_at, updated_at, 
          deleted_at, tenant_id
        FROM product_tag 
        WHERE id = $1 AND tenant_id = $2
      `, [tagId, tenantId]);
            tag = result.rows[0] || null;
            console.log(`📦 [TENANT FILTER] Retrieved tag: ${tag ? 'Found' : 'Not Found'}`);
            if (tag) {
                // Parse metadata if it's a string
                if (typeof tag.metadata === 'string') {
                    try {
                        tag.metadata = JSON.parse(tag.metadata);
                    }
                    catch (e) {
                        console.log(`⚠️ Could not parse metadata for tag ${tag.id}`);
                    }
                }
            }
            await client.end();
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            await client.end().catch(() => { });
            throw dbError;
        }
        if (!tag) {
            return res.status(404).json({
                error: 'Tag not found or access denied',
                tag_id: tagId,
                tenant_id: tenantId,
                _debug: {
                    message: 'Tag either does not exist or belongs to a different tenant'
                }
            });
        }
        // Return response in Medusa format
        const response = {
            product_tag: tag,
            _tenant: {
                id: tenantId,
                filtered: true,
                method: 'direct_db_connection'
            }
        };
        // Add tenant headers
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Filtered', 'true');
        console.log(`📤 [TENANT FILTER] Returning tag ${tagId} for tenant ${tenantId}`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error getting tag:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to get tag',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_tag_get_error',
                timestamp: new Date().toISOString()
            }
        });
    }
}
async function POST(req, res) {
    console.log(`🚀 [TENANT FILTER] === CUSTOM TAG UPDATE ENDPOINT CALLED ===`);
    console.log(`🚀 [TENANT FILTER] Body:`, JSON.stringify(req.body, null, 2));
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        // Get tag ID from URL params
        const tagId = req.params?.id;
        if (!tagId) {
            return res.status(400).json({
                error: 'Tag ID is required for update',
                tenant_id: tenantId
            });
        }
        console.log(`🔄 [TENANT FILTER] Updating tag ${tagId} for tenant: ${tenantId}`);
        // Get update data from request body
        const updateData = req.body;
        // Remove tenant_id from update data to prevent modification
        const { tenant_id: _, ...safeUpdateData } = updateData;
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
        });
        let updatedTag = null;
        try {
            await client.connect();
            console.log(`🔗 [TENANT FILTER] Connected to database directly`);
            // First, verify the tag belongs to this tenant
            const checkQuery = 'SELECT id FROM product_tag WHERE id = $1 AND tenant_id = $2';
            const checkResult = await client.query(checkQuery, [tagId, tenantId]);
            if (checkResult.rows.length === 0) {
                await client.end();
                return res.status(404).json({
                    error: 'Tag not found or access denied',
                    tag_id: tagId,
                    tenant_id: tenantId,
                    _debug: {
                        message: 'Tag either does not exist or belongs to a different tenant'
                    }
                });
            }
            // Check if new value already exists for this tenant (if value is being updated)
            if (safeUpdateData.value) {
                const existingResult = await client.query('SELECT id FROM product_tag WHERE value = $1 AND tenant_id = $2 AND id != $3', [safeUpdateData.value, tenantId, tagId]);
                if (existingResult.rows.length > 0) {
                    await client.end();
                    return res.status(400).json({
                        type: 'invalid_data',
                        message: `Tag with value: ${safeUpdateData.value}, already exists for tenant: ${tenantId}.`,
                        tenant_id: tenantId
                    });
                }
            }
            // Update the tag (tenant_id cannot be changed)
            const updateQuery = `
        UPDATE product_tag 
        SET 
          value = COALESCE($1, value),
          metadata = COALESCE($2, metadata),
          updated_at = NOW()
        WHERE id = $3 AND tenant_id = $4
        RETURNING *
      `;
            const values = [
                safeUpdateData.value,
                safeUpdateData.metadata ? JSON.stringify({
                    ...safeUpdateData.metadata,
                    tenant_id: tenantId
                }) : null,
                tagId,
                tenantId
            ];
            const result = await client.query(updateQuery, values);
            updatedTag = result.rows[0];
            // Parse metadata
            if (typeof updatedTag.metadata === 'string') {
                try {
                    updatedTag.metadata = JSON.parse(updatedTag.metadata);
                }
                catch (e) {
                    console.log(`⚠️ Could not parse metadata for updated tag ${updatedTag.id}`);
                }
            }
            console.log(`✅ [TENANT FILTER] Updated tag ${tagId} for tenant: ${tenantId}`);
            await client.end();
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            await client.end().catch(() => { });
            throw dbError;
        }
        // Return response in Medusa format
        const response = {
            product_tag: updatedTag,
            _tenant: {
                id: tenantId,
                validated: true,
                method: 'direct_db_connection'
            }
        };
        // Add tenant headers
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Validated', 'true');
        console.log(`📤 [TENANT FILTER] Returning updated tag for tenant ${tenantId}`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error updating tag:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to update tag',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_tag_update_error',
                timestamp: new Date().toISOString()
            }
        });
    }
}
async function DELETE(req, res) {
    console.log(`🚀 [TENANT FILTER] === CUSTOM TAG DELETE ENDPOINT CALLED ===`);
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        // Get tag ID from URL params
        const tagId = req.params?.id;
        if (!tagId) {
            return res.status(400).json({
                error: 'Tag ID is required for deletion',
                tenant_id: tenantId
            });
        }
        console.log(`🗑️ [TENANT FILTER] Deleting tag ${tagId} for tenant: ${tenantId}`);
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
        });
        let deletedTag = null;
        try {
            await client.connect();
            console.log(`🔗 [TENANT FILTER] Connected to database directly`);
            // First, verify the tag belongs to this tenant and get it
            const checkQuery = 'SELECT * FROM product_tag WHERE id = $1 AND tenant_id = $2';
            const checkResult = await client.query(checkQuery, [tagId, tenantId]);
            if (checkResult.rows.length === 0) {
                await client.end();
                return res.status(404).json({
                    error: 'Tag not found or access denied',
                    tag_id: tagId,
                    tenant_id: tenantId,
                    _debug: {
                        message: 'Tag either does not exist or belongs to a different tenant'
                    }
                });
            }
            // Hard delete the tag (tags don't typically use soft delete)
            const deleteQuery = 'DELETE FROM product_tag WHERE id = $1 AND tenant_id = $2 RETURNING *';
            const result = await client.query(deleteQuery, [tagId, tenantId]);
            deletedTag = result.rows[0];
            console.log(`✅ [TENANT FILTER] Deleted tag ${tagId} for tenant: ${tenantId}`);
            await client.end();
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            await client.end().catch(() => { });
            throw dbError;
        }
        // Return response in Medusa format
        const response = {
            id: tagId,
            object: 'product_tag',
            deleted: true,
            _tenant: {
                id: tenantId,
                validated: true,
                method: 'direct_db_connection'
            }
        };
        // Add tenant headers
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Validated', 'true');
        console.log(`📤 [TENANT FILTER] Confirmed deletion of tag ${tagId} for tenant ${tenantId}`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error deleting tag:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to delete tag',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_tag_delete_error',
                timestamp: new Date().toISOString()
            }
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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