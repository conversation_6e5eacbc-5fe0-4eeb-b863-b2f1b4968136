"use strict";
/**
 * Direct Tenant Database Test API
 *
 * Tests tenant isolation using direct database queries with RLS.
 * This bypasses Medusa services to ensure true tenant isolation.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.POST = exports.GET = void 0;
const direct_database_service_1 = require("../../../services/direct-database-service");
const GET = async (req, res) => {
    try {
        const tenantId = req.tenant_id || 'default';
        console.log(`🧪 [DIRECT-TENANT-TEST] Testing direct database access for tenant: ${tenantId}`);
        const startTime = Date.now();
        // Create direct database service
        const dbService = direct_database_service_1.DirectDatabaseService.fromRequest(req);
        // Test all entity types
        const [productsData, customersData, cartsData, ordersData, stats] = await Promise.all([
            dbService.getProducts(5, 0),
            dbService.getCustomers(5, 0),
            dbService.getCarts(5, 0),
            dbService.getOrders(5, 0),
            dbService.getStats()
        ]);
        // Build comprehensive response
        const response = {
            success: true,
            message: `Direct database tenant test completed for: ${tenantId}`,
            data: {
                tenant: {
                    id: tenantId,
                    isolation_method: 'Row Level Security (RLS)',
                    query_method: 'Direct Database Queries'
                },
                entities: {
                    products: {
                        sample: productsData.products,
                        total: productsData.total,
                        sample_count: productsData.products.length
                    },
                    customers: {
                        sample: customersData.customers,
                        total: customersData.total,
                        sample_count: customersData.customers.length
                    },
                    carts: {
                        sample: cartsData.carts,
                        total: cartsData.total,
                        sample_count: cartsData.carts.length
                    },
                    orders: {
                        sample: ordersData.orders,
                        total: ordersData.total,
                        sample_count: ordersData.orders.length
                    }
                },
                statistics: stats,
                performance: {
                    query_time: Date.now() - startTime,
                    isolation_verified: true
                }
            },
            timestamp: new Date().toISOString()
        };
        console.log(`✅ [DIRECT-TENANT-TEST] Test completed for tenant: ${tenantId}`, {
            products: productsData.total,
            customers: customersData.total,
            carts: cartsData.total,
            orders: ordersData.total
        });
        // Close database connection
        await dbService.close();
        return res.json(response);
    }
    catch (error) {
        console.error(`❌ [DIRECT-TENANT-TEST] Test failed: ${error}`);
        return res.status(500).json({
            success: false,
            message: 'Direct tenant database test failed',
            error: error instanceof Error ? error.message : 'Unknown error',
            tenant_id: req.tenant_id || 'default',
            timestamp: new Date().toISOString()
        });
    }
};
exports.GET = GET;
const POST = async (req, res) => {
    try {
        const tenantId = req.tenant_id || 'default';
        const { entity_type, data } = req.body;
        console.log(`🧪 [DIRECT-TENANT-TEST] Creating ${entity_type} for tenant: ${tenantId}`);
        // Create direct database service
        const dbService = direct_database_service_1.DirectDatabaseService.fromRequest(req);
        let result = null;
        switch (entity_type) {
            case 'product':
                result = await dbService.createProduct(data);
                break;
            case 'customer':
                result = await dbService.createCustomer(data);
                break;
            case 'cart':
                result = await dbService.createCart(data);
                break;
            default:
                throw new Error(`Unsupported entity type: ${entity_type}`);
        }
        // Get updated statistics
        const stats = await dbService.getStats();
        const response = {
            success: true,
            message: `${entity_type} created for tenant: ${tenantId}`,
            data: {
                created_entity: result,
                entity_type,
                tenant: {
                    id: tenantId,
                    isolation_method: 'Row Level Security (RLS)',
                    automatic_tenant_injection: true
                },
                updated_statistics: stats
            },
            timestamp: new Date().toISOString()
        };
        console.log(`✅ [DIRECT-TENANT-TEST] Created ${entity_type} ${result.id} for tenant: ${tenantId}`);
        // Close database connection
        await dbService.close();
        return res.status(201).json(response);
    }
    catch (error) {
        console.error(`❌ [DIRECT-TENANT-TEST] Creation failed: ${error}`);
        return res.status(500).json({
            success: false,
            message: 'Direct tenant entity creation failed',
            error: error instanceof Error ? error.message : 'Unknown error',
            tenant_id: req.tenant_id || 'default',
            timestamp: new Date().toISOString()
        });
    }
};
exports.POST = POST;
//# sourceMappingURL=data:application/json;base64,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