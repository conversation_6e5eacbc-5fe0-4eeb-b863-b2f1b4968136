"use strict";
/**
 * Tenant-Aware Products API
 *
 * Demonstrates working multi-tenant product operations with automatic tenant filtering.
 * This endpoint shows how the tenant-aware services work in practice.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DELETE = exports.PUT = exports.POST = exports.GET = void 0;
const tenant_service_factory_1 = require("../../../services/tenant-service-factory");
const product_auto_config_1 = require("../../../workflows/product-auto-config");
const GET = async (req, res) => {
    try {
        const tenantId = req.tenant_id || 'default';
        console.log(`🏢 [TENANT-PRODUCTS] Getting products for tenant: ${tenantId}`);
        // Create tenant-aware services
        const services = tenant_service_factory_1.TenantServiceFactory.fromRequest(req);
        // Get query parameters
        const { limit = 20, offset = 0, search, status, category_id } = req.query;
        // Build filters
        const filters = {};
        if (search) {
            filters.title = { $ilike: `%${search}%` };
        }
        if (status) {
            filters.status = status;
        }
        if (category_id) {
            filters.category_id = category_id;
        }
        // Get products using tenant-aware service
        const [products, totalCount] = await services.product.listAndCountProducts(filters, {
            take: parseInt(limit),
            skip: parseInt(offset),
        });
        // Get product statistics
        const stats = await services.product.getProductStats();
        // Build response
        const response = {
            success: true,
            message: `Products retrieved for tenant: ${tenantId}`,
            data: {
                products,
                pagination: {
                    total: totalCount,
                    count: products.length,
                    limit: parseInt(limit),
                    offset: parseInt(offset),
                    hasMore: parseInt(offset) + products.length < totalCount,
                },
                statistics: stats,
                tenant: {
                    id: tenantId,
                    context: 'Products filtered by tenant automatically',
                },
            },
            timestamp: new Date().toISOString(),
        };
        console.log(`✅ [TENANT-PRODUCTS] Retrieved ${products.length}/${totalCount} products for tenant: ${tenantId}`);
        return res.json(response);
    }
    catch (error) {
        console.error(`❌ [TENANT-PRODUCTS] Error getting products: ${error}`);
        return res.status(500).json({
            success: false,
            message: 'Failed to get tenant products',
            error: error instanceof Error ? error.message : 'Unknown error',
            tenant_id: req.tenant_id || 'default',
            timestamp: new Date().toISOString(),
        });
    }
};
exports.GET = GET;
const POST = async (req, res) => {
    try {
        const tenantId = req.tenant_id || 'default';
        console.log(`🏢 [TENANT-PRODUCTS] Creating product for tenant: ${tenantId}`);
        // Create tenant-aware services
        const services = tenant_service_factory_1.TenantServiceFactory.fromRequest(req);
        const { title, description, handle, status = 'draft', ...otherData } = req.body;
        // Validate required fields
        if (!title) {
            return res.status(400).json({
                success: false,
                message: 'Title is required',
                tenant_id: tenantId,
            });
        }
        // Prepare product data
        const productData = {
            title,
            description: description || `Product created for tenant: ${tenantId}`,
            handle: handle ||
                title
                    .toLowerCase()
                    .replace(/\s+/g, '-')
                    .replace(/[^a-z0-9-]/g, ''),
            status,
            ...otherData,
        };
        console.log(`🏢 [TENANT-PRODUCTS] Creating product with data:`, productData);
        // Create product using enhanced workflow with auto-configuration
        console.log(`🔧 [TENANT-PRODUCTS] Using auto-config workflow for product creation`);
        const workflowResult = await (0, product_auto_config_1.createProductsWithAutoConfigWorkflow)(req.scope).run({
            input: {
                products: [productData],
                tenantId: tenantId,
            },
        });
        const createdProduct = workflowResult.result.products[0];
        const configResults = workflowResult.result.configurationResults;
        console.log(`🔧 [TENANT-PRODUCTS] Auto-configuration results:`, configResults);
        // Get updated statistics
        const stats = await services.product.getProductStats();
        const response = {
            success: true,
            message: `Product created for tenant: ${tenantId}`,
            data: {
                product: createdProduct,
                statistics: stats,
                tenant: {
                    id: tenantId,
                    context: 'Product automatically associated with tenant',
                },
                autoConfiguration: {
                    salesChannelAssigned: configResults.successful > 0,
                    inventoryManagementDisabled: configResults.successful > 0,
                    shippingProfileAssigned: configResults.successful > 0,
                    salesChannelId: process.env.DEFAULT_SALES_CHANNEL_ID,
                    shippingProfileId: process.env.DEFAULT_SHIPPING_PROFILE_ID,
                    results: configResults,
                },
            },
            timestamp: new Date().toISOString(),
        };
        console.log(`✅ [TENANT-PRODUCTS] Created product ${createdProduct.id} for tenant: ${tenantId}`);
        return res.status(201).json(response);
    }
    catch (error) {
        console.error(`❌ [TENANT-PRODUCTS] Error creating product: ${error}`);
        return res.status(500).json({
            success: false,
            message: 'Failed to create tenant product',
            error: error instanceof Error ? error.message : 'Unknown error',
            tenant_id: req.tenant_id || 'default',
            timestamp: new Date().toISOString(),
        });
    }
};
exports.POST = POST;
const PUT = async (req, res) => {
    try {
        const tenantId = req.tenant_id || 'default';
        const { product_id } = req.query;
        if (!product_id) {
            return res.status(400).json({
                success: false,
                message: 'Product ID is required',
                tenant_id: tenantId,
            });
        }
        console.log(`🏢 [TENANT-PRODUCTS] Updating product ${product_id} for tenant: ${tenantId}`);
        // Create tenant-aware services
        const services = tenant_service_factory_1.TenantServiceFactory.fromRequest(req);
        const updateData = req.body;
        // Update product using tenant-aware service
        const updatedProducts = await services.product.updateProducts([
            {
                id: product_id,
                ...updateData,
            },
        ]);
        const updatedProduct = updatedProducts[0];
        const response = {
            success: true,
            message: `Product updated for tenant: ${tenantId}`,
            data: {
                product: updatedProduct,
                tenant: {
                    id: tenantId,
                    context: 'Product update respects tenant boundaries',
                },
            },
            timestamp: new Date().toISOString(),
        };
        console.log(`✅ [TENANT-PRODUCTS] Updated product ${product_id} for tenant: ${tenantId}`);
        return res.json(response);
    }
    catch (error) {
        console.error(`❌ [TENANT-PRODUCTS] Error updating product: ${error}`);
        return res.status(500).json({
            success: false,
            message: 'Failed to update tenant product',
            error: error instanceof Error ? error.message : 'Unknown error',
            tenant_id: req.tenant_id || 'default',
            timestamp: new Date().toISOString(),
        });
    }
};
exports.PUT = PUT;
const DELETE = async (req, res) => {
    try {
        const tenantId = req.tenant_id || 'default';
        const { product_id } = req.query;
        if (!product_id) {
            return res.status(400).json({
                success: false,
                message: 'Product ID is required',
                tenant_id: tenantId,
            });
        }
        console.log(`🏢 [TENANT-PRODUCTS] Deleting product ${product_id} for tenant: ${tenantId}`);
        // Create tenant-aware services
        const services = tenant_service_factory_1.TenantServiceFactory.fromRequest(req);
        // Delete product using tenant-aware service
        await services.product.deleteProducts([product_id]);
        // Get updated statistics
        const stats = await services.product.getProductStats();
        const response = {
            success: true,
            message: `Product deleted for tenant: ${tenantId}`,
            data: {
                deleted_product_id: product_id,
                statistics: stats,
                tenant: {
                    id: tenantId,
                    context: 'Product deletion respects tenant boundaries',
                },
            },
            timestamp: new Date().toISOString(),
        };
        console.log(`✅ [TENANT-PRODUCTS] Deleted product ${product_id} for tenant: ${tenantId}`);
        return res.json(response);
    }
    catch (error) {
        console.error(`❌ [TENANT-PRODUCTS] Error deleting product: ${error}`);
        return res.status(500).json({
            success: false,
            message: 'Failed to delete tenant product',
            error: error instanceof Error ? error.message : 'Unknown error',
            tenant_id: req.tenant_id || 'default',
            timestamp: new Date().toISOString(),
        });
    }
};
exports.DELETE = DELETE;
//# sourceMappingURL=data:application/json;base64,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