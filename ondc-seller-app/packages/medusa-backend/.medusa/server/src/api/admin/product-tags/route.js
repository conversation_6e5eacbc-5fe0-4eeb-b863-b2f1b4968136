"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
exports.POST = POST;
async function GET(req, res) {
    console.log(`🚀 [TENANT FILTER] === CUSTOM PRODUCT TAGS ENDPOINT CALLED ===`);
    console.log(`🚀 [TENANT FILTER] Headers:`, JSON.stringify(req.headers, null, 2));
    console.log(`🚀 [TENANT FILTER] Query:`, JSON.stringify(req.query, null, 2));
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        console.log(`🔍 [TENANT FILTER] Processing tags request for tenant: ${tenantId}`);
        // Get query parameters
        const { limit = 50, offset = 0 } = req.query;
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
        });
        let tags = [];
        let count = 0;
        try {
            await client.connect();
            console.log(`🔗 [TENANT FILTER] Connected to database directly`);
            // Get total count for this tenant
            const countResult = await client.query('SELECT COUNT(*) as total FROM product_tag WHERE tenant_id = $1', [tenantId]);
            count = parseInt(countResult.rows[0]?.total || 0);
            console.log(`📊 [TENANT FILTER] Total tags for tenant ${tenantId}: ${count}`);
            // Get tags for this tenant
            const result = await client.query(`
        SELECT 
          id, value, metadata, created_at, updated_at, 
          deleted_at, tenant_id
        FROM product_tag 
        WHERE tenant_id = $1
        ORDER BY created_at DESC 
        LIMIT $2 OFFSET $3
      `, [tenantId, parseInt(limit), parseInt(offset)]);
            tags = result.rows || [];
            // Parse metadata for each tag
            tags.forEach(tag => {
                if (typeof tag.metadata === 'string') {
                    try {
                        tag.metadata = JSON.parse(tag.metadata);
                    }
                    catch (e) {
                        console.log(`⚠️ Could not parse metadata for tag ${tag.id}`);
                    }
                }
            });
            console.log(`📦 [TENANT FILTER] Retrieved ${tags.length} tags`);
            await client.end();
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            await client.end().catch(() => { });
        }
        // Return response in Medusa format
        const response = {
            product_tags: tags,
            count: tags.length,
            offset: parseInt(offset),
            limit: parseInt(limit),
            // Add tenant info for debugging
            _tenant: {
                id: tenantId,
                filtered: true,
                method: 'direct_db_connection',
                total_in_db: count
            }
        };
        // Add tenant headers for debugging
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Filtered', 'true');
        res.setHeader('X-Tags-Count', tags.length.toString());
        console.log(`📤 [TENANT FILTER] Returning response:`, {
            tags_count: tags.length,
            total_count: count,
            tenant_id: tenantId
        });
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error getting tags:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to get tags',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_tags_get_error',
                timestamp: new Date().toISOString()
            }
        });
    }
}
async function POST(req, res) {
    console.log(`🚀 [TENANT FILTER] === CUSTOM PRODUCT TAG CREATE ENDPOINT CALLED ===`);
    console.log(`🚀 [TENANT FILTER] Body:`, JSON.stringify(req.body, null, 2));
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        console.log(`🔄 [TENANT FILTER] Creating tag for tenant: ${tenantId}`);
        // Get tag data from request body
        const tagData = req.body;
        // Ensure tenant_id is injected and cannot be modified
        const tagWithTenant = {
            ...tagData,
            tenant_id: tenantId
        };
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
        });
        let createdTag = null;
        try {
            await client.connect();
            console.log(`🔗 [TENANT FILTER] Connected to database for tag creation`);
            // Generate tag ID
            const tagId = `ptag_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
            // Check if value already exists for this tenant
            if (tagWithTenant.value) {
                const existingResult = await client.query('SELECT id FROM product_tag WHERE value = $1 AND tenant_id = $2', [tagWithTenant.value, tenantId]);
                if (existingResult.rows.length > 0) {
                    await client.end();
                    return res.status(400).json({
                        type: 'invalid_data',
                        message: `Tag with value: ${tagWithTenant.value}, already exists for tenant: ${tenantId}.`,
                        tenant_id: tenantId
                    });
                }
            }
            // Insert tag
            const insertTagQuery = `
        INSERT INTO product_tag (
          id, value, metadata, tenant_id, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, NOW(), NOW())
        RETURNING *
      `;
            const tagValues = [
                tagId,
                tagWithTenant.value,
                tagWithTenant.metadata ? JSON.stringify({
                    ...tagWithTenant.metadata,
                    tenant_id: tenantId
                }) : JSON.stringify({ tenant_id: tenantId }),
                tenantId
            ];
            const result = await client.query(insertTagQuery, tagValues);
            createdTag = result.rows[0];
            // Parse metadata
            if (typeof createdTag.metadata === 'string') {
                try {
                    createdTag.metadata = JSON.parse(createdTag.metadata);
                }
                catch (e) {
                    console.log(`⚠️ Could not parse metadata for created tag ${createdTag.id}`);
                }
            }
            console.log(`✅ [TENANT FILTER] Created tag ${tagId} for tenant: ${tenantId}`);
            await client.end();
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            await client.end().catch(() => { });
            throw dbError;
        }
        // Return response in Medusa format
        const response = {
            product_tag: createdTag,
            _tenant: {
                id: tenantId,
                injected: true,
                method: 'direct_db_creation'
            }
        };
        // Add tenant headers
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Injected', 'true');
        console.log(`📤 [TENANT FILTER] Returning created tag for tenant ${tenantId}`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error creating tag:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to create tag',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_tag_create_error',
                timestamp: new Date().toISOString(),
                stack: error.stack
            }
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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