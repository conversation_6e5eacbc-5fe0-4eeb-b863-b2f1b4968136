"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
const zod_1 = require("zod");
// Query parameter validation schema
const KPIQuerySchema = zod_1.z.object({
    period: zod_1.z.enum(['7d', '30d', '90d', '1y']).default('30d'),
    tenant_id: zod_1.z.string().optional(),
    sales_channel_id: zod_1.z.string().optional(),
    compare_previous: zod_1.z.boolean().default(true),
});
/**
 * GET /admin/analytics/kpi
 *
 * Comprehensive KPI dashboard endpoint that provides:
 * - Key performance indicators across all business areas
 * - Period-over-period comparisons
 * - Trend analysis for each metric
 * - Performance scoring and alerts
 * - Target tracking and achievement status
 */
async function GET(req, res) {
    try {
        // Validate query parameters
        const query = KPIQuerySchema.parse(req.query);
        const { period, tenant_id, sales_channel_id, compare_previous } = query;
        // Get services using proper Medusa v2 service resolution
        const orderService = req.scope.resolve('order');
        const productService = req.scope.resolve('product');
        const customerService = req.scope.resolve('customer');
        // Calculate date ranges
        const endDate = new Date();
        const startDate = new Date();
        switch (period) {
            case '7d':
                startDate.setDate(endDate.getDate() - 7);
                break;
            case '30d':
                startDate.setDate(endDate.getDate() - 30);
                break;
            case '90d':
                startDate.setDate(endDate.getDate() - 90);
                break;
            case '1y':
                startDate.setFullYear(endDate.getFullYear() - 1);
                break;
        }
        // Previous period for comparison
        const periodDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
        const previousStartDate = new Date(startDate);
        const previousEndDate = new Date(startDate);
        previousStartDate.setDate(previousStartDate.getDate() - periodDays);
        // Build filters (without date filtering since Medusa v2 doesn't support it)
        const filters = {};
        const previousFilters = {};
        if (tenant_id) {
            filters.tenant_id = tenant_id;
            previousFilters.tenant_id = tenant_id;
        }
        if (sales_channel_id) {
            filters.sales_channel_id = sales_channel_id;
            previousFilters.sales_channel_id = sales_channel_id;
        }
        // Fetch current period data using correct method names and supported relations
        const [ordersResult, productsResult] = await Promise.all([
            orderService.listAndCountOrders(filters, { relations: ['items'] }),
            productService.listProducts(tenant_id ? { tenant_id } : {}),
        ]);
        // Extract data from results
        const allOrders = ordersResult[0]; // First element is the orders array
        const products = Array.isArray(productsResult) ? productsResult : productsResult[0] || [];
        // Filter orders by date range in memory (since Medusa v2 doesn't support date filtering)
        const orders = allOrders.filter(order => {
            const orderDate = new Date(order.created_at);
            return orderDate >= startDate && orderDate <= endDate;
        });
        // For customers, count unique customer IDs from filtered orders
        const uniqueCustomerIds = new Set();
        orders.forEach(order => {
            if (order.customer_id) {
                uniqueCustomerIds.add(order.customer_id);
            }
        });
        const totalCustomers = uniqueCustomerIds.size;
        // Filter previous period data for comparison
        let previousOrders = [];
        if (compare_previous) {
            // Filter from the same allOrders data for previous period
            previousOrders = allOrders.filter(order => {
                const orderDate = new Date(order.created_at);
                return orderDate >= previousStartDate && orderDate <= previousEndDate;
            });
        }
        // Calculate metrics
        const totalRevenue = orders.reduce((sum, order) => sum + (order.total || 0), 0);
        const totalOrders = orders.length;
        // totalCustomers is already calculated above from unique customer IDs
        const totalProducts = products.length;
        const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
        const completedOrders = orders.filter(order => order.status === 'completed');
        const conversionRate = totalOrders > 0 ? (completedOrders.length / totalOrders) * 100 : 0;
        // Previous period metrics
        const previousRevenue = previousOrders.reduce((sum, order) => sum + (order.total || 0), 0);
        const previousOrderCount = previousOrders.length;
        const previousAOV = previousOrderCount > 0 ? previousRevenue / previousOrderCount : 0;
        // Helper function to calculate change
        const calculateChange = (current, previous) => {
            if (previous === 0)
                return { value: current, percentage: 0, type: 'neutral' };
            const value = current - previous;
            const percentage = (value / previous) * 100;
            const type = value > 0 ? 'increase' : value < 0 ? 'decrease' : 'neutral';
            return { value, percentage, type };
        };
        // Generate trend data for the last 7 days
        const generateTrend = (orders, days = 7) => {
            const trend = [];
            for (let i = days - 1; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                const dayStart = new Date(date.setHours(0, 0, 0, 0));
                const dayEnd = new Date(date.setHours(23, 59, 59, 999));
                const dayOrders = orders.filter(order => {
                    const orderDate = new Date(order.created_at);
                    return orderDate >= dayStart && orderDate <= dayEnd;
                });
                trend.push({
                    date: dayStart.toISOString().split('T')[0],
                    value: dayOrders.reduce((sum, order) => sum + (order.total || 0), 0),
                });
            }
            return trend;
        };
        // Build KPI metrics
        const metrics = [
            // Revenue Metrics
            {
                id: 'total_revenue',
                title: 'Total Revenue',
                value: totalRevenue,
                formatted_value: `₹${totalRevenue.toLocaleString('en-IN')}`,
                previous_value: previousRevenue,
                change: calculateChange(totalRevenue, previousRevenue),
                trend: generateTrend(orders),
                target: 500000, // ₹5 lakh target
                unit: 'INR',
                category: 'revenue',
                priority: 'high',
                description: 'Total revenue generated from all orders',
            },
            {
                id: 'average_order_value',
                title: 'Average Order Value',
                value: averageOrderValue,
                formatted_value: `₹${Math.round(averageOrderValue).toLocaleString('en-IN')}`,
                previous_value: previousAOV,
                change: calculateChange(averageOrderValue, previousAOV),
                target: 2000, // ₹2000 target AOV
                unit: 'INR',
                category: 'revenue',
                priority: 'high',
                description: 'Average value per order',
            },
            // Order Metrics
            {
                id: 'total_orders',
                title: 'Total Orders',
                value: totalOrders,
                formatted_value: totalOrders.toLocaleString('en-IN'),
                previous_value: previousOrderCount,
                change: calculateChange(totalOrders, previousOrderCount),
                target: 500, // 500 orders target
                unit: 'count',
                category: 'orders',
                priority: 'high',
                description: 'Total number of orders placed',
            },
            {
                id: 'conversion_rate',
                title: 'Order Completion Rate',
                value: conversionRate,
                formatted_value: `${conversionRate.toFixed(1)}%`,
                target: 85, // 85% completion rate target
                unit: 'percentage',
                category: 'orders',
                priority: 'medium',
                description: 'Percentage of orders that are completed',
            },
            // Customer Metrics
            {
                id: 'total_customers',
                title: 'Total Customers',
                value: totalCustomers,
                formatted_value: totalCustomers.toLocaleString('en-IN'),
                target: 1000, // 1000 customers target
                unit: 'count',
                category: 'customers',
                priority: 'medium',
                description: 'Total number of registered customers',
            },
            {
                id: 'customer_lifetime_value',
                title: 'Customer Lifetime Value',
                value: totalCustomers > 0 ? totalRevenue / totalCustomers : 0,
                formatted_value: `₹${totalCustomers > 0 ? Math.round(totalRevenue / totalCustomers).toLocaleString('en-IN') : '0'}`,
                target: 5000, // ₹5000 CLV target
                unit: 'INR',
                category: 'customers',
                priority: 'medium',
                description: 'Average revenue per customer',
            },
            // Product Metrics
            {
                id: 'total_products',
                title: 'Total Products',
                value: totalProducts,
                formatted_value: totalProducts.toLocaleString('en-IN'),
                target: 100, // 100 products target
                unit: 'count',
                category: 'products',
                priority: 'low',
                description: 'Total number of products in catalog',
            },
            {
                id: 'products_sold',
                title: 'Products Sold',
                value: orders.reduce((sum, order) => sum + (order.items?.length || 0), 0),
                formatted_value: orders
                    .reduce((sum, order) => sum + (order.items?.length || 0), 0)
                    .toLocaleString('en-IN'),
                unit: 'count',
                category: 'products',
                priority: 'medium',
                description: 'Total number of product units sold',
            },
            // Operational Metrics
            {
                id: 'pending_orders',
                title: 'Pending Orders',
                value: orders.filter(order => order.status === 'pending').length,
                formatted_value: orders.filter(order => order.status === 'pending').length.toString(),
                target: 10, // Keep pending orders under 10
                unit: 'count',
                category: 'operations',
                priority: 'high',
                description: 'Number of orders awaiting processing',
            },
        ];
        // Calculate performance scores
        const calculateCategoryScore = (categoryMetrics) => {
            const metricsWithTargets = categoryMetrics.filter(m => m.target !== undefined);
            if (metricsWithTargets.length === 0)
                return 100;
            const scores = metricsWithTargets.map(metric => {
                const target = metric.target;
                const achievement = Math.min((metric.value / target) * 100, 100);
                return achievement;
            });
            return scores.reduce((sum, score) => sum + score, 0) / scores.length;
        };
        const categoryScores = {
            revenue: calculateCategoryScore(metrics.filter(m => m.category === 'revenue')),
            orders: calculateCategoryScore(metrics.filter(m => m.category === 'orders')),
            customers: calculateCategoryScore(metrics.filter(m => m.category === 'customers')),
            products: calculateCategoryScore(metrics.filter(m => m.category === 'products')),
            operations: calculateCategoryScore(metrics.filter(m => m.category === 'operations')),
        };
        const overallScore = Object.values(categoryScores).reduce((sum, score) => sum + score, 0) / 5;
        // Generate alerts
        const alerts = [];
        metrics.forEach(metric => {
            if (metric.target && metric.value < metric.target * 0.8) {
                alerts.push({
                    metric_id: metric.id,
                    alert_type: 'target_missed',
                    severity: 'high',
                    message: `${metric.title} is ${((1 - metric.value / metric.target) * 100).toFixed(1)}% below target`,
                });
            }
            if (metric.change && Math.abs(metric.change.percentage) > 20) {
                alerts.push({
                    metric_id: metric.id,
                    alert_type: 'significant_change',
                    severity: 'medium',
                    message: `${metric.title} has ${metric.change.type}d by ${Math.abs(metric.change.percentage).toFixed(1)}%`,
                });
            }
        });
        // Build response
        const dashboard = {
            summary: {
                period,
                generated_at: new Date().toISOString(),
                total_metrics: metrics.length,
                metrics_with_targets: metrics.filter(m => m.target !== undefined).length,
                metrics_meeting_targets: metrics.filter(m => m.target && m.value >= m.target).length,
            },
            metrics,
            performance_score: {
                overall_score: Math.round(overallScore),
                category_scores: {
                    revenue: Math.round(categoryScores.revenue),
                    orders: Math.round(categoryScores.orders),
                    customers: Math.round(categoryScores.customers),
                    products: Math.round(categoryScores.products),
                    operations: Math.round(categoryScores.operations),
                },
            },
            alerts,
        };
        res.status(200).json(dashboard);
    }
    catch (error) {
        console.error('KPI analytics error:', error);
        res.status(500).json({
            error: 'Failed to fetch KPI analytics',
            details: error instanceof Error ? error.message : 'Unknown error',
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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