"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
exports.POST = POST;
exports.DELETE = DELETE;
async function GET(req, res) {
    console.log(`🚀 [TENANT FILTER] === CUSTOM COLLECTION GET BY ID ENDPOINT CALLED ===`);
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        // Get collection ID from URL params
        const collectionId = req.params?.id;
        if (!collectionId) {
            return res.status(400).json({
                error: 'Collection ID is required',
                tenant_id: tenantId
            });
        }
        console.log(`🔍 [TENANT FILTER] Getting collection ${collectionId} for tenant: ${tenantId}`);
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
        });
        let collection = null;
        try {
            await client.connect();
            console.log(`🔗 [TENANT FILTER] Connected to database directly`);
            // Get collection with tenant validation
            const result = await client.query(`
        SELECT 
          id, title, handle, metadata, created_at, updated_at, 
          deleted_at, tenant_id
        FROM product_collection 
        WHERE id = $1 AND tenant_id = $2 AND deleted_at IS NULL
      `, [collectionId, tenantId]);
            collection = result.rows[0] || null;
            console.log(`📦 [TENANT FILTER] Retrieved collection: ${collection ? 'Found' : 'Not Found'}`);
            if (collection) {
                // Parse metadata if it's a string
                if (typeof collection.metadata === 'string') {
                    try {
                        collection.metadata = JSON.parse(collection.metadata);
                    }
                    catch (e) {
                        console.log(`⚠️ Could not parse metadata for collection ${collection.id}`);
                    }
                }
            }
            await client.end();
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            await client.end().catch(() => { });
            throw dbError;
        }
        if (!collection) {
            return res.status(404).json({
                error: 'Collection not found or access denied',
                collection_id: collectionId,
                tenant_id: tenantId,
                _debug: {
                    message: 'Collection either does not exist or belongs to a different tenant'
                }
            });
        }
        // Return response in Medusa format
        const response = {
            collection,
            _tenant: {
                id: tenantId,
                filtered: true,
                method: 'direct_db_connection'
            }
        };
        // Add tenant headers
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Filtered', 'true');
        console.log(`📤 [TENANT FILTER] Returning collection ${collectionId} for tenant ${tenantId}`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error getting collection:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to get collection',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_collection_get_error',
                timestamp: new Date().toISOString()
            }
        });
    }
}
async function POST(req, res) {
    console.log(`🚀 [TENANT FILTER] === CUSTOM COLLECTION UPDATE ENDPOINT CALLED ===`);
    console.log(`🚀 [TENANT FILTER] Body:`, JSON.stringify(req.body, null, 2));
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        // Get collection ID from URL params
        const collectionId = req.params?.id;
        if (!collectionId) {
            return res.status(400).json({
                error: 'Collection ID is required for update',
                tenant_id: tenantId
            });
        }
        console.log(`🔄 [TENANT FILTER] Updating collection ${collectionId} for tenant: ${tenantId}`);
        // Get update data from request body
        const updateData = req.body;
        // Remove tenant_id from update data to prevent modification
        const { tenant_id: _, ...safeUpdateData } = updateData;
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
        });
        let updatedCollection = null;
        try {
            await client.connect();
            console.log(`🔗 [TENANT FILTER] Connected to database directly`);
            // First, verify the collection belongs to this tenant
            const checkQuery = 'SELECT id FROM product_collection WHERE id = $1 AND tenant_id = $2 AND deleted_at IS NULL';
            const checkResult = await client.query(checkQuery, [collectionId, tenantId]);
            if (checkResult.rows.length === 0) {
                await client.end();
                return res.status(404).json({
                    error: 'Collection not found or access denied',
                    collection_id: collectionId,
                    tenant_id: tenantId,
                    _debug: {
                        message: 'Collection either does not exist or belongs to a different tenant'
                    }
                });
            }
            // Check if new handle already exists for this tenant (if handle is being updated)
            if (safeUpdateData.handle) {
                const existingResult = await client.query('SELECT id FROM product_collection WHERE handle = $1 AND tenant_id = $2 AND id != $3 AND deleted_at IS NULL', [safeUpdateData.handle, tenantId, collectionId]);
                if (existingResult.rows.length > 0) {
                    await client.end();
                    return res.status(400).json({
                        type: 'invalid_data',
                        message: `Collection with handle: ${safeUpdateData.handle}, already exists for tenant: ${tenantId}.`,
                        tenant_id: tenantId
                    });
                }
            }
            // Update the collection (tenant_id cannot be changed)
            const updateQuery = `
        UPDATE product_collection 
        SET 
          title = COALESCE($1, title),
          handle = COALESCE($2, handle),
          metadata = COALESCE($3, metadata),
          updated_at = NOW()
        WHERE id = $4 AND tenant_id = $5
        RETURNING *
      `;
            const values = [
                safeUpdateData.title,
                safeUpdateData.handle,
                safeUpdateData.metadata ? JSON.stringify({
                    ...safeUpdateData.metadata,
                    tenant_id: tenantId
                }) : null,
                collectionId,
                tenantId
            ];
            const result = await client.query(updateQuery, values);
            updatedCollection = result.rows[0];
            // Parse metadata
            if (typeof updatedCollection.metadata === 'string') {
                try {
                    updatedCollection.metadata = JSON.parse(updatedCollection.metadata);
                }
                catch (e) {
                    console.log(`⚠️ Could not parse metadata for updated collection ${updatedCollection.id}`);
                }
            }
            console.log(`✅ [TENANT FILTER] Updated collection ${collectionId} for tenant: ${tenantId}`);
            await client.end();
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            await client.end().catch(() => { });
            throw dbError;
        }
        // Return response in Medusa format
        const response = {
            collection: updatedCollection,
            _tenant: {
                id: tenantId,
                validated: true,
                method: 'direct_db_connection'
            }
        };
        // Add tenant headers
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Validated', 'true');
        console.log(`📤 [TENANT FILTER] Returning updated collection for tenant ${tenantId}`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error updating collection:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to update collection',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_collection_update_error',
                timestamp: new Date().toISOString()
            }
        });
    }
}
async function DELETE(req, res) {
    console.log(`🚀 [TENANT FILTER] === CUSTOM COLLECTION DELETE ENDPOINT CALLED ===`);
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        // Get collection ID from URL params
        const collectionId = req.params?.id;
        if (!collectionId) {
            return res.status(400).json({
                error: 'Collection ID is required for deletion',
                tenant_id: tenantId
            });
        }
        console.log(`🗑️ [TENANT FILTER] Deleting collection ${collectionId} for tenant: ${tenantId}`);
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
        });
        let deletedCollection = null;
        try {
            await client.connect();
            console.log(`🔗 [TENANT FILTER] Connected to database directly`);
            // First, verify the collection belongs to this tenant and get it
            const checkQuery = 'SELECT * FROM product_collection WHERE id = $1 AND tenant_id = $2 AND deleted_at IS NULL';
            const checkResult = await client.query(checkQuery, [collectionId, tenantId]);
            if (checkResult.rows.length === 0) {
                await client.end();
                return res.status(404).json({
                    error: 'Collection not found or access denied',
                    collection_id: collectionId,
                    tenant_id: tenantId,
                    _debug: {
                        message: 'Collection either does not exist or belongs to a different tenant'
                    }
                });
            }
            // Soft delete the collection (set deleted_at timestamp)
            const deleteQuery = 'UPDATE product_collection SET deleted_at = NOW(), updated_at = NOW() WHERE id = $1 AND tenant_id = $2 RETURNING *';
            const result = await client.query(deleteQuery, [collectionId, tenantId]);
            deletedCollection = result.rows[0];
            console.log(`✅ [TENANT FILTER] Soft deleted collection ${collectionId} for tenant: ${tenantId}`);
            await client.end();
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            await client.end().catch(() => { });
            throw dbError;
        }
        // Return response in Medusa format
        const response = {
            id: collectionId,
            object: 'collection',
            deleted: true,
            _tenant: {
                id: tenantId,
                validated: true,
                method: 'direct_db_connection'
            }
        };
        // Add tenant headers
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Validated', 'true');
        console.log(`📤 [TENANT FILTER] Confirmed deletion of collection ${collectionId} for tenant ${tenantId}`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error deleting collection:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to delete collection',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_collection_delete_error',
                timestamp: new Date().toISOString()
            }
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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