"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
exports.POST = POST;
const centralized_database_1 = require("../../../services/centralized-database");
const product_auto_config_1 = require("../../../workflows/product-auto-config");
async function GET(req, res) {
    console.log(`🚀 [TENANT FILTER] === ENHANCED PRODUCTS ENDPOINT CALLED ===`);
    console.log(`🚀 [TENANT FILTER] Headers:`, JSON.stringify(req.headers, null, 2));
    console.log(`🚀 [TENANT FILTER] Query:`, JSON.stringify(req.query, null, 2));
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        console.log(`🔍 [TENANT FILTER] Processing products request for tenant: ${tenantId}`);
        // Get query parameters
        const { limit = 50, offset = 0, category_id, status,
        // fields and order are available but not used in direct DB query
        // ...filters - removed unused destructuring
         } = req.query;
        // Use centralized database service instead of creating new connections
        const filters = {};
        if (category_id)
            filters.category_id = category_id;
        if (status)
            filters.status = status;
        let products = [];
        let count = 0;
        try {
            console.log(`🔗 [TENANT FILTER] Using centralized database service`);
            // Get total count for this tenant using centralized service
            count = await centralized_database_1.centralizedDb.getCount('product', tenantId, 'deleted_at IS NULL');
            console.log(`📊 [TENANT FILTER] Total products for tenant ${tenantId}: ${count}`);
            // Get products using centralized service with enhanced query
            const productsResult = await centralized_database_1.centralizedDb.query(`
        SELECT
          id, title, handle, description, status,
          created_at, updated_at, tenant_id, metadata,
          thumbnail, collection_id
        FROM product
        WHERE tenant_id = $1 AND deleted_at IS NULL
        ${filters.category_id ? 'AND id IN (SELECT product_id FROM product_category WHERE category_id = $4)' : ''}
        ${filters.status ? `AND status = $${filters.category_id ? '5' : '4'}` : ''}
        ORDER BY created_at DESC
        LIMIT $2 OFFSET $3
      `, [
                tenantId,
                parseInt(limit),
                parseInt(offset),
                ...(filters.category_id ? [filters.category_id] : []),
                ...(filters.status ? [filters.status] : []),
            ], { tenantId });
            products = productsResult.rows || [];
            console.log(`📦 [TENANT FILTER] Retrieved ${products.length} basic products`);
            // Now get related data for each product using centralized service
            for (let i = 0; i < products.length; i++) {
                const product = products[i];
                // Get variants using centralized service
                try {
                    const variantsResult = await centralized_database_1.centralizedDb.query(`
            SELECT id, title, sku, metadata, weight, width, length, height, tenant_id
            FROM product_variant
            WHERE product_id = $1 AND tenant_id = $2
          `, [product.id, tenantId], { tenantId });
                    product.variants = variantsResult.rows || [];
                }
                catch (e) {
                    console.log(`⚠️ Could not fetch variants for product ${product.id}:`, e.message);
                    product.variants = [];
                }
                // Get images using centralized service (no tenant_id in image table)
                try {
                    const imagesResult = await centralized_database_1.centralizedDb.query(`
            SELECT id, url, metadata
            FROM image
            WHERE product_id = $1
          `, [product.id], { tenantId });
                    // Filter by tenant_id from metadata
                    product.images = imagesResult.rows.filter(img => {
                        try {
                            const metadata = typeof img.metadata === 'string' ? JSON.parse(img.metadata) : img.metadata;
                            return metadata && metadata.tenant_id === tenantId;
                        }
                        catch {
                            return false;
                        }
                    });
                }
                catch (e) {
                    console.log(`⚠️ Could not fetch images for product ${product.id}:`, e.message);
                    product.images = [];
                }
                // Get options (no tenant_id in product_option table)
                try {
                    const optionsResult = await client.query(`
            SELECT id, title, metadata
            FROM product_option 
            WHERE product_id = $1
          `, [product.id]);
                    // Filter by tenant_id from metadata
                    product.options = optionsResult.rows.filter(opt => {
                        try {
                            const metadata = typeof opt.metadata === 'string' ? JSON.parse(opt.metadata) : opt.metadata;
                            return metadata && metadata.tenant_id === tenantId;
                        }
                        catch {
                            return false;
                        }
                    });
                }
                catch (e) {
                    console.log(`⚠️ Could not fetch options for product ${product.id}:`, e.message);
                    product.options = [];
                }
                // Get tags
                try {
                    const tagsResult = await client.query(`
            SELECT pt.id, pt.value
            FROM product_tag pt
            JOIN product_tags ptags ON pt.id = ptags.product_tag_id
            WHERE ptags.product_id = $1 AND pt.tenant_id = $2
          `, [product.id, tenantId]);
                    product.tags = tagsResult.rows || [];
                }
                catch (e) {
                    console.log(`⚠️ Could not fetch tags for product ${product.id}:`, e.message);
                    product.tags = [];
                }
                // Get categories
                try {
                    const categoriesResult = await client.query(`
            SELECT pc.id, pc.name, pc.handle
            FROM product_category pc
            JOIN product_category_product pcp ON pc.id = pcp.product_category_id
            WHERE pcp.product_id = $1 AND pc.tenant_id = $2
          `, [product.id, tenantId]);
                    product.categories = categoriesResult.rows || [];
                }
                catch (e) {
                    console.log(`⚠️ Could not fetch categories for product ${product.id}:`, e.message);
                    product.categories = [];
                }
            }
            console.log(`📦 [TENANT FILTER] Enhanced ${products.length} products with related data`);
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            products = [];
            count = 0;
        }
        // No finally block needed - centralized service handles connection cleanup
        // Return response in Medusa format
        const response = {
            products,
            count: products.length,
            offset: parseInt(offset),
            limit: parseInt(limit),
            // Add tenant info for debugging
            _tenant: {
                id: tenantId,
                filtered: true,
                method: 'centralized_db_service',
                total_in_db: count,
            },
        };
        // Add tenant headers for debugging
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Filtered', 'true');
        res.setHeader('X-Products-Count', products.length.toString());
        console.log(`📤 [TENANT FILTER] Returning response:`, {
            products_count: products.length,
            total_count: count,
            tenant_id: tenantId,
        });
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error in products endpoint:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to fetch products',
            message: error.message,
            tenant_id: tenantId,
            stack: error.stack,
            _debug: {
                error_type: 'tenant_filter_error',
                timestamp: new Date().toISOString(),
            },
        });
    }
}
async function POST(req, res) {
    console.log(`🚀 [TENANT FILTER] === COMPREHENSIVE PRODUCTS POST ENDPOINT CALLED ===`);
    console.log(`🚀 [TENANT FILTER] Body:`, JSON.stringify(req.body, null, 2));
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        console.log(`🔍 [TENANT FILTER] Creating comprehensive product for tenant: ${tenantId}`);
        // Get product data from request body
        const productData = req.body;
        // Inject tenant_id into the product data and all related entities
        const productWithTenant = {
            ...productData,
            tenant_id: tenantId,
            metadata: {
                ...productData.metadata,
                tenant_id: tenantId,
            },
        };
        console.log(`🏷️ [TENANT FILTER] Injected tenant_id: ${tenantId} into product data`);
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: process.env.DATABASE_URL ||
                process.env.POSTGRES_URL ||
                'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
        });
        let createdProduct = null;
        let createdVariants = [];
        let createdImages = [];
        let createdOptions = [];
        try {
            await client.connect();
            console.log(`🔗 [TENANT FILTER] Connected to database directly`);
            // Start transaction for data consistency
            await client.query('BEGIN');
            // Generate a unique product ID
            const productId = `prod_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
            // 1. Insert main product with all fields
            const insertProductQuery = `
        INSERT INTO product (
          id, title, handle, description, status, 
          tenant_id, metadata, thumbnail, collection_id,
          created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW())
        RETURNING *
      `;
            const productValues = [
                productId,
                productWithTenant.title,
                productWithTenant.handle || productWithTenant.title?.toLowerCase().replace(/\s+/g, '-'),
                productWithTenant.description || '',
                productWithTenant.status || 'draft',
                tenantId,
                JSON.stringify(productWithTenant.metadata || {}),
                productWithTenant.thumbnail || null,
                productWithTenant.collection_id || null,
            ];
            const productResult = await client.query(insertProductQuery, productValues);
            createdProduct = productResult.rows[0];
            console.log(`✅ [TENANT FILTER] Created main product: ${createdProduct.id}`);
            // 2. Insert product options
            if (productWithTenant.options && productWithTenant.options.length > 0) {
                for (const option of productWithTenant.options) {
                    const optionId = `opt_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                    const insertOptionQuery = `
            INSERT INTO product_option (
              id, title, product_id, metadata, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, NOW(), NOW())
            RETURNING *
          `;
                    const optionValues = [
                        optionId,
                        option.title,
                        productId,
                        JSON.stringify({ values: option.values || [], tenant_id: tenantId }),
                    ];
                    const optionResult = await client.query(insertOptionQuery, optionValues);
                    createdOptions.push(optionResult.rows[0]);
                    // Insert option values
                    if (option.values && option.values.length > 0) {
                        for (const value of option.values) {
                            const valueId = `optval_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                            const insertValueQuery = `
                INSERT INTO product_option_value (
                  id, value, option_id, metadata, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, NOW(), NOW())
              `;
                            await client.query(insertValueQuery, [
                                valueId,
                                value,
                                optionId,
                                JSON.stringify({ tenant_id: tenantId }),
                            ]);
                        }
                    }
                }
                console.log(`✅ [TENANT FILTER] Created ${createdOptions.length} product options`);
            }
            // 3. Insert product variants
            if (productWithTenant.variants && productWithTenant.variants.length > 0) {
                for (const variant of productWithTenant.variants) {
                    const variantId = `variant_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                    const insertVariantQuery = `
            INSERT INTO product_variant (
              id, title, sku, product_id, tenant_id,
              metadata, weight, width, length, height, manage_inventory,
              created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW(), NOW())
            RETURNING *
          `;
                    const variantMetadata = {
                        ...variant.metadata,
                        tenant_id: tenantId,
                        sale_price: variant.metadata?.sale_price,
                        original_price: variant.metadata?.original_price,
                        product_quantity: variant.metadata?.product_quantity,
                        product_inventory_status: variant.metadata?.product_inventory_status,
                    };
                    const variantValues = [
                        variantId,
                        variant.title || 'Default',
                        variant.sku || `sku_${productId}`,
                        productId,
                        tenantId,
                        JSON.stringify(variantMetadata),
                        variant.weight || null,
                        variant.width || null,
                        variant.length || null,
                        variant.height || null,
                        false, // manage_inventory: false as per requirements
                    ];
                    const variantResult = await client.query(insertVariantQuery, variantValues);
                    createdVariants.push(variantResult.rows[0]);
                    // Insert variant prices (Medusa v2 uses price_set structure)
                    if (variant.prices && variant.prices.length > 0) {
                        // First create a price set for this variant
                        const priceSetId = `pset_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                        const insertPriceSetQuery = `
              INSERT INTO price_set (
                id, created_at, updated_at
              ) VALUES ($1, NOW(), NOW())
            `;
                        await client.query(insertPriceSetQuery, [priceSetId]);
                        // Link variant to price set
                        const linkVariantPriceSetQuery = `
              INSERT INTO product_variant_price_set (
                id, variant_id, price_set_id, created_at, updated_at
              ) VALUES ($1, $2, $3, NOW(), NOW())
            `;
                        const linkId = `pvps_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                        await client.query(linkVariantPriceSetQuery, [linkId, variantId, priceSetId]);
                        // Add prices to the price set
                        for (const price of variant.prices) {
                            const priceId = `price_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                            const insertPriceQuery = `
                INSERT INTO price (
                  id, currency_code, amount, raw_amount, price_set_id, tenant_id, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
              `;
                            await client.query(insertPriceQuery, [
                                priceId,
                                price.currency_code || 'inr',
                                price.amount,
                                JSON.stringify({ value: price.amount.toString(), precision: 20 }),
                                priceSetId,
                                tenantId,
                            ]);
                        }
                    }
                }
                console.log(`✅ [TENANT FILTER] Created ${createdVariants.length} product variants`);
            }
            // 4. Insert product images
            if (productWithTenant.images && productWithTenant.images.length > 0) {
                for (const image of productWithTenant.images) {
                    const imageId = `img_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                    const insertImageQuery = `
            INSERT INTO image (
              id, url, product_id, metadata, rank, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
            RETURNING *
          `;
                    const imageValues = [
                        imageId,
                        image.url,
                        productId,
                        JSON.stringify({ tenant_id: tenantId }),
                        0, // rank is required
                    ];
                    const imageResult = await client.query(insertImageQuery, imageValues);
                    createdImages.push(imageResult.rows[0]);
                }
                console.log(`✅ [TENANT FILTER] Created ${createdImages.length} product images`);
            }
            // 5. Handle product categories (if provided)
            if (productWithTenant.categories && productWithTenant.categories.length > 0) {
                for (const category of productWithTenant.categories) {
                    // Handle both string IDs and objects with id property
                    const categoryId = typeof category === 'string' ? category : category.id;
                    if (!categoryId) {
                        console.warn(`⚠️ [TENANT FILTER] Skipping invalid category:`, category);
                        continue;
                    }
                    const insertCategoryLinkQuery = `
            INSERT INTO product_category_product (
              product_id, product_category_id, tenant_id, created_at, updated_at
            ) VALUES ($1, $2, $3, NOW(), NOW())
          `;
                    await client.query(insertCategoryLinkQuery, [productId, categoryId, tenantId]);
                }
                console.log(`✅ [TENANT FILTER] Linked product to ${productWithTenant.categories.length} categories`);
            }
            // 6. Handle product tags (if provided)
            if (productWithTenant.tags && productWithTenant.tags.length > 0) {
                for (const tag of productWithTenant.tags) {
                    // First ensure tag exists or create it
                    const tagId = `tag_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                    const insertTagQuery = `
            INSERT INTO product_tag (
              id, value, tenant_id, created_at, updated_at
            ) VALUES ($1, $2, $3, NOW(), NOW())
            ON CONFLICT (value, tenant_id) DO NOTHING
          `;
                    await client.query(insertTagQuery, [tagId, tag, tenantId]);
                    // Link tag to product
                    const insertTagLinkQuery = `
            INSERT INTO product_tags (
              product_id, product_tag_id, tenant_id, created_at
            ) VALUES ($1, (SELECT id FROM product_tag WHERE value = $2 AND tenant_id = $3), $3, NOW())
          `;
                    await client.query(insertTagLinkQuery, [productId, tag, tenantId]);
                }
                console.log(`✅ [TENANT FILTER] Linked product to ${productWithTenant.tags.length} tags`);
            }
            // Commit transaction
            await client.query('COMMIT');
            console.log(`✅ [TENANT FILTER] Transaction committed successfully`);
            await client.end();
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            await client.query('ROLLBACK').catch(() => { });
            await client.end().catch(() => { });
            throw dbError;
        }
        // Apply auto-configuration to the created product
        console.log(`🔧 [TENANT FILTER] Applying auto-configuration to product: ${createdProduct.id}`);
        let autoConfigResults = {
            successful: 0,
            failed: 0,
            errors: [],
        };
        try {
            const workflowResult = await (0, product_auto_config_1.createProductsWithAutoConfigWorkflow)(req.scope).run({
                input: {
                    products: [], // Empty since product is already created
                    tenantId: tenantId,
                },
            });
            // Apply configuration to existing product
            const productAutoConfigService = req.scope.resolve('productAutoConfigService');
            await productAutoConfigService.applyAutoConfiguration(createdProduct.id, tenantId);
            autoConfigResults.successful = 1;
            console.log(`✅ [TENANT FILTER] Auto-configuration applied successfully`);
        }
        catch (autoConfigError) {
            console.error('❌ [TENANT FILTER] Auto-configuration failed:', autoConfigError);
            autoConfigResults.failed = 1;
            autoConfigResults.errors.push(`Auto-configuration failed: ${autoConfigError.message}`);
        }
        // Build comprehensive response
        const response = {
            product: {
                ...createdProduct,
                variants: createdVariants,
                images: createdImages,
                options: createdOptions,
                categories: productWithTenant.categories || [],
                tags: productWithTenant.tags || [],
            },
            _tenant: {
                id: tenantId,
                injected: true,
                method: 'comprehensive_db_creation',
                entities_created: {
                    product: 1,
                    variants: createdVariants.length,
                    images: createdImages.length,
                    options: createdOptions.length,
                    categories: productWithTenant.categories?.length || 0,
                    tags: productWithTenant.tags?.length || 0,
                },
            },
            autoConfiguration: {
                salesChannelAssigned: autoConfigResults.successful > 0,
                inventoryManagementDisabled: autoConfigResults.successful > 0,
                shippingProfileAssigned: autoConfigResults.successful > 0,
                salesChannelId: process.env.DEFAULT_SALES_CHANNEL_ID,
                shippingProfileId: process.env.DEFAULT_SHIPPING_PROFILE_ID,
                results: autoConfigResults,
            },
        };
        // Add tenant headers
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Injected', 'true');
        res.setHeader('X-Entities-Created', JSON.stringify(response._tenant.entities_created));
        console.log(`📤 [TENANT FILTER] Returning comprehensive product for tenant ${tenantId}`);
        console.log(`📊 [TENANT FILTER] Entities created:`, response._tenant.entities_created);
        res.status(201).json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error creating comprehensive product:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to create comprehensive product',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_comprehensive_create_error',
                timestamp: new Date().toISOString(),
                stack: error.stack,
            },
        });
    }
}
// PUT method removed - Medusa v2 uses POST to /admin/products/:id for updates
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicm91dGUuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi9zcmMvYXBpL2FkbWluL3Byb2R1Y3RzL3JvdXRlLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7O0FBSUEsa0JBNk5DO0FBRUQsb0JBNFhDO0FBOWxCRCxpRkFBdUU7QUFDdkUsZ0ZBQThGO0FBRXZGLEtBQUssVUFBVSxHQUFHLENBQUMsR0FBa0IsRUFBRSxHQUFtQjtJQUMvRCxPQUFPLENBQUMsR0FBRyxDQUFDLDhEQUE4RCxDQUFDLENBQUM7SUFDNUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyw2QkFBNkIsRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDLEdBQUcsQ0FBQyxPQUFPLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7SUFDakYsT0FBTyxDQUFDLEdBQUcsQ0FBQywyQkFBMkIsRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDLEdBQUcsQ0FBQyxLQUFLLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7SUFFN0UsSUFBSSxDQUFDO1FBQ0gsZ0NBQWdDO1FBQ2hDLE1BQU0sUUFBUSxHQUFJLEdBQUcsQ0FBQyxPQUFPLENBQUMsYUFBYSxDQUFZLElBQUksU0FBUyxDQUFDO1FBRXJFLE9BQU8sQ0FBQyxHQUFHLENBQUMsOERBQThELFFBQVEsRUFBRSxDQUFDLENBQUM7UUFFdEYsdUJBQXVCO1FBQ3ZCLE1BQU0sRUFDSixLQUFLLEdBQUcsRUFBRSxFQUNWLE1BQU0sR0FBRyxDQUFDLEVBQ1YsV0FBVyxFQUNYLE1BQU07UUFDTixpRUFBaUU7UUFDakUsNENBQTRDO1VBQzdDLEdBQUcsR0FBRyxDQUFDLEtBQUssQ0FBQztRQUVkLHVFQUF1RTtRQUN2RSxNQUFNLE9BQU8sR0FBUSxFQUFFLENBQUM7UUFDeEIsSUFBSSxXQUFXO1lBQUUsT0FBTyxDQUFDLFdBQVcsR0FBRyxXQUFXLENBQUM7UUFDbkQsSUFBSSxNQUFNO1lBQUUsT0FBTyxDQUFDLE1BQU0sR0FBRyxNQUFNLENBQUM7UUFFcEMsSUFBSSxRQUFRLEdBQVUsRUFBRSxDQUFDO1FBQ3pCLElBQUksS0FBSyxHQUFHLENBQUMsQ0FBQztRQUVkLElBQUksQ0FBQztZQUNILE9BQU8sQ0FBQyxHQUFHLENBQUMsdURBQXVELENBQUMsQ0FBQztZQUVyRSw0REFBNEQ7WUFDNUQsS0FBSyxHQUFHLE1BQU0sb0NBQWEsQ0FBQyxRQUFRLENBQUMsU0FBUyxFQUFFLFFBQVEsRUFBRSxvQkFBb0IsQ0FBQyxDQUFDO1lBQ2hGLE9BQU8sQ0FBQyxHQUFHLENBQUMsZ0RBQWdELFFBQVEsS0FBSyxLQUFLLEVBQUUsQ0FBQyxDQUFDO1lBRWxGLDZEQUE2RDtZQUM3RCxNQUFNLGNBQWMsR0FBRyxNQUFNLG9DQUFhLENBQUMsS0FBSyxDQUM5Qzs7Ozs7OztVQU9FLE9BQU8sQ0FBQyxXQUFXLENBQUMsQ0FBQyxDQUFDLDRFQUE0RSxDQUFDLENBQUMsQ0FBQyxFQUFFO1VBQ3ZHLE9BQU8sQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLGlCQUFpQixPQUFPLENBQUMsV0FBVyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEdBQUcsRUFBRSxDQUFDLENBQUMsQ0FBQyxFQUFFOzs7T0FHM0UsRUFDQztnQkFDRSxRQUFRO2dCQUNSLFFBQVEsQ0FBQyxLQUFlLENBQUM7Z0JBQ3pCLFFBQVEsQ0FBQyxNQUFnQixDQUFDO2dCQUMxQixHQUFHLENBQUMsT0FBTyxDQUFDLFdBQVcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsV0FBVyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQztnQkFDckQsR0FBRyxDQUFDLE9BQU8sQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUM7YUFDNUMsRUFDRCxFQUFFLFFBQVEsRUFBRSxDQUNiLENBQUM7WUFFRixRQUFRLEdBQUcsY0FBYyxDQUFDLElBQUksSUFBSSxFQUFFLENBQUM7WUFDckMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxnQ0FBZ0MsUUFBUSxDQUFDLE1BQU0saUJBQWlCLENBQUMsQ0FBQztZQUU5RSxrRUFBa0U7WUFDbEUsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLFFBQVEsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQztnQkFDekMsTUFBTSxPQUFPLEdBQUcsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDO2dCQUU1Qix5Q0FBeUM7Z0JBQ3pDLElBQUksQ0FBQztvQkFDSCxNQUFNLGNBQWMsR0FBRyxNQUFNLG9DQUFhLENBQUMsS0FBSyxDQUM5Qzs7OztXQUlELEVBQ0MsQ0FBQyxPQUFPLENBQUMsRUFBRSxFQUFFLFFBQVEsQ0FBQyxFQUN0QixFQUFFLFFBQVEsRUFBRSxDQUNiLENBQUM7b0JBQ0YsT0FBTyxDQUFDLFFBQVEsR0FBRyxjQUFjLENBQUMsSUFBSSxJQUFJLEVBQUUsQ0FBQztnQkFDL0MsQ0FBQztnQkFBQyxPQUFPLENBQUMsRUFBRSxDQUFDO29CQUNYLE9BQU8sQ0FBQyxHQUFHLENBQUMsMkNBQTJDLE9BQU8sQ0FBQyxFQUFFLEdBQUcsRUFBRSxDQUFDLENBQUMsT0FBTyxDQUFDLENBQUM7b0JBQ2pGLE9BQU8sQ0FBQyxRQUFRLEdBQUcsRUFBRSxDQUFDO2dCQUN4QixDQUFDO2dCQUVELHFFQUFxRTtnQkFDckUsSUFBSSxDQUFDO29CQUNILE1BQU0sWUFBWSxHQUFHLE1BQU0sb0NBQWEsQ0FBQyxLQUFLLENBQzVDOzs7O1dBSUQsRUFDQyxDQUFDLE9BQU8sQ0FBQyxFQUFFLENBQUMsRUFDWixFQUFFLFFBQVEsRUFBRSxDQUNiLENBQUM7b0JBQ0Ysb0NBQW9DO29CQUNwQyxPQUFPLENBQUMsTUFBTSxHQUFHLFlBQVksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxFQUFFO3dCQUM5QyxJQUFJLENBQUM7NEJBQ0gsTUFBTSxRQUFRLEdBQ1osT0FBTyxHQUFHLENBQUMsUUFBUSxLQUFLLFFBQVEsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxRQUFRLENBQUM7NEJBQzdFLE9BQU8sUUFBUSxJQUFJLFFBQVEsQ0FBQyxTQUFTLEtBQUssUUFBUSxDQUFDO3dCQUNyRCxDQUFDO3dCQUFDLE1BQU0sQ0FBQzs0QkFDUCxPQUFPLEtBQUssQ0FBQzt3QkFDZixDQUFDO29CQUNILENBQUMsQ0FBQyxDQUFDO2dCQUNMLENBQUM7Z0JBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQztvQkFDWCxPQUFPLENBQUMsR0FBRyxDQUFDLHlDQUF5QyxPQUFPLENBQUMsRUFBRSxHQUFHLEVBQUUsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDO29CQUMvRSxPQUFPLENBQUMsTUFBTSxHQUFHLEVBQUUsQ0FBQztnQkFDdEIsQ0FBQztnQkFFRCxxREFBcUQ7Z0JBQ3JELElBQUksQ0FBQztvQkFDSCxNQUFNLGFBQWEsR0FBRyxNQUFNLE1BQU0sQ0FBQyxLQUFLLENBQ3RDOzs7O1dBSUQsRUFDQyxDQUFDLE9BQU8sQ0FBQyxFQUFFLENBQUMsQ0FDYixDQUFDO29CQUNGLG9DQUFvQztvQkFDcEMsT0FBTyxDQUFDLE9BQU8sR0FBRyxhQUFhLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsRUFBRTt3QkFDaEQsSUFBSSxDQUFDOzRCQUNILE1BQU0sUUFBUSxHQUNaLE9BQU8sR0FBRyxDQUFDLFFBQVEsS0FBSyxRQUFRLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsUUFBUSxDQUFDOzRCQUM3RSxPQUFPLFFBQVEsSUFBSSxRQUFRLENBQUMsU0FBUyxLQUFLLFFBQVEsQ0FBQzt3QkFDckQsQ0FBQzt3QkFBQyxNQUFNLENBQUM7NEJBQ1AsT0FBTyxLQUFLLENBQUM7d0JBQ2YsQ0FBQztvQkFDSCxDQUFDLENBQUMsQ0FBQztnQkFDTCxDQUFDO2dCQUFDLE9BQU8sQ0FBQyxFQUFFLENBQUM7b0JBQ1gsT0FBTyxDQUFDLEdBQUcsQ0FBQywwQ0FBMEMsT0FBTyxDQUFDLEVBQUUsR0FBRyxFQUFFLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQztvQkFDaEYsT0FBTyxDQUFDLE9BQU8sR0FBRyxFQUFFLENBQUM7Z0JBQ3ZCLENBQUM7Z0JBRUQsV0FBVztnQkFDWCxJQUFJLENBQUM7b0JBQ0gsTUFBTSxVQUFVLEdBQUcsTUFBTSxNQUFNLENBQUMsS0FBSyxDQUNuQzs7Ozs7V0FLRCxFQUNDLENBQUMsT0FBTyxDQUFDLEVBQUUsRUFBRSxRQUFRLENBQUMsQ0FDdkIsQ0FBQztvQkFDRixPQUFPLENBQUMsSUFBSSxHQUFHLFVBQVUsQ0FBQyxJQUFJLElBQUksRUFBRSxDQUFDO2dCQUN2QyxDQUFDO2dCQUFDLE9BQU8sQ0FBQyxFQUFFLENBQUM7b0JBQ1gsT0FBTyxDQUFDLEdBQUcsQ0FBQyx1Q0FBdUMsT0FBTyxDQUFDLEVBQUUsR0FBRyxFQUFFLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQztvQkFDN0UsT0FBTyxDQUFDLElBQUksR0FBRyxFQUFFLENBQUM7Z0JBQ3BCLENBQUM7Z0JBRUQsaUJBQWlCO2dCQUNqQixJQUFJLENBQUM7b0JBQ0gsTUFBTSxnQkFBZ0IsR0FBRyxNQUFNLE1BQU0sQ0FBQyxLQUFLLENBQ3pDOzs7OztXQUtELEVBQ0MsQ0FBQyxPQUFPLENBQUMsRUFBRSxFQUFFLFFBQVEsQ0FBQyxDQUN2QixDQUFDO29CQUNGLE9BQU8sQ0FBQyxVQUFVLEdBQUcsZ0JBQWdCLENBQUMsSUFBSSxJQUFJLEVBQUUsQ0FBQztnQkFDbkQsQ0FBQztnQkFBQyxPQUFPLENBQUMsRUFBRSxDQUFDO29CQUNYLE9BQU8sQ0FBQyxHQUFHLENBQUMsNkNBQTZDLE9BQU8sQ0FBQyxFQUFFLEdBQUcsRUFBRSxDQUFDLENBQUMsT0FBTyxDQUFDLENBQUM7b0JBQ25GLE9BQU8sQ0FBQyxVQUFVLEdBQUcsRUFBRSxDQUFDO2dCQUMxQixDQUFDO1lBQ0gsQ0FBQztZQUVELE9BQU8sQ0FBQyxHQUFHLENBQUMsK0JBQStCLFFBQVEsQ0FBQyxNQUFNLDZCQUE2QixDQUFDLENBQUM7UUFDM0YsQ0FBQztRQUFDLE9BQU8sT0FBTyxFQUFFLENBQUM7WUFDakIsT0FBTyxDQUFDLEtBQUssQ0FBQyxtQ0FBbUMsRUFBRSxPQUFPLENBQUMsQ0FBQztZQUM1RCxRQUFRLEdBQUcsRUFBRSxDQUFDO1lBQ2QsS0FBSyxHQUFHLENBQUMsQ0FBQztRQUNaLENBQUM7UUFDRCwyRUFBMkU7UUFFM0UsbUNBQW1DO1FBQ25DLE1BQU0sUUFBUSxHQUFHO1lBQ2YsUUFBUTtZQUNSLEtBQUssRUFBRSxRQUFRLENBQUMsTUFBTTtZQUN0QixNQUFNLEVBQUUsUUFBUSxDQUFDLE1BQWdCLENBQUM7WUFDbEMsS0FBSyxFQUFFLFFBQVEsQ0FBQyxLQUFlLENBQUM7WUFDaEMsZ0NBQWdDO1lBQ2hDLE9BQU8sRUFBRTtnQkFDUCxFQUFFLEVBQUUsUUFBUTtnQkFDWixRQUFRLEVBQUUsSUFBSTtnQkFDZCxNQUFNLEVBQUUsd0JBQXdCO2dCQUNoQyxXQUFXLEVBQUUsS0FBSzthQUNuQjtTQUNGLENBQUM7UUFFRixtQ0FBbUM7UUFDbkMsR0FBRyxDQUFDLFNBQVMsQ0FBQyxhQUFhLEVBQUUsUUFBUSxDQUFDLENBQUM7UUFDdkMsR0FBRyxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsRUFBRSxNQUFNLENBQUMsQ0FBQztRQUMzQyxHQUFHLENBQUMsU0FBUyxDQUFDLGtCQUFrQixFQUFFLFFBQVEsQ0FBQyxNQUFNLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQztRQUU5RCxPQUFPLENBQUMsR0FBRyxDQUFDLHdDQUF3QyxFQUFFO1lBQ3BELGNBQWMsRUFBRSxRQUFRLENBQUMsTUFBTTtZQUMvQixXQUFXLEVBQUUsS0FBSztZQUNsQixTQUFTLEVBQUUsUUFBUTtTQUNwQixDQUFDLENBQUM7UUFFSCxHQUFHLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDO0lBQ3JCLENBQUM7SUFBQyxPQUFPLEtBQVUsRUFBRSxDQUFDO1FBQ3BCLE9BQU8sQ0FBQyxLQUFLLENBQUMsK0NBQStDLEVBQUUsS0FBSyxDQUFDLENBQUM7UUFFdEUsTUFBTSxRQUFRLEdBQUksR0FBRyxDQUFDLE9BQU8sQ0FBQyxhQUFhLENBQVksSUFBSSxTQUFTLENBQUM7UUFFckUsR0FBRyxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsQ0FBQyxJQUFJLENBQUM7WUFDbkIsS0FBSyxFQUFFLDBCQUEwQjtZQUNqQyxPQUFPLEVBQUUsS0FBSyxDQUFDLE9BQU87WUFDdEIsU0FBUyxFQUFFLFFBQVE7WUFDbkIsS0FBSyxFQUFFLEtBQUssQ0FBQyxLQUFLO1lBQ2xCLE1BQU0sRUFBRTtnQkFDTixVQUFVLEVBQUUscUJBQXFCO2dCQUNqQyxTQUFTLEVBQUUsSUFBSSxJQUFJLEVBQUUsQ0FBQyxXQUFXLEVBQUU7YUFDcEM7U0FDRixDQUFDLENBQUM7SUFDTCxDQUFDO0FBQ0gsQ0FBQztBQUVNLEtBQUssVUFBVSxJQUFJLENBQUMsR0FBa0IsRUFBRSxHQUFtQjtJQUNoRSxPQUFPLENBQUMsR0FBRyxDQUFDLHdFQUF3RSxDQUFDLENBQUM7SUFDdEYsT0FBTyxDQUFDLEdBQUcsQ0FBQywwQkFBMEIsRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDLEdBQUcsQ0FBQyxJQUFJLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7SUFFM0UsSUFBSSxDQUFDO1FBQ0gsZ0NBQWdDO1FBQ2hDLE1BQU0sUUFBUSxHQUFJLEdBQUcsQ0FBQyxPQUFPLENBQUMsYUFBYSxDQUFZLElBQUksU0FBUyxDQUFDO1FBRXJFLE9BQU8sQ0FBQyxHQUFHLENBQUMsaUVBQWlFLFFBQVEsRUFBRSxDQUFDLENBQUM7UUFFekYscUNBQXFDO1FBQ3JDLE1BQU0sV0FBVyxHQUFHLEdBQUcsQ0FBQyxJQUFXLENBQUM7UUFFcEMsa0VBQWtFO1FBQ2xFLE1BQU0saUJBQWlCLEdBQUc7WUFDeEIsR0FBRyxXQUFXO1lBQ2QsU0FBUyxFQUFFLFFBQVE7WUFDbkIsUUFBUSxFQUFFO2dCQUNSLEdBQUcsV0FBVyxDQUFDLFFBQVE7Z0JBQ3ZCLFNBQVMsRUFBRSxRQUFRO2FBQ3BCO1NBQ0YsQ0FBQztRQUVGLE9BQU8sQ0FBQyxHQUFHLENBQUMsMkNBQTJDLFFBQVEsb0JBQW9CLENBQUMsQ0FBQztRQUVyRixzQ0FBc0M7UUFDdEMsTUFBTSxFQUFFLE1BQU0sRUFBRSxHQUFHLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUNqQyxNQUFNLE1BQU0sR0FBRyxJQUFJLE1BQU0sQ0FBQztZQUN4QixnQkFBZ0IsRUFDZCxPQUFPLENBQUMsR0FBRyxDQUFDLFlBQVk7Z0JBQ3hCLE9BQU8sQ0FBQyxHQUFHLENBQUMsWUFBWTtnQkFDeEIsbUVBQW1FO1NBQ3RFLENBQUMsQ0FBQztRQUVILElBQUksY0FBYyxHQUFRLElBQUksQ0FBQztRQUMvQixJQUFJLGVBQWUsR0FBVSxFQUFFLENBQUM7UUFDaEMsSUFBSSxhQUFhLEdBQVUsRUFBRSxDQUFDO1FBQzlCLElBQUksY0FBYyxHQUFVLEVBQUUsQ0FBQztRQUUvQixJQUFJLENBQUM7WUFDSCxNQUFNLE1BQU0sQ0FBQyxPQUFPLEVBQUUsQ0FBQztZQUN2QixPQUFPLENBQUMsR0FBRyxDQUFDLG1EQUFtRCxDQUFDLENBQUM7WUFFakUseUNBQXlDO1lBQ3pDLE1BQU0sTUFBTSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsQ0FBQztZQUU1QiwrQkFBK0I7WUFDL0IsTUFBTSxTQUFTLEdBQUcsUUFBUSxJQUFJLENBQUMsR0FBRyxFQUFFLElBQUksSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQyxTQUFTLENBQUMsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxFQUFFLENBQUM7WUFFdEYseUNBQXlDO1lBQ3pDLE1BQU0sa0JBQWtCLEdBQUc7Ozs7Ozs7T0FPMUIsQ0FBQztZQUVGLE1BQU0sYUFBYSxHQUFHO2dCQUNwQixTQUFTO2dCQUNULGlCQUFpQixDQUFDLEtBQUs7Z0JBQ3ZCLGlCQUFpQixDQUFDLE1BQU0sSUFBSSxpQkFBaUIsQ0FBQyxLQUFLLEVBQUUsV0FBVyxFQUFFLENBQUMsT0FBTyxDQUFDLE1BQU0sRUFBRSxHQUFHLENBQUM7Z0JBQ3ZGLGlCQUFpQixDQUFDLFdBQVcsSUFBSSxFQUFFO2dCQUNuQyxpQkFBaUIsQ0FBQyxNQUFNLElBQUksT0FBTztnQkFDbkMsUUFBUTtnQkFDUixJQUFJLENBQUMsU0FBUyxDQUFDLGlCQUFpQixDQUFDLFFBQVEsSUFBSSxFQUFFLENBQUM7Z0JBQ2hELGlCQUFpQixDQUFDLFNBQVMsSUFBSSxJQUFJO2dCQUNuQyxpQkFBaUIsQ0FBQyxhQUFhLElBQUksSUFBSTthQUN4QyxDQUFDO1lBRUYsTUFBTSxhQUFhLEdBQUcsTUFBTSxNQUFNLENBQUMsS0FBSyxDQUFDLGtCQUFrQixFQUFFLGFBQWEsQ0FBQyxDQUFDO1lBQzVFLGNBQWMsR0FBRyxhQUFhLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ3ZDLE9BQU8sQ0FBQyxHQUFHLENBQUMsMkNBQTJDLGNBQWMsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDO1lBRTVFLDRCQUE0QjtZQUM1QixJQUFJLGlCQUFpQixDQUFDLE9BQU8sSUFBSSxpQkFBaUIsQ0FBQyxPQUFPLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRSxDQUFDO2dCQUN0RSxLQUFLLE1BQU0sTUFBTSxJQUFJLGlCQUFpQixDQUFDLE9BQU8sRUFBRSxDQUFDO29CQUMvQyxNQUFNLFFBQVEsR0FBRyxPQUFPLElBQUksQ0FBQyxHQUFHLEVBQUUsSUFBSSxJQUFJLENBQUMsTUFBTSxFQUFFLENBQUMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQztvQkFFbkYsTUFBTSxpQkFBaUIsR0FBRzs7Ozs7V0FLekIsQ0FBQztvQkFFRixNQUFNLFlBQVksR0FBRzt3QkFDbkIsUUFBUTt3QkFDUixNQUFNLENBQUMsS0FBSzt3QkFDWixTQUFTO3dCQUNULElBQUksQ0FBQyxTQUFTLENBQUMsRUFBRSxNQUFNLEVBQUUsTUFBTSxDQUFDLE1BQU0sSUFBSSxFQUFFLEVBQUUsU0FBUyxFQUFFLFFBQVEsRUFBRSxDQUFDO3FCQUNyRSxDQUFDO29CQUVGLE1BQU0sWUFBWSxHQUFHLE1BQU0sTUFBTSxDQUFDLEtBQUssQ0FBQyxpQkFBaUIsRUFBRSxZQUFZLENBQUMsQ0FBQztvQkFDekUsY0FBYyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7b0JBRTFDLHVCQUF1QjtvQkFDdkIsSUFBSSxNQUFNLENBQUMsTUFBTSxJQUFJLE1BQU0sQ0FBQyxNQUFNLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRSxDQUFDO3dCQUM5QyxLQUFLLE1BQU0sS0FBSyxJQUFJLE1BQU0sQ0FBQyxNQUFNLEVBQUUsQ0FBQzs0QkFDbEMsTUFBTSxPQUFPLEdBQUcsVUFBVSxJQUFJLENBQUMsR0FBRyxFQUFFLElBQUksSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQyxTQUFTLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUM7NEJBRXJGLE1BQU0sZ0JBQWdCLEdBQUc7Ozs7ZUFJeEIsQ0FBQzs0QkFFRixNQUFNLE1BQU0sQ0FBQyxLQUFLLENBQUMsZ0JBQWdCLEVBQUU7Z0NBQ25DLE9BQU87Z0NBQ1AsS0FBSztnQ0FDTCxRQUFRO2dDQUNSLElBQUksQ0FBQyxTQUFTLENBQUMsRUFBRSxTQUFTLEVBQUUsUUFBUSxFQUFFLENBQUM7NkJBQ3hDLENBQUMsQ0FBQzt3QkFDTCxDQUFDO29CQUNILENBQUM7Z0JBQ0gsQ0FBQztnQkFDRCxPQUFPLENBQUMsR0FBRyxDQUFDLDZCQUE2QixjQUFjLENBQUMsTUFBTSxrQkFBa0IsQ0FBQyxDQUFDO1lBQ3BGLENBQUM7WUFFRCw2QkFBNkI7WUFDN0IsSUFBSSxpQkFBaUIsQ0FBQyxRQUFRLElBQUksaUJBQWlCLENBQUMsUUFBUSxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUUsQ0FBQztnQkFDeEUsS0FBSyxNQUFNLE9BQU8sSUFBSSxpQkFBaUIsQ0FBQyxRQUFRLEVBQUUsQ0FBQztvQkFDakQsTUFBTSxTQUFTLEdBQUcsV0FBVyxJQUFJLENBQUMsR0FBRyxFQUFFLElBQUksSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQyxTQUFTLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUM7b0JBRXhGLE1BQU0sa0JBQWtCLEdBQUc7Ozs7Ozs7V0FPMUIsQ0FBQztvQkFFRixNQUFNLGVBQWUsR0FBRzt3QkFDdEIsR0FBRyxPQUFPLENBQUMsUUFBUTt3QkFDbkIsU0FBUyxFQUFFLFFBQVE7d0JBQ25CLFVBQVUsRUFBRSxPQUFPLENBQUMsUUFBUSxFQUFFLFVBQVU7d0JBQ3hDLGNBQWMsRUFBRSxPQUFPLENBQUMsUUFBUSxFQUFFLGNBQWM7d0JBQ2hELGdCQUFnQixFQUFFLE9BQU8sQ0FBQyxRQUFRLEVBQUUsZ0JBQWdCO3dCQUNwRCx3QkFBd0IsRUFBRSxPQUFPLENBQUMsUUFBUSxFQUFFLHdCQUF3QjtxQkFDckUsQ0FBQztvQkFFRixNQUFNLGFBQWEsR0FBRzt3QkFDcEIsU0FBUzt3QkFDVCxPQUFPLENBQUMsS0FBSyxJQUFJLFNBQVM7d0JBQzFCLE9BQU8sQ0FBQyxHQUFHLElBQUksT0FBTyxTQUFTLEVBQUU7d0JBQ2pDLFNBQVM7d0JBQ1QsUUFBUTt3QkFDUixJQUFJLENBQUMsU0FBUyxDQUFDLGVBQWUsQ0FBQzt3QkFDL0IsT0FBTyxDQUFDLE1BQU0sSUFBSSxJQUFJO3dCQUN0QixPQUFPLENBQUMsS0FBSyxJQUFJLElBQUk7d0JBQ3JCLE9BQU8sQ0FBQyxNQUFNLElBQUksSUFBSTt3QkFDdEIsT0FBTyxDQUFDLE1BQU0sSUFBSSxJQUFJO3dCQUN0QixLQUFLLEVBQUUsOENBQThDO3FCQUN0RCxDQUFDO29CQUVGLE1BQU0sYUFBYSxHQUFHLE1BQU0sTUFBTSxDQUFDLEtBQUssQ0FBQyxrQkFBa0IsRUFBRSxhQUFhLENBQUMsQ0FBQztvQkFDNUUsZUFBZSxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7b0JBRTVDLDZEQUE2RDtvQkFDN0QsSUFBSSxPQUFPLENBQUMsTUFBTSxJQUFJLE9BQU8sQ0FBQyxNQUFNLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRSxDQUFDO3dCQUNoRCw0Q0FBNEM7d0JBQzVDLE1BQU0sVUFBVSxHQUFHLFFBQVEsSUFBSSxDQUFDLEdBQUcsRUFBRSxJQUFJLElBQUksQ0FBQyxNQUFNLEVBQUUsQ0FBQyxRQUFRLENBQUMsRUFBRSxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDO3dCQUV0RixNQUFNLG1CQUFtQixHQUFHOzs7O2FBSTNCLENBQUM7d0JBQ0YsTUFBTSxNQUFNLENBQUMsS0FBSyxDQUFDLG1CQUFtQixFQUFFLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQzt3QkFFdEQsNEJBQTRCO3dCQUM1QixNQUFNLHdCQUF3QixHQUFHOzs7O2FBSWhDLENBQUM7d0JBQ0YsTUFBTSxNQUFNLEdBQUcsUUFBUSxJQUFJLENBQUMsR0FBRyxFQUFFLElBQUksSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQyxTQUFTLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUM7d0JBQ2xGLE1BQU0sTUFBTSxDQUFDLEtBQUssQ0FBQyx3QkFBd0IsRUFBRSxDQUFDLE1BQU0sRUFBRSxTQUFTLEVBQUUsVUFBVSxDQUFDLENBQUMsQ0FBQzt3QkFFOUUsOEJBQThCO3dCQUM5QixLQUFLLE1BQU0sS0FBSyxJQUFJLE9BQU8sQ0FBQyxNQUFNLEVBQUUsQ0FBQzs0QkFDbkMsTUFBTSxPQUFPLEdBQUcsU0FBUyxJQUFJLENBQUMsR0FBRyxFQUFFLElBQUksSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQyxTQUFTLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUM7NEJBRXBGLE1BQU0sZ0JBQWdCLEdBQUc7Ozs7ZUFJeEIsQ0FBQzs0QkFFRixNQUFNLE1BQU0sQ0FBQyxLQUFLLENBQUMsZ0JBQWdCLEVBQUU7Z0NBQ25DLE9BQU87Z0NBQ1AsS0FBSyxDQUFDLGFBQWEsSUFBSSxLQUFLO2dDQUM1QixLQUFLLENBQUMsTUFBTTtnQ0FDWixJQUFJLENBQUMsU0FBUyxDQUFDLEVBQUUsS0FBSyxFQUFFLEtBQUssQ0FBQyxNQUFNLENBQUMsUUFBUSxFQUFFLEVBQUUsU0FBUyxFQUFFLEVBQUUsRUFBRSxDQUFDO2dDQUNqRSxVQUFVO2dDQUNWLFFBQVE7NkJBQ1QsQ0FBQyxDQUFDO3dCQUNMLENBQUM7b0JBQ0gsQ0FBQztnQkFDSCxDQUFDO2dCQUNELE9BQU8sQ0FBQyxHQUFHLENBQUMsNkJBQTZCLGVBQWUsQ0FBQyxNQUFNLG1CQUFtQixDQUFDLENBQUM7WUFDdEYsQ0FBQztZQUVELDJCQUEyQjtZQUMzQixJQUFJLGlCQUFpQixDQUFDLE1BQU0sSUFBSSxpQkFBaUIsQ0FBQyxNQUFNLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRSxDQUFDO2dCQUNwRSxLQUFLLE1BQU0sS0FBSyxJQUFJLGlCQUFpQixDQUFDLE1BQU0sRUFBRSxDQUFDO29CQUM3QyxNQUFNLE9BQU8sR0FBRyxPQUFPLElBQUksQ0FBQyxHQUFHLEVBQUUsSUFBSSxJQUFJLENBQUMsTUFBTSxFQUFFLENBQUMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQztvQkFFbEYsTUFBTSxnQkFBZ0IsR0FBRzs7Ozs7V0FLeEIsQ0FBQztvQkFFRixNQUFNLFdBQVcsR0FBRzt3QkFDbEIsT0FBTzt3QkFDUCxLQUFLLENBQUMsR0FBRzt3QkFDVCxTQUFTO3dCQUNULElBQUksQ0FBQyxTQUFTLENBQUMsRUFBRSxTQUFTLEVBQUUsUUFBUSxFQUFFLENBQUM7d0JBQ3ZDLENBQUMsRUFBRSxtQkFBbUI7cUJBQ3ZCLENBQUM7b0JBRUYsTUFBTSxXQUFXLEdBQUcsTUFBTSxNQUFNLENBQUMsS0FBSyxDQUFDLGdCQUFnQixFQUFFLFdBQVcsQ0FBQyxDQUFDO29CQUN0RSxhQUFhLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztnQkFDMUMsQ0FBQztnQkFDRCxPQUFPLENBQUMsR0FBRyxDQUFDLDZCQUE2QixhQUFhLENBQUMsTUFBTSxpQkFBaUIsQ0FBQyxDQUFDO1lBQ2xGLENBQUM7WUFFRCw2Q0FBNkM7WUFDN0MsSUFBSSxpQkFBaUIsQ0FBQyxVQUFVLElBQUksaUJBQWlCLENBQUMsVUFBVSxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUUsQ0FBQztnQkFDNUUsS0FBSyxNQUFNLFFBQVEsSUFBSSxpQkFBaUIsQ0FBQyxVQUFVLEVBQUUsQ0FBQztvQkFDcEQsc0RBQXNEO29CQUN0RCxNQUFNLFVBQVUsR0FBRyxPQUFPLFFBQVEsS0FBSyxRQUFRLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDLEVBQUUsQ0FBQztvQkFFekUsSUFBSSxDQUFDLFVBQVUsRUFBRSxDQUFDO3dCQUNoQixPQUFPLENBQUMsSUFBSSxDQUFDLCtDQUErQyxFQUFFLFFBQVEsQ0FBQyxDQUFDO3dCQUN4RSxTQUFTO29CQUNYLENBQUM7b0JBRUQsTUFBTSx1QkFBdUIsR0FBRzs7OztXQUkvQixDQUFDO29CQUVGLE1BQU0sTUFBTSxDQUFDLEtBQUssQ0FBQyx1QkFBdUIsRUFBRSxDQUFDLFNBQVMsRUFBRSxVQUFVLEVBQUUsUUFBUSxDQUFDLENBQUMsQ0FBQztnQkFDakYsQ0FBQztnQkFDRCxPQUFPLENBQUMsR0FBRyxDQUNULHVDQUF1QyxpQkFBaUIsQ0FBQyxVQUFVLENBQUMsTUFBTSxhQUFhLENBQ3hGLENBQUM7WUFDSixDQUFDO1lBRUQsdUNBQXVDO1lBQ3ZDLElBQUksaUJBQWlCLENBQUMsSUFBSSxJQUFJLGlCQUFpQixDQUFDLElBQUksQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUM7Z0JBQ2hFLEtBQUssTUFBTSxHQUFHLElBQUksaUJBQWlCLENBQUMsSUFBSSxFQUFFLENBQUM7b0JBQ3pDLHVDQUF1QztvQkFDdkMsTUFBTSxLQUFLLEdBQUcsT0FBTyxJQUFJLENBQUMsR0FBRyxFQUFFLElBQUksSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQyxTQUFTLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUM7b0JBRWhGLE1BQU0sY0FBYyxHQUFHOzs7OztXQUt0QixDQUFDO29CQUVGLE1BQU0sTUFBTSxDQUFDLEtBQUssQ0FBQyxjQUFjLEVBQUUsQ0FBQyxLQUFLLEVBQUUsR0FBRyxFQUFFLFFBQVEsQ0FBQyxDQUFDLENBQUM7b0JBRTNELHNCQUFzQjtvQkFDdEIsTUFBTSxrQkFBa0IsR0FBRzs7OztXQUkxQixDQUFDO29CQUVGLE1BQU0sTUFBTSxDQUFDLEtBQUssQ0FBQyxrQkFBa0IsRUFBRSxDQUFDLFNBQVMsRUFBRSxHQUFHLEVBQUUsUUFBUSxDQUFDLENBQUMsQ0FBQztnQkFDckUsQ0FBQztnQkFDRCxPQUFPLENBQUMsR0FBRyxDQUFDLHVDQUF1QyxpQkFBaUIsQ0FBQyxJQUFJLENBQUMsTUFBTSxPQUFPLENBQUMsQ0FBQztZQUMzRixDQUFDO1lBRUQscUJBQXFCO1lBQ3JCLE1BQU0sTUFBTSxDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUMsQ0FBQztZQUM3QixPQUFPLENBQUMsR0FBRyxDQUFDLHNEQUFzRCxDQUFDLENBQUM7WUFFcEUsTUFBTSxNQUFNLENBQUMsR0FBRyxFQUFFLENBQUM7UUFDckIsQ0FBQztRQUFDLE9BQU8sT0FBTyxFQUFFLENBQUM7WUFDakIsT0FBTyxDQUFDLEtBQUssQ0FBQyxtQ0FBbUMsRUFBRSxPQUFPLENBQUMsQ0FBQztZQUM1RCxNQUFNLE1BQU0sQ0FBQyxLQUFLLENBQUMsVUFBVSxDQUFDLENBQUMsS0FBSyxDQUFDLEdBQUcsRUFBRSxHQUFFLENBQUMsQ0FBQyxDQUFDO1lBQy9DLE1BQU0sTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLEtBQUssQ0FBQyxHQUFHLEVBQUUsR0FBRSxDQUFDLENBQUMsQ0FBQztZQUNuQyxNQUFNLE9BQU8sQ0FBQztRQUNoQixDQUFDO1FBRUQsa0RBQWtEO1FBQ2xELE9BQU8sQ0FBQyxHQUFHLENBQUMsOERBQThELGNBQWMsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDO1FBQy9GLElBQUksaUJBQWlCLEdBQUc7WUFDdEIsVUFBVSxFQUFFLENBQUM7WUFDYixNQUFNLEVBQUUsQ0FBQztZQUNULE1BQU0sRUFBRSxFQUFjO1NBQ3ZCLENBQUM7UUFFRixJQUFJLENBQUM7WUFDSCxNQUFNLGNBQWMsR0FBRyxNQUFNLElBQUEsMERBQW9DLEVBQUMsR0FBRyxDQUFDLEtBQUssQ0FBQyxDQUFDLEdBQUcsQ0FBQztnQkFDL0UsS0FBSyxFQUFFO29CQUNMLFFBQVEsRUFBRSxFQUFFLEVBQUUseUNBQXlDO29CQUN2RCxRQUFRLEVBQUUsUUFBUTtpQkFDbkI7YUFDRixDQUFDLENBQUM7WUFFSCwwQ0FBMEM7WUFDMUMsTUFBTSx3QkFBd0IsR0FBRyxHQUFHLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQywwQkFBMEIsQ0FBQyxDQUFDO1lBQy9FLE1BQU0sd0JBQXdCLENBQUMsc0JBQXNCLENBQUMsY0FBYyxDQUFDLEVBQUUsRUFBRSxRQUFRLENBQUMsQ0FBQztZQUVuRixpQkFBaUIsQ0FBQyxVQUFVLEdBQUcsQ0FBQyxDQUFDO1lBQ2pDLE9BQU8sQ0FBQyxHQUFHLENBQUMsMkRBQTJELENBQUMsQ0FBQztRQUMzRSxDQUFDO1FBQUMsT0FBTyxlQUFlLEVBQUUsQ0FBQztZQUN6QixPQUFPLENBQUMsS0FBSyxDQUFDLDhDQUE4QyxFQUFFLGVBQWUsQ0FBQyxDQUFDO1lBQy9FLGlCQUFpQixDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUM7WUFDN0IsaUJBQWlCLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyw4QkFBOEIsZUFBZSxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUM7UUFDekYsQ0FBQztRQUVELCtCQUErQjtRQUMvQixNQUFNLFFBQVEsR0FBRztZQUNmLE9BQU8sRUFBRTtnQkFDUCxHQUFHLGNBQWM7Z0JBQ2pCLFFBQVEsRUFBRSxlQUFlO2dCQUN6QixNQUFNLEVBQUUsYUFBYTtnQkFDckIsT0FBTyxFQUFFLGNBQWM7Z0JBQ3ZCLFVBQVUsRUFBRSxpQkFBaUIsQ0FBQyxVQUFVLElBQUksRUFBRTtnQkFDOUMsSUFBSSxFQUFFLGlCQUFpQixDQUFDLElBQUksSUFBSSxFQUFFO2FBQ25DO1lBQ0QsT0FBTyxFQUFFO2dCQUNQLEVBQUUsRUFBRSxRQUFRO2dCQUNaLFFBQVEsRUFBRSxJQUFJO2dCQUNkLE1BQU0sRUFBRSwyQkFBMkI7Z0JBQ25DLGdCQUFnQixFQUFFO29CQUNoQixPQUFPLEVBQUUsQ0FBQztvQkFDVixRQUFRLEVBQUUsZUFBZSxDQUFDLE1BQU07b0JBQ2hDLE1BQU0sRUFBRSxhQUFhLENBQUMsTUFBTTtvQkFDNUIsT0FBTyxFQUFFLGNBQWMsQ0FBQyxNQUFNO29CQUM5QixVQUFVLEVBQUUsaUJBQWlCLENBQUMsVUFBVSxFQUFFLE1BQU0sSUFBSSxDQUFDO29CQUNyRCxJQUFJLEVBQUUsaUJBQWlCLENBQUMsSUFBSSxFQUFFLE1BQU0sSUFBSSxDQUFDO2lCQUMxQzthQUNGO1lBQ0QsaUJBQWlCLEVBQUU7Z0JBQ2pCLG9CQUFvQixFQUFFLGlCQUFpQixDQUFDLFVBQVUsR0FBRyxDQUFDO2dCQUN0RCwyQkFBMkIsRUFBRSxpQkFBaUIsQ0FBQyxVQUFVLEdBQUcsQ0FBQztnQkFDN0QsdUJBQXVCLEVBQUUsaUJBQWlCLENBQUMsVUFBVSxHQUFHLENBQUM7Z0JBQ3pELGNBQWMsRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLHdCQUF3QjtnQkFDcEQsaUJBQWlCLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQywyQkFBMkI7Z0JBQzFELE9BQU8sRUFBRSxpQkFBaUI7YUFDM0I7U0FDRixDQUFDO1FBRUYscUJBQXFCO1FBQ3JCLEdBQUcsQ0FBQyxTQUFTLENBQUMsYUFBYSxFQUFFLFFBQVEsQ0FBQyxDQUFDO1FBQ3ZDLEdBQUcsQ0FBQyxTQUFTLENBQUMsbUJBQW1CLEVBQUUsTUFBTSxDQUFDLENBQUM7UUFDM0MsR0FBRyxDQUFDLFNBQVMsQ0FBQyxvQkFBb0IsRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsZ0JBQWdCLENBQUMsQ0FBQyxDQUFDO1FBRXZGLE9BQU8sQ0FBQyxHQUFHLENBQUMsaUVBQWlFLFFBQVEsRUFBRSxDQUFDLENBQUM7UUFDekYsT0FBTyxDQUFDLEdBQUcsQ0FBQyxzQ0FBc0MsRUFBRSxRQUFRLENBQUMsT0FBTyxDQUFDLGdCQUFnQixDQUFDLENBQUM7UUFFdkYsR0FBRyxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUM7SUFDakMsQ0FBQztJQUFDLE9BQU8sS0FBVSxFQUFFLENBQUM7UUFDcEIsT0FBTyxDQUFDLEtBQUssQ0FBQyx5REFBeUQsRUFBRSxLQUFLLENBQUMsQ0FBQztRQUVoRixNQUFNLFFBQVEsR0FBSSxHQUFHLENBQUMsT0FBTyxDQUFDLGFBQWEsQ0FBWSxJQUFJLFNBQVMsQ0FBQztRQUVyRSxHQUFHLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUFDLElBQUksQ0FBQztZQUNuQixLQUFLLEVBQUUsd0NBQXdDO1lBQy9DLE9BQU8sRUFBRSxLQUFLLENBQUMsT0FBTztZQUN0QixTQUFTLEVBQUUsUUFBUTtZQUNuQixNQUFNLEVBQUU7Z0JBQ04sVUFBVSxFQUFFLG1DQUFtQztnQkFDL0MsU0FBUyxFQUFFLElBQUksSUFBSSxFQUFFLENBQUMsV0FBVyxFQUFFO2dCQUNuQyxLQUFLLEVBQUUsS0FBSyxDQUFDLEtBQUs7YUFDbkI7U0FDRixDQUFDLENBQUM7SUFDTCxDQUFDO0FBQ0gsQ0FBQztBQUVELDhFQUE4RSJ9