"use strict";
/**
 * Tenant API Testing Endpoint
 *
 * Comprehensive testing endpoint for tenant-aware services and data isolation.
 * Tests all CRUD operations with tenant context.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.POST = exports.GET = void 0;
const tenant_service_factory_1 = require("../../../services/tenant-service-factory");
const GET = async (req, res) => {
    try {
        console.log(`🧪 [TENANT-TEST] Starting comprehensive tenant API test`);
        console.log(`🧪 [TENANT-TEST] Request headers:`, req.headers);
        console.log(`🧪 [TENANT-TEST] Tenant ID from middleware:`, req.tenant_id);
        const startTime = Date.now();
        const results = {
            timestamp: new Date().toISOString(),
            tenantId: req.tenant_id || 'default',
            tests: {},
            summary: {
                totalTests: 0,
                passedTests: 0,
                failedTests: 0,
                errors: []
            }
        };
        // Test 1: Tenant Service Factory Creation
        console.log(`🧪 [TENANT-TEST] Test 1: Service Factory Creation`);
        try {
            const services = tenant_service_factory_1.TenantServiceFactory.fromRequest(req);
            results.tests.serviceFactory = {
                success: true,
                message: 'Service factory created successfully',
                tenantId: services.getTenantId()
            };
            results.summary.passedTests++;
        }
        catch (error) {
            results.tests.serviceFactory = {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
            results.summary.failedTests++;
            results.summary.errors.push(`Service Factory: ${error}`);
        }
        results.summary.totalTests++;
        // Test 2: Product Service Operations
        console.log(`🧪 [TENANT-TEST] Test 2: Product Service Operations`);
        try {
            const services = tenant_service_factory_1.TenantServiceFactory.fromRequest(req);
            // Test product listing
            const products = await services.product.listProducts({}, { take: 5 });
            // Test product statistics
            const stats = await services.product.getProductStats();
            results.tests.productService = {
                success: true,
                message: 'Product service operations completed',
                data: {
                    productsFound: Array.isArray(products) ? products.length : 0,
                    statistics: stats
                }
            };
            results.summary.passedTests++;
        }
        catch (error) {
            results.tests.productService = {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
            results.summary.failedTests++;
            results.summary.errors.push(`Product Service: ${error}`);
        }
        results.summary.totalTests++;
        // Test 3: Customer Service Operations
        console.log(`🧪 [TENANT-TEST] Test 3: Customer Service Operations`);
        try {
            const services = tenant_service_factory_1.TenantServiceFactory.fromRequest(req);
            // Test customer listing
            const customers = await services.customer.listCustomers({}, { take: 5 });
            // Test customer statistics
            const stats = await services.customer.getCustomerStats();
            results.tests.customerService = {
                success: true,
                message: 'Customer service operations completed',
                data: {
                    customersFound: Array.isArray(customers) ? customers.length : 0,
                    statistics: stats
                }
            };
            results.summary.passedTests++;
        }
        catch (error) {
            results.tests.customerService = {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
            results.summary.failedTests++;
            results.summary.errors.push(`Customer Service: ${error}`);
        }
        results.summary.totalTests++;
        // Test 4: Order Service Operations
        console.log(`🧪 [TENANT-TEST] Test 4: Order Service Operations`);
        try {
            const services = tenant_service_factory_1.TenantServiceFactory.fromRequest(req);
            // Test order listing
            const orders = await services.order.listOrders({}, { take: 5 });
            // Test order statistics
            const stats = await services.order.getOrderStats();
            results.tests.orderService = {
                success: true,
                message: 'Order service operations completed',
                data: {
                    ordersFound: Array.isArray(orders) ? orders.length : 0,
                    statistics: stats
                }
            };
            results.summary.passedTests++;
        }
        catch (error) {
            results.tests.orderService = {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
            results.summary.failedTests++;
            results.summary.errors.push(`Order Service: ${error}`);
        }
        results.summary.totalTests++;
        // Test 5: Tenant Context Validation
        console.log(`🧪 [TENANT-TEST] Test 5: Tenant Context Validation`);
        try {
            const validation = tenant_service_factory_1.TenantServiceFactory.validateTenantContext(req);
            results.tests.tenantValidation = {
                success: validation.isValid,
                message: validation.isValid ? 'Tenant context is valid' : 'Tenant context validation failed',
                data: validation
            };
            if (validation.isValid) {
                results.summary.passedTests++;
            }
            else {
                results.summary.failedTests++;
                results.summary.errors.push(`Tenant Validation: ${validation.errors.join(', ')}`);
            }
        }
        catch (error) {
            results.tests.tenantValidation = {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
            results.summary.failedTests++;
            results.summary.errors.push(`Tenant Validation: ${error}`);
        }
        results.summary.totalTests++;
        // Test 6: Cross-Tenant Data Isolation
        console.log(`🧪 [TENANT-TEST] Test 6: Cross-Tenant Data Isolation`);
        try {
            const currentServices = tenant_service_factory_1.TenantServiceFactory.fromRequest(req);
            // Create a mock request with different tenant
            const mockReq = {
                ...req,
                tenant_id: 'different-tenant-' + Date.now()
            };
            const differentServices = tenant_service_factory_1.TenantServiceFactory.fromRequest(mockReq);
            // Compare tenant IDs
            const currentTenant = currentServices.getTenantId();
            const differentTenant = differentServices.getTenantId();
            const isolationWorking = currentTenant !== differentTenant;
            results.tests.dataIsolation = {
                success: isolationWorking,
                message: isolationWorking ? 'Data isolation working correctly' : 'Data isolation failed',
                data: {
                    currentTenant,
                    differentTenant,
                    isolated: isolationWorking
                }
            };
            if (isolationWorking) {
                results.summary.passedTests++;
            }
            else {
                results.summary.failedTests++;
                results.summary.errors.push('Data Isolation: Tenants not properly isolated');
            }
        }
        catch (error) {
            results.tests.dataIsolation = {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
            results.summary.failedTests++;
            results.summary.errors.push(`Data Isolation: ${error}`);
        }
        results.summary.totalTests++;
        // Calculate final metrics
        const endTime = Date.now();
        results.performance = {
            totalTime: endTime - startTime,
            averageTestTime: (endTime - startTime) / results.summary.totalTests
        };
        results.summary.successRate = `${((results.summary.passedTests / results.summary.totalTests) * 100).toFixed(1)}%`;
        results.summary.overallSuccess = results.summary.failedTests === 0;
        console.log(`🧪 [TENANT-TEST] Test completed:`, results.summary);
        return res.json({
            success: true,
            message: 'Tenant API test completed',
            results
        });
    }
    catch (error) {
        console.error(`❌ [TENANT-TEST] Test failed:`, error);
        return res.status(500).json({
            success: false,
            message: 'Tenant API test failed',
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        });
    }
};
exports.GET = GET;
const POST = async (req, res) => {
    try {
        console.log(`🧪 [TENANT-TEST] Starting tenant data creation test`);
        const { testType, data } = req.body;
        const services = tenant_service_factory_1.TenantServiceFactory.fromRequest(req);
        const results = {
            timestamp: new Date().toISOString(),
            tenantId: req.tenant_id || 'default',
            testType,
            success: false,
            data: null,
            error: null
        };
        switch (testType) {
            case 'create-product':
                console.log(`🧪 [TENANT-TEST] Creating test product for tenant: ${req.tenant_id}`);
                const productData = {
                    title: data?.title || `Test Product ${Date.now()}`,
                    description: data?.description || 'Test product created by tenant API test',
                    handle: data?.handle || `test-product-${Date.now()}`,
                    status: 'draft',
                    ...data
                };
                const createdProduct = await services.product.createProducts([productData]);
                results.success = true;
                results.data = createdProduct;
                results.message = 'Product created successfully';
                break;
            case 'create-customer':
                console.log(`🧪 [TENANT-TEST] Creating test customer for tenant: ${req.tenant_id}`);
                const customerData = {
                    email: data?.email || `test-${Date.now()}@example.com`,
                    first_name: data?.first_name || 'Test',
                    last_name: data?.last_name || 'Customer',
                    ...data
                };
                const createdCustomer = await services.customer.createCustomers([customerData]);
                results.success = true;
                results.data = createdCustomer;
                results.message = 'Customer created successfully';
                break;
            default:
                throw new Error(`Unknown test type: ${testType}`);
        }
        console.log(`✅ [TENANT-TEST] Creation test completed:`, results);
        return res.json(results);
    }
    catch (error) {
        console.error(`❌ [TENANT-TEST] Creation test failed:`, error);
        return res.status(500).json({
            success: false,
            message: 'Tenant creation test failed',
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        });
    }
};
exports.POST = POST;
//# sourceMappingURL=data:application/json;base64,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