"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.preprocessProductData = preprocessProductData;
// Middleware to preprocess request data and fix data types
function preprocessProductData(req, res, next) {
    console.log(`🔧 [MIDDLEWARE] Preprocessing product update data`);
    if (req.method === 'POST' && req.body) {
        try {
            const originalBody = req.body;
            console.log(`🔧 [MIDDLEWARE] Original body:`, JSON.stringify(originalBody, null, 2));
            // Fix variants data types
            if (originalBody.variants && Array.isArray(originalBody.variants)) {
                originalBody.variants = originalBody.variants.map((variant) => {
                    const processedVariant = { ...variant };
                    // Fix prices data types
                    if (processedVariant.prices && Array.isArray(processedVariant.prices)) {
                        processedVariant.prices = processedVariant.prices.map((price) => ({
                            ...price,
                            amount: typeof price.amount === 'string' ? parseFloat(price.amount) : price.amount,
                            min_quantity: typeof price.min_quantity === 'string' ? parseInt(price.min_quantity) : price.min_quantity,
                            max_quantity: typeof price.max_quantity === 'string' ? parseInt(price.max_quantity) : price.max_quantity
                        }));
                    }
                    // Fix numeric fields in variant
                    if (processedVariant.weight && typeof processedVariant.weight === 'string') {
                        processedVariant.weight = parseFloat(processedVariant.weight);
                    }
                    if (processedVariant.width && typeof processedVariant.width === 'string') {
                        processedVariant.width = parseFloat(processedVariant.width);
                    }
                    if (processedVariant.length && typeof processedVariant.length === 'string') {
                        processedVariant.length = parseFloat(processedVariant.length);
                    }
                    if (processedVariant.height && typeof processedVariant.height === 'string') {
                        processedVariant.height = parseFloat(processedVariant.height);
                    }
                    return processedVariant;
                });
            }
            // Fix product-level numeric fields
            if (originalBody.weight && typeof originalBody.weight === 'string') {
                originalBody.weight = parseFloat(originalBody.weight);
            }
            if (originalBody.width && typeof originalBody.width === 'string') {
                originalBody.width = parseFloat(originalBody.width);
            }
            if (originalBody.length && typeof originalBody.length === 'string') {
                originalBody.length = parseFloat(originalBody.length);
            }
            if (originalBody.height && typeof originalBody.height === 'string') {
                originalBody.height = parseFloat(originalBody.height);
            }
            // Update the request body
            req.body = originalBody;
            console.log(`✅ [MIDDLEWARE] Preprocessed body:`, JSON.stringify(req.body, null, 2));
        }
        catch (error) {
            console.error(`❌ [MIDDLEWARE] Error preprocessing data:`, error);
        }
    }
    next();
}
//# sourceMappingURL=data:application/json;base64,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