"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
/**
 * Enhanced Admin Orders Endpoint - Complete order data with all related fields
 *
 * This endpoint provides comprehensive order data including:
 * - Order items with product/variant details
 * - Customer information
 * - Billing and shipping addresses
 * - Payment information
 * - Financial totals and calculations
 * - Fulfillment status
 */
async function GET(req, res) {
    console.log(`🚀 [TENANT FILTER] === ENHANCED ORDERS ENDPOINT CALLED ===`);
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        console.log(`🔍 [TENANT FILTER] Processing orders request for tenant: ${tenantId}`);
        // Get query parameters
        const { limit = 50, offset = 0, fields, expand, order = '-created_at', status, customer_id, email, ...filters } = req.query;
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: process.env.DATABASE_URL ||
                'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
        });
        let orders = [];
        let count = 0;
        try {
            await client.connect();
            console.log(`🔗 [TENANT FILTER] Connected to database directly`);
            // Build WHERE conditions
            let whereConditions = ['o.tenant_id = $1', 'o.deleted_at IS NULL'];
            let queryParams = [tenantId];
            let paramIndex = 2;
            // Add filters
            if (status) {
                whereConditions.push(`o.status = $${paramIndex}`);
                queryParams.push(status);
                paramIndex++;
            }
            if (customer_id) {
                whereConditions.push(`o.customer_id = $${paramIndex}`);
                queryParams.push(customer_id);
                paramIndex++;
            }
            if (email) {
                whereConditions.push(`o.email ILIKE $${paramIndex}`);
                queryParams.push(`%${email}%`);
                paramIndex++;
            }
            const whereClause = whereConditions.join(' AND ');
            // Get total count for this tenant with filters
            const countResult = await client.query(`SELECT COUNT(*) as total FROM "order" o WHERE ${whereClause}`, queryParams);
            count = parseInt(countResult.rows[0]?.total || 0);
            console.log(`📊 [TENANT FILTER] Total orders for tenant ${tenantId}: ${count}`);
            // Get comprehensive orders with all related data
            const result = await client.query(`
        SELECT
          -- Basic order information
          o.id, o.status, o.currency_code, o.email, o.display_id,
          o.created_at, o.updated_at, o.tenant_id, o.metadata,
          o.customer_id, o.region_id, o.sales_channel_id,
          o.shipping_address_id, o.billing_address_id,
          o.is_draft_order, o.no_notification,

          -- Customer information
          c.id as customer_id, c.email as customer_email,
          c.first_name as customer_first_name, c.last_name as customer_last_name,
          c.phone as customer_phone, c.has_account as customer_has_account,
          c.created_at as customer_created_at,

          -- Shipping address
          sa.id as shipping_address_id, sa.first_name as shipping_first_name,
          sa.last_name as shipping_last_name, sa.address_1 as shipping_address_1,
          sa.address_2 as shipping_address_2, sa.city as shipping_city,
          sa.postal_code as shipping_postal_code, sa.province as shipping_province,
          sa.country_code as shipping_country_code, sa.phone as shipping_phone,
          sa.company as shipping_company,

          -- Billing address
          ba.id as billing_address_id, ba.first_name as billing_first_name,
          ba.last_name as billing_last_name, ba.address_1 as billing_address_1,
          ba.address_2 as billing_address_2, ba.city as billing_city,
          ba.postal_code as billing_postal_code, ba.province as billing_province,
          ba.country_code as billing_country_code, ba.phone as billing_phone,
          ba.company as billing_company,

          -- Region information
          r.id as region_id, r.name as region_name, r.currency_code as region_currency,

          -- Sales channel information
          sc.id as sales_channel_id, sc.name as sales_channel_name,
          sc.description as sales_channel_description

        FROM "order" o
        LEFT JOIN customer c ON o.customer_id = c.id AND c.deleted_at IS NULL
        LEFT JOIN order_address sa ON o.shipping_address_id = sa.id AND sa.deleted_at IS NULL
        LEFT JOIN order_address ba ON o.billing_address_id = ba.id AND ba.deleted_at IS NULL
        LEFT JOIN region r ON o.region_id = r.id AND r.deleted_at IS NULL
        LEFT JOIN sales_channel sc ON o.sales_channel_id = sc.id AND sc.deleted_at IS NULL
        WHERE ${whereClause}
        ORDER BY o.created_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `, [...queryParams, parseInt(limit), parseInt(offset)]);
            const orderRows = result.rows || [];
            console.log(`📋 [TENANT FILTER] Retrieved ${orderRows.length} orders`);
            // For each order, get items and financial details
            for (const orderRow of orderRows) {
                // Get order items with product/variant details
                const itemsResult = await client.query(`
          SELECT
            oli.id, oli.title, oli.subtitle, oli.thumbnail,
            oli.unit_price, oli.metadata, oli.variant_id, oli.product_id,
            oli.created_at, oli.updated_at, oli.product_title, oli.product_description,
            oli.variant_title, oli.variant_sku, oli.variant_barcode,

            -- Order item quantities and totals
            oi.quantity, oi.fulfilled_quantity, oi.shipped_quantity,
            oi.returned_quantity, oi.detail as item_detail,

            -- Product information
            p.id as product_id, p.title as product_title,
            p.description as product_description, p.thumbnail as product_thumbnail,
            p.status as product_status, p.handle as product_handle,
            p.weight as product_weight, p.length as product_length,
            p.height as product_height, p.width as product_width,

            -- Variant information
            pv.id as variant_id, pv.title as variant_title,
            pv.sku as variant_sku, pv.barcode as variant_barcode,
            pv.manage_inventory as variant_manage_inventory,
            pv.allow_backorder as variant_allow_backorder,
            pv.weight as variant_weight, pv.length as variant_length,
            pv.height as variant_height, pv.width as variant_width

          FROM order_line_item oli
          LEFT JOIN order_item oi ON oli.id = oi.item_id AND oi.deleted_at IS NULL
          LEFT JOIN product p ON oli.product_id = p.id AND p.deleted_at IS NULL
          LEFT JOIN product_variant pv ON oli.variant_id = pv.id AND pv.deleted_at IS NULL
          WHERE oi.order_id = $1
          AND oli.deleted_at IS NULL
          ORDER BY oli.created_at ASC
        `, [orderRow.id]);
                const items = itemsResult.rows.map(item => ({
                    id: item.id,
                    title: item.title,
                    subtitle: item.subtitle,
                    thumbnail: item.thumbnail,
                    quantity: item.quantity,
                    fulfilled_quantity: item.fulfilled_quantity,
                    shipped_quantity: item.shipped_quantity,
                    returned_quantity: item.returned_quantity,
                    unit_price: item.unit_price,
                    total: (item.unit_price || 0) * (item.quantity || 0),
                    metadata: item.metadata,
                    variant_id: item.variant_id,
                    product_id: item.product_id,
                    created_at: item.created_at,
                    updated_at: item.updated_at,
                    detail: item.item_detail,
                    product: item.product_id ? {
                        id: item.product_id,
                        title: item.product_title,
                        description: item.product_description,
                        thumbnail: item.product_thumbnail,
                        status: item.product_status,
                        handle: item.product_handle,
                        weight: item.product_weight,
                        length: item.product_length,
                        height: item.product_height,
                        width: item.product_width,
                    } : null,
                    variant: item.variant_id ? {
                        id: item.variant_id,
                        title: item.variant_title,
                        sku: item.variant_sku,
                        barcode: item.variant_barcode,
                        manage_inventory: item.variant_manage_inventory,
                        allow_backorder: item.variant_allow_backorder,
                        weight: item.variant_weight,
                        length: item.variant_length,
                        height: item.variant_height,
                        width: item.variant_width,
                    } : null,
                }));
                // Get financial totals
                const financialResult = await client.query(`
          SELECT
            -- Item totals
            COALESCE(SUM(oli.unit_price * oi.quantity), 0) as original_item_total,
            COALESCE(SUM(oli.unit_price * oi.quantity), 0) as original_item_subtotal,
            0 as original_item_tax_total,
            COALESCE(SUM(oli.unit_price * oi.quantity), 0) as item_total,
            COALESCE(SUM(oli.unit_price * oi.quantity), 0) as item_subtotal,
            0 as item_tax_total,

            -- Shipping totals (can be made dynamic based on shipping methods)
            50 as original_shipping_total,
            50 as original_shipping_subtotal,
            0 as original_shipping_tax_total,
            50 as shipping_total,
            50 as shipping_subtotal,
            0 as shipping_tax_total,

            -- Discount and gift card totals
            0 as discount_total,
            0 as discount_tax_total,
            0 as gift_card_total,
            0 as gift_card_tax_total,

            -- Payment totals
            0 as paid_total,
            0 as refunded_total

          FROM order_line_item oli
          LEFT JOIN order_item oi ON oli.id = oi.item_id AND oi.deleted_at IS NULL
          WHERE oi.order_id = $1
          AND oli.deleted_at IS NULL
        `, [orderRow.id]);
                const financials = financialResult.rows[0] || {};
                // Calculate derived totals
                const originalSubtotal = Number(financials.original_item_subtotal) || 0;
                const originalTaxTotal = (Number(financials.original_item_tax_total) || 0) +
                    (Number(financials.original_shipping_tax_total) || 0);
                const originalTotal = originalSubtotal +
                    (Number(financials.original_shipping_subtotal) || 0) +
                    originalTaxTotal -
                    (Number(financials.discount_total) || 0) -
                    (Number(financials.gift_card_total) || 0);
                const subtotal = Number(financials.item_subtotal) || 0;
                const taxTotal = (Number(financials.item_tax_total) || 0) +
                    (Number(financials.shipping_tax_total) || 0);
                const total = subtotal +
                    (Number(financials.shipping_subtotal) || 0) +
                    taxTotal -
                    (Number(financials.discount_total) || 0) -
                    (Number(financials.gift_card_total) || 0);
                const pendingDifference = total - (Number(financials.paid_total) || 0);
                // Get payment information (if payment tables exist)
                let payments = [];
                try {
                    const paymentsResult = await client.query(`
            SELECT 
              p.id, p.amount, p.currency_code, p.status as payment_status,
              p.provider_id, p.data as payment_data, p.created_at as payment_created_at,
              p.updated_at as payment_updated_at
            FROM payment p
            WHERE p.order_id = $1
            AND p.deleted_at IS NULL
            ORDER BY p.created_at DESC
          `, [orderRow.id]);
                    payments = paymentsResult.rows || [];
                }
                catch (paymentError) {
                    console.log(`⚠️ [TENANT FILTER] Payment table not found or accessible for order ${orderRow.id}`);
                    payments = [];
                }
                // Build comprehensive order object
                const order = {
                    // Basic order information
                    id: orderRow.id,
                    status: orderRow.status,
                    currency_code: orderRow.currency_code,
                    email: orderRow.email,
                    display_id: orderRow.display_id,
                    created_at: orderRow.created_at,
                    updated_at: orderRow.updated_at,
                    tenant_id: orderRow.tenant_id,
                    metadata: orderRow.metadata,
                    customer_id: orderRow.customer_id,
                    region_id: orderRow.region_id,
                    sales_channel_id: orderRow.sales_channel_id,
                    shipping_address_id: orderRow.shipping_address_id,
                    billing_address_id: orderRow.billing_address_id,
                    is_draft_order: orderRow.is_draft_order,
                    no_notification: orderRow.no_notification,
                    // Financial totals
                    original_item_total: financials.original_item_total || 0,
                    original_item_subtotal: financials.original_item_subtotal || 0,
                    original_item_tax_total: financials.original_item_tax_total || 0,
                    item_total: financials.item_total || 0,
                    item_subtotal: financials.item_subtotal || 0,
                    item_tax_total: financials.item_tax_total || 0,
                    original_total: originalTotal,
                    original_subtotal: originalSubtotal,
                    original_tax_total: originalTaxTotal,
                    total: total,
                    subtotal: subtotal,
                    tax_total: taxTotal,
                    discount_total: financials.discount_total || 0,
                    discount_tax_total: financials.discount_tax_total || 0,
                    gift_card_total: financials.gift_card_total || 0,
                    gift_card_tax_total: financials.gift_card_tax_total || 0,
                    shipping_total: financials.shipping_total || 0,
                    shipping_subtotal: financials.shipping_subtotal || 0,
                    shipping_tax_total: financials.shipping_tax_total || 0,
                    original_shipping_total: financials.original_shipping_total || 0,
                    original_shipping_subtotal: financials.original_shipping_subtotal || 0,
                    original_shipping_tax_total: financials.original_shipping_tax_total || 0,
                    paid_total: financials.paid_total || 0,
                    refunded_total: financials.refunded_total || 0,
                    pending_difference: pendingDifference,
                    // Customer information
                    customer: orderRow.customer_id ? {
                        id: orderRow.customer_id,
                        email: orderRow.customer_email,
                        first_name: orderRow.customer_first_name,
                        last_name: orderRow.customer_last_name,
                        phone: orderRow.customer_phone,
                        has_account: orderRow.customer_has_account,
                        created_at: orderRow.customer_created_at,
                    } : null,
                    // Shipping address
                    shipping_address: orderRow.shipping_address_id ? {
                        id: orderRow.shipping_address_id,
                        first_name: orderRow.shipping_first_name,
                        last_name: orderRow.shipping_last_name,
                        address_1: orderRow.shipping_address_1,
                        address_2: orderRow.shipping_address_2,
                        city: orderRow.shipping_city,
                        postal_code: orderRow.shipping_postal_code,
                        province: orderRow.shipping_province,
                        country_code: orderRow.shipping_country_code,
                        phone: orderRow.shipping_phone,
                        company: orderRow.shipping_company,
                    } : null,
                    // Billing address
                    billing_address: orderRow.billing_address_id ? {
                        id: orderRow.billing_address_id,
                        first_name: orderRow.billing_first_name,
                        last_name: orderRow.billing_last_name,
                        address_1: orderRow.billing_address_1,
                        address_2: orderRow.billing_address_2,
                        city: orderRow.billing_city,
                        postal_code: orderRow.billing_postal_code,
                        province: orderRow.billing_province,
                        country_code: orderRow.billing_country_code,
                        phone: orderRow.billing_phone,
                        company: orderRow.billing_company,
                    } : null,
                    // Region information
                    region: orderRow.region_id ? {
                        id: orderRow.region_id,
                        name: orderRow.region_name,
                        currency_code: orderRow.region_currency,
                    } : null,
                    // Sales channel information
                    sales_channel: orderRow.sales_channel_id ? {
                        id: orderRow.sales_channel_id,
                        name: orderRow.sales_channel_name,
                        description: orderRow.sales_channel_description,
                    } : null,
                    // Order items
                    items: items,
                    // Payment information
                    payments: payments,
                    // Summary counts
                    item_count: items.length,
                    total_quantity: items.reduce((sum, item) => sum + (item.quantity || 0), 0),
                    fulfilled_quantity: items.reduce((sum, item) => sum + (item.fulfilled_quantity || 0), 0),
                    shipped_quantity: items.reduce((sum, item) => sum + (item.shipped_quantity || 0), 0),
                    returned_quantity: items.reduce((sum, item) => sum + (item.returned_quantity || 0), 0),
                };
                orders.push(order);
            }
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            throw dbError;
        }
        finally {
            // Always close the database connection
            try {
                await client.end();
                console.log(`🔗 [TENANT FILTER] Database connection closed`);
            }
            catch (closeError) {
                console.error('❌ [TENANT FILTER] Error closing database connection:', closeError);
            }
        }
        // Return response in Medusa format
        const response = {
            orders,
            count: orders.length,
            offset: parseInt(offset),
            limit: parseInt(limit),
            total: count,
            _tenant: {
                id: tenantId,
                filtered: true,
                method: 'direct_db_connection_enhanced',
                total_in_db: count,
            },
        };
        // Add tenant headers for debugging
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Filtered', 'true');
        res.setHeader('X-Orders-Count', orders.length.toString());
        res.setHeader('X-Orders-Total', count.toString());
        console.log(`📤 [TENANT FILTER] Returning ${orders.length} enhanced orders for tenant ${tenantId}`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error in enhanced orders endpoint:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to fetch orders',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_filter_error',
                timestamp: new Date().toISOString(),
            },
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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