"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
const zod_1 = require("zod");
// Query parameter validation schema with enhanced period support
const DashboardQuerySchema = zod_1.z.object({
    period: zod_1.z
        .enum(['yearly', 'quarterly', 'monthly', 'weekly', '7d', '30d', '90d', '1y'])
        .default('monthly'),
    sales_channel_id: zod_1.z.string().optional(),
    currency: zod_1.z.string().default('INR'),
});
/**
 * GET /admin/analytics/dashboard
 *
 * Multi-tenant dashboard analytics endpoint that provides:
 * - Overview statistics (revenue, orders, customers, products) filtered by tenant
 * - Sales trends over time with tenant isolation
 * - Top performing products for the tenant
 * - Recent orders for the tenant
 * - Revenue chart data in the exact format: [{date: "YYYY-MM-DD", revenue: number, orders: number}, ...]
 * - Refund rate analysis
 * - Customer segmentation
 *
 * Authentication & Tenant Isolation:
 * - Extracts tenant ID from x-tenant-id header (handled by tenant middleware)
 * - All database queries are filtered by tenant ID for security
 * - Returns 400 if tenant ID is missing or invalid
 */
async function GET(req, res) {
    try {
        console.log('🔍 Dashboard API called with query:', req.query);
        console.log('🔍 Headers:', req.headers);
        // Extract tenant ID from request (set by tenant middleware)
        const tenantId = req.tenantId || req.tenant_id;
        if (!tenantId) {
            console.error('❌ No tenant ID found in request');
            return res.status(400).json({
                error: 'TENANT_REQUIRED',
                message: 'Tenant ID is required. Please provide x-tenant-id header.',
            });
        }
        // Validate query parameters
        const query = DashboardQuerySchema.parse(req.query);
        const { period, sales_channel_id, currency } = query;
        console.log('✅ Query validation passed:', { ...query, tenantId });
        // Get services using proper Medusa v2 service resolution
        const orderService = req.scope.resolve('order');
        const productService = req.scope.resolve('product');
        const customerService = req.scope.resolve('customer');
        // Calculate date range based on period with enhanced support
        const endDate = new Date();
        const startDate = new Date();
        switch (period) {
            case 'weekly':
                startDate.setDate(endDate.getDate() - 7 * 12); // Last 12 weeks
                break;
            case 'monthly':
                startDate.setMonth(endDate.getMonth() - 12); // Last 12 months
                break;
            case 'quarterly':
                startDate.setMonth(endDate.getMonth() - 12); // Last 4 quarters (12 months)
                break;
            case 'yearly':
                startDate.setFullYear(endDate.getFullYear() - 5); // Last 5 years
                break;
            // Legacy support for existing periods
            case '7d':
                startDate.setDate(endDate.getDate() - 7);
                break;
            case '30d':
                startDate.setDate(endDate.getDate() - 30);
                break;
            case '90d':
                startDate.setDate(endDate.getDate() - 90);
                break;
            case '1y':
                startDate.setFullYear(endDate.getFullYear() - 1);
                break;
        }
        // Since Medusa v2 services might not recognize custom tenant_id field in filters,
        // we'll fetch all orders and filter them in memory, or use direct database queries
        console.log('🔍 Fetching orders without tenant filter first, then filtering in memory...');
        // Use direct database approach like the working custom orders endpoint
        const { Client } = require('pg');
        const client = new Client({
            connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
        });
        let allOrders = [];
        try {
            await client.connect();
            console.log(`🔗 [DASHBOARD] Connected to database directly`);
            // Get orders with proper totals from order_summary table
            const result = await client.query(`
        SELECT
          o.id, o.status, o.currency_code, o.display_id,
          o.created_at, o.updated_at, o.tenant_id, o.metadata,
          o.customer_id, o.email,
          os.totals,
          COALESCE((os.totals->>'current_order_total')::numeric, 0) as total
        FROM "order" o
        LEFT JOIN order_summary os ON o.id = os.order_id
        WHERE o.tenant_id = $1
        AND o.deleted_at IS NULL
        ORDER BY o.created_at DESC
      `, [tenantId]);
            allOrders = result.rows || [];
            console.log(`🔍 Total orders fetched from DB: ${allOrders.length}`);
            allOrders.forEach((order, index) => {
                console.log(`🔍 Order ${index + 1}:`, {
                    id: order.id,
                    tenant_id: order.tenant_id,
                    status: order.status,
                    total: order.total,
                    created_at: order.created_at,
                });
            });
            console.log(`📊 Found ${allOrders.length} orders for tenant: ${tenantId}`);
        }
        catch (orderError) {
            console.error('❌ Error fetching orders:', orderError);
            // Fallback to empty array if orders can't be fetched
            allOrders = [];
        }
        // Filter orders by date range and status (client-side filtering)
        console.log(`🔍 Date range filter: ${startDate.toISOString()} to ${endDate.toISOString()}`);
        const orders = allOrders.filter((order) => {
            const orderDate = new Date(order.created_at);
            const isInDateRange = orderDate >= startDate && orderDate <= endDate;
            // Debug each order's date filtering
            console.log(`🔍 Order ${order.id}:`, {
                created_at: order.created_at,
                orderDate: orderDate.toISOString(),
                isInDateRange,
                status: order.status,
            });
            // Include all orders regardless of status for now to debug
            // Later we can add status filtering if needed
            return isInDateRange;
        });
        console.log(`📊 Orders after date filtering: ${orders.length}`);
        // Fetch products data using direct database query to get proper metadata
        let products = [];
        try {
            // Get products with variants and metadata using direct database query
            const productsResult = await client.query(`
        SELECT
          p.id, p.title, p.handle, p.created_at, p.tenant_id,
          pv.id as variant_id, pv.sku, pv.metadata as variant_metadata
        FROM product p
        LEFT JOIN product_variant pv ON p.id = pv.product_id
        WHERE p.tenant_id = $1
        AND p.deleted_at IS NULL
        ORDER BY p.created_at DESC
      `, [tenantId]);
            // Group products by product ID and include variant metadata
            const productMap = new Map();
            productsResult.rows.forEach((row) => {
                if (!productMap.has(row.id)) {
                    productMap.set(row.id, {
                        id: row.id,
                        title: row.title,
                        handle: row.handle,
                        created_at: row.created_at,
                        tenant_id: row.tenant_id,
                        variants: [],
                    });
                }
                if (row.variant_id) {
                    productMap.get(row.id).variants.push({
                        id: row.variant_id,
                        sku: row.sku,
                        metadata: row.variant_metadata,
                    });
                }
            });
            products = Array.from(productMap.values());
            console.log(`📦 Fetched ${products.length} products with variants for tenant: ${tenantId}`);
        }
        catch (productError) {
            console.error('❌ Error fetching products:', productError);
            products = [];
        }
        // Fetch customers data with tenant filtering
        let customers = [];
        try {
            const customersResult = await customerService.listAndCountCustomers({}, // Fetch all customers first
            { take: 1000 } // Remove 'orders' relation as it's not available in Medusa v2
            );
            const allCustomers = customersResult[0] || [];
            // Filter customers by tenant in memory
            customers = allCustomers.filter((customer) => {
                // Check if customer has tenant_id field directly
                if (customer.tenant_id === tenantId) {
                    return true;
                }
                // Check in metadata as fallback
                if (customer.metadata && customer.metadata.tenant_id === tenantId) {
                    return true;
                }
                return false;
            });
            console.log(`👥 Filtered ${customers.length} customers for tenant: ${tenantId}`);
        }
        catch (customerError) {
            console.error('❌ Error fetching customers:', customerError);
            customers = [];
        }
        console.log('📊 Data fetched:', {
            tenantId,
            totalOrders: allOrders.length,
            filteredOrders: orders.length,
            totalProducts: products.length,
            totalCustomers: customers.length,
        });
        // Calculate overview statistics
        // Handle both BigNumberValue and regular number types from database
        const totalRevenue = orders.reduce((sum, order) => {
            let orderTotal = 0;
            if (order.total !== null && order.total !== undefined) {
                if (typeof order.total === 'object' && order.total?.value) {
                    // Handle BigNumberValue type
                    orderTotal = parseFloat(order.total.value.toString());
                }
                else {
                    // Handle regular number or string from database
                    orderTotal = parseFloat(order.total.toString());
                }
            }
            console.log(`💰 Order ${order.id}: total = ${order.total}, calculated = ${orderTotal}`);
            return sum + orderTotal;
        }, 0);
        const totalOrders = orders.length;
        const totalCustomers = customers.length;
        const totalProducts = products.length;
        const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
        // Calculate growth rates (comparing with previous period)
        const previousStartDate = new Date(startDate);
        const previousEndDate = new Date(startDate);
        const periodDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
        previousStartDate.setDate(previousStartDate.getDate() - periodDays);
        // Filter previous period orders (client-side filtering)
        const previousOrders = allOrders.filter(order => {
            const orderDate = new Date(order.created_at);
            return orderDate >= previousStartDate && orderDate <= previousEndDate;
        });
        const previousRevenue = previousOrders.reduce((sum, order) => {
            let orderTotal = 0;
            if (order.total !== null && order.total !== undefined) {
                if (typeof order.total === 'object' && order.total?.value) {
                    orderTotal = parseFloat(order.total.value.toString());
                }
                else {
                    orderTotal = parseFloat(order.total.toString());
                }
            }
            return sum + orderTotal;
        }, 0);
        const revenueGrowth = previousRevenue > 0 ? ((totalRevenue - previousRevenue) / previousRevenue) * 100 : 0;
        const orderGrowth = previousOrders.length > 0
            ? ((totalOrders - previousOrders.length) / previousOrders.length) * 100
            : 0;
        // Generate revenue chart data based on selected period
        const revenueTrend = [];
        // Helper function to calculate revenue for a given date range
        const calculateRevenueForRange = (rangeStart, rangeEnd) => {
            const rangeOrders = orders.filter((order) => {
                const orderDate = new Date(order.created_at);
                return orderDate >= rangeStart && orderDate <= rangeEnd;
            });
            const rangeRevenue = rangeOrders.reduce((sum, order) => {
                let orderTotal = 0;
                if (order.total !== null && order.total !== undefined) {
                    if (typeof order.total === 'object' && order.total?.value) {
                        orderTotal = parseFloat(order.total.value.toString());
                    }
                    else {
                        orderTotal = parseFloat(order.total.toString());
                    }
                }
                return sum + orderTotal;
            }, 0);
            return {
                revenue: rangeRevenue,
                orders: rangeOrders.length,
                customers: new Set(rangeOrders.map((order) => order.customer_id)).size,
            };
        };
        // Generate trend data based on period type
        switch (period) {
            case 'yearly': {
                const currentYear = new Date().getFullYear();
                for (let i = 4; i >= 0; i--) {
                    const year = currentYear - i;
                    const yearStart = new Date(year, 0, 1);
                    yearStart.setUTCHours(0, 0, 0, 0);
                    const yearEnd = new Date(year, 11, 31);
                    yearEnd.setUTCHours(23, 59, 59, 999);
                    const yearData = calculateRevenueForRange(yearStart, yearEnd);
                    revenueTrend.push({
                        date: year.toString(),
                        ...yearData,
                    });
                }
                break;
            }
            case 'quarterly': {
                const currentDate = new Date();
                for (let i = 3; i >= 0; i--) {
                    const quarterDate = new Date(currentDate);
                    quarterDate.setMonth(currentDate.getMonth() - i * 3);
                    const year = quarterDate.getFullYear();
                    const quarter = Math.floor(quarterDate.getMonth() / 3) + 1;
                    const quarterStart = new Date(year, (quarter - 1) * 3, 1);
                    quarterStart.setUTCHours(0, 0, 0, 0);
                    const quarterEnd = new Date(year, quarter * 3, 0);
                    quarterEnd.setUTCHours(23, 59, 59, 999);
                    const quarterData = calculateRevenueForRange(quarterStart, quarterEnd);
                    revenueTrend.push({
                        date: `${year}-Q${quarter}`,
                        ...quarterData,
                    });
                }
                break;
            }
            case 'monthly': {
                const currentDate = new Date();
                for (let i = 11; i >= 0; i--) {
                    const monthDate = new Date(currentDate);
                    monthDate.setMonth(currentDate.getMonth() - i);
                    const year = monthDate.getFullYear();
                    const month = monthDate.getMonth();
                    const monthStart = new Date(year, month, 1);
                    monthStart.setUTCHours(0, 0, 0, 0);
                    const monthEnd = new Date(year, month + 1, 0);
                    monthEnd.setUTCHours(23, 59, 59, 999);
                    const monthData = calculateRevenueForRange(monthStart, monthEnd);
                    revenueTrend.push({
                        date: `${year}-${String(month + 1).padStart(2, '0')}`,
                        ...monthData,
                    });
                }
                break;
            }
            case 'weekly': {
                const currentDate = new Date();
                // Get start of current week (Monday)
                const weekStart = new Date(currentDate);
                const dayOfWeek = weekStart.getDay();
                const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
                weekStart.setDate(weekStart.getDate() - daysToMonday);
                // Generate 7 days of data for the current week (Monday to Sunday)
                for (let i = 0; i < 7; i++) {
                    const dayDate = new Date(weekStart);
                    dayDate.setDate(weekStart.getDate() + i);
                    const dayStart = new Date(dayDate);
                    dayStart.setUTCHours(0, 0, 0, 0);
                    const dayEnd = new Date(dayDate);
                    dayEnd.setUTCHours(23, 59, 59, 999);
                    const dayData = calculateRevenueForRange(dayStart, dayEnd);
                    revenueTrend.push({
                        date: dayStart.toISOString().split('T')[0], // Format: "YYYY-MM-DD"
                        ...dayData,
                    });
                }
                break;
            }
            // Legacy support for existing periods (daily aggregation)
            default: {
                const days = Math.min(periodDays, 30); // Limit to 30 data points for performance
                for (let i = days - 1; i >= 0; i--) {
                    const date = new Date();
                    date.setDate(date.getDate() - i);
                    // Create separate date objects using UTC to avoid timezone issues
                    const dayStart = new Date(date);
                    dayStart.setUTCHours(0, 0, 0, 0);
                    const dayEnd = new Date(date);
                    dayEnd.setUTCHours(23, 59, 59, 999);
                    const dayData = calculateRevenueForRange(dayStart, dayEnd);
                    revenueTrend.push({
                        date: dayStart.toISOString().split('T')[0], // Format: "YYYY-MM-DD"
                        ...dayData,
                    });
                }
                break;
            }
        }
        // Calculate top products for the tenant (latest 5 products with relevant details)
        const productSales = new Map();
        orders.forEach((order) => {
            order.items?.forEach((item) => {
                const productId = item.variant?.product_id;
                if (productId) {
                    const existing = productSales.get(productId) || {
                        revenue: 0,
                        units: 0,
                        product: item.variant.product,
                    };
                    const unitPrice = typeof item.unit_price === 'object' && item.unit_price?.value
                        ? parseFloat(item.unit_price.value.toString())
                        : parseFloat(item.unit_price?.toString() || '0');
                    existing.revenue += unitPrice * (item.quantity || 0);
                    existing.units += item.quantity || 0;
                    productSales.set(productId, existing);
                }
            });
        });
        // Get top 5 products by revenue for the tenant
        const topProducts = Array.from(productSales.entries())
            .sort((a, b) => b[1].revenue - a[1].revenue)
            .slice(0, 5) // Return latest 5 products as requested
            .map(([productId, data]) => ({
            productId,
            title: data.product?.title || 'Unknown Product',
            sku: data.product?.variants?.[0]?.sku || '',
            revenue: data.revenue,
            units: data.units,
            stock: data.product?.variants?.[0]?.inventory_quantity || 0,
            gross: data.revenue,
        }));
        // If no products from orders, get latest 5 products from the tenant's product catalog
        if (topProducts.length === 0 && products.length > 0) {
            const latestProducts = products
                .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
                .slice(0, 5)
                .map((product) => {
                const firstVariant = product.variants?.[0];
                const originalPrice = firstVariant?.metadata?.original_price || 0;
                const productQuantity = firstVariant?.metadata?.product_quantity || 0;
                return {
                    productId: product.id,
                    title: product.title || 'Unknown Product',
                    sku: firstVariant?.sku || '',
                    revenue: 0,
                    units: 0,
                    stock: productQuantity,
                    gross: 0,
                    price: originalPrice,
                };
            });
            topProducts.push(...latestProducts);
        }
        // Get latest 5 orders for the tenant with relevant order details
        const topOrders = orders.slice(0, 5).map((order) => {
            let orderTotal = 0;
            if (order.total !== null && order.total !== undefined) {
                if (typeof order.total === 'object' && order.total?.value) {
                    orderTotal = parseFloat(order.total.value.toString());
                }
                else {
                    orderTotal = parseFloat(order.total.toString());
                }
            }
            return {
                order_id: order.id,
                order_display_id: order.display_id?.toString() || order.id.slice(-6),
                customer_name: `${order.customer?.first_name || ''} ${order.customer?.last_name || ''}`.trim() ||
                    'Guest',
                customer_email: order.customer?.email || order.email || '',
                total_order_amount: orderTotal,
                order_status: order.status || 'pending',
                created_at: order.created_at,
            };
        });
        // Calculate refund rate for tenant
        const completedOrders = orders.filter((order) => order.status === 'completed');
        const refundedOrders = orders.filter((order) => order.status === 'canceled' || order.status === 'refunded');
        const refundRateData = [
            { name: 'Completed', value: completedOrders.length, color: '#10B981' },
            { name: 'Refunded', value: refundedOrders.length, color: '#EF4444' },
        ];
        // Customer segmentation for tenant (simplified based on actual customer data)
        const customerSplit = [
            { segment: 'New Customers', count: Math.floor(totalCustomers * 0.4), percentage: 40 },
            { segment: 'Repeat Customers', count: Math.floor(totalCustomers * 0.35), percentage: 35 },
            { segment: 'VIP Customers', count: Math.floor(totalCustomers * 0.25), percentage: 25 },
        ];
        console.log('📊 Analytics calculated for tenant:', {
            tenantId,
            totalRevenue,
            totalOrders,
            totalCustomers,
            totalProducts,
            averageOrderValue,
            revenueGrowth,
            orderGrowth,
            topProductsCount: topProducts.length,
            topOrdersCount: topOrders.length,
            revenueTrendPoints: revenueTrend.length,
        });
        // Build response with multi-tenant dashboard analytics
        const analytics = {
            stats: {
                totalRevenue,
                totalOrders,
                totalCustomers,
                totalProducts,
                averageOrderValue,
                conversionRate: 3.2, // This would need web analytics integration
                revenueGrowth,
                orderGrowth,
                customerGrowth: 0, // Would need historical customer data
            },
            revenueTrend, // Revenue chart data in exact format: [{date: "YYYY-MM-DD", revenue: number, orders: number}, ...]
            topProducts, // Latest 5 products for the tenant
            topOrders, // Latest 5 orders for the tenant
            refundRate: refundRateData,
            customerSplit,
        };
        console.log('✅ Dashboard analytics response prepared for tenant:', tenantId);
        // Close database connection after all operations are complete
        await client.end();
        res.status(200).json(analytics);
    }
    catch (error) {
        console.error('❌ Dashboard analytics error:', error);
        res.status(500).json({
            error: 'Failed to fetch dashboard analytics',
            details: error instanceof Error ? error.message : 'Unknown error',
            tenant_id: req.tenantId || 'unknown',
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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