"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
const zod_1 = require("zod");
// Query parameter validation schema
const InventoryQuerySchema = zod_1.z.object({
    location_id: zod_1.z.string().optional(),
    tenant_id: zod_1.z.string().optional(),
    low_stock_threshold: zod_1.z.number().min(0).default(10),
    category_id: zod_1.z.string().optional(),
});
/**
 * GET /admin/analytics/inventory
 *
 * Inventory analytics endpoint that provides:
 * - Inventory summary and stock levels
 * - Stock movement tracking
 * - Inventory alerts and notifications
 * - Location-based inventory analysis
 * - Turnover rate analysis
 * - Stock trend analysis
 */
async function GET(req, res) {
    try {
        // Validate query parameters
        const query = InventoryQuerySchema.parse(req.query);
        const { location_id, tenant_id, low_stock_threshold, category_id } = query;
        // Get services using correct service names
        const productService = req.scope.resolve('product');
        const orderService = req.scope.resolve('order');
        // Build filters
        const productFilters = {};
        if (tenant_id) {
            productFilters.tenant_id = tenant_id;
        }
        if (category_id) {
            productFilters.categories = { id: category_id };
        }
        // Fetch products with variants
        const products = await productService.listProducts(productFilters, {
            select: ['id', 'title', 'status', 'created_at'],
            relations: ['variants', 'categories'],
        });
        // Calculate date range for analysis (last 30 days)
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(endDate.getDate() - 30);
        // Fetch recent orders for turnover calculation
        const ordersResult = await orderService.listAndCountOrders({}, {
            relations: ['items'],
            order: { created_at: 'DESC' },
        });
        const allOrders = ordersResult[0];
        // Filter orders by date range and successful status in memory
        const recentOrders = allOrders.filter(order => {
            const orderDate = new Date(order.created_at);
            const isInDateRange = orderDate >= startDate && orderDate <= endDate;
            const isSuccessful = ['completed', 'shipped', 'delivered'].includes(order.status);
            const matchesTenant = !tenant_id || order.tenant_id === tenant_id;
            return isInDateRange && isSuccessful && matchesTenant;
        });
        // Calculate sales velocity for each variant
        const variantSales = new Map();
        recentOrders.forEach(order => {
            order.items?.forEach(item => {
                if (item.variant_id) {
                    const existing = variantSales.get(item.variant_id) || {
                        quantity: 0,
                        lastSale: new Date(0),
                    };
                    existing.quantity += item.quantity || 0;
                    const orderDate = new Date(order.created_at);
                    if (orderDate > existing.lastSale) {
                        existing.lastSale = orderDate;
                    }
                    variantSales.set(item.variant_id, existing);
                }
            });
        });
        // Build inventory items list
        const inventoryItems = [];
        const alerts = [];
        // Handle products that might not have variants loaded
        const productsArray = Array.isArray(products) ? products : products[0] || [];
        productsArray.forEach(product => {
            // Ensure variants exist, create a default variant if none
            const variants = product.variants || [
                {
                    id: `${product.id}_default`,
                    sku: product.handle || product.id,
                    inventory_quantity: 0,
                },
            ];
            variants.forEach(variant => {
                const currentStock = variant.inventory_quantity || 0;
                const reservedStock = 0; // Would need reservation system
                const availableStock = currentStock - reservedStock;
                const sales = variantSales.get(variant.id);
                const salesQuantity = sales?.quantity || 0;
                const daysSinceLastSale = sales?.lastSale
                    ? Math.ceil((endDate.getTime() - sales.lastSale.getTime()) / (1000 * 60 * 60 * 24))
                    : 999;
                // Calculate turnover rate (sales per day)
                const turnoverRate = salesQuantity / 30; // Sales per day over 30 days
                // Determine stock status
                let status;
                if (currentStock === 0) {
                    status = 'out_of_stock';
                }
                else if (currentStock <= low_stock_threshold) {
                    status = 'low_stock';
                }
                else if (currentStock > 100) {
                    // Overstock threshold
                    status = 'overstock';
                }
                else {
                    status = 'in_stock';
                }
                const inventoryItem = {
                    product_id: product.id,
                    variant_id: variant.id,
                    title: product.title,
                    sku: variant.sku || '',
                    current_stock: currentStock,
                    reserved_stock: reservedStock,
                    available_stock: availableStock,
                    reorder_level: low_stock_threshold,
                    status,
                    location: 'Default Location',
                    category: product.categories?.[0]?.name || 'Uncategorized',
                    last_restocked: null, // Would need stock movement history
                    turnover_rate: turnoverRate,
                };
                inventoryItems.push(inventoryItem);
                // Generate alerts
                if (status === 'out_of_stock') {
                    alerts.push({
                        alert_type: 'out_of_stock',
                        product_id: product.id,
                        variant_id: variant.id,
                        title: product.title,
                        sku: variant.sku || '',
                        current_stock: currentStock,
                        threshold: 0,
                        priority: 'high',
                        days_since_last_sale: daysSinceLastSale,
                    });
                }
                else if (status === 'low_stock') {
                    alerts.push({
                        alert_type: 'low_stock',
                        product_id: product.id,
                        variant_id: variant.id,
                        title: product.title,
                        sku: variant.sku || '',
                        current_stock: currentStock,
                        threshold: low_stock_threshold,
                        priority: 'medium',
                        days_since_last_sale: daysSinceLastSale,
                    });
                }
                else if (status === 'overstock') {
                    alerts.push({
                        alert_type: 'overstock',
                        product_id: product.id,
                        variant_id: variant.id,
                        title: product.title,
                        sku: variant.sku || '',
                        current_stock: currentStock,
                        threshold: 100,
                        priority: 'low',
                        days_since_last_sale: daysSinceLastSale,
                    });
                }
                // Slow moving items (no sales in 30 days)
                if (daysSinceLastSale > 30 && currentStock > 0) {
                    alerts.push({
                        alert_type: 'slow_moving',
                        product_id: product.id,
                        variant_id: variant.id,
                        title: product.title,
                        sku: variant.sku || '',
                        current_stock: currentStock,
                        threshold: 30,
                        priority: 'low',
                        days_since_last_sale: daysSinceLastSale,
                    });
                }
            });
        });
        // Calculate summary metrics
        const totalItems = inventoryItems.length;
        const totalValue = inventoryItems.reduce((sum, item) => sum + item.current_stock * 100, 0); // Assuming ₹100 average value
        const inStockItems = inventoryItems.filter(item => item.status === 'in_stock').length;
        const lowStockItems = inventoryItems.filter(item => item.status === 'low_stock').length;
        const outOfStockItems = inventoryItems.filter(item => item.status === 'out_of_stock').length;
        const averageTurnoverRate = inventoryItems.reduce((sum, item) => sum + item.turnover_rate, 0) / totalItems;
        // Location summary (mock data since stock location service not available)
        const locationSummary = [
            {
                location_id: 'loc_default',
                location_name: 'Default Location',
                total_items: inventoryItems.length,
                total_value: inventoryItems.reduce((sum, item) => sum + item.current_stock * 100, 0),
                low_stock_items: inventoryItems.filter(item => item.status === 'low_stock').length,
                out_of_stock_items: inventoryItems.filter(item => item.status === 'out_of_stock').length,
                utilization_rate: 85, // Would need capacity data
            },
        ];
        // Turnover analysis by category
        const categoryTurnover = new Map();
        inventoryItems.forEach(item => {
            const category = item.category;
            const existing = categoryTurnover.get(category) || { items: [], totalTurnover: 0 };
            existing.items.push(item);
            existing.totalTurnover += item.turnover_rate;
            categoryTurnover.set(category, existing);
        });
        const turnoverAnalysis = Array.from(categoryTurnover.entries()).map(([category, data]) => ({
            category,
            turnover_rate: data.totalTurnover / data.items.length,
            total_items: data.items.length,
            slow_moving_items: data.items.filter(item => item.turnover_rate < 0.1).length,
        }));
        // Stock trends (simplified - would need historical data)
        const stockTrends = [];
        for (let i = 29; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            stockTrends.push({
                date: date.toISOString().split('T')[0],
                total_stock: totalItems * 50, // Mock data
                stock_value: totalValue,
                movements_in: Math.floor(Math.random() * 100),
                movements_out: Math.floor(Math.random() * 80),
            });
        }
        // Mock stock movements (would come from actual movement tracking)
        const stockMovements = inventoryItems.slice(0, 20).map(item => ({
            date: new Date().toISOString(),
            product_id: item.product_id,
            variant_id: item.variant_id,
            title: item.title,
            sku: item.sku,
            movement_type: 'outbound',
            quantity: -Math.floor(Math.random() * 5) - 1,
            reason: 'Sale',
            location: item.location,
        }));
        // Build response
        const analytics = {
            summary: {
                total_items: totalItems,
                total_value: totalValue,
                in_stock_items: inStockItems,
                low_stock_items: lowStockItems,
                out_of_stock_items: outOfStockItems,
                average_turnover_rate: averageTurnoverRate,
                total_locations: 1, // Default location count
            },
            inventory_items: inventoryItems.slice(0, 100), // Limit for performance
            stock_movements: stockMovements,
            alerts: alerts.sort((a, b) => {
                const priorityOrder = { high: 3, medium: 2, low: 1 };
                return priorityOrder[b.priority] - priorityOrder[a.priority];
            }),
            location_summary: locationSummary,
            turnover_analysis: turnoverAnalysis,
            stock_trends: stockTrends,
        };
        res.status(200).json(analytics);
    }
    catch (error) {
        console.error('Inventory analytics error:', error);
        res.status(500).json({
            error: 'Failed to fetch inventory analytics',
            details: error instanceof Error ? error.message : 'Unknown error',
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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