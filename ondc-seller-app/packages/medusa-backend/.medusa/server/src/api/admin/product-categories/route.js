"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
exports.POST = POST;
async function GET(req, res) {
    console.log(`🚀 [TENANT FILTER] === CUSTOM PRODUCT CATEGORIES ENDPOINT CALLED ===`);
    console.log(`🚀 [TENANT FILTER] Headers:`, JSON.stringify(req.headers, null, 2));
    console.log(`🚀 [TENANT FILTER] Query:`, JSON.stringify(req.query, null, 2));
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        console.log(`🔍 [TENANT FILTER] Processing categories request for tenant: ${tenantId}`);
        // Get query parameters
        const { limit = 50, offset = 0, parent_category_id, include_descendants_tree = false } = req.query;
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
        });
        let categories = [];
        let count = 0;
        try {
            await client.connect();
            console.log(`🔗 [TENANT FILTER] Connected to database directly`);
            // Get total count for this tenant (exclude soft-deleted)
            const countResult = await client.query('SELECT COUNT(*) as total FROM product_category WHERE tenant_id = $1 AND deleted_at IS NULL', [tenantId]);
            count = parseInt(countResult.rows[0]?.total || 0);
            console.log(`📊 [TENANT FILTER] Total categories for tenant ${tenantId}: ${count}`);
            // Build query based on parent_category_id filter (exclude soft-deleted)
            let query = `
        SELECT 
          id, name, handle, description, is_active, is_internal,
          parent_category_id, rank, created_at, updated_at, 
          deleted_at, tenant_id, metadata
        FROM product_category 
        WHERE tenant_id = $1 AND deleted_at IS NULL
      `;
            let queryParams = [tenantId];
            if (parent_category_id) {
                query += ` AND parent_category_id = $2`;
                queryParams.push(parent_category_id);
            }
            else if (parent_category_id === null || parent_category_id === 'null') {
                query += ` AND parent_category_id IS NULL`;
            }
            query += ` ORDER BY rank ASC, created_at DESC LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}`;
            queryParams.push(parseInt(limit), parseInt(offset));
            const result = await client.query(query, queryParams);
            categories = result.rows || [];
            // Parse metadata for each category
            categories.forEach(category => {
                if (typeof category.metadata === 'string') {
                    try {
                        category.metadata = JSON.parse(category.metadata);
                    }
                    catch (e) {
                        console.log(`⚠️ Could not parse metadata for category ${category.id}`);
                    }
                }
            });
            console.log(`📦 [TENANT FILTER] Retrieved ${categories.length} categories`);
            await client.end();
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            await client.end().catch(() => { });
        }
        // Return response in Medusa format
        const response = {
            product_categories: categories,
            count: categories.length,
            offset: parseInt(offset),
            limit: parseInt(limit),
            // Add tenant info for debugging
            _tenant: {
                id: tenantId,
                filtered: true,
                method: 'direct_db_connection',
                total_in_db: count
            }
        };
        // Add tenant headers for debugging
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Filtered', 'true');
        res.setHeader('X-Categories-Count', categories.length.toString());
        console.log(`📤 [TENANT FILTER] Returning response:`, {
            categories_count: categories.length,
            total_count: count,
            tenant_id: tenantId
        });
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error getting categories:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to get categories',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_categories_get_error',
                timestamp: new Date().toISOString()
            }
        });
    }
}
async function POST(req, res) {
    console.log(`🚀 [TENANT FILTER] === CUSTOM PRODUCT CATEGORY CREATE ENDPOINT CALLED ===`);
    console.log(`🚀 [TENANT FILTER] Body:`, JSON.stringify(req.body, null, 2));
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        console.log(`🔄 [TENANT FILTER] Creating category for tenant: ${tenantId}`);
        // Get category data from request body
        const categoryData = req.body;
        // Ensure tenant_id is injected and cannot be modified
        const categoryWithTenant = {
            ...categoryData,
            tenant_id: tenantId
        };
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
        });
        let createdCategory = null;
        try {
            await client.connect();
            console.log(`🔗 [TENANT FILTER] Connected to database for category creation`);
            // Generate category ID
            const categoryId = `pcat_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
            // Check if handle already exists for this tenant
            if (categoryWithTenant.handle) {
                const existingResult = await client.query('SELECT id FROM product_category WHERE handle = $1 AND tenant_id = $2', [categoryWithTenant.handle, tenantId]);
                if (existingResult.rows.length > 0) {
                    await client.end();
                    return res.status(400).json({
                        type: 'invalid_data',
                        message: `Product category with handle: ${categoryWithTenant.handle}, already exists for tenant: ${tenantId}.`,
                        tenant_id: tenantId
                    });
                }
            }
            // Calculate mpath based on parent category
            let mpath = categoryId; // Default for root categories
            if (categoryWithTenant.parent_category_id) {
                // Get parent category's mpath
                const parentResult = await client.query('SELECT mpath FROM product_category WHERE id = $1 AND tenant_id = $2', [categoryWithTenant.parent_category_id, tenantId]);
                if (parentResult.rows.length === 0) {
                    await client.end();
                    return res.status(400).json({
                        type: 'invalid_data',
                        message: `Parent category with id: ${categoryWithTenant.parent_category_id} not found for tenant: ${tenantId}.`,
                        tenant_id: tenantId
                    });
                }
                const parentMpath = parentResult.rows[0].mpath;
                mpath = `${parentMpath}.${categoryId}`;
            }
            // Insert category
            const insertCategoryQuery = `
        INSERT INTO product_category (
          id, name, handle, description, mpath, is_active, is_internal,
          parent_category_id, rank, tenant_id, metadata,
          created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW(), NOW())
        RETURNING *
      `;
            const categoryValues = [
                categoryId,
                categoryWithTenant.name,
                categoryWithTenant.handle,
                categoryWithTenant.description || '',
                mpath,
                categoryWithTenant.is_active !== false, // default to true
                categoryWithTenant.is_internal || false,
                categoryWithTenant.parent_category_id || null,
                categoryWithTenant.rank || 0,
                tenantId,
                categoryWithTenant.metadata ? JSON.stringify({
                    ...categoryWithTenant.metadata,
                    tenant_id: tenantId
                }) : JSON.stringify({ tenant_id: tenantId })
            ];
            const result = await client.query(insertCategoryQuery, categoryValues);
            createdCategory = result.rows[0];
            // Parse metadata
            if (typeof createdCategory.metadata === 'string') {
                try {
                    createdCategory.metadata = JSON.parse(createdCategory.metadata);
                }
                catch (e) {
                    console.log(`⚠️ Could not parse metadata for created category ${createdCategory.id}`);
                }
            }
            console.log(`✅ [TENANT FILTER] Created category ${categoryId} for tenant: ${tenantId}`);
            await client.end();
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            await client.end().catch(() => { });
            throw dbError;
        }
        // Return response in Medusa format
        const response = {
            product_category: createdCategory,
            _tenant: {
                id: tenantId,
                injected: true,
                method: 'direct_db_creation'
            }
        };
        // Add tenant headers
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Injected', 'true');
        console.log(`📤 [TENANT FILTER] Returning created category for tenant ${tenantId}`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error creating category:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to create category',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_category_create_error',
                timestamp: new Date().toISOString(),
                stack: error.stack
            }
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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