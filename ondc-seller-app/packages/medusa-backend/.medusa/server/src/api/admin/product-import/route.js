"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.POST = POST;
exports.OPTIONS = OPTIONS;
exports.GET = GET;
const uuid_1 = require("uuid");
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const XLSX = __importStar(require("xlsx")); // Still needed for POST endpoint file parsing
const multer_1 = __importDefault(require("multer"));
const direct_database_service_1 = require("../../../services/direct-database-service");
const product_auto_config_1 = require("../../../workflows/product-auto-config");
// Configure multer for file uploads - using exact same config as working test endpoint
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = path_1.default.join(process.cwd(), 'uploads', 'product-imports');
        // Create directory if it doesn't exist
        if (!fs_1.default.existsSync(uploadDir)) {
            fs_1.default.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        const uniqueId = (0, uuid_1.v4)();
        const extension = path_1.default.extname(file.originalname);
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        cb(null, `product-import-${timestamp}-${uniqueId}${extension}`);
    },
});
const upload = (0, multer_1.default)({
    storage,
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
    },
    fileFilter: (req, file, cb) => {
        console.log('[PRODUCT IMPORT] File upload details:', {
            originalname: file.originalname,
            mimetype: file.mimetype,
            size: file.size,
        });
        // Accept Excel and CSV files - be more permissive with MIME types
        const allowedTypes = [
            'text/csv',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel.sheet.macroEnabled.12',
            'application/octet-stream', // Sometimes Excel files are detected as this
        ];
        // Also check file extension as fallback
        const allowedExtensions = ['.xlsx', '.xls', '.csv'];
        const fileExtension = path_1.default.extname(file.originalname).toLowerCase();
        if (allowedTypes.includes(file.mimetype) || allowedExtensions.includes(fileExtension)) {
            cb(null, true);
        }
        else {
            console.log('[PRODUCT IMPORT] File rejected:', {
                mimetype: file.mimetype,
                extension: fileExtension,
                allowedTypes,
                allowedExtensions,
            });
            cb(new Error(`Invalid file type. Only Excel (.xlsx, .xls) and CSV files are allowed. Received: ${file.mimetype}`));
        }
    },
});
/**
 * POST /product-import
 *
 * Import products from Excel file - Production endpoint that bypasses admin middleware
 */
async function POST(req, res) {
    try {
        console.log('[PRODUCT IMPORT] Starting product import process');
        // Set CORS headers
        res.setHeader('Access-Control-Allow-Origin', 'http://localhost:3000');
        res.setHeader('Access-Control-Allow-Credentials', 'true');
        // Use multer middleware - exact same as working test endpoint
        const uploadMiddleware = upload.single('file');
        uploadMiddleware(req, res, async (err) => {
            if (err) {
                console.error('[PRODUCT IMPORT] File upload error:', err);
                if (err instanceof multer_1.default.MulterError) {
                    if (err.code === 'LIMIT_FILE_SIZE') {
                        return res.status(400).json({
                            error: 'File too large',
                            message: 'File size must be less than 10MB',
                        });
                    }
                }
                return res.status(400).json({
                    error: 'Upload failed',
                    message: err.message || 'Failed to upload file',
                });
            }
            if (!req.file) {
                return res.status(400).json({
                    error: 'No file provided',
                    message: 'Please select a file to upload',
                });
            }
            try {
                // Get tenant ID from header - ensure it's properly extracted
                const tenantId = req.headers['x-tenant-id'];
                if (!tenantId) {
                    return res.status(400).json({
                        error: 'Missing tenant ID',
                        message: 'x-tenant-id header is required for multi-tenant operations',
                    });
                }
                console.log(`[PRODUCT IMPORT] Processing import for tenant: ${tenantId}`);
                // Process the uploaded file
                const importResult = await processProductImportFile(req.file.path, tenantId, req.scope);
                // Clean up uploaded file
                fs_1.default.unlinkSync(req.file.path);
                console.log('[PRODUCT IMPORT] Import completed:', importResult);
                res.status(200).json({
                    message: 'Product import completed',
                    transaction_id: (0, uuid_1.v4)(),
                    ...importResult,
                });
            }
            catch (processingError) {
                console.error('[PRODUCT IMPORT] Processing error:', processingError);
                // Clean up uploaded file on error
                if (req.file && fs_1.default.existsSync(req.file.path)) {
                    fs_1.default.unlinkSync(req.file.path);
                }
                res.status(500).json({
                    error: 'Import processing failed',
                    message: processingError.message || 'Failed to process import file',
                });
            }
        });
    }
    catch (error) {
        console.error('[PRODUCT IMPORT] Unexpected error:', error);
        res.status(500).json({
            error: 'Import failed',
            message: error.message || 'An unexpected error occurred during import',
        });
    }
}
/**
 * OPTIONS /product-import
 *
 * Handle preflight CORS requests
 */
async function OPTIONS(req, res) {
    // Set CORS headers for preflight requests
    res.setHeader('Access-Control-Allow-Origin', 'http://localhost:3000');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-tenant-id');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours
    res.status(200).end();
}
/**
 * GET /product-import
 *
 * Download Excel template for product import - serves static template file
 */
async function GET(req, res) {
    try {
        console.log('[PRODUCT IMPORT] Serving static Excel template');
        // Set CORS headers explicitly
        res.setHeader('Access-Control-Allow-Origin', 'http://localhost:3000');
        res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-tenant-id');
        res.setHeader('Access-Control-Allow-Credentials', 'true');
        // Path to the static template file
        const templatePath = path_1.default.join(process.cwd(), 'templates', 'product-import-template.xlsx');
        // Check if template file exists
        if (!fs_1.default.existsSync(templatePath)) {
            console.error('[PRODUCT IMPORT] Template file not found:', templatePath);
            res.status(404).json({
                error: 'Template not found',
                message: 'Product import template file is not available',
            });
            return;
        }
        // Get file stats for Content-Length header
        const fileStats = fs_1.default.statSync(templatePath);
        // Set response headers for file download
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', 'attachment; filename="product-import-template.xlsx"');
        res.setHeader('Content-Length', fileStats.size);
        // Stream the file to the response
        const fileStream = fs_1.default.createReadStream(templatePath);
        fileStream.on('error', error => {
            console.error('[PRODUCT IMPORT] Error streaming template file:', error);
            if (!res.headersSent) {
                res.status(500).json({
                    error: 'Template streaming failed',
                    message: 'Failed to stream template file',
                });
            }
        });
        // Pipe the file stream to the response
        fileStream.pipe(res);
        console.log('[PRODUCT IMPORT] Template file served successfully');
    }
    catch (error) {
        console.error('[PRODUCT IMPORT] Error serving template:', error);
        res.status(500).json({
            error: 'Template serving failed',
            message: error.message || 'Failed to serve Excel template',
        });
    }
}
/**
 * Validate products and variants for duplicates before processing
 */
async function validateDuplicates(rawData, tenantId, scope) {
    const errors = [];
    try {
        // Get product service for database queries
        let productService;
        try {
            productService = scope.resolve('product');
        }
        catch (e) {
            console.warn('[DUPLICATE VALIDATION] Product service not available, skipping database validation');
            return errors;
        }
        // For handle+SKU duplicate detection
        const handleSkuToRow = new Map();
        // For title and SKU detection (kept as in original)
        const importTitles = new Set();
        const importSkus = new Set();
        const titleToRow = new Map();
        const skuToRow = new Map();
        rawData.forEach((row, index) => {
            const rowNumber = index + 2; // Excel row number (accounting for header)
            // Generate handle if not provided
            const handle = row.handle ||
                (row.title && typeof row.title === 'string'
                    ? row.title
                        .toLowerCase()
                        .replace(/[^a-z0-9]+/g, '-')
                        .replace(/^-+|-+$/g, '')
                    : `product-${index + 1}`);
            const sku = row.variant_sku || '';
            // Build a unique key for handle + sku
            const handleSkuKey = `${handle}|${sku}`;
            // Check for internal duplicates in import file (handle + sku)
            console.log('handleSkuToRow.has(handleSkuKey)::::::::::', handleSkuToRow.has(handleSkuKey));
            if (handleSkuToRow.has(handleSkuKey)) {
                errors.push({
                    row: rowNumber,
                    field: 'handle',
                    message: `Duplicate handle '${handle}' with SKU '${sku}' found in import file at row ${handleSkuToRow.get(handleSkuKey)}`,
                    value: handle,
                    conflictType: 'duplicate_handle_sku',
                });
            }
            else {
                handleSkuToRow.set(handleSkuKey, rowNumber);
            }
            // Title duplicate detection (optional)
            // if (importTitles.has(row.title)) {
            //   errors.push({
            //     row: rowNumber,
            //     field: 'title',
            //     message: `Duplicate title '${row.title}' found in import file at row ${titleToRow.get(row.title)}`,
            //     value: row.title,
            //     conflictType: 'duplicate_title',
            //   });
            // } else {
            //   importTitles.add(row.title);
            //   titleToRow.set(row.title, rowNumber);
            // }
            // SKU duplicate detection (optional)
            if (row.variant_sku) {
                if (importSkus.has(row.variant_sku)) {
                    errors.push({
                        row: rowNumber,
                        field: 'variant_sku',
                        message: `Duplicate SKU '${row.variant_sku}' found in import file at row ${skuToRow.get(row.variant_sku)}`,
                        value: row.variant_sku,
                        conflictType: 'duplicate_sku',
                    });
                }
                else {
                    importSkus.add(row.variant_sku);
                    skuToRow.set(row.variant_sku, rowNumber);
                }
            }
        });
        // Check for database duplicates
        await validateDatabaseDuplicates(rawData, tenantId, productService, errors);
    }
    catch (error) {
        console.error('[DUPLICATE VALIDATION] Error during validation:', error);
        errors.push({
            row: 0,
            field: 'general',
            message: `Validation error: ${error.message}`,
            value: null,
        });
    }
    return errors;
}
/**
 * Check for duplicates against existing database records
 */
async function validateDatabaseDuplicates(rawData, tenantId, productService, errors) {
    try {
        // Collect unique handles and titles for batch checking
        const uniqueHandles = new Set();
        const uniqueTitles = new Set();
        const uniqueSkus = new Set();
        rawData.forEach((row, index) => {
            const handle = row.handle ||
                (row.title && typeof row.title === 'string'
                    ? row.title
                        .toLowerCase()
                        .replace(/[^a-z0-9]+/g, '-')
                        .replace(/^-+|-+$/g, '')
                    : `product-${index + 1}`);
            uniqueHandles.add(handle);
            if (row.title && typeof row.title === 'string') {
                uniqueTitles.add(row.title);
            }
            if (row.variant_sku) {
                uniqueSkus.add(row.variant_sku);
            }
        });
        // Check for existing products by handle with proper tenant filtering
        if (productService.listProducts) {
            try {
                const existingProductsByHandle = await productService.listProducts({
                    handle: Array.from(uniqueHandles),
                    tenant_id: tenantId, // Use tenant_id directly, not in metadata
                });
                const existingHandles = new Set(existingProductsByHandle.map(p => p.handle));
                rawData.forEach((row, index) => {
                    const rowNumber = index + 2;
                    const handle = row.handle ||
                        (row.title && typeof row.title === 'string'
                            ? row.title
                                .toLowerCase()
                                .replace(/[^a-z0-9]+/g, '-')
                                .replace(/^-+|-+$/g, '')
                            : `product-${index + 1}`);
                    if (existingHandles.has(handle)) {
                        const existingProduct = existingProductsByHandle.find(p => p.handle === handle);
                        errors.push({
                            row: rowNumber,
                            field: 'handle',
                            message: `Product with handle '${handle}' already exists in database`,
                            value: handle,
                            conflictType: 'duplicate_handle',
                            existingRecord: {
                                id: existingProduct.id,
                                title: existingProduct.title,
                                handle: existingProduct.handle,
                            },
                        });
                    }
                });
            }
            catch (e) {
                console.warn('[DUPLICATE VALIDATION] Could not check existing products by handle:', e.message);
            }
            // Check for existing products by title with proper tenant filtering
            try {
                const existingProductsByTitle = await productService.listProducts({
                    title: Array.from(uniqueTitles),
                    tenant_id: tenantId, // Use tenant_id directly, not in metadata
                });
                const existingTitles = new Set(existingProductsByTitle.map(p => p.title));
                rawData.forEach((row, index) => {
                    const rowNumber = index + 2;
                    if (row.title && typeof row.title === 'string' && existingTitles.has(row.title)) {
                        const existingProduct = existingProductsByTitle.find(p => p.title === row.title);
                        errors.push({
                            row: rowNumber,
                            field: 'title',
                            message: `Product with title '${row.title}' already exists in database`,
                            value: row.title,
                            conflictType: 'duplicate_title',
                            existingRecord: {
                                id: existingProduct.id,
                                title: existingProduct.title,
                                handle: existingProduct.handle,
                            },
                        });
                    }
                });
            }
            catch (e) {
                console.warn('[DUPLICATE VALIDATION] Could not check existing products by title:', e.message);
            }
        }
        // Check for existing variants by SKU
        if (productService.listVariants && uniqueSkus.size > 0) {
            try {
                const existingVariants = await productService.listVariants({
                    sku: Array.from(uniqueSkus),
                });
                const existingSkus = new Set(existingVariants.map(v => v.sku));
                rawData.forEach((row, index) => {
                    const rowNumber = index + 2;
                    if (row.variant_sku && existingSkus.has(row.variant_sku)) {
                        const existingVariant = existingVariants.find(v => v.sku === row.variant_sku);
                        errors.push({
                            row: rowNumber,
                            field: 'variant_sku',
                            message: `Variant with SKU '${row.variant_sku}' already exists in database`,
                            value: row.variant_sku,
                            conflictType: 'duplicate_sku',
                            existingRecord: {
                                id: existingVariant.id,
                                sku: existingVariant.sku,
                                productId: existingVariant.product_id,
                            },
                        });
                    }
                });
            }
            catch (e) {
                console.warn('[DUPLICATE VALIDATION] Could not check existing variants by SKU:', e.message);
            }
        }
    }
    catch (error) {
        console.error('[DUPLICATE VALIDATION] Database validation error:', error);
    }
}
// Helper function to process the import file
async function processProductImportFile(filePath, tenantId, scope) {
    const errors = [];
    const createdProducts = [];
    const createdCategories = [];
    let successfulImports = 0;
    let failedImports = 0;
    let createdVariants = 0;
    const categoryStats = {
        existing_categories_used: 0,
        new_categories_created: 0,
        subcategories_created: 0,
    };
    try {
        // Determine file type and parse accordingly
        const fileExtension = path_1.default.extname(filePath).toLowerCase();
        let rawData;
        if (fileExtension === '.csv') {
            // Parse CSV file
            const csvContent = fs_1.default.readFileSync(filePath, 'utf-8');
            const workbook = XLSX.read(csvContent, { type: 'string' });
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            const parsedData = XLSX.utils.sheet_to_json(worksheet);
            rawData = parsedData.map(row => mapCsvFieldsToExpected(row));
        }
        else {
            // Parse Excel file
            const workbook = XLSX.readFile(filePath);
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            const parsedData = XLSX.utils.sheet_to_json(worksheet);
            rawData = parsedData.map(row => mapCsvFieldsToExpected(row));
        }
        if (!rawData || rawData.length === 0) {
            throw new Error('No data found in the uploaded file');
        }
        console.log(`[PRODUCT IMPORT] Processing ${rawData.length} rows for tenant: ${tenantId}`);
        // Perform comprehensive duplicate validation before processing
        console.log('[PRODUCT IMPORT] Starting duplicate validation...');
        const duplicateErrors = await validateDuplicates(rawData, tenantId, scope);
        if (duplicateErrors.length > 0) {
            console.log(`[PRODUCT IMPORT] Found ${duplicateErrors.length} duplicate validation errors`);
            // Return early with validation errors - don't process any products
            return {
                success: false,
                total_rows: rawData.length,
                successful_imports: 0,
                failed_imports: rawData.length,
                errors: duplicateErrors,
                created_products: [],
                created_categories: [],
                created_variants: 0,
                category_stats: {
                    existing_categories_used: 0,
                    new_categories_created: 0,
                    subcategories_created: 0,
                },
            };
        }
        console.log('[PRODUCT IMPORT] Duplicate validation passed, proceeding with import...');
        // Use direct database operations for better control over tenant isolation
        let productService;
        try {
            // Try to resolve the product service
            productService = scope.resolve('product');
            console.log('[PRODUCT IMPORT] Successfully resolved product service');
        }
        catch (e) {
            console.error('[PRODUCT IMPORT] Failed to resolve product service:', e);
            // We'll use direct database operations instead
            productService = null;
        }
        // Group rows by product handle for multi-variant support
        const productGroups = new Map();
        for (const [index, row] of rawData.entries()) {
            const handle = row.handle ||
                (row.title && typeof row.title === 'string'
                    ? row.title
                        .toLowerCase()
                        .replace(/[^a-z0-9]+/g, '-')
                        .replace(/^-+|-+$/g, '')
                    : `product-${index + 1}`);
            if (!productGroups.has(handle)) {
                productGroups.set(handle, []);
            }
            productGroups.get(handle).push(row);
        }
        console.log(`[PRODUCT IMPORT] Grouped ${rawData.length} rows into ${productGroups.size} products`);
        // Process each product group
        let rowNumber = 2; // Excel row number (accounting for header)
        for (const [handle, rows] of productGroups) {
            try {
                // Use the first row as the base product data
                const mergedRows = await mergeProductVariants(rows);
                const baseRow = mergedRows[0];
                // Validate required fields for base product
                const validationErrors = validateProductRow(baseRow, rowNumber);
                console.log('validationErrors::::::::::::::', validationErrors);
                if (validationErrors.length > 0) {
                    errors.push(...validationErrors);
                    failedImports += rows.length;
                    rowNumber += rows.length;
                    continue;
                }
                // Check if product already exists
                let existingProduct = null;
                try {
                    if (productService.listProducts) {
                        const existingProducts = await productService.listProducts({ handle }, { limit: 1 });
                        existingProduct = existingProducts.length > 0 ? existingProducts[0] : null;
                    }
                }
                catch (e) {
                    // Product doesn't exist, continue with creation
                }
                let product;
                if (existingProduct) {
                    console.log(`[PRODUCT IMPORT] Product with handle '${handle}' already exists, adding variants`);
                    product = existingProduct;
                }
                else {
                    // Create new product using direct database operations
                    try {
                        const createResult = await createProductWithVariantsEnhanced(baseRow, tenantId, scope);
                        product = createResult.product;
                        // Track category statistics
                        if (createResult.categoryResult) {
                            createdCategories.push(...createResult.categoryResult.createdCategories);
                            categoryStats.existing_categories_used +=
                                createResult.categoryResult.stats.existing_categories_used;
                            categoryStats.new_categories_created +=
                                createResult.categoryResult.stats.new_categories_created;
                            categoryStats.subcategories_created +=
                                createResult.categoryResult.stats.subcategories_created;
                        }
                        // Track variant count
                        createdVariants += createResult.variantCount;
                        console.log(`[PRODUCT IMPORT] Successfully created product: ${product.title} (${product.id}) with ${createResult.variantCount} variants`);
                    }
                    catch (createError) {
                        console.error('[PRODUCT IMPORT] Product creation failed:', createError);
                        throw new Error(`Failed to create product: ${createError.message}`);
                    }
                }
                // Track success - variants are created within createProductWithVariants
                if (!existingProduct) {
                    createdProducts.push(product.id);
                }
                successfulImports += rows.length; // Count all rows as successful since variants are created with product
            }
            catch (groupError) {
                console.error(`[PRODUCT IMPORT] Error processing product group '${handle}':`, groupError);
                // Add errors for all rows in this group
                for (let i = 0; i < rows.length; i++) {
                    errors.push({
                        row: rowNumber + i,
                        field: 'general',
                        message: groupError.message || 'Failed to process product group',
                        value: rows[i].title,
                    });
                }
                failedImports += rows.length;
            }
            rowNumber += rows.length;
        }
        // Apply auto-configuration to all created products
        console.log(`[PRODUCT IMPORT] Applying auto-configuration to ${createdProducts.length} created products`);
        let autoConfigResults = {
            successful: 0,
            failed: 0,
            errors: [],
        };
        if (createdProducts.length > 0) {
            try {
                const workflowResult = await (0, product_auto_config_1.applyAutoConfigToExistingProductsWorkflow)(scope).run({
                    input: {
                        productIds: createdProducts,
                        tenantId: tenantId,
                    },
                });
                autoConfigResults = workflowResult.result.configurationResults;
                console.log(`[PRODUCT IMPORT] Auto-configuration completed:`, autoConfigResults);
            }
            catch (autoConfigError) {
                console.error('[PRODUCT IMPORT] Auto-configuration failed:', autoConfigError);
                autoConfigResults.failed = createdProducts.length;
                autoConfigResults.errors.push(`Auto-configuration failed: ${autoConfigError.message}`);
            }
        }
        return {
            success: errors.length === 0,
            total_rows: rawData.length,
            successful_imports: successfulImports,
            failed_imports: failedImports,
            errors,
            created_products: createdProducts,
            created_categories: createdCategories,
            created_variants: createdVariants,
            category_stats: categoryStats,
            auto_configuration: autoConfigResults,
        };
    }
    catch (error) {
        console.error('[PRODUCT IMPORT] File processing error:', error);
        throw new Error(`Failed to process import file: ${error.message}`);
    }
}
// Enhanced helper function to create product with variants using direct database operations
async function createProductWithVariantsEnhanced(row, tenantId, _scope) {
    try {
        // Use DirectDatabaseService for tenant-aware database operations
        const dbService = new direct_database_service_1.DirectDatabaseService(tenantId);
        // Generate product ID and handle
        const productId = `prod_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        const handle = row.handle ||
            (row.title && typeof row.title === 'string'
                ? row.title
                    .toLowerCase()
                    .replace(/[^a-z0-9]+/g, '-')
                    .replace(/^-+|-+$/g, '')
                : `product-${Date.now()}`);
        // Create product in database using correct Medusa table name
        const productQuery = `
      INSERT INTO product (
        id, tenant_id, title, handle, description, status,
        metadata, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
      RETURNING *
    `;
        // Enhanced product metadata structure as per requirements
        const productMetadata = {
            tenant_id: tenantId,
            additional_data: {
                product_prices: row.variants && row.variants.length > 0
                    ? [
                        {
                            sale_price: Number(row.variants[0].sale_price) || 0,
                            original_price: Number(row.variants[0].original_price) || 0,
                        },
                    ]
                    : [
                        {
                            sale_price: Number(row.sale_price) || 0,
                            original_price: Number(row.original_price) || 0,
                        },
                    ],
                product_features: row.product_features || '',
                product_overview: row.product_overview || '',
                product_quantity: Number(row.product_quantity) || 0,
                product_specifications: row.product_specification || '',
                product_inventory_status: row.product_inventory_status || 'in_stock',
            },
            hsn_code: row.ondc_hsn_code,
        };
        const productValues = [
            productId,
            tenantId,
            row.title,
            handle,
            row.description || '',
            row.status || 'published',
            JSON.stringify(productMetadata),
        ];
        const productResult = await dbService.query(productQuery, productValues);
        const product = productResult.rows[0];
        console.log(`[PRODUCT IMPORT] Created product: ${product.id}`);
        // Create variants using correct Supabase table name and schema
        const variants = row.variants && row.variants.length > 0 ? row.variants : [row];
        let variantCount = 0;
        for (const variant of variants) {
            const variantId = `variant_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
            const variantQuery = `
        INSERT INTO product_variant (
          id, product_id, title, sku, tenant_id, metadata,
          weight, width, length, height, manage_inventory,
          created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW(), NOW())
        RETURNING *
      `;
            // Enhanced variant metadata structure as per requirements
            // Handle multi-currency prices from CSV
            const priceINR = variant['variant price inr'] || variant.variant_price || variant.sale_price || 0;
            const priceEUR = variant['variant price eur'] || 0;
            const priceUSD = variant['variant price usd'] || 0;
            const variantMetadata = {
                tenant_id: tenantId,
                sale_price: Number(priceINR) || 0,
                original_price: Number(variant.original_price) || Number(variant.variant_compare_at_price) || 0,
                product_quantity: Number(variant.variant_inventory_quantity) || Number(variant.stock_quantity) || 0,
                product_inventory_status: variant.inventory_status || variant.product_inventory_status || 'in_stock',
                // Store multi-currency prices
                prices: {
                    inr: Number(priceINR) || 0,
                    eur: Number(priceEUR) || 0,
                    usd: Number(priceUSD) || 0,
                },
            };
            const variantValues = [
                variantId,
                productId,
                variant.variant_title || 'Default Variant',
                variant.variant_sku || `sku_${productId}_${Date.now()}`,
                tenantId,
                JSON.stringify(variantMetadata),
                convertDimensionToInteger(variant.variant_weight),
                convertDimensionToInteger(variant.variant_width),
                convertDimensionToInteger(variant.variant_length),
                convertDimensionToInteger(variant.variant_height),
                false, // manage_inventory: false as per requirements
            ];
            await dbService.query(variantQuery, variantValues);
            console.log(`[PRODUCT IMPORT] Created variant: ${variantId}`);
            variantCount++;
        }
        // Handle category and subcategory associations if provided
        let categoryResult;
        if (row.category || row.subcategory) {
            try {
                categoryResult = await handleCategoryAssociation(row, productId, tenantId, dbService);
            }
            catch (categoryError) {
                console.warn(`[PRODUCT IMPORT] Category association failed: ${categoryError.message}`);
                // Continue without failing the entire import
            }
        }
        return {
            product,
            variantCount,
            categoryResult,
        };
    }
    catch (error) {
        console.error('[PRODUCT IMPORT] Error creating product with variants:', error);
        throw error;
    }
}
// Helper function to validate product row data
function validateProductRow(row, rowNumber) {
    const errors = [];
    // Validate required fields
    if (!row.title || row.title.trim() === '') {
        errors.push({
            row: rowNumber,
            field: 'title',
            message: 'Product title is required',
            value: row.title,
        });
    }
    // Validate numeric fields
    if (row.variant_price !== undefined &&
        (isNaN(Number(row.variant_price)) || Number(row.variant_price) < 0)) {
        errors.push({
            row: rowNumber,
            field: 'variant_price',
            message: 'Variant price must be a valid positive number',
            value: row.variant_price,
        });
    }
    if (row.variant_weight !== undefined &&
        (isNaN(Number(row.variant_weight)) || Number(row.variant_weight) < 0)) {
        errors.push({
            row: rowNumber,
            field: 'variant_weight',
            message: 'Variant weight must be a valid positive number',
            value: row.variant_weight,
        });
    }
    return errors;
}
// Enhanced helper function to handle category and subcategory associations with hierarchical support
async function handleCategoryAssociation(row, productId, tenantId, dbService) {
    const result = {
        categoryId: undefined,
        subcategoryId: undefined,
        createdCategories: [],
        stats: {
            existing_categories_used: 0,
            new_categories_created: 0,
            subcategories_created: 0,
        },
    };
    try {
        // Handle main category first
        if (row.category) {
            console.log(`[PRODUCT IMPORT] Processing category: ${row.category} for tenant: ${tenantId}`);
            const categoryResult = await findOrCreateCategory(row.category, null, // No parent for main category
            tenantId, dbService);
            result.categoryId = categoryResult.categoryId;
            if (categoryResult.wasCreated) {
                result.createdCategories.push(categoryResult.categoryId);
                result.stats.new_categories_created++;
            }
            else {
                result.stats.existing_categories_used++;
            }
            // Associate product with main category
            await associateProductWithCategory(productId, categoryResult.categoryId, tenantId, dbService);
        }
        // Handle subcategory if provided
        if (row.subcategory) {
            console.log(`[PRODUCT IMPORT] Processing subcategory: ${row.subcategory} for tenant: ${tenantId}`);
            // Determine parent category ID
            const parentCategoryId = result.categoryId ||
                (row.parent_category
                    ? (await findOrCreateCategory(row.parent_category, null, tenantId, dbService)).categoryId
                    : null);
            const subcategoryResult = await findOrCreateCategory(row.subcategory, parentCategoryId, tenantId, dbService);
            result.subcategoryId = subcategoryResult.categoryId;
            if (subcategoryResult.wasCreated) {
                result.createdCategories.push(subcategoryResult.categoryId);
                result.stats.subcategories_created++;
            }
            else {
                result.stats.existing_categories_used++;
            }
            // Associate product with subcategory (in addition to main category)
            await associateProductWithCategory(productId, subcategoryResult.categoryId, tenantId, dbService);
        }
        console.log(`[PRODUCT IMPORT] Category association completed:`, result.stats);
        return result;
    }
    catch (error) {
        console.error(`[PRODUCT IMPORT] Error handling category association: ${error.message}`);
        throw error;
    }
}
// Helper function to find or create a category
async function findOrCreateCategory(categoryName, parentCategoryId, tenantId, dbService) {
    // Check if category exists for this tenant
    const existingCategoryQuery = `
    SELECT id, name FROM product_category
    WHERE name = $1 AND tenant_id = $2 AND parent_category_id ${parentCategoryId ? '= $3' : 'IS NULL'}
    LIMIT 1
  `;
    const queryParams = parentCategoryId
        ? [categoryName, tenantId, parentCategoryId]
        : [categoryName, tenantId];
    const existingCategoryResult = await dbService.query(existingCategoryQuery, queryParams);
    if (existingCategoryResult.rows.length > 0) {
        // Category exists, use it
        const categoryId = existingCategoryResult.rows[0].id;
        console.log(`[PRODUCT IMPORT] Found existing category: ${categoryName} (${categoryId})`);
        return { categoryId, wasCreated: false };
    }
    else {
        // Category doesn't exist, create it
        const categoryId = `pcat_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
        const handle = categoryName
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-+|-+$/g, '');
        // Build mpath for hierarchical structure
        let mpath = categoryId;
        if (parentCategoryId) {
            // Get parent's mpath and append current category ID
            const parentQuery = `SELECT mpath FROM product_category WHERE id = $1 AND tenant_id = $2`;
            const parentResult = await dbService.query(parentQuery, [parentCategoryId, tenantId]);
            if (parentResult.rows.length > 0) {
                mpath = `${parentResult.rows[0].mpath}.${categoryId}`;
            }
        }
        const createCategoryQuery = `
      INSERT INTO product_category (
        id, tenant_id, name, handle, description, is_active,
        rank, parent_category_id, mpath, metadata, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW())
      RETURNING id, name
    `;
        const categoryValues = [
            categoryId,
            tenantId,
            categoryName,
            handle,
            `Auto-created ${parentCategoryId ? 'subcategory' : 'category'} for ${categoryName}`,
            true,
            0,
            parentCategoryId,
            mpath,
            JSON.stringify({
                created_by: 'product_import',
                auto_created: true,
                tenant_id: tenantId,
                is_subcategory: !!parentCategoryId,
            }),
        ];
        await dbService.query(createCategoryQuery, categoryValues);
        console.log(`[PRODUCT IMPORT] Created new ${parentCategoryId ? 'subcategory' : 'category'}: ${categoryName} (${categoryId})`);
        return { categoryId, wasCreated: true };
    }
}
// Helper function to associate product with category
async function associateProductWithCategory(productId, categoryId, tenantId, dbService) {
    const associationQuery = `
    INSERT INTO product_category_product (product_id, product_category_id, tenant_id, created_at, updated_at)
    VALUES ($1, $2, $3, NOW(), NOW())
    ON CONFLICT (product_id, product_category_id) DO NOTHING
  `;
    await dbService.query(associationQuery, [productId, categoryId, tenantId]);
    console.log(`[PRODUCT IMPORT] Associated product ${productId} with category ${categoryId}`);
}
// Helper function to safely convert price values to integers (cents/paise)
function convertPriceToInteger(value) {
    if (value === undefined || value === null || value === '') {
        return 0;
    }
    const numValue = Number(value);
    if (isNaN(numValue)) {
        return 0;
    }
    // Convert to integer (assuming prices are in major currency units, convert to minor units)
    // For example: 10.50 USD -> 1050 cents, 599 INR -> 59900 paise
    return Math.round(numValue * 100);
}
// Helper function to safely convert dimension values to integers (millimeters)
function convertDimensionToInteger(value) {
    if (value === undefined || value === null || value === '') {
        return null;
    }
    const numValue = Number(value);
    if (isNaN(numValue)) {
        return null;
    }
    // Convert to integer (assuming dimensions are in major units, convert to minor units)
    // For example: 1.2 cm -> 12 mm
    return Math.round(numValue * 10);
}
// Helper function to map CSV field names to our expected field names
function mapCsvFieldsToExpected(row) {
    const fieldMapping = {
        // Product fields
        'Product Id': 'id',
        'Product Handle': 'handle',
        'Product Title': 'title',
        'Product Subtitle': 'subtitle',
        'Product Description': 'description',
        'Product Status': 'status',
        'Product Thumbnail': 'thumbnail',
        'Product Weight': 'weight',
        'Product Length': 'length',
        'Product Width': 'width',
        'Product Height': 'height',
        'Product HS Code': 'hs_code',
        'Product Origin Country': 'origin_country',
        'Product MID Code': 'mid_code',
        'Product Material': 'material',
        'Product Collection Id': 'collection_id',
        'Product Type Id': 'type_id',
        'Product Tag 1': 'tag_1',
        'Product Discountable': 'discountable',
        'Product External Id': 'external_id',
        // Variant fields
        'Variant Id': 'variant_id',
        'Variant Title': 'variant_title',
        'Variant SKU': 'variant_sku',
        'Variant Barcode': 'variant_barcode',
        'Variant Allow Backorder': 'variant_allow_backorder',
        'Variant Manage Inventory': 'variant_manage_inventory',
        'Variant Weight': 'variant_weight',
        'Variant Length': 'variant_length',
        'Variant Width': 'variant_width',
        'Variant Height': 'variant_height',
        'Variant HS Code': 'variant_hs_code',
        'Variant Origin Country': 'variant_origin_country',
        'Variant MID Code': 'variant_mid_code',
        'Variant Material': 'variant_material',
        'Variant Price INR': 'variant price inr',
        'Variant Price EUR': 'variant price eur',
        'Variant Price USD': 'variant price usd',
        'Variant Option 1 Name': 'variant_option_1_name',
        'Variant Option 1 Value': 'variant_option_1_value',
        // Image fields
        'Product Image 1 Url': 'image_1_url',
        'Product Image 2 Url': 'image_2_url',
    };
    const mappedRow = {};
    // Map known fields
    for (const [csvField, expectedField] of Object.entries(fieldMapping)) {
        if (row[csvField] !== undefined) {
            mappedRow[expectedField] = row[csvField];
        }
    }
    // Copy any unmapped fields as-is (for backward compatibility)
    for (const [key, value] of Object.entries(row)) {
        if (!fieldMapping[key] && !mappedRow[key.toLowerCase()]) {
            mappedRow[key.toLowerCase()] = value;
        }
    }
    return mappedRow;
}
// Removed unused buildProductData function - using direct database operations instead
// Removed unused buildVariantData function - using direct database operations instead
// Removed unused handleCategoryIntegration function - using direct database operations instead
async function mergeProductVariants(products) {
    // If single product, just return it as-is with variants array
    if (products.length === 1) {
        const product = products[0];
        return [
            {
                ...product,
                variants: [product], // Single variant
            },
        ];
    }
    if (!Array.isArray(products)) {
        throw new TypeError('Input must be an array of product objects');
    }
    // Define which fields belong in each category
    const variantFields = new Set([
        // Standard variant fields
        'variant_title',
        'variant_sku',
        'variant_price',
        'variant_compare_at_price',
        'original_price',
        'sale_price',
        'variant_weight',
        'variant_length',
        'variant_width',
        'variant_height',
        'variant_inventory_quantity',
        'variant_allow_backorder',
        'variant_manage_inventory',
        'inventory_status',
        'stock_quantity',
        // Multi-currency price fields (variant-specific)
        'variant price inr',
        'variant price eur',
        'variant price usd',
        // Physical properties that can vary by variant
        'weight',
        'length',
        'width',
        'height',
        // Variant-specific identifiers and options
        'variant_id',
        'variant_barcode',
        'variant_hs_code',
        'variant_origin_country',
        'variant_mid_code',
        'variant_material',
        'variant_option_1_name',
        'variant_option_1_value',
        'variant_option_2_name',
        'variant_option_2_value',
        'variant_option_3_name',
        'variant_option_3_value',
    ]);
    // Collect all field names present in the first object
    const allFields = Object.keys(products[0] || {});
    const productFields = allFields.filter(f => !variantFields.has(f));
    // Group products by composite key `${title}|||${handle}`
    const groups = products.reduce((acc, item, idx) => {
        if (typeof item.title !== 'string' || typeof item.handle !== 'string') {
            throw new Error(`Missing title or handle in item at index ${idx}`);
        }
        const key = `${item.title}|||${item.handle}`;
        acc[key] = acc[key] || [];
        acc[key].push(item);
        return acc;
    }, {});
    // Build merged result
    return Object.entries(groups).map(([_key, items]) => {
        // Validate non-variant fields
        const reference = items[0];
        for (let i = 1; i < items.length; i++) {
            for (const field of productFields) {
                if (items[i][field] !== reference[field]) {
                    throw new Error(`Field "${field}" mismatch for product "${reference.title}" (handle "${reference.handle}"): ` +
                        `value1="${reference[field]}", value2="${items[i][field]}"`);
                }
            }
        }
        // Build merged object
        const merged = { ...reference };
        // Extract variants from all items
        merged.variants = items.map(item => ({
            ...item,
            // Keep variant-specific fields
            variant_title: item.variant_title,
            variant_sku: item.variant_sku,
            variant_price: item.variant_price,
            variant_compare_at_price: item.variant_compare_at_price,
            original_price: item.original_price,
            sale_price: item.sale_price,
            variant_weight: item.variant_weight,
            variant_length: item.variant_length,
            variant_width: item.variant_width,
            variant_height: item.variant_height,
            variant_inventory_quantity: item.variant_inventory_quantity,
            variant_allow_backorder: item.variant_allow_backorder,
            variant_manage_inventory: item.variant_manage_inventory,
            inventory_status: item.inventory_status,
            stock_quantity: item.stock_quantity,
        }));
        return merged;
    });
}
//# sourceMappingURL=data:application/json;base64,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