"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
async function GET(req, res) {
    console.log(`🚀 [TENANT FILTER] === CUSTOM INVENTORY ENDPOINT CALLED ===`);
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        console.log(`🔍 [TENANT FILTER] Processing inventory request for tenant: ${tenantId}`);
        // Get query parameters
        const { limit = 50, offset = 0, fields, order = '-created_at', ...filters } = req.query;
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
        });
        let inventory_items = [];
        let count = 0;
        try {
            await client.connect();
            console.log(`🔗 [TENANT FILTER] Connected to database directly`);
            // Get total count for this tenant
            const countResult = await client.query('SELECT COUNT(*) as total FROM inventory_item WHERE tenant_id = $1', [tenantId]);
            count = parseInt(countResult.rows[0]?.total || 0);
            console.log(`📊 [TENANT FILTER] Total inventory items for tenant ${tenantId}: ${count}`);
            // Get inventory items with pagination
            const result = await client.query(`
        SELECT 
          id, sku, title, description, thumbnail,
          created_at, updated_at, tenant_id, metadata,
          requires_shipping, weight, length, height, width
        FROM inventory_item 
        WHERE tenant_id = $1 
        ORDER BY created_at DESC 
        LIMIT $2 OFFSET $3
      `, [tenantId, parseInt(limit), parseInt(offset)]);
            inventory_items = result.rows || [];
            console.log(`📦 [TENANT FILTER] Retrieved ${inventory_items.length} inventory items`);
            await client.end();
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            await client.end().catch(() => { });
        }
        // Return response in Medusa format
        const response = {
            inventory_items,
            count: inventory_items.length,
            offset: parseInt(offset),
            limit: parseInt(limit),
            _tenant: {
                id: tenantId,
                filtered: true,
                method: 'direct_db_connection',
                total_in_db: count
            }
        };
        // Add tenant headers for debugging
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Filtered', 'true');
        res.setHeader('X-Inventory-Count', inventory_items.length.toString());
        console.log(`📤 [TENANT FILTER] Returning ${inventory_items.length} inventory items for tenant ${tenantId}`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error in inventory endpoint:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to fetch inventory items',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_filter_error',
                timestamp: new Date().toISOString()
            }
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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