"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
/**
 * GET /admin/analytics
 *
 * Analytics API overview endpoint that provides information about available analytics endpoints.
 * This serves as a discovery endpoint for the analytics API system.
 */
async function GET(req, res) {
    try {
        const analyticsOverview = {
            message: "ONDC Seller Analytics API",
            version: "1.0.0",
            description: "Comprehensive analytics API system for multi-tenant e-commerce operations",
            endpoints: {
                dashboard: {
                    path: "/admin/analytics/dashboard",
                    method: "GET",
                    description: "Complete dashboard overview with key metrics and trends",
                    parameters: ["period", "sales_channel_id", "tenant_id", "currency"]
                },
                sales: {
                    path: "/admin/analytics/sales",
                    method: "GET",
                    description: "Detailed sales performance analysis with trends and breakdowns",
                    parameters: ["period", "group_by", "sales_channel_id", "tenant_id", "category_id"]
                },
                customers: {
                    path: "/admin/analytics/customers",
                    method: "GET",
                    description: "Customer behavior analysis and segmentation",
                    parameters: ["period", "segment", "tenant_id", "sales_channel_id"]
                },
                products: {
                    path: "/admin/analytics/products",
                    method: "GET",
                    description: "Product performance and inventory insights",
                    parameters: ["period", "limit", "sort_by", "category_id", "tenant_id", "sales_channel_id"]
                },
                inventory: {
                    path: "/admin/analytics/inventory",
                    method: "GET",
                    description: "Inventory management and stock analysis",
                    parameters: ["location_id", "tenant_id", "low_stock_threshold", "category_id"]
                },
                kpi: {
                    path: "/admin/analytics/kpi",
                    method: "GET",
                    description: "Key Performance Indicators with targets and scoring",
                    parameters: ["period", "tenant_id", "sales_channel_id", "compare_previous"]
                }
            },
            authentication: {
                required: true,
                type: "Bearer Token",
                header: "Authorization: Bearer <jwt_token>"
            },
            features: [
                "Multi-tenant data isolation",
                "Sales channel filtering",
                "Real-time analytics",
                "Period-over-period comparisons",
                "Target tracking and scoring",
                "Inventory alerts and monitoring",
                "Customer segmentation",
                "Product performance analysis"
            ],
            response_format: {
                success: true,
                timestamp: "ISO 8601 timestamp",
                data: "Endpoint-specific data structure",
                metadata: "Additional response metadata"
            },
            error_format: {
                success: false,
                error: {
                    code: "Error code",
                    message: "Human-readable error message",
                    details: "Additional error details"
                }
            }
        };
        res.status(200).json(analyticsOverview);
    }
    catch (error) {
        console.error('Analytics overview error:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'ANALYTICS_OVERVIEW_ERROR',
                message: 'Failed to fetch analytics overview',
                details: error instanceof Error ? error.message : 'Unknown error',
            },
            timestamp: new Date().toISOString()
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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