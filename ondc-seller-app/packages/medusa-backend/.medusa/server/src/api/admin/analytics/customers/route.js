"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
const zod_1 = require("zod");
// Query parameter validation schema
const CustomerQuerySchema = zod_1.z.object({
    period: zod_1.z.enum(['7d', '30d', '90d', '1y']).default('30d'),
    segment: zod_1.z.enum(['all', 'new', 'repeat', 'vip', 'inactive']).default('all'),
    tenant_id: zod_1.z.string().optional(),
    sales_channel_id: zod_1.z.string().optional(),
});
/**
 * GET /admin/analytics/customers
 *
 * Customer analytics endpoint that provides:
 * - Customer summary metrics
 * - Customer segmentation analysis
 * - Customer acquisition trends
 * - Customer lifetime value analysis
 * - Top customers by value
 * - Geographic distribution
 */
async function GET(req, res) {
    try {
        // Validate query parameters
        const query = CustomerQuerySchema.parse(req.query);
        const { period, segment, tenant_id, sales_channel_id } = query;
        // Get services
        const customerService = req.scope.resolve("customerService");
        const orderService = req.scope.resolve("orderService");
        // Calculate date range
        const endDate = new Date();
        const startDate = new Date();
        switch (period) {
            case '7d':
                startDate.setDate(endDate.getDate() - 7);
                break;
            case '30d':
                startDate.setDate(endDate.getDate() - 30);
                break;
            case '90d':
                startDate.setDate(endDate.getDate() - 90);
                break;
            case '1y':
                startDate.setFullYear(endDate.getFullYear() - 1);
                break;
        }
        // Build filters
        const customerFilters = {};
        const orderFilters = {
            created_at: {
                gte: startDate,
                lte: endDate,
            },
            status: ['completed', 'shipped', 'delivered'],
        };
        if (tenant_id) {
            customerFilters.tenant_id = tenant_id;
            orderFilters.tenant_id = tenant_id;
        }
        if (sales_channel_id) {
            orderFilters.sales_channel_id = sales_channel_id;
        }
        // Fetch customers and orders
        const customers = await customerService.list(customerFilters, {
            relations: ["orders"],
        });
        const orders = await orderService.list(orderFilters, {
            relations: ["customer"],
            order: { created_at: "DESC" },
        });
        // Calculate customer metrics
        const totalCustomers = customers.length;
        const customersWithOrders = new Set(orders.map(order => order.customer_id));
        const activeCustomers = customersWithOrders.size;
        // Identify new vs repeat customers
        const customerOrderCounts = new Map();
        const customerRevenue = new Map();
        const customerFirstOrder = new Map();
        const customerLastOrder = new Map();
        orders.forEach(order => {
            const customerId = order.customer_id;
            if (customerId) {
                // Order count
                customerOrderCounts.set(customerId, (customerOrderCounts.get(customerId) || 0) + 1);
                // Revenue
                customerRevenue.set(customerId, (customerRevenue.get(customerId) || 0) + (order.total || 0));
                // First and last order dates
                const orderDate = new Date(order.created_at);
                if (!customerFirstOrder.has(customerId) || orderDate < customerFirstOrder.get(customerId)) {
                    customerFirstOrder.set(customerId, orderDate);
                }
                if (!customerLastOrder.has(customerId) || orderDate > customerLastOrder.get(customerId)) {
                    customerLastOrder.set(customerId, orderDate);
                }
            }
        });
        // Categorize customers
        const newCustomers = Array.from(customerFirstOrder.entries())
            .filter(([_, firstOrder]) => firstOrder >= startDate)
            .map(([customerId]) => customerId);
        const repeatCustomers = Array.from(customerOrderCounts.entries())
            .filter(([_, count]) => count > 1)
            .map(([customerId]) => customerId);
        const vipCustomers = Array.from(customerRevenue.entries())
            .filter(([_, revenue]) => revenue > 10000) // VIP threshold: ₹10,000
            .map(([customerId]) => customerId);
        // Calculate segments
        const segments = [
            {
                segment: 'New Customers',
                count: newCustomers.length,
                revenue: newCustomers.reduce((sum, id) => sum + (customerRevenue.get(id) || 0), 0),
                average_order_value: 0,
                percentage: totalCustomers > 0 ? (newCustomers.length / totalCustomers) * 100 : 0,
                orders_per_customer: 0,
            },
            {
                segment: 'Repeat Customers',
                count: repeatCustomers.length,
                revenue: repeatCustomers.reduce((sum, id) => sum + (customerRevenue.get(id) || 0), 0),
                average_order_value: 0,
                percentage: totalCustomers > 0 ? (repeatCustomers.length / totalCustomers) * 100 : 0,
                orders_per_customer: 0,
            },
            {
                segment: 'VIP Customers',
                count: vipCustomers.length,
                revenue: vipCustomers.reduce((sum, id) => sum + (customerRevenue.get(id) || 0), 0),
                average_order_value: 0,
                percentage: totalCustomers > 0 ? (vipCustomers.length / totalCustomers) * 100 : 0,
                orders_per_customer: 0,
            },
        ];
        // Calculate additional metrics for segments
        segments.forEach(segment => {
            let customerIds = [];
            switch (segment.segment) {
                case 'New Customers':
                    customerIds = newCustomers;
                    break;
                case 'Repeat Customers':
                    customerIds = repeatCustomers;
                    break;
                case 'VIP Customers':
                    customerIds = vipCustomers;
                    break;
            }
            const totalOrders = customerIds.reduce((sum, id) => sum + (customerOrderCounts.get(id) || 0), 0);
            segment.average_order_value = totalOrders > 0 ? segment.revenue / totalOrders : 0;
            segment.orders_per_customer = segment.count > 0 ? totalOrders / segment.count : 0;
        });
        // Customer acquisition trends
        const acquisitionTrends = [];
        const days = Math.min(30, Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)));
        for (let i = days - 1; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const dayStart = new Date(date.setHours(0, 0, 0, 0));
            const dayEnd = new Date(date.setHours(23, 59, 59, 999));
            const dayNewCustomers = Array.from(customerFirstOrder.entries())
                .filter(([_, firstOrder]) => firstOrder >= dayStart && firstOrder <= dayEnd)
                .length;
            const dayRepeatCustomers = orders
                .filter(order => {
                const orderDate = new Date(order.created_at);
                return orderDate >= dayStart && orderDate <= dayEnd &&
                    (customerOrderCounts.get(order.customer_id) || 0) > 1;
            })
                .map(order => order.customer_id)
                .filter((id, index, arr) => arr.indexOf(id) === index)
                .length;
            acquisitionTrends.push({
                date: dayStart.toISOString().split('T')[0],
                new_customers: dayNewCustomers,
                repeat_customers: dayRepeatCustomers,
                total_customers: dayNewCustomers + dayRepeatCustomers,
            });
        }
        // Customer lifetime value analysis
        const lifetimeValue = [
            {
                segment: 'All Customers',
                average_ltv: Array.from(customerRevenue.values()).reduce((sum, rev) => sum + rev, 0) / customerRevenue.size,
                median_ltv: 0, // Would need proper median calculation
                total_customers: customerRevenue.size,
            },
        ];
        // Top customers
        const topCustomers = Array.from(customerRevenue.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 10)
            .map(([customerId, revenue]) => {
            const customer = customers.find(c => c.id === customerId);
            return {
                customer_id: customerId,
                name: customer ? `${customer.first_name || ''} ${customer.last_name || ''}`.trim() : 'Unknown',
                email: customer?.email || '',
                total_orders: customerOrderCounts.get(customerId) || 0,
                total_spent: revenue,
                last_order_date: customerLastOrder.get(customerId)?.toISOString() || '',
            };
        });
        // Geographic distribution (simplified - would need address data)
        const geographicDistribution = [
            {
                country: 'India',
                customers: totalCustomers,
                revenue: Array.from(customerRevenue.values()).reduce((sum, rev) => sum + rev, 0),
                percentage: 100,
            },
        ];
        // Calculate retention and churn rates
        const retentionRate = totalCustomers > 0 ? (repeatCustomers.length / totalCustomers) * 100 : 0;
        const churnRate = 100 - retentionRate;
        // Build response
        const analytics = {
            summary: {
                total_customers: totalCustomers,
                new_customers: newCustomers.length,
                repeat_customers: repeatCustomers.length,
                customer_retention_rate: retentionRate,
                average_customer_lifetime_value: lifetimeValue[0].average_ltv,
                churn_rate: churnRate,
            },
            segments,
            acquisition_trends: acquisitionTrends,
            lifetime_value: lifetimeValue,
            top_customers: topCustomers,
            geographic_distribution: geographicDistribution,
        };
        res.status(200).json(analytics);
    }
    catch (error) {
        console.error('Customer analytics error:', error);
        res.status(500).json({
            error: 'Failed to fetch customer analytics',
            details: error instanceof Error ? error.message : 'Unknown error',
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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