"use strict";
/**
 * Debug Container Endpoint
 *
 * Helps debug what services and connections are available in the Medusa container.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = void 0;
const GET = async (req, res) => {
    try {
        console.log(`🔍 [DEBUG-CONTAINER] Inspecting Medusa container`);
        const containerInfo = {
            timestamp: new Date().toISOString(),
            availableServices: [],
            databaseConnections: [],
            containerKeys: [],
            errors: []
        };
        // Get all registered services/keys in the container
        try {
            // Try to access the container's registration map
            const container = req.scope;
            // Try different ways to get container information
            if (container && typeof container === 'object') {
                // Get all keys if possible
                try {
                    const keys = Object.keys(container);
                    containerInfo.containerKeys = keys.slice(0, 50); // Limit to first 50 keys
                }
                catch (error) {
                    containerInfo.errors.push(`Error getting container keys: ${error}`);
                }
            }
        }
        catch (error) {
            containerInfo.errors.push(`Error accessing container: ${error}`);
        }
        // Test common service names
        const commonServices = [
            'product', 'customer', 'order', 'cart', 'inventory', 'pricing',
            'productService', 'customerService', 'orderService',
            'manager', 'dbConnection', 'database', 'dataSource',
            '__pg_connection__', 'pgConnection', 'db', 'connection'
        ];
        for (const serviceName of commonServices) {
            try {
                const service = req.scope.resolve(serviceName);
                containerInfo.availableServices.push({
                    name: serviceName,
                    type: typeof service,
                    available: true,
                    hasQuery: typeof service?.query === 'function',
                    constructor: service?.constructor?.name || 'Unknown'
                });
                // Check if this might be a database connection
                if (typeof service?.query === 'function') {
                    containerInfo.databaseConnections.push({
                        name: serviceName,
                        type: service?.constructor?.name || 'Unknown',
                        hasQuery: true
                    });
                }
            }
            catch (error) {
                containerInfo.availableServices.push({
                    name: serviceName,
                    available: false,
                    error: error instanceof Error ? error.message : 'Unknown error'
                });
            }
        }
        // Try to get database manager/connection through different paths
        const dbPaths = [
            'manager',
            'dbConnection',
            'database',
            'dataSource',
            '__pg_connection__'
        ];
        for (const path of dbPaths) {
            try {
                const connection = req.scope.resolve(path);
                if (connection && typeof connection.query === 'function') {
                    // Test a simple query
                    try {
                        await connection.query('SELECT 1 as test');
                        containerInfo.databaseConnections.push({
                            name: path,
                            working: true,
                            tested: true
                        });
                    }
                    catch (queryError) {
                        containerInfo.databaseConnections.push({
                            name: path,
                            working: false,
                            tested: true,
                            queryError: queryError instanceof Error ? queryError.message : 'Unknown error'
                        });
                    }
                }
            }
            catch (error) {
                // Service not available
            }
        }
        console.log(`🔍 [DEBUG-CONTAINER] Container inspection complete`);
        return res.json({
            success: true,
            message: 'Container debug information',
            data: containerInfo
        });
    }
    catch (error) {
        console.error(`❌ [DEBUG-CONTAINER] Debug failed:`, error);
        return res.status(500).json({
            success: false,
            message: 'Container debug failed',
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        });
    }
};
exports.GET = GET;
//# sourceMappingURL=data:application/json;base64,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