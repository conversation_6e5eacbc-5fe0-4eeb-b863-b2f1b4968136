"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.POST = POST;
// Helper function to preprocess data and fix data types
function preprocessUpdateData(data) {
    if (!data)
        return data;
    const processed = { ...data };
    // Fix variants data types
    if (processed.variants && Array.isArray(processed.variants)) {
        processed.variants = processed.variants.map((variant) => {
            const processedVariant = { ...variant };
            // Fix prices data types
            if (processedVariant.prices && Array.isArray(processedVariant.prices)) {
                processedVariant.prices = processedVariant.prices.map((price) => ({
                    ...price,
                    amount: typeof price.amount === 'string' ? parseFloat(price.amount) : price.amount,
                    min_quantity: typeof price.min_quantity === 'string' ? parseInt(price.min_quantity) : price.min_quantity,
                    max_quantity: typeof price.max_quantity === 'string' ? parseInt(price.max_quantity) : price.max_quantity
                }));
            }
            // Fix numeric fields in variant
            if (processedVariant.weight && typeof processedVariant.weight === 'string') {
                processedVariant.weight = parseFloat(processedVariant.weight);
            }
            if (processedVariant.width && typeof processedVariant.width === 'string') {
                processedVariant.width = parseFloat(processedVariant.width);
            }
            if (processedVariant.length && typeof processedVariant.length === 'string') {
                processedVariant.length = parseFloat(processedVariant.length);
            }
            if (processedVariant.height && typeof processedVariant.height === 'string') {
                processedVariant.height = parseFloat(processedVariant.height);
            }
            return processedVariant;
        });
    }
    // Fix product-level numeric fields
    if (processed.weight && typeof processed.weight === 'string') {
        processed.weight = parseFloat(processed.weight);
    }
    if (processed.width && typeof processed.width === 'string') {
        processed.width = parseFloat(processed.width);
    }
    if (processed.length && typeof processed.length === 'string') {
        processed.length = parseFloat(processed.length);
    }
    if (processed.height && typeof processed.height === 'string') {
        processed.height = parseFloat(processed.height);
    }
    return processed;
}
async function POST(req, res) {
    console.log(`🚀 [TENANT FILTER] === CUSTOM PRODUCT UPDATE ENDPOINT (NO VALIDATION) ===`);
    console.log(`🚀 [TENANT FILTER] Raw Body:`, JSON.stringify(req.body, null, 2));
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        // Get product ID from URL params
        const productId = req.params?.id;
        if (!productId) {
            return res.status(400).json({
                error: 'Product ID is required for update',
                tenant_id: tenantId
            });
        }
        console.log(`🔄 [TENANT FILTER] Custom update for product ${productId}, tenant: ${tenantId}`);
        // Get update data from request body and preprocess it
        const rawUpdateData = req.body;
        // Preprocess data to fix data types
        const updateData = preprocessUpdateData(rawUpdateData);
        console.log(`🔧 [TENANT FILTER] Preprocessed data:`, JSON.stringify(updateData, null, 2));
        // Ensure tenant_id is injected and cannot be modified
        const productWithTenant = {
            ...updateData,
            tenant_id: tenantId
        };
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
        });
        let updatedProduct = null;
        try {
            await client.connect();
            console.log(`🔗 [TENANT FILTER] Connected to database for custom update`);
            // Start transaction
            await client.query('BEGIN');
            // First, verify the product belongs to this tenant
            const checkQuery = 'SELECT * FROM product WHERE id = $1 AND tenant_id = $2';
            const checkResult = await client.query(checkQuery, [productId, tenantId]);
            if (checkResult.rows.length === 0) {
                await client.query('ROLLBACK');
                await client.end();
                return res.status(404).json({
                    error: 'Product not found or access denied',
                    product_id: productId,
                    tenant_id: tenantId,
                    _debug: {
                        message: 'Product either does not exist or belongs to a different tenant'
                    }
                });
            }
            // 1. Update main product fields
            const updateProductQuery = `
        UPDATE product 
        SET 
          title = COALESCE($1, title),
          handle = COALESCE($2, handle),
          description = COALESCE($3, description),
          status = COALESCE($4, status),
          thumbnail = COALESCE($5, thumbnail),
          metadata = COALESCE($6, metadata),
          updated_at = NOW()
        WHERE id = $7 AND tenant_id = $8
        RETURNING *
      `;
            const productValues = [
                productWithTenant.title,
                productWithTenant.handle,
                productWithTenant.description,
                productWithTenant.status,
                productWithTenant.thumbnail,
                productWithTenant.metadata ? JSON.stringify({
                    ...productWithTenant.metadata,
                    tenant_id: tenantId
                }) : null,
                productId,
                tenantId
            ];
            const productResult = await client.query(updateProductQuery, productValues);
            updatedProduct = productResult.rows[0];
            // 2. Handle variants update
            if (productWithTenant.variants && productWithTenant.variants.length > 0) {
                console.log(`🔄 [TENANT FILTER] Processing ${productWithTenant.variants.length} variants`);
                for (let i = 0; i < productWithTenant.variants.length; i++) {
                    const variant = productWithTenant.variants[i];
                    console.log(`🔄 [TENANT FILTER] Processing variant ${i + 1}:`, {
                        id: variant.id,
                        title: variant.title,
                        sku: variant.sku,
                        hasId: !!variant.id,
                        action: variant.id ? 'UPDATE' : 'CREATE'
                    });
                    if (variant.id) {
                        // Update existing variant
                        const updateVariantQuery = `
              UPDATE product_variant 
              SET 
                title = COALESCE($1, title),
                sku = COALESCE($2, sku),
                barcode = COALESCE($3, barcode),
                ean = COALESCE($4, ean),
                upc = COALESCE($5, upc),
                material = COALESCE($6, material),
                weight = COALESCE($7, weight),
                width = COALESCE($8, width),
                length = COALESCE($9, length),
                height = COALESCE($10, height),
                metadata = COALESCE($11, metadata),
                updated_at = NOW()
              WHERE id = $12 AND product_id = $13 AND tenant_id = $14
            `;
                        const variantValues = [
                            variant.title,
                            variant.sku,
                            variant.barcode,
                            variant.ean,
                            variant.upc,
                            variant.material,
                            variant.weight,
                            variant.width,
                            variant.length,
                            variant.height,
                            variant.metadata ? JSON.stringify({
                                ...variant.metadata,
                                tenant_id: tenantId
                            }) : null,
                            variant.id,
                            productId,
                            tenantId
                        ];
                        await client.query(updateVariantQuery, variantValues);
                        // Update variant prices if provided
                        if (variant.prices && variant.prices.length > 0) {
                            // First, delete existing prices for this variant
                            await client.query(`
                DELETE FROM price 
                WHERE price_set_id IN (
                  SELECT price_set_id FROM product_variant_price_set 
                  WHERE variant_id = $1
                ) AND tenant_id = $2
              `, [variant.id, tenantId]);
                            // Get the price set for this variant
                            const priceSetResult = await client.query(`
                SELECT price_set_id FROM product_variant_price_set 
                WHERE variant_id = $1
              `, [variant.id]);
                            let priceSetId = priceSetResult.rows[0]?.price_set_id;
                            if (!priceSetId) {
                                // Create new price set if it doesn't exist
                                priceSetId = `pset_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                                await client.query(`
                  INSERT INTO price_set (id, created_at, updated_at) 
                  VALUES ($1, NOW(), NOW())
                `, [priceSetId]);
                                const linkId = `pvps_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                                await client.query(`
                  INSERT INTO product_variant_price_set (id, variant_id, price_set_id, created_at, updated_at) 
                  VALUES ($1, $2, $3, NOW(), NOW())
                `, [linkId, variant.id, priceSetId]);
                            }
                            // Add new prices
                            for (const price of variant.prices) {
                                const priceId = `price_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                                const amount = typeof price.amount === 'string' ? parseFloat(price.amount) : price.amount;
                                await client.query(`
                  INSERT INTO price (
                    id, currency_code, amount, raw_amount, price_set_id, tenant_id, created_at, updated_at
                  ) VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
                `, [
                                    priceId,
                                    price.currency_code || 'inr',
                                    amount,
                                    JSON.stringify({ value: amount.toString(), precision: 20 }),
                                    priceSetId,
                                    tenantId
                                ]);
                            }
                        }
                    }
                    else {
                        // Create new variant
                        console.log(`✨ [TENANT FILTER] Creating new variant:`, {
                            title: variant.title,
                            sku: variant.sku,
                            hasPrices: !!(variant.prices && variant.prices.length > 0),
                            pricesCount: variant.prices?.length || 0
                        });
                        const variantId = `variant_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                        const insertVariantQuery = `
              INSERT INTO product_variant (
                id, title, sku, product_id, tenant_id, 
                metadata, weight, width, length, height,
                barcode, ean, upc, material, variant_rank,
                allow_backorder, manage_inventory, created_at, updated_at
              ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, NOW(), NOW())
              RETURNING *
            `;
                        const variantValues = [
                            variantId,
                            variant.title || 'Default',
                            variant.sku,
                            productId,
                            tenantId,
                            JSON.stringify({
                                ...variant.metadata,
                                tenant_id: tenantId
                            }),
                            variant.weight,
                            variant.width,
                            variant.length,
                            variant.height,
                            variant.barcode,
                            variant.ean,
                            variant.upc,
                            variant.material,
                            0, // variant_rank
                            variant.allow_backorder || false,
                            variant.manage_inventory !== false // default to true
                        ];
                        console.log(`🔧 [TENANT FILTER] Executing variant insert with values:`, {
                            variantId,
                            title: variant.title || 'Default',
                            sku: variant.sku,
                            productId,
                            tenantId
                        });
                        const variantResult = await client.query(insertVariantQuery, variantValues);
                        console.log(`✅ [TENANT FILTER] New variant created successfully:`, variantResult.rows[0]?.id);
                        // Add prices for new variant
                        if (variant.prices && variant.prices.length > 0) {
                            // Create price set for new variant
                            const priceSetId = `pset_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                            await client.query(`
                INSERT INTO price_set (id, created_at, updated_at) 
                VALUES ($1, NOW(), NOW())
              `, [priceSetId]);
                            const linkId = `pvps_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                            await client.query(`
                INSERT INTO product_variant_price_set (id, variant_id, price_set_id, created_at, updated_at) 
                VALUES ($1, $2, $3, NOW(), NOW())
              `, [linkId, variantId, priceSetId]);
                            // Add prices
                            for (const price of variant.prices) {
                                const priceId = `price_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                                const amount = typeof price.amount === 'string' ? parseFloat(price.amount) : price.amount;
                                await client.query(`
                  INSERT INTO price (
                    id, currency_code, amount, raw_amount, price_set_id, tenant_id, created_at, updated_at
                  ) VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
                `, [
                                    priceId,
                                    price.currency_code || 'inr',
                                    amount,
                                    JSON.stringify({ value: amount.toString(), precision: 20 }),
                                    priceSetId,
                                    tenantId
                                ]);
                            }
                        }
                    }
                }
            }
            // 3. Handle images update
            if (productWithTenant.images && productWithTenant.images.length > 0) {
                // Delete existing images for this product and tenant
                await client.query(`
          DELETE FROM image 
          WHERE product_id = $1 AND metadata::jsonb @> $2
        `, [productId, JSON.stringify({ tenant_id: tenantId })]);
                // Add new images
                for (const image of productWithTenant.images) {
                    const imageId = `img_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                    await client.query(`
            INSERT INTO image (
              id, url, product_id, metadata, rank, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
          `, [
                        imageId,
                        image.url,
                        productId,
                        JSON.stringify({ tenant_id: tenantId }),
                        0
                    ]);
                }
            }
            // Commit transaction
            await client.query('COMMIT');
            console.log(`✅ [TENANT FILTER] Custom update completed for product ${productId}`);
            await client.end();
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error during custom update:', dbError);
            await client.query('ROLLBACK').catch(() => { });
            await client.end().catch(() => { });
            throw dbError;
        }
        // Return response in Medusa format
        const response = {
            product: updatedProduct,
            _tenant: {
                id: tenantId,
                validated: true,
                method: 'custom_db_update_no_validation',
                updated_entities: {
                    product: 1,
                    variants: productWithTenant.variants?.length || 0,
                    images: productWithTenant.images?.length || 0
                }
            }
        };
        // Add tenant headers
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Validated', 'true');
        console.log(`📤 [TENANT FILTER] Returning custom updated product for tenant ${tenantId}`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error in custom product update:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to update product via custom endpoint',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'tenant_custom_update_error',
                timestamp: new Date().toISOString(),
                stack: error.stack
            }
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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