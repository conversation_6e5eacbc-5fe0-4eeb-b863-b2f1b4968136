"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
exports.POST = POST;
async function GET(req, res) {
    console.log(`🚀 [TENANT FILTER] === CUSTOM CUSTOMER GET BY ID ENDPOINT CALLED ===`);
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        // Get customer ID from URL params
        const customerId = req.params?.id;
        if (!customerId) {
            return res.status(400).json({
                error: 'Customer ID is required',
                tenant_id: tenantId,
            });
        }
        console.log(`🔍 [TENANT FILTER] Getting customer ${customerId} for tenant: ${tenantId}`);
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
        });
        let customer = null;
        try {
            await client.connect();
            // Get customer with tenant validation
            const result = await client.query(`
        SELECT 
          id, email, first_name, last_name, phone,
          created_at, updated_at, tenant_id, metadata
        FROM customer 
        WHERE id = $1 AND tenant_id = $2
      `, [customerId, tenantId]);
            customer = result.rows[0] || null;
            console.log(`👥 [TENANT FILTER] Retrieved customer: ${customer ? 'Found' : 'Not Found'}`);
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            throw dbError;
        }
        finally {
            // Always close the database connection
            try {
                await client.end();
                console.log(`🔗 [TENANT FILTER] Database connection closed`);
            }
            catch (closeError) {
                console.error('❌ [TENANT FILTER] Error closing database connection:', closeError);
            }
        }
        if (!customer) {
            return res.status(404).json({
                error: 'Customer not found or access denied',
                customer_id: customerId,
                tenant_id: tenantId,
            });
        }
        // Return response in Medusa format
        const response = {
            customer,
            _tenant: {
                id: tenantId,
                filtered: true,
                method: 'direct_db_connection',
            },
        };
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Filtered', 'true');
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error getting customer:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to get customer',
            message: error.message,
            tenant_id: tenantId,
        });
    }
}
async function POST(req, res) {
    console.log(`🚀 [TENANT FILTER] === CUSTOM CUSTOMER UPDATE ENDPOINT CALLED ===`);
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        // Get customer ID from URL params
        const customerId = req.params?.id;
        if (!customerId) {
            return res.status(400).json({
                error: 'Customer ID is required for update',
                tenant_id: tenantId,
            });
        }
        console.log(`🔄 [TENANT FILTER] Updating customer ${customerId} for tenant: ${tenantId}`);
        // Get update data from request body
        const updateData = req.body;
        // Remove tenant_id from update data to prevent modification
        const { tenant_id: _, ...safeUpdateData } = updateData;
        // Direct database connection approach
        const { Client } = require('pg');
        const client = new Client({
            connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
        });
        let updatedCustomer = null;
        try {
            await client.connect();
            // First, verify the customer belongs to this tenant
            const checkQuery = 'SELECT id FROM customer WHERE id = $1 AND tenant_id = $2';
            const checkResult = await client.query(checkQuery, [customerId, tenantId]);
            if (checkResult.rows.length === 0) {
                await client.end();
                return res.status(404).json({
                    error: 'Customer not found or access denied',
                    customer_id: customerId,
                    tenant_id: tenantId,
                });
            }
            // Update the customer (tenant_id cannot be changed)
            const updateQuery = `
        UPDATE customer 
        SET 
          email = COALESCE($1, email),
          first_name = COALESCE($2, first_name),
          last_name = COALESCE($3, last_name),
          phone = COALESCE($4, phone),
          metadata = COALESCE($5, metadata),
          updated_at = NOW()
        WHERE id = $6 AND tenant_id = $7
        RETURNING *
      `;
            const values = [
                safeUpdateData.email,
                safeUpdateData.first_name,
                safeUpdateData.last_name,
                safeUpdateData.phone,
                safeUpdateData.metadata ? JSON.stringify(safeUpdateData.metadata) : null,
                customerId,
                tenantId,
            ];
            const result = await client.query(updateQuery, values);
            updatedCustomer = result.rows[0];
            console.log(`✅ [TENANT FILTER] Updated customer ${customerId} for tenant: ${tenantId}`);
        }
        catch (dbError) {
            console.error('❌ [TENANT FILTER] Database error:', dbError);
            throw dbError;
        }
        finally {
            // Always close the database connection
            try {
                await client.end();
                console.log(`🔗 [TENANT FILTER] Database connection closed`);
            }
            catch (closeError) {
                console.error('❌ [TENANT FILTER] Error closing database connection:', closeError);
            }
        }
        // Return response in Medusa format
        const response = {
            customer: updatedCustomer,
            _tenant: {
                id: tenantId,
                validated: true,
                method: 'direct_db_connection',
            },
        };
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Tenant-Validated', 'true');
        res.json(response);
    }
    catch (error) {
        console.error('❌ [TENANT FILTER] Error updating customer:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        res.status(500).json({
            error: 'Failed to update customer',
            message: error.message,
            tenant_id: tenantId,
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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