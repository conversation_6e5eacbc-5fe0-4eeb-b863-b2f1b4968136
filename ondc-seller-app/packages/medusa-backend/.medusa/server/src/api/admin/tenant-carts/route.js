"use strict";
/**
 * Tenant-Aware Carts API
 *
 * Complete CRUD operations for carts with automatic tenant isolation.
 * All operations respect tenant boundaries and inject tenant_id automatically.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DELETE = exports.PUT = exports.POST = exports.GET = void 0;
const tenant_service_factory_1 = require("../../../services/tenant-service-factory");
const GET = async (req, res) => {
    try {
        const tenantId = req.tenant_id || 'default';
        console.log(`🛒 [TENANT-CARTS] Getting carts for tenant: ${tenantId}`);
        // Create tenant-aware services
        const services = tenant_service_factory_1.TenantServiceFactory.fromRequest(req);
        // Get query parameters
        const { limit = 20, offset = 0, status, customer_id } = req.query;
        // Build filters
        const filters = {};
        if (status) {
            filters.status = status;
        }
        if (customer_id) {
            filters.customer_id = customer_id;
        }
        // Get carts using tenant-aware service
        const [carts, totalCount] = await services.cart.listAndCountCarts(filters, {
            take: parseInt(limit),
            skip: parseInt(offset)
        });
        // Get cart statistics
        const stats = await services.cart.getCartStats();
        // Build response
        const response = {
            success: true,
            message: `Carts retrieved for tenant: ${tenantId}`,
            data: {
                carts,
                pagination: {
                    total: totalCount,
                    count: carts.length,
                    limit: parseInt(limit),
                    offset: parseInt(offset),
                    hasMore: (parseInt(offset) + carts.length) < totalCount
                },
                statistics: stats,
                tenant: {
                    id: tenantId,
                    context: 'Carts filtered by tenant automatically'
                }
            },
            timestamp: new Date().toISOString()
        };
        console.log(`✅ [TENANT-CARTS] Retrieved ${carts.length}/${totalCount} carts for tenant: ${tenantId}`);
        return res.json(response);
    }
    catch (error) {
        console.error(`❌ [TENANT-CARTS] Error getting carts: ${error}`);
        return res.status(500).json({
            success: false,
            message: 'Failed to get tenant carts',
            error: error instanceof Error ? error.message : 'Unknown error',
            tenant_id: req.tenant_id || 'default',
            timestamp: new Date().toISOString()
        });
    }
};
exports.GET = GET;
const POST = async (req, res) => {
    try {
        const tenantId = req.tenant_id || 'default';
        console.log(`🛒 [TENANT-CARTS] Creating cart for tenant: ${tenantId}`);
        // Create tenant-aware services
        const services = tenant_service_factory_1.TenantServiceFactory.fromRequest(req);
        const { customer_id, region_id, sales_channel_id, currency_code = 'usd', ...otherData } = req.body;
        // Prepare cart data
        const cartData = {
            customer_id,
            region_id,
            sales_channel_id,
            currency_code,
            ...otherData
        };
        console.log(`🛒 [TENANT-CARTS] Creating cart with data:`, cartData);
        // Create cart using tenant-aware service
        const createdCarts = await services.cart.createCarts([cartData]);
        const createdCart = createdCarts[0];
        // Get updated statistics
        const stats = await services.cart.getCartStats();
        const response = {
            success: true,
            message: `Cart created for tenant: ${tenantId}`,
            data: {
                cart: createdCart,
                statistics: stats,
                tenant: {
                    id: tenantId,
                    context: 'Cart automatically associated with tenant'
                }
            },
            timestamp: new Date().toISOString()
        };
        console.log(`✅ [TENANT-CARTS] Created cart ${createdCart.id} for tenant: ${tenantId}`);
        return res.status(201).json(response);
    }
    catch (error) {
        console.error(`❌ [TENANT-CARTS] Error creating cart: ${error}`);
        return res.status(500).json({
            success: false,
            message: 'Failed to create tenant cart',
            error: error instanceof Error ? error.message : 'Unknown error',
            tenant_id: req.tenant_id || 'default',
            timestamp: new Date().toISOString()
        });
    }
};
exports.POST = POST;
const PUT = async (req, res) => {
    try {
        const tenantId = req.tenant_id || 'default';
        const { cart_id } = req.query;
        if (!cart_id) {
            return res.status(400).json({
                success: false,
                message: 'Cart ID is required',
                tenant_id: tenantId
            });
        }
        console.log(`🛒 [TENANT-CARTS] Updating cart ${cart_id} for tenant: ${tenantId}`);
        // Create tenant-aware services
        const services = tenant_service_factory_1.TenantServiceFactory.fromRequest(req);
        const updateData = req.body;
        // Update cart using tenant-aware service
        const updatedCarts = await services.cart.updateCarts([{
                id: cart_id,
                ...updateData
            }]);
        const updatedCart = updatedCarts[0];
        const response = {
            success: true,
            message: `Cart updated for tenant: ${tenantId}`,
            data: {
                cart: updatedCart,
                tenant: {
                    id: tenantId,
                    context: 'Cart update respects tenant boundaries'
                }
            },
            timestamp: new Date().toISOString()
        };
        console.log(`✅ [TENANT-CARTS] Updated cart ${cart_id} for tenant: ${tenantId}`);
        return res.json(response);
    }
    catch (error) {
        console.error(`❌ [TENANT-CARTS] Error updating cart: ${error}`);
        return res.status(500).json({
            success: false,
            message: 'Failed to update tenant cart',
            error: error instanceof Error ? error.message : 'Unknown error',
            tenant_id: req.tenant_id || 'default',
            timestamp: new Date().toISOString()
        });
    }
};
exports.PUT = PUT;
const DELETE = async (req, res) => {
    try {
        const tenantId = req.tenant_id || 'default';
        const { cart_id } = req.query;
        if (!cart_id) {
            return res.status(400).json({
                success: false,
                message: 'Cart ID is required',
                tenant_id: tenantId
            });
        }
        console.log(`🛒 [TENANT-CARTS] Deleting cart ${cart_id} for tenant: ${tenantId}`);
        // Create tenant-aware services
        const services = tenant_service_factory_1.TenantServiceFactory.fromRequest(req);
        // Delete cart using tenant-aware service
        await services.cart.deleteCarts([cart_id]);
        // Get updated statistics
        const stats = await services.cart.getCartStats();
        const response = {
            success: true,
            message: `Cart deleted for tenant: ${tenantId}`,
            data: {
                deleted_cart_id: cart_id,
                statistics: stats,
                tenant: {
                    id: tenantId,
                    context: 'Cart deletion respects tenant boundaries'
                }
            },
            timestamp: new Date().toISOString()
        };
        console.log(`✅ [TENANT-CARTS] Deleted cart ${cart_id} for tenant: ${tenantId}`);
        return res.json(response);
    }
    catch (error) {
        console.error(`❌ [TENANT-CARTS] Error deleting cart: ${error}`);
        return res.status(500).json({
            success: false,
            message: 'Failed to delete tenant cart',
            error: error instanceof Error ? error.message : 'Unknown error',
            tenant_id: req.tenant_id || 'default',
            timestamp: new Date().toISOString()
        });
    }
};
exports.DELETE = DELETE;
//# sourceMappingURL=data:application/json;base64,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