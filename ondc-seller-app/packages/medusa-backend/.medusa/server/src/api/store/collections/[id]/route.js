"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
const tenant_service_factory_1 = require("../../../../services/tenant-service-factory");
/**
 * Store Collection Detail Endpoint - Customer-facing API with tenant filtering
 *
 * This endpoint provides tenant-filtered individual collection details for the store/customer-facing API.
 */
async function GET(req, res) {
    try {
        // Extract tenant ID and collection ID
        const tenantId = req.headers['x-tenant-id'] || 'default';
        const collectionId = req.params.id;
        console.log(`🛒 [STORE-COLLECTION-DETAIL] Getting collection ${collectionId} for tenant: ${tenantId}`);
        if (!collectionId) {
            return res.status(400).json({
                error: 'Collection ID is required',
                message: 'Collection ID must be provided in the URL path',
                tenant_id: tenantId
            });
        }
        // Get query parameters
        const { fields, expand } = req.query;
        // Create tenant-aware services
        const services = tenant_service_factory_1.TenantServiceFactory.fromRequest(req);
        // Build config for field selection and relations
        const config = {};
        // Add field selection if specified
        if (fields) {
            const fieldArray = Array.isArray(fields) ? fields : [fields];
            config.select = fieldArray;
        }
        // Add relations to expand if specified
        if (expand) {
            const expandArray = Array.isArray(expand) ? expand : [expand];
            config.relations = expandArray;
        }
        else {
            // Default relations for store API
            config.relations = [
                'products',
                'products.variants',
                'products.variants.prices',
                'products.images',
                'products.categories'
            ];
        }
        console.log(`🛒 [STORE-COLLECTION-DETAIL] Config:`, config);
        // Get collection using tenant-aware service
        const collection = await services.collection.retrieveCollection(collectionId, config);
        if (!collection) {
            console.log(`❌ [STORE-COLLECTION-DETAIL] Collection ${collectionId} not found for tenant: ${tenantId}`);
            return res.status(404).json({
                error: 'Collection not found',
                message: `Collection with ID ${collectionId} not found or not accessible for tenant ${tenantId}`,
                collection_id: collectionId,
                tenant_id: tenantId
            });
        }
        // Build response in Medusa store API format
        const response = {
            collection: collection
        };
        console.log(`✅ [STORE-COLLECTION-DETAIL] Retrieved collection ${collectionId} (${collection.title}) for tenant: ${tenantId}`);
        // Set response headers for debugging
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Store-API', 'true');
        res.setHeader('X-Tenant-Filtered', 'true');
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('❌ [STORE-COLLECTION-DETAIL] Error:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        const collectionId = req.params.id;
        return res.status(500).json({
            error: 'Failed to fetch collection',
            message: error.message,
            collection_id: collectionId,
            tenant_id: tenantId,
            _debug: {
                error_type: 'store_collection_detail_error',
                timestamp: new Date().toISOString(),
                stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
            }
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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