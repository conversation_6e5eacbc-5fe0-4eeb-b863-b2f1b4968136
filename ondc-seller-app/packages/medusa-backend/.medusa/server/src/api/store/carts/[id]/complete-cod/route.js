"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.POST = POST;
const utils_1 = require("@medusajs/framework/utils");
/**
 * Complete Cart with Cash on Delivery
 * This endpoint bypasses complex payment sessions and creates an order with COD payment
 */
async function POST(req, res) {
    try {
        const cartId = req.params.id;
        const cartModuleService = req.scope.resolve('cart');
        const orderModuleService = req.scope.resolve('order');
        const logger = req.scope.resolve(utils_1.ContainerRegistrationKeys.LOGGER);
        // Extract tenant ID and customer ID from request
        const tenantId = req.headers['x-tenant-id'] || 'default';
        // Enhanced customer ID extraction - handle both customer and admin tokens
        let customerId = null;
        if (req.auth_context?.actor_id) {
            const actorType = req.auth_context.actor_type;
            const actorId = req.auth_context.actor_id;
            if (actorType === 'customer') {
                // Direct customer token
                customerId = actorId;
                logger.info(`[COD Order] Using customer token: ${customerId}`);
            }
            else if (actorType === 'user' && req.auth_context.app_metadata?.customer_id) {
                // Admin token with customer_id in metadata (for admin-initiated orders)
                customerId = req.auth_context.app_metadata.customer_id;
                logger.info(`[COD Order] Using admin token with customer metadata: ${customerId}`);
            }
            else {
                logger.warn(`[COD Order] Unsupported actor type: ${actorType}, creating guest order`);
            }
        }
        logger.info(`[COD Order] Starting COD order creation for cart: ${cartId}`);
        logger.info(`[COD Order] Tenant ID: ${tenantId}, Customer ID: ${customerId}, Actor Type: ${req.auth_context?.actor_type || 'none'}`);
        // Get cart details
        const cart = await cartModuleService.retrieveCart(cartId, {
            relations: ['items', 'shipping_address', 'billing_address'],
        });
        if (!cart) {
            res.status(404).json({
                error: 'Cart not found',
                message: `Cart with ID ${cartId} not found`,
            });
            return;
        }
        // FALLBACK: If no customer ID from token, try to get it from cart
        if (!customerId && cart.customer_id) {
            customerId = cart.customer_id;
            logger.info(`[COD Order] Using customer ID from cart: ${customerId}`);
        }
        if (!cart.items || cart.items.length === 0) {
            res.status(400).json({
                error: 'Empty cart',
                message: 'Cannot create order from empty cart',
            });
            return;
        }
        logger.info(`[COD Order] Cart found with ${cart.items.length} items, total: ${cart.total}`);
        // Create order data
        const orderData = {
            cart_id: cartId,
            region_id: cart.region_id,
            currency_code: cart.currency_code,
            email: cart.email || '<EMAIL>',
            // Customer and tenant association
            customer_id: customerId,
            tenant_id: tenantId,
            // Order totals
            total: cart.total || 0,
            subtotal: cart.subtotal || 0,
            tax_total: cart.tax_total || 0,
            shipping_total: cart.shipping_total || 0,
            // Payment information for COD
            payment_status: 'awaiting',
            payment_method: 'cash_on_delivery',
            // Fulfillment status
            fulfillment_status: 'not_fulfilled',
            // Order items
            items: cart.items.map((item) => ({
                variant_id: item.variant_id,
                product_id: item.product_id,
                title: item.title || item.product?.title || 'Product',
                quantity: item.quantity,
                unit_price: item.unit_price,
                total: item.quantity * item.unit_price,
                metadata: {
                    product_title: item.product?.title,
                    variant_title: item.variant?.title,
                    sku: item.variant?.sku,
                },
            })),
            // Addresses
            shipping_address: cart.shipping_address || {
                first_name: 'Guest',
                last_name: 'Customer',
                address_1: 'Default Address',
                city: 'Default City',
                postal_code: '00000',
                country_code: 'us',
            },
            billing_address: cart.billing_address ||
                cart.shipping_address || {
                first_name: 'Guest',
                last_name: 'Customer',
                address_1: 'Default Address',
                city: 'Default City',
                postal_code: '00000',
                country_code: 'us',
            },
            // Metadata
            metadata: {
                payment_method: 'cash_on_delivery',
                payment_provider: 'manual',
                created_via: 'cod_api',
                cart_id: cartId,
                tenant_id: tenantId,
                customer_id: customerId,
            },
        };
        logger.info(`[COD Order] Creating order with customer_id: ${customerId}, tenant_id: ${tenantId}`);
        logger.info(`[COD Order] Order data:`, JSON.stringify(orderData, null, 2));
        // Create the order
        const order = await orderModuleService.createOrders(orderData);
        logger.info(`[COD Order] Order created successfully: ${order.id} for customer: ${customerId} in tenant: ${tenantId}`);
        // CRITICAL FIX: Ensure tenant_id is properly set in database
        // The Medusa orderModuleService may not respect custom tenant_id field
        // Use direct database update as a workaround
        if (tenantId && tenantId !== 'default') {
            try {
                const { Client } = require('pg');
                const client = new Client({ connectionString: process.env.DATABASE_URL });
                await client.connect();
                const updateResult = await client.query('UPDATE "order" SET tenant_id = $1, metadata = COALESCE(metadata, \'{}\') || $2 WHERE id = $3 RETURNING id, tenant_id;', [
                    tenantId,
                    JSON.stringify({
                        tenant_id_fix_applied: true,
                        tenant_id_fix_timestamp: new Date().toISOString(),
                    }),
                    order.id,
                ]);
                await client.end();
                if (updateResult.rows.length > 0) {
                    logger.info(`[COD Order] Tenant ID fix applied via direct DB update: ${order.id} → ${tenantId}`);
                }
                else {
                    logger.error(`[COD Order] Direct DB update failed for order: ${order.id}`);
                }
            }
            catch (error) {
                logger.error(`[COD Order] Failed to apply tenant ID fix via direct DB: ${error.message}`);
            }
        }
        // Mark cart as completed (optional - you might want to keep it for reference)
        await cartModuleService.updateCarts(cartId, {
            completed_at: new Date(),
            metadata: {
                ...cart.metadata,
                order_id: order.id,
                completed_via: 'cod_api',
            },
        });
        // Return success response
        res.status(200).json({
            success: true,
            order: {
                id: order.id,
                cart_id: cartId,
                total: orderData.total,
                currency_code: orderData.currency_code,
                payment_method: 'cash_on_delivery',
                payment_status: 'awaiting',
                fulfillment_status: 'not_fulfilled',
                items: orderData.items,
                shipping_address: orderData.shipping_address,
                created_at: new Date().toISOString(),
                metadata: orderData.metadata,
            },
            message: 'Order created successfully with Cash on Delivery payment',
        });
    }
    catch (error) {
        const logger = req.scope.resolve(utils_1.ContainerRegistrationKeys.LOGGER);
        logger.error(`[COD Order] Error creating COD order:`, error);
        res.status(500).json({
            error: 'Order creation failed',
            message: error.message || 'Failed to create order with Cash on Delivery',
            details: process.env.NODE_ENV === 'development' ? error.stack : undefined,
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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