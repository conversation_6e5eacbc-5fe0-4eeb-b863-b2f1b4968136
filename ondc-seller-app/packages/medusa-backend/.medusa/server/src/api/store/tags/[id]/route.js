"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
const tenant_service_factory_1 = require("../../../../services/tenant-service-factory");
/**
 * GET /store/tags/{id}
 * Get a specific product tag by ID with tenant filtering
 */
async function GET(req, res) {
    const { id } = req.params;
    console.log(`🛒 [STORE-TAG-DETAIL] Getting tag ${id} for tenant: ${req.tenantId}`);
    try {
        // Get tenant-aware services
        const tenantServices = tenant_service_factory_1.TenantServiceFactory.fromRequest(req);
        // Build config
        const config = {
            relations: req.query.expand ? req.query.expand.split(',') : []
        };
        console.log(`🛒 [STORE-TAG-DETAIL] Config:`, config);
        // Get tag using tenant-aware service
        const tag = await tenantServices.tagService.retrieveTag(id, config);
        if (!tag) {
            console.log(`❌ [STORE-TAG-DETAIL] Tag ${id} not found for tenant: ${req.tenantId}`);
            return res.status(404).json({
                error: "Not found",
                message: `Tag with id ${id} not found`
            });
        }
        console.log(`✅ [STORE-TAG-DETAIL] Retrieved tag ${id} (${tag.value}) for tenant: ${req.tenantId}`);
        res.json({
            tag
        });
    }
    catch (error) {
        console.error(`❌ [STORE-TAG-DETAIL] Error getting tag ${id}: ${error}`);
        res.status(500).json({
            error: "Internal server error",
            message: error.message
        });
    }
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicm91dGUuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi9zcmMvYXBpL3N0b3JlL3RhZ3MvW2lkXS9yb3V0ZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOztBQU9BLGtCQXVDQztBQTdDRCx3RkFBbUY7QUFFbkY7OztHQUdHO0FBQ0ksS0FBSyxVQUFVLEdBQUcsQ0FBQyxHQUFrQixFQUFFLEdBQW1CO0lBQy9ELE1BQU0sRUFBRSxFQUFFLEVBQUUsR0FBRyxHQUFHLENBQUMsTUFBTSxDQUFDO0lBRTFCLE9BQU8sQ0FBQyxHQUFHLENBQUMscUNBQXFDLEVBQUUsZ0JBQWdCLEdBQUcsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDO0lBRW5GLElBQUksQ0FBQztRQUNILDRCQUE0QjtRQUM1QixNQUFNLGNBQWMsR0FBRyw2Q0FBb0IsQ0FBQyxXQUFXLENBQUMsR0FBRyxDQUFDLENBQUM7UUFFN0QsZUFBZTtRQUNmLE1BQU0sTUFBTSxHQUFHO1lBQ2IsU0FBUyxFQUFFLEdBQUcsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBRSxHQUFHLENBQUMsS0FBSyxDQUFDLE1BQWlCLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFO1NBQzNFLENBQUM7UUFFRixPQUFPLENBQUMsR0FBRyxDQUFDLCtCQUErQixFQUFFLE1BQU0sQ0FBQyxDQUFDO1FBRXJELHFDQUFxQztRQUNyQyxNQUFNLEdBQUcsR0FBRyxNQUFNLGNBQWMsQ0FBQyxVQUFVLENBQUMsV0FBVyxDQUFDLEVBQUUsRUFBRSxNQUFNLENBQUMsQ0FBQztRQUVwRSxJQUFJLENBQUMsR0FBRyxFQUFFLENBQUM7WUFDVCxPQUFPLENBQUMsR0FBRyxDQUFDLDRCQUE0QixFQUFFLDBCQUEwQixHQUFHLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQztZQUNwRixPQUFPLEdBQUcsQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDO2dCQUMxQixLQUFLLEVBQUUsV0FBVztnQkFDbEIsT0FBTyxFQUFFLGVBQWUsRUFBRSxZQUFZO2FBQ3ZDLENBQUMsQ0FBQztRQUNMLENBQUM7UUFFRCxPQUFPLENBQUMsR0FBRyxDQUFDLHNDQUFzQyxFQUFFLEtBQUssR0FBRyxDQUFDLEtBQUssaUJBQWlCLEdBQUcsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDO1FBRW5HLEdBQUcsQ0FBQyxJQUFJLENBQUM7WUFDUCxHQUFHO1NBQ0osQ0FBQyxDQUFDO0lBQ0wsQ0FBQztJQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7UUFDZixPQUFPLENBQUMsS0FBSyxDQUFDLDBDQUEwQyxFQUFFLEtBQUssS0FBSyxFQUFFLENBQUMsQ0FBQztRQUN4RSxHQUFHLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUFDLElBQUksQ0FBQztZQUNuQixLQUFLLEVBQUUsdUJBQXVCO1lBQzlCLE9BQU8sRUFBRSxLQUFLLENBQUMsT0FBTztTQUN2QixDQUFDLENBQUM7SUFDTCxDQUFDO0FBQ0gsQ0FBQyJ9