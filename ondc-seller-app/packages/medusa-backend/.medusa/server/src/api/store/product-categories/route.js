"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
const tenant_service_factory_1 = require("../../../services/tenant-service-factory");
const enhanced_query_parser_1 = require("../../../utils/enhanced-query-parser");
const store_api_configs_1 = require("../../../config/store-api-configs");
/**
 * Store Product Categories Endpoint - Customer-facing API with tenant filtering
 *
 * This endpoint provides tenant-filtered product categories for the store/customer-facing API.
 * It uses the tenant-aware services to ensure proper data isolation.
 */
async function GET(req, res) {
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        console.log(`🛒 [STORE-CATEGORIES] Getting categories for tenant: ${tenantId}`);
        console.log(`🛒 [STORE-CATEGORIES] Query params:`, req.query);
        // Validate query parameters
        const validation = (0, store_api_configs_1.validateQueryParams)(req.query, store_api_configs_1.PRODUCT_CATEGORIES_CONFIG);
        if (!validation.valid) {
            console.warn(`⚠️ [STORE-CATEGORIES] Invalid query parameters:`, validation.errors);
            return res.status(400).json({
                error: 'Invalid query parameters',
                details: validation.errors,
                tenant_id: tenantId,
            });
        }
        // Parse query parameters with comprehensive support
        const parsedQuery = (0, enhanced_query_parser_1.parseQueryParameters)(req, store_api_configs_1.PRODUCT_CATEGORIES_CONFIG);
        console.log(`🔍 [STORE-CATEGORIES] Parsed query:`, parsedQuery);
        // Create tenant-aware services
        const services = tenant_service_factory_1.TenantServiceFactory.fromRequest(req);
        // Build filters for store API (customer-facing)
        const filters = {
            is_active: true, // Store API should only show active categories
            is_internal: false, // Store API should only show public categories (not internal)
            ...parsedQuery.filters, // Add parsed filters
        };
        // Add date filters
        if (parsedQuery.dateFilters.created_after) {
            filters.created_at = { ...filters.created_at, $gte: parsedQuery.dateFilters.created_after };
        }
        if (parsedQuery.dateFilters.created_before) {
            filters.created_at = { ...filters.created_at, $lte: parsedQuery.dateFilters.created_before };
        }
        if (parsedQuery.dateFilters.updated_after) {
            filters.updated_at = { ...filters.updated_at, $gte: parsedQuery.dateFilters.updated_after };
        }
        if (parsedQuery.dateFilters.updated_before) {
            filters.updated_at = { ...filters.updated_at, $lte: parsedQuery.dateFilters.updated_before };
        }
        // Add search filters
        if (parsedQuery.search) {
            const searchConditions = parsedQuery.search.fields.map(field => ({
                [field]: { $ilike: `%${parsedQuery.search.query}%` },
            }));
            filters.$or = searchConditions;
        }
        // Build config for pagination, field selection, and sorting
        const config = {
            take: parsedQuery.pagination.limit,
            skip: parsedQuery.pagination.offset,
        };
        // Add field selection if specified
        if (parsedQuery.fields) {
            config.select = parsedQuery.fields;
        }
        // Add relations to expand if specified
        if (parsedQuery.expand) {
            config.relations = parsedQuery.expand;
        }
        else {
            // Default relations for store API
            config.relations = ['parent_category', 'category_children'];
        }
        // Add sorting configuration
        if (parsedQuery.sorting.length > 0) {
            config.order = {};
            parsedQuery.sorting.forEach(sort => {
                config.order[sort.field] = sort.direction;
            });
        }
        // Include descendants tree if requested (legacy support)
        const includeDescendantsTree = req.query.include_descendants_tree;
        if (includeDescendantsTree === 'true' || includeDescendantsTree === true) {
            config.include_descendants_tree = true;
        }
        console.log(`🛒 [STORE-CATEGORIES] Filters:`, filters);
        console.log(`🛒 [STORE-CATEGORIES] Config:`, config);
        // Get categories using tenant-aware service
        const [categories, totalCount] = await services.productCategory.listAndCountCategories(filters, config);
        // Build response in Medusa store API format with enhanced metadata
        const response = {
            product_categories: categories || [],
            count: categories?.length || 0,
            offset: parsedQuery.pagination.offset,
            limit: parsedQuery.pagination.limit,
            total: totalCount || 0,
            // Enhanced metadata
            _meta: {
                pagination: {
                    page: parsedQuery.pagination.page ||
                        Math.floor(parsedQuery.pagination.offset / parsedQuery.pagination.limit) + 1,
                    per_page: parsedQuery.pagination.limit,
                    total_pages: Math.ceil((totalCount || 0) / parsedQuery.pagination.limit),
                    has_more: parsedQuery.pagination.offset + parsedQuery.pagination.limit < (totalCount || 0),
                },
                filters_applied: Object.keys(parsedQuery.filters).length > 0 ? parsedQuery.filters : null,
                sorting: parsedQuery.sorting,
                search: parsedQuery.search ? parsedQuery.search.query : null,
                tenant_id: tenantId,
            },
        };
        console.log(`✅ [STORE-CATEGORIES] Retrieved ${response.count}/${response.total} categories for tenant: ${tenantId}`);
        // Set response headers for debugging
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Store-API', 'true');
        res.setHeader('X-Tenant-Filtered', 'true');
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('❌ [STORE-CATEGORIES] Error:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        return res.status(500).json({
            error: 'Failed to fetch product categories',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'store_categories_error',
                timestamp: new Date().toISOString(),
                stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
            },
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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