"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
const tenant_service_factory_1 = require("../../../../services/tenant-service-factory");
/**
 * Store Product Detail Endpoint - Customer-facing API with tenant filtering
 *
 * This endpoint provides tenant-filtered individual product details for the store/customer-facing API.
 */
async function GET(req, res) {
    try {
        // Extract tenant ID and product ID
        const tenantId = req.headers['x-tenant-id'] || 'default';
        const productId = req.params.id;
        console.log(`🛒 [STORE-PRODUCT-DETAIL] Getting product ${productId} for tenant: ${tenantId}`);
        if (!productId) {
            return res.status(400).json({
                error: 'Product ID is required',
                message: 'Product ID must be provided in the URL path',
                tenant_id: tenantId
            });
        }
        // Get query parameters
        const { fields, expand, region_id, currency_code, sales_channel_id } = req.query;
        // Create tenant-aware services
        const services = tenant_service_factory_1.TenantServiceFactory.fromRequest(req);
        // Build config for field selection and relations
        const config = {};
        // Add field selection if specified
        if (fields) {
            const fieldArray = Array.isArray(fields) ? fields : [fields];
            config.select = fieldArray;
        }
        // Add relations to expand if specified
        if (expand) {
            const expandArray = Array.isArray(expand) ? expand : [expand];
            config.relations = expandArray;
        }
        else {
            // Default relations for store API
            config.relations = [
                'variants',
                'variants.prices',
                'variants.options',
                'images',
                'categories',
                'collections',
                'tags',
                'type',
                'profile'
            ];
        }
        // Add region context if specified
        if (region_id) {
            config.region_id = region_id;
        }
        // Add currency context if specified
        if (currency_code) {
            config.currency_code = currency_code;
        }
        // Add sales channel context if specified
        if (sales_channel_id) {
            config.sales_channel_id = sales_channel_id;
        }
        console.log(`🛒 [STORE-PRODUCT-DETAIL] Config:`, config);
        // Get product using tenant-aware service
        const product = await services.product.retrieveProduct(productId, config);
        if (!product) {
            console.log(`❌ [STORE-PRODUCT-DETAIL] Product ${productId} not found for tenant: ${tenantId}`);
            return res.status(404).json({
                error: 'Product not found',
                message: `Product with ID ${productId} not found or not accessible for tenant ${tenantId}`,
                product_id: productId,
                tenant_id: tenantId
            });
        }
        // Ensure product is published (store API should only show published products)
        if (product.status !== 'published') {
            console.log(`❌ [STORE-PRODUCT-DETAIL] Product ${productId} not published for tenant: ${tenantId}`);
            return res.status(404).json({
                error: 'Product not found',
                message: `Product with ID ${productId} is not available`,
                product_id: productId,
                tenant_id: tenantId
            });
        }
        // Build response in Medusa store API format
        const response = {
            product: product
        };
        console.log(`✅ [STORE-PRODUCT-DETAIL] Retrieved product ${productId} (${product.title}) for tenant: ${tenantId}`);
        // Set response headers for debugging
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Store-API', 'true');
        res.setHeader('X-Tenant-Filtered', 'true');
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('❌ [STORE-PRODUCT-DETAIL] Error:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        const productId = req.params.id;
        return res.status(500).json({
            error: 'Failed to fetch product',
            message: error.message,
            product_id: productId,
            tenant_id: tenantId,
            _debug: {
                error_type: 'store_product_detail_error',
                timestamp: new Date().toISOString(),
                stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
            }
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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