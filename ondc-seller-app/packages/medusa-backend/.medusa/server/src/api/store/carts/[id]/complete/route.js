"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.POST = POST;
const utils_1 = require("@medusajs/framework/utils");
/**
 * Complete Cart - Standard Medusa v2 Cart Completion
 * This endpoint properly handles cart completion with customer and tenant association
 */
async function POST(req, res) {
    try {
        const cartId = req.params.id;
        const cartModuleService = req.scope.resolve('cart');
        const orderModuleService = req.scope.resolve('order');
        const logger = req.scope.resolve(utils_1.ContainerRegistrationKeys.LOGGER);
        // Extract tenant ID and customer ID from request
        const tenantId = req.headers['x-tenant-id'] || 'default';
        // Enhanced customer ID extraction - handle both customer and admin tokens
        let customerId = null;
        if (req.auth_context?.actor_id) {
            const actorType = req.auth_context.actor_type;
            const actorId = req.auth_context.actor_id;
            if (actorType === 'customer') {
                // Direct customer token
                customerId = actorId;
                logger.info(`[CART-COMPLETE] Using customer token: ${customerId}`);
            }
            else if (actorType === 'user' && req.auth_context.app_metadata?.customer_id) {
                // Admin token with customer_id in metadata (for admin-initiated orders)
                customerId = req.auth_context.app_metadata.customer_id;
                logger.info(`[CART-COMPLETE] Using admin token with customer metadata: ${customerId}`);
            }
            else {
                logger.warn(`[CART-COMPLETE] Unsupported actor type: ${actorType}, creating guest order`);
            }
        }
        logger.info(`[CART-COMPLETE] Starting cart completion for cart: ${cartId}`);
        logger.info(`[CART-COMPLETE] Tenant ID: ${tenantId}, Customer ID: ${customerId}, Actor Type: ${req.auth_context?.actor_type || 'none'}`);
        // Get cart details without relations to avoid MikroORM issues
        const cart = await cartModuleService.retrieveCart(cartId);
        if (!cart) {
            logger.error(`[CART-COMPLETE] Cart not found: ${cartId}`);
            res.status(404).json({
                error: 'Cart not found',
                message: `Cart with ID ${cartId} not found`,
            });
            return;
        }
        // FALLBACK: If no customer ID from token, try to get it from cart
        if (!customerId && cart.customer_id) {
            customerId = cart.customer_id;
            logger.info(`[CART-COMPLETE] Using customer ID from cart: ${customerId}`);
        }
        // Get cart items separately to avoid relation issues
        let cartItems = [];
        try {
            const cartWithItems = await cartModuleService.retrieveCart(cartId, {
                relations: ['items'],
            });
            cartItems = cartWithItems.items || [];
        }
        catch (error) {
            logger.warn(`[CART-COMPLETE] Could not fetch cart items, proceeding with empty items: ${error.message}`);
            cartItems = [];
        }
        if (cartItems.length === 0) {
            logger.error(`[CART-COMPLETE] Empty cart: ${cartId}`);
            res.status(400).json({
                error: 'Empty cart',
                message: 'Cannot create order from empty cart',
            });
            return;
        }
        logger.info(`[CART-COMPLETE] Cart found with ${cartItems.length} items, total: ${cart.total}`);
        // Validate customer authentication for non-guest orders
        if (!customerId) {
            logger.warn(`[CART-COMPLETE] No customer authentication found, creating guest order`);
        }
        // Create comprehensive order data
        const orderData = {
            cart_id: cartId,
            region_id: cart.region_id,
            currency_code: cart.currency_code,
            email: cart.email || (customerId ? '<EMAIL>' : '<EMAIL>'),
            // Customer and tenant association - CRITICAL for order visibility
            customer_id: customerId,
            tenant_id: tenantId,
            sales_channel_id: cart.sales_channel_id,
            // Order totals
            total: cart.total || 0,
            subtotal: cart.subtotal || 0,
            tax_total: cart.tax_total || 0,
            shipping_total: cart.shipping_total || 0,
            discount_total: cart.discount_total || 0,
            // Order status
            status: 'pending',
            payment_status: 'awaiting',
            fulfillment_status: 'not_fulfilled',
            // Order items with safe mapping (using separately fetched items)
            items: cartItems.map((item) => ({
                variant_id: item.variant_id,
                product_id: item.product_id,
                title: item.title || 'Product',
                quantity: item.quantity,
                unit_price: item.unit_price,
                total: item.quantity * item.unit_price,
                metadata: {
                    cart_item_id: item.id,
                    variant_id: item.variant_id,
                    product_id: item.product_id,
                },
            })),
            // Addresses (with fallback defaults)
            shipping_address: {
                first_name: customerId ? 'Customer' : 'Guest',
                last_name: customerId ? 'User' : 'Customer',
                address_1: 'Default Address',
                city: 'Default City',
                postal_code: '00000',
                country_code: 'in',
            },
            billing_address: {
                first_name: customerId ? 'Customer' : 'Guest',
                last_name: customerId ? 'User' : 'Customer',
                address_1: 'Default Address',
                city: 'Default City',
                postal_code: '00000',
                country_code: 'in',
            },
            // Shipping methods (simplified - will be handled by Medusa workflows)
            shipping_methods: [],
            // Comprehensive metadata for tracking and debugging
            metadata: {
                cart_id: cartId,
                tenant_id: tenantId,
                customer_id: customerId,
                created_via: 'store_api',
                completion_method: 'standard',
                customer_type: customerId ? 'authenticated' : 'guest',
                completion_timestamp: new Date().toISOString(),
            },
        };
        logger.info(`[CART-COMPLETE] Creating order with customer_id: ${customerId}, tenant_id: ${tenantId}`);
        logger.info(`[CART-COMPLETE] Order data summary:`, {
            cart_id: orderData.cart_id,
            customer_id: orderData.customer_id,
            tenant_id: orderData.tenant_id,
            email: orderData.email,
            total: orderData.total,
            items_count: orderData.items.length,
        });
        // Create the order
        const order = await orderModuleService.createOrders(orderData);
        logger.info(`[CART-COMPLETE] Order created successfully: ${order.id} for customer: ${customerId} in tenant: ${tenantId}`);
        // CRITICAL FIX: Ensure tenant_id is properly set in database
        // The Medusa orderModuleService may not respect custom tenant_id field
        // Use direct database update as a workaround
        if (tenantId && tenantId !== 'default') {
            try {
                const { Client } = require('pg');
                const client = new Client({ connectionString: process.env.DATABASE_URL });
                await client.connect();
                const updateResult = await client.query('UPDATE "order" SET tenant_id = $1, metadata = COALESCE(metadata, \'{}\') || $2 WHERE id = $3 RETURNING id, tenant_id;', [
                    tenantId,
                    JSON.stringify({
                        tenant_id_fix_applied: true,
                        tenant_id_fix_timestamp: new Date().toISOString(),
                    }),
                    order.id,
                ]);
                await client.end();
                if (updateResult.rows.length > 0) {
                    logger.info(`[CART-COMPLETE] Tenant ID fix applied via direct DB update: ${order.id} → ${tenantId}`);
                }
                else {
                    logger.error(`[CART-COMPLETE] Direct DB update failed for order: ${order.id}`);
                }
            }
            catch (error) {
                logger.error(`[CART-COMPLETE] Failed to apply tenant ID fix via direct DB: ${error.message}`);
            }
        }
        // Mark cart as completed
        await cartModuleService.updateCarts(cartId, {
            completed_at: new Date(),
            metadata: {
                ...cart.metadata,
                order_id: order.id,
                completed_via: 'store_api',
                completion_timestamp: new Date().toISOString(),
            },
        });
        logger.info(`[CART-COMPLETE] Cart ${cartId} marked as completed`);
        // Return success response in Medusa v2 format
        res.status(200).json({
            type: 'order',
            order: {
                id: order.id,
                cart_id: cartId,
                customer_id: customerId,
                tenant_id: tenantId,
                email: orderData.email,
                status: orderData.status,
                payment_status: orderData.payment_status,
                fulfillment_status: orderData.fulfillment_status,
                total: orderData.total,
                subtotal: orderData.subtotal,
                tax_total: orderData.tax_total,
                shipping_total: orderData.shipping_total,
                currency_code: orderData.currency_code,
                items: orderData.items,
                shipping_address: orderData.shipping_address,
                billing_address: orderData.billing_address,
                created_at: new Date().toISOString(),
                metadata: orderData.metadata,
            },
            _debug: {
                customer_associated: !!customerId,
                tenant_associated: !!tenantId,
                completion_method: 'standard_medusa_v2',
            },
        });
    }
    catch (error) {
        const logger = req.scope.resolve(utils_1.ContainerRegistrationKeys.LOGGER);
        logger.error(`[CART-COMPLETE] Error completing cart:`, error);
        res.status(500).json({
            error: 'Cart completion failed',
            message: error.message || 'Failed to complete cart and create order',
            details: process.env.NODE_ENV === 'development' ? error.stack : undefined,
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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