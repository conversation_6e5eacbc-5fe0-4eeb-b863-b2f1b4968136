"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.POST = POST;
// Mock cart data (shared with parent routes)
const mockCarts = {
    'tenant-electronics-001': [
        {
            id: 'cart_electronics_001',
            customer_id: 'cus_electronics_001',
            tenant_id: 'tenant-electronics-001',
            region_id: 'reg_01',
            currency_code: 'INR',
            items: [
                {
                    id: 'item_001',
                    cart_id: 'cart_electronics_001',
                    product_id: 'prod_electronics_001',
                    variant_id: 'variant_001',
                    title: 'Smartphone Pro Max',
                    description: 'Latest smartphone with advanced features',
                    thumbnail: '/images/products/smartphone-pro.jpg',
                    quantity: 1,
                    unit_price: 79999,
                    total: 79999,
                    created_at: '2024-01-01T00:00:00Z',
                    updated_at: '2024-01-01T00:00:00Z'
                }
            ],
            subtotal: 79999,
            tax_total: 14399,
            total: 94398,
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:00:00Z'
        }
    ],
    'tenant-fashion-002': [
        {
            id: 'cart_fashion_001',
            customer_id: 'cus_fashion_001',
            tenant_id: 'tenant-fashion-002',
            region_id: 'reg_01',
            currency_code: 'INR',
            items: [
                {
                    id: 'item_002',
                    cart_id: 'cart_fashion_001',
                    product_id: 'prod_fashion_001',
                    variant_id: 'variant_002',
                    title: 'Designer Dress',
                    description: 'Elegant evening dress',
                    thumbnail: '/images/products/designer-dress.jpg',
                    quantity: 1,
                    unit_price: 12999,
                    total: 12999,
                    created_at: '2024-01-01T00:00:00Z',
                    updated_at: '2024-01-01T00:00:00Z'
                }
            ],
            subtotal: 12999,
            tax_total: 2339,
            total: 15338,
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:00:00Z'
        }
    ],
    'default': []
};
// Helper function to recalculate cart totals
function recalculateCartTotals(cart) {
    const subtotal = cart.items.reduce((sum, item) => sum + item.total, 0);
    const tax_total = Math.round(subtotal * 0.18); // 18% GST
    const total = subtotal + tax_total;
    cart.subtotal = subtotal;
    cart.tax_total = tax_total;
    cart.total = total;
    cart.updated_at = new Date().toISOString();
}
/**
 * POST /store/carts/[id]/line-items
 * Add item to cart
 */
async function POST(req, res) {
    try {
        const tenantId = req.headers['x-tenant-id'] || 'default';
        const cartId = req.params.id;
        const { product_id, variant_id, quantity = 1, unit_price, title, description, thumbnail } = req.body;
        console.log(`[CART LINE ITEMS API] POST item to cart ${cartId} for tenant: ${tenantId}`);
        // Validate required fields
        if (!product_id || !variant_id || !unit_price) {
            return res.status(400).json({
                error: 'Missing required fields',
                message: 'product_id, variant_id, and unit_price are required',
                timestamp: new Date().toISOString()
            });
        }
        // Get carts for the tenant
        const carts = mockCarts[tenantId] || [];
        const cartIndex = carts.findIndex(c => c.id === cartId);
        if (cartIndex === -1) {
            return res.status(404).json({
                error: 'Cart not found',
                message: `Cart with ID ${cartId} not found for tenant ${tenantId}`,
                timestamp: new Date().toISOString()
            });
        }
        const cart = carts[cartIndex];
        // Check if item already exists in cart
        const existingItemIndex = cart.items.findIndex((item) => item.product_id === product_id && item.variant_id === variant_id);
        if (existingItemIndex !== -1) {
            // Update existing item quantity
            cart.items[existingItemIndex].quantity += Number(quantity);
            cart.items[existingItemIndex].total = cart.items[existingItemIndex].quantity * cart.items[existingItemIndex].unit_price;
            cart.items[existingItemIndex].updated_at = new Date().toISOString();
        }
        else {
            // Add new item
            const newItem = {
                id: `item_${Date.now()}`,
                cart_id: cartId,
                product_id,
                variant_id,
                title: title || `Product ${product_id}`,
                description: description || '',
                thumbnail: thumbnail || '/images/products/default.jpg',
                quantity: Number(quantity),
                unit_price: Number(unit_price),
                total: Number(quantity) * Number(unit_price),
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };
            cart.items.push(newItem);
        }
        // Recalculate cart totals
        recalculateCartTotals(cart);
        res.status(201).json({
            cart,
            message: 'Item added to cart successfully',
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('[CART LINE ITEMS API] Error:', error);
        res.status(500).json({
            error: 'Internal server error',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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