"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
/**
 * Store Orders Endpoint - Customer-facing API with tenant filtering and authentication
 *
 * This endpoint provides tenant-filtered orders for authenticated customers.
 * It ensures proper multi-tenant isolation and customer authentication.
 */
async function GET(req, res) {
    console.log(`📦 [STORE-ORDERS] Getting orders for authenticated customer`);
    console.log(`📦 [STORE-ORDERS] Headers:`, {
        authorization: req.headers.authorization ? 'Bearer [REDACTED]' : 'None',
        'x-tenant-id': req.headers['x-tenant-id'],
        'x-publishable-api-key': req.headers['x-publishable-api-key'],
    });
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        console.log(`📦 [STORE-ORDERS] Processing orders request for tenant: ${tenantId}`);
        // Check if user is authenticated
        if (!req.auth_context || !req.auth_context.actor_id) {
            console.log(`❌ [STORE-ORDERS] No authentication context found`);
            return res.status(401).json({
                error: 'Unauthorized',
                message: 'Authentication required to access orders',
            });
        }
        const customerId = req.auth_context.actor_id;
        console.log(`📦 [STORE-ORDERS] Authenticated customer ID: ${customerId}`);
        // Get query parameters
        const { limit = 50, offset = 0, fields, order = '-created_at', ...filters } = req.query;
        console.log(`📦 [STORE-ORDERS] Query params:`, { limit, offset, order, filters });
        // Use direct database connection for now to ensure proper tenant filtering
        const { Client } = require('pg');
        const client = new Client({
            connectionString: process.env.DATABASE_URL ||
                'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
        });
        let orders = [];
        let count = 0;
        try {
            await client.connect();
            console.log(`🔗 [STORE-ORDERS] Connected to database`);
            // Get total count for this customer and tenant
            const countResult = await client.query(`SELECT COUNT(*) as total 
         FROM "order" 
         WHERE customer_id = $1 
         AND tenant_id = $2 
         AND deleted_at IS NULL`, [customerId, tenantId]);
            count = parseInt(countResult.rows[0]?.total || 0);
            console.log(`📊 [STORE-ORDERS] Total orders for customer ${customerId} in tenant ${tenantId}: ${count}`);
            if (count === 0) {
                console.log(`📦 [STORE-ORDERS] No orders found for customer`);
                return res.status(200).json({
                    orders: [],
                    count: 0,
                    offset: parseInt(offset),
                    limit: parseInt(limit),
                });
            }
            // Get orders with pagination and totals
            const result = await client.query(`
        SELECT
          o.id, o.status, o.currency_code, o.email, o.display_id,
          o.created_at, o.updated_at, o.tenant_id, o.metadata,
          o.customer_id, o.region_id, o.sales_channel_id,
          o.shipping_address_id, o.billing_address_id,
          o.is_draft_order, o.no_notification,
          os.totals as order_totals
        FROM "order" o
        LEFT JOIN order_summary os ON o.id = os.order_id AND os.deleted_at IS NULL
        WHERE o.customer_id = $1
        AND o.tenant_id = $2
        AND o.deleted_at IS NULL
        ORDER BY o.created_at DESC
        LIMIT $3 OFFSET $4
      `, [customerId, tenantId, parseInt(limit), parseInt(offset)]);
            orders = result.rows;
            console.log(`✅ [STORE-ORDERS] Found ${orders.length} orders for customer`);
            // Populate order items for each order
            for (let i = 0; i < orders.length; i++) {
                const order = orders[i];
                console.log(`📦 [STORE-ORDERS] Fetching items for order: ${order.id}`);
                const itemsResult = await client.query(`
          SELECT
            oi.id as order_item_id,
            oi.quantity,
            oi.fulfilled_quantity,
            oi.shipped_quantity,
            oi.unit_price as order_item_unit_price,
            oli.id as line_item_id,
            oli.title,
            oli.subtitle,
            oli.thumbnail,
            oli.variant_id,
            oli.product_id,
            oli.product_title,
            oli.product_description,
            oli.variant_title,
            oli.variant_sku,
            oli.unit_price,
            oli.compare_at_unit_price,
            oli.metadata as item_metadata,
            oli.created_at as item_created_at,
            oli.updated_at as item_updated_at
          FROM order_item oi
          JOIN order_line_item oli ON oi.item_id = oli.id
          WHERE oi.order_id = $1
          AND oi.deleted_at IS NULL
          AND oli.deleted_at IS NULL
          ORDER BY oi.created_at ASC
          `, [order.id]);
                orders[i].items = itemsResult.rows;
                console.log(`📦 [STORE-ORDERS] Found ${itemsResult.rows.length} items for order ${order.id}`);
                // Fetch shipping methods for this order
                const shippingResult = await client.query(`
          SELECT
            osm.name as shipping_method_name,
            osm.amount as shipping_amount,
            osm.raw_amount as raw_shipping_amount,
            osm.is_tax_inclusive as shipping_tax_inclusive
          FROM order_shipping os
          JOIN order_shipping_method osm ON os.shipping_method_id = osm.id
          WHERE os.order_id = $1
          AND os.deleted_at IS NULL
          AND osm.deleted_at IS NULL
          `, [order.id]);
                // Calculate totals from items and shipping
                const items = itemsResult.rows;
                let itemSubtotal = 0;
                let itemTotal = 0;
                items.forEach(item => {
                    const unitPrice = parseFloat(item.unit_price || 0);
                    const quantity = parseFloat(item.quantity || 0);
                    const itemAmount = unitPrice * quantity;
                    itemSubtotal += itemAmount;
                    itemTotal += itemAmount;
                });
                // Get shipping total
                let shippingTotal = 0;
                let shippingSubtotal = 0;
                shippingResult.rows.forEach(shipping => {
                    const amount = parseFloat(shipping.shipping_amount || 0);
                    shippingTotal += amount;
                    shippingSubtotal += amount;
                });
                // Parse order totals from order_summary
                const orderTotals = order.order_totals || {};
                const currentOrderTotal = orderTotals.current_order_total || 0;
                const originalOrderTotal = orderTotals.original_order_total || 0;
                // Add calculated financial fields to the order
                orders[i] = {
                    ...orders[i],
                    // Item totals
                    original_item_total: itemTotal,
                    original_item_subtotal: itemSubtotal,
                    original_item_tax_total: 0, // Would need tax calculation logic
                    item_total: itemTotal,
                    item_subtotal: itemSubtotal,
                    item_tax_total: 0, // Would need tax calculation logic
                    // Order totals
                    original_total: originalOrderTotal,
                    original_subtotal: originalOrderTotal - shippingTotal,
                    original_tax_total: 0, // Would need tax calculation logic
                    total: currentOrderTotal,
                    subtotal: currentOrderTotal - shippingTotal,
                    tax_total: 0, // Would need tax calculation logic
                    // Discount totals
                    discount_total: 0, // Would need discount calculation logic
                    discount_tax_total: 0,
                    // Gift card totals
                    gift_card_total: 0, // Would need gift card logic
                    gift_card_tax_total: 0,
                    // Shipping totals
                    shipping_total: shippingTotal,
                    shipping_subtotal: shippingSubtotal,
                    shipping_tax_total: 0, // Would need shipping tax calculation
                    original_shipping_total: shippingTotal,
                    original_shipping_subtotal: shippingSubtotal,
                    original_shipping_tax_total: 0,
                    // Payment totals from order_summary
                    paid_total: orderTotals.paid_total || 0,
                    refunded_total: orderTotals.refunded_total || 0,
                    pending_difference: orderTotals.pending_difference || 0,
                };
            }
        }
        catch (dbError) {
            console.error('❌ [STORE-ORDERS] Database error:', dbError);
            throw dbError;
        }
        finally {
            await client.end();
        }
        // Return response in Medusa v2 format
        const response = {
            orders,
            count,
            offset: parseInt(offset),
            limit: parseInt(limit),
        };
        console.log(`✅ [STORE-ORDERS] Returning ${orders.length} orders`);
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('❌ [STORE-ORDERS] Error fetching orders:', error);
        return res.status(500).json({
            error: 'Internal Server Error',
            message: 'Failed to fetch orders',
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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