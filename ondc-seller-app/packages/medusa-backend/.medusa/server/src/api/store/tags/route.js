"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
const tenant_service_factory_1 = require("../../../services/tenant-service-factory");
/**
 * GET /store/tags
 * List all product tags with tenant filtering
 */
async function GET(req, res) {
    console.log(`🛒 [STORE-TAGS] Getting tags for tenant: ${req.tenantId}`);
    console.log(`🛒 [STORE-TAGS] Query params:`, req.query);
    try {
        // Get tenant-aware services
        const tenantServices = tenant_service_factory_1.TenantServiceFactory.fromRequest(req);
        // Parse query parameters
        const limit = parseInt(req.query.limit) || 20;
        const offset = parseInt(req.query.offset) || 0;
        // Build filters
        const filters = {};
        if (req.query.value) {
            filters.value = req.query.value;
        }
        // Build config
        const config = {
            take: limit,
            skip: offset,
            relations: req.query.expand ? req.query.expand.split(',') : [],
        };
        console.log(`🛒 [STORE-TAGS] Filters:`, filters);
        console.log(`🛒 [STORE-TAGS] Config:`, config);
        // Get tags using tenant-aware service
        const [tags, count] = await tenantServices.tagService.listAndCountTags(filters, config);
        console.log(`✅ [STORE-TAGS] Retrieved ${tags.length}/${count} tags for tenant: ${req.tenantId}`);
        res.json({
            tags,
            count: tags.length,
            total: count,
            offset,
            limit,
        });
    }
    catch (error) {
        console.error(`❌ [STORE-TAGS] Error getting tags: ${error}`);
        res.status(500).json({
            error: 'Internal server error',
            message: error.message,
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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