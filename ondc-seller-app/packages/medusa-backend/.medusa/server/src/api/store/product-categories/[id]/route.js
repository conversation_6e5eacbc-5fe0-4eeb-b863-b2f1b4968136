"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
const tenant_service_factory_1 = require("../../../../services/tenant-service-factory");
/**
 * Store Product Category Detail Endpoint - Customer-facing API with tenant filtering
 *
 * This endpoint provides tenant-filtered individual category details for the store/customer-facing API.
 */
async function GET(req, res) {
    try {
        // Extract tenant ID and category ID
        const tenantId = req.headers['x-tenant-id'] || 'default';
        const categoryId = req.params.id;
        console.log(`🛒 [STORE-CATEGORY-DETAIL] Getting category ${categoryId} for tenant: ${tenantId}`);
        if (!categoryId) {
            return res.status(400).json({
                error: 'Category ID is required',
                message: 'Category ID must be provided in the URL path',
                tenant_id: tenantId
            });
        }
        // Get query parameters
        const { fields, expand, include_descendants_tree = false } = req.query;
        // Create tenant-aware services
        const services = tenant_service_factory_1.TenantServiceFactory.fromRequest(req);
        // Build config for field selection and relations
        const config = {};
        // Add field selection if specified
        if (fields) {
            const fieldArray = Array.isArray(fields) ? fields : [fields];
            config.select = fieldArray;
        }
        // Add relations to expand if specified
        if (expand) {
            const expandArray = Array.isArray(expand) ? expand : [expand];
            config.relations = expandArray;
        }
        else {
            // Default relations for store API
            config.relations = [
                'parent_category',
                'category_children',
                'products'
            ];
        }
        // Include descendants tree if requested
        if (include_descendants_tree === 'true' || include_descendants_tree === true) {
            config.include_descendants_tree = true;
        }
        console.log(`🛒 [STORE-CATEGORY-DETAIL] Config:`, config);
        // Get category using tenant-aware service
        const category = await services.productCategory.retrieveCategory(categoryId, config);
        if (!category) {
            console.log(`❌ [STORE-CATEGORY-DETAIL] Category ${categoryId} not found for tenant: ${tenantId}`);
            return res.status(404).json({
                error: 'Category not found',
                message: `Category with ID ${categoryId} not found or not accessible for tenant ${tenantId}`,
                category_id: categoryId,
                tenant_id: tenantId
            });
        }
        // Ensure category is active and not internal (store API should only show public categories)
        if (category.is_active === false) {
            console.log(`❌ [STORE-CATEGORY-DETAIL] Category ${categoryId} not active for tenant: ${tenantId}`);
            return res.status(404).json({
                error: 'Category not found',
                message: `Category with ID ${categoryId} is not available`,
                category_id: categoryId,
                tenant_id: tenantId
            });
        }
        if (category.is_internal === true) {
            console.log(`❌ [STORE-CATEGORY-DETAIL] Category ${categoryId} is internal for tenant: ${tenantId}`);
            return res.status(404).json({
                error: 'Category not found',
                message: `Category with ID ${categoryId} is not available`,
                category_id: categoryId,
                tenant_id: tenantId
            });
        }
        // Build response in Medusa store API format
        const response = {
            product_category: category
        };
        console.log(`✅ [STORE-CATEGORY-DETAIL] Retrieved category ${categoryId} (${category.name}) for tenant: ${tenantId}`);
        // Set response headers for debugging
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Store-API', 'true');
        res.setHeader('X-Tenant-Filtered', 'true');
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('❌ [STORE-CATEGORY-DETAIL] Error:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        const categoryId = req.params.id;
        return res.status(500).json({
            error: 'Failed to fetch category',
            message: error.message,
            category_id: categoryId,
            tenant_id: tenantId,
            _debug: {
                error_type: 'store_category_detail_error',
                timestamp: new Date().toISOString(),
                stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
            }
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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