"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.POST = POST;
const utils_1 = require("@medusajs/framework/utils");
/**
 * Simple Customer Registration
 * This endpoint creates a customer without complex authentication flows
 */
async function POST(req, res) {
    try {
        const { email, first_name, last_name, phone } = req.body;
        const customerModuleService = req.scope.resolve("customer");
        const logger = req.scope.resolve(utils_1.ContainerRegistrationKeys.LOGGER);
        logger.info(`[Customer Registration] Creating customer: ${email}`);
        // Validate required fields
        if (!email) {
            res.status(400).json({
                error: "Validation error",
                message: "Email is required"
            });
            return;
        }
        // Check if customer already exists
        try {
            const existingCustomers = await customerModuleService.listCustomers({
                email: email
            });
            if (existingCustomers && existingCustomers.length > 0) {
                res.status(409).json({
                    error: "Customer exists",
                    message: "Customer with this email already exists",
                    customer: {
                        id: existingCustomers[0].id,
                        email: existingCustomers[0].email,
                        first_name: existingCustomers[0].first_name,
                        last_name: existingCustomers[0].last_name
                    }
                });
                return;
            }
        }
        catch (error) {
            // If listing fails, continue with creation
            logger.warn(`[Customer Registration] Could not check existing customer: ${error}`);
        }
        // Create customer data
        const customerData = {
            email: email.toLowerCase().trim(),
            first_name: first_name || "Guest",
            last_name: last_name || "Customer",
            phone: phone || null,
            metadata: {
                registration_method: "store_api",
                created_via: "simple_registration",
                registration_date: new Date().toISOString()
            }
        };
        logger.info(`[Customer Registration] Creating customer with data:`, customerData);
        // Create the customer
        const customer = await customerModuleService.createCustomers(customerData);
        logger.info(`[Customer Registration] Customer created successfully:`, customer.id);
        // Return success response
        res.status(201).json({
            success: true,
            customer: {
                id: customer.id,
                email: customer.email,
                first_name: customer.first_name,
                last_name: customer.last_name,
                phone: customer.phone,
                created_at: customer.created_at,
                metadata: customer.metadata
            },
            message: "Customer registered successfully"
        });
    }
    catch (error) {
        const logger = req.scope.resolve(utils_1.ContainerRegistrationKeys.LOGGER);
        logger.error(`[Customer Registration] Error creating customer:`, error);
        res.status(500).json({
            error: "Registration failed",
            message: error.message || "Failed to register customer",
            details: process.env.NODE_ENV === "development" ? error.stack : undefined
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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