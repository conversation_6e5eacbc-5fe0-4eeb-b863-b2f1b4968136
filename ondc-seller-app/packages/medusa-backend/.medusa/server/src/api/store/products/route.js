"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
const tenant_service_factory_1 = require("../../../services/tenant-service-factory");
const enhanced_query_parser_1 = require("../../../utils/enhanced-query-parser");
const store_api_configs_1 = require("../../../config/store-api-configs");
/**
 * Store Products Endpoint - Customer-facing API with tenant filtering
 *
 * This endpoint provides tenant-filtered products for the store/customer-facing API.
 * It uses the tenant-aware services to ensure proper data isolation.
 */
async function GET(req, res) {
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        console.log(`🛒 [STORE-PRODUCTS] Getting products for tenant: ${tenantId}`);
        console.log(`🛒 [STORE-PRODUCTS] Query params:`, req.query);
        // Validate query parameters
        const validation = (0, store_api_configs_1.validateQueryParams)(req.query, store_api_configs_1.PRODUCTS_CONFIG);
        if (!validation.valid) {
            console.warn(`⚠️ [STORE-PRODUCTS] Invalid query parameters:`, validation.errors);
            return res.status(400).json({
                error: 'Invalid query parameters',
                details: validation.errors,
                tenant_id: tenantId,
            });
        }
        // Parse query parameters with comprehensive support
        const parsedQuery = (0, enhanced_query_parser_1.parseQueryParameters)(req, store_api_configs_1.PRODUCTS_CONFIG);
        console.log(`🔍 [STORE-PRODUCTS] Parsed query:`, parsedQuery);
        // Create tenant-aware services
        const services = tenant_service_factory_1.TenantServiceFactory.fromRequest(req);
        // Build filters for store API (customer-facing)
        const filters = {
            status: 'published', // Only show published products in store
            ...parsedQuery.filters, // Add parsed filters
        };
        // Add date filters
        if (parsedQuery.dateFilters.created_after) {
            filters.created_at = { ...filters.created_at, $gte: parsedQuery.dateFilters.created_after };
        }
        if (parsedQuery.dateFilters.created_before) {
            filters.created_at = { ...filters.created_at, $lte: parsedQuery.dateFilters.created_before };
        }
        if (parsedQuery.dateFilters.updated_after) {
            filters.updated_at = { ...filters.updated_at, $gte: parsedQuery.dateFilters.updated_after };
        }
        if (parsedQuery.dateFilters.updated_before) {
            filters.updated_at = { ...filters.updated_at, $lte: parsedQuery.dateFilters.updated_before };
        }
        // Add search filters
        if (parsedQuery.search) {
            const searchConditions = parsedQuery.search.fields.map(field => ({
                [field]: { $ilike: `%${parsedQuery.search.query}%` },
            }));
            filters.$or = searchConditions;
        }
        // Legacy search support (for backward compatibility)
        const legacySearch = req.query.search || req.query.q;
        if (legacySearch && !parsedQuery.search) {
            filters.title = { $ilike: `%${legacySearch}%` };
        }
        // Legacy filter support (for backward compatibility)
        const legacyFilters = ['price_list_id', 'sales_channel_id', 'region_id', 'currency_code'];
        legacyFilters.forEach(filterKey => {
            if (req.query[filterKey]) {
                filters[filterKey] = req.query[filterKey];
            }
        });
        // Build config for pagination, field selection, and sorting
        const config = {
            take: parsedQuery.pagination.limit,
            skip: parsedQuery.pagination.offset,
        };
        // Add field selection if specified
        if (parsedQuery.fields) {
            config.select = parsedQuery.fields;
        }
        // Add relations to expand if specified
        if (parsedQuery.expand) {
            config.relations = parsedQuery.expand;
        }
        else {
            // Default relations for store API (only include existing relations)
            config.relations = ['variants', 'variants.prices', 'images', 'categories', 'tags'];
        }
        // Add sorting configuration
        if (parsedQuery.sorting.length > 0) {
            config.order = {};
            parsedQuery.sorting.forEach(sort => {
                config.order[sort.field] = sort.direction;
            });
        }
        console.log(`🛒 [STORE-PRODUCTS] Filters:`, filters);
        console.log(`🛒 [STORE-PRODUCTS] Config:`, config);
        // Get products using tenant-aware service
        const [products, totalCount] = await services.product.listAndCountProducts(filters, config);
        // Build response in Medusa store API format with enhanced metadata
        const response = {
            products: products || [],
            count: products?.length || 0,
            offset: parsedQuery.pagination.offset,
            limit: parsedQuery.pagination.limit,
            total: totalCount || 0,
            // Enhanced metadata
            _meta: {
                pagination: {
                    page: parsedQuery.pagination.page ||
                        Math.floor(parsedQuery.pagination.offset / parsedQuery.pagination.limit) + 1,
                    per_page: parsedQuery.pagination.limit,
                    total_pages: Math.ceil((totalCount || 0) / parsedQuery.pagination.limit),
                    has_more: parsedQuery.pagination.offset + parsedQuery.pagination.limit < (totalCount || 0),
                },
                filters_applied: Object.keys(parsedQuery.filters).length > 0 ? parsedQuery.filters : null,
                sorting: parsedQuery.sorting,
                search: parsedQuery.search ? parsedQuery.search.query : null,
                tenant_id: tenantId,
            },
        };
        console.log(`✅ [STORE-PRODUCTS] Retrieved ${response.count}/${response.total} products for tenant: ${tenantId}`);
        // Set response headers for debugging
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Store-API', 'true');
        res.setHeader('X-Tenant-Filtered', 'true');
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('❌ [STORE-PRODUCTS] Error:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        return res.status(500).json({
            error: 'Failed to fetch products',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'store_products_error',
                timestamp: new Date().toISOString(),
                stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
            },
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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