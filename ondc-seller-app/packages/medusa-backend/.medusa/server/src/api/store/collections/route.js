"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
const tenant_service_factory_1 = require("../../../services/tenant-service-factory");
const enhanced_query_parser_1 = require("../../../utils/enhanced-query-parser");
const store_api_configs_1 = require("../../../config/store-api-configs");
/**
 * Store Collections Endpoint - Customer-facing API with tenant filtering
 *
 * This endpoint provides tenant-filtered collections for the store/customer-facing API.
 * It uses the tenant-aware services to ensure proper data isolation.
 */
async function GET(req, res) {
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        console.log(`🛒 [STORE-COLLECTIONS] Getting collections for tenant: ${tenantId}`);
        console.log(`🛒 [STORE-COLLECTIONS] Query params:`, req.query);
        // Validate query parameters
        const validation = (0, store_api_configs_1.validateQueryParams)(req.query, store_api_configs_1.COLLECTIONS_CONFIG);
        if (!validation.valid) {
            console.warn(`⚠️ [STORE-COLLECTIONS] Invalid query parameters:`, validation.errors);
            return res.status(400).json({
                error: 'Invalid query parameters',
                details: validation.errors,
                tenant_id: tenantId,
            });
        }
        // Parse query parameters with comprehensive support
        const parsedQuery = (0, enhanced_query_parser_1.parseQueryParameters)(req, store_api_configs_1.COLLECTIONS_CONFIG);
        console.log(`🔍 [STORE-COLLECTIONS] Parsed query:`, parsedQuery);
        // Create tenant-aware services
        const services = tenant_service_factory_1.TenantServiceFactory.fromRequest(req);
        // Build filters for store API (customer-facing)
        const filters = {};
        // Add handle filter
        if (handle) {
            filters.handle = handle;
        }
        // Add title search filter
        if (title) {
            filters.title = { $ilike: `%${title}%` };
        }
        // Add date filters
        if (created_at) {
            filters.created_at = created_at;
        }
        if (updated_at) {
            filters.updated_at = updated_at;
        }
        // Build config for pagination and field selection
        const config = {
            take: parseInt(limit),
            skip: parseInt(offset),
        };
        // Add field selection if specified
        if (fields) {
            const fieldArray = Array.isArray(fields) ? fields : [fields];
            config.select = fieldArray;
        }
        // Add relations to expand if specified
        if (expand) {
            const expandArray = Array.isArray(expand) ? expand : [expand];
            config.relations = expandArray;
        }
        else {
            // Default relations for store API
            config.relations = ['products'];
        }
        console.log(`🛒 [STORE-COLLECTIONS] Filters:`, filters);
        console.log(`🛒 [STORE-COLLECTIONS] Config:`, config);
        // Get collections using tenant-aware service
        const [collections, totalCount] = await services.collection.listAndCountCollections(filters, config);
        // Build response in Medusa store API format
        const response = {
            collections: collections || [],
            count: collections?.length || 0,
            offset: parseInt(offset),
            limit: parseInt(limit),
            total: totalCount || 0,
        };
        console.log(`✅ [STORE-COLLECTIONS] Retrieved ${response.count}/${response.total} collections for tenant: ${tenantId}`);
        // Set response headers for debugging
        res.setHeader('X-Tenant-ID', tenantId);
        res.setHeader('X-Store-API', 'true');
        res.setHeader('X-Tenant-Filtered', 'true');
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('❌ [STORE-COLLECTIONS] Error:', error);
        const tenantId = req.headers['x-tenant-id'] || 'default';
        return res.status(500).json({
            error: 'Failed to fetch collections',
            message: error.message,
            tenant_id: tenantId,
            _debug: {
                error_type: 'store_collections_error',
                timestamp: new Date().toISOString(),
                stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
            },
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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