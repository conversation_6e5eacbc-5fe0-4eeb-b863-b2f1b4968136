"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GET = GET;
/**
 * Store Single Order Endpoint - Customer-facing API with tenant filtering and authentication
 *
 * This endpoint provides tenant-filtered individual order details for authenticated customers.
 * It ensures proper multi-tenant isolation and customer authentication.
 */
async function GET(req, res) {
    const orderId = req.params.id;
    console.log(`📦 [STORE-ORDER-DETAIL] Getting order ${orderId} for authenticated customer`);
    console.log(`📦 [STORE-ORDER-DETAIL] Headers:`, {
        authorization: req.headers.authorization ? 'Bearer [REDACTED]' : 'None',
        'x-tenant-id': req.headers['x-tenant-id'],
        'x-publishable-api-key': req.headers['x-publishable-api-key'],
    });
    try {
        // Extract tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        console.log(`📦 [STORE-ORDER-DETAIL] Processing order request for tenant: ${tenantId}`);
        // Validate order ID
        if (!orderId) {
            console.log(`❌ [STORE-ORDER-DETAIL] Order ID is required`);
            return res.status(400).json({
                error: 'Bad Request',
                message: 'Order ID is required',
                tenant_id: tenantId,
            });
        }
        // Check if user is authenticated
        if (!req.auth_context || !req.auth_context.actor_id) {
            console.log(`❌ [STORE-ORDER-DETAIL] No authentication context found`);
            return res.status(401).json({
                error: 'Unauthorized',
                message: 'Authentication required to access order details',
            });
        }
        const customerId = req.auth_context.actor_id;
        console.log(`📦 [STORE-ORDER-DETAIL] Authenticated customer ID: ${customerId}`);
        // Get query parameters for field selection and expansion
        const { fields, expand } = req.query;
        console.log(`📦 [STORE-ORDER-DETAIL] Query params:`, { fields, expand });
        // Use direct database connection for now to ensure proper tenant filtering
        const { Client } = require('pg');
        const client = new Client({
            connectionString: process.env.DATABASE_URL ||
                'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
        });
        let order = null;
        try {
            await client.connect();
            console.log(`🔗 [STORE-ORDER-DETAIL] Connected to database`);
            // First, check if order exists and belongs to the authenticated customer and tenant
            const orderCheckResult = await client.query(`SELECT id, customer_id, tenant_id 
         FROM "order" 
         WHERE id = $1 
         AND customer_id = $2 
         AND tenant_id = $3 
         AND deleted_at IS NULL`, [orderId, customerId, tenantId]);
            if (orderCheckResult.rows.length === 0) {
                console.log(`❌ [STORE-ORDER-DETAIL] Order ${orderId} not found for customer ${customerId} in tenant ${tenantId}`);
                return res.status(404).json({
                    error: 'Not Found',
                    message: 'Order not found or access denied',
                    order_id: orderId,
                    tenant_id: tenantId,
                });
            }
            console.log(`✅ [STORE-ORDER-DETAIL] Order ${orderId} found and belongs to customer`);
            // Get comprehensive order details with related data
            const result = await client.query(`
        SELECT
          o.id, o.status, o.currency_code, o.email, o.display_id,
          o.created_at, o.updated_at, o.tenant_id, o.metadata,
          o.customer_id, o.region_id, o.sales_channel_id,
          o.shipping_address_id, o.billing_address_id,
          o.is_draft_order, o.no_notification,
          os.totals as order_totals,

          -- Customer information
          c.id as customer_id, c.email as customer_email,
          c.first_name as customer_first_name, c.last_name as customer_last_name,
          c.phone as customer_phone,

          -- Shipping address
          sa.id as shipping_address_id, sa.first_name as shipping_first_name,
          sa.last_name as shipping_last_name, sa.address_1 as shipping_address_1,
          sa.address_2 as shipping_address_2, sa.city as shipping_city,
          sa.postal_code as shipping_postal_code, sa.province as shipping_province,
          sa.country_code as shipping_country_code, sa.phone as shipping_phone,

          -- Billing address
          ba.id as billing_address_id, ba.first_name as billing_first_name,
          ba.last_name as billing_last_name, ba.address_1 as billing_address_1,
          ba.address_2 as billing_address_2, ba.city as billing_city,
          ba.postal_code as billing_postal_code, ba.province as billing_province,
          ba.country_code as billing_country_code, ba.phone as billing_phone

        FROM "order" o
        LEFT JOIN order_summary os ON o.id = os.order_id AND os.deleted_at IS NULL
        LEFT JOIN customer c ON o.customer_id = c.id AND c.deleted_at IS NULL
        LEFT JOIN order_address sa ON o.shipping_address_id = sa.id AND sa.deleted_at IS NULL
        LEFT JOIN order_address ba ON o.billing_address_id = ba.id AND ba.deleted_at IS NULL
        WHERE o.id = $1
        AND o.customer_id = $2
        AND o.tenant_id = $3
        AND o.deleted_at IS NULL
      `, [orderId, customerId, tenantId]);
            if (result.rows.length === 0) {
                console.log(`❌ [STORE-ORDER-DETAIL] Order ${orderId} not found after detailed query`);
                return res.status(404).json({
                    error: 'Not Found',
                    message: 'Order not found',
                    order_id: orderId,
                });
            }
            const orderRow = result.rows[0];
            console.log(`✅ [STORE-ORDER-DETAIL] Retrieved order details for ${orderId}`);
            // Get order line items with order_item quantities
            const itemsResult = await client.query(`
        SELECT
          oli.id, oli.title, oli.subtitle, oli.thumbnail,
          oli.unit_price, oli.metadata, oli.variant_id, oli.product_id,
          oli.created_at, oli.updated_at, oli.product_title, oli.product_description,
          oli.variant_title, oli.variant_sku, oli.variant_barcode,

          -- Order item quantities and totals
          oi.quantity, oi.fulfilled_quantity, oi.shipped_quantity,

          -- Product information
          p.id as product_id, p.title as product_title,
          p.description as product_description, p.thumbnail as product_thumbnail,

          -- Variant information
          pv.id as variant_id, pv.title as variant_title,
          pv.sku as variant_sku, pv.barcode as variant_barcode

        FROM order_line_item oli
        LEFT JOIN order_item oi ON oli.id = oi.item_id AND oi.deleted_at IS NULL
        LEFT JOIN product p ON oli.product_id = p.id AND p.deleted_at IS NULL
        LEFT JOIN product_variant pv ON oli.variant_id = pv.id AND pv.deleted_at IS NULL
        WHERE oi.order_id = $1
        AND oli.deleted_at IS NULL
        ORDER BY oli.created_at ASC
      `, [orderId]);
            const items = itemsResult.rows.map(item => ({
                id: item.id,
                title: item.title,
                subtitle: item.subtitle,
                thumbnail: item.thumbnail,
                quantity: item.quantity,
                fulfilled_quantity: item.fulfilled_quantity,
                shipped_quantity: item.shipped_quantity,
                unit_price: item.unit_price,
                metadata: item.metadata,
                variant_id: item.variant_id,
                product_id: item.product_id,
                created_at: item.created_at,
                updated_at: item.updated_at,
                product: item.product_id
                    ? {
                        id: item.product_id,
                        title: item.product_title,
                        description: item.product_description,
                        thumbnail: item.product_thumbnail,
                    }
                    : null,
                variant: item.variant_id
                    ? {
                        id: item.variant_id,
                        title: item.variant_title,
                        sku: item.variant_sku,
                        barcode: item.variant_barcode,
                    }
                    : null,
            }));
            console.log(`📦 [STORE-ORDER-DETAIL] Found ${items.length} items for order ${orderId}`);
            // Get comprehensive financial totals (same as store orders list endpoint)
            const financialResult = await client.query(`
        SELECT
          -- Item totals
          COALESCE(SUM(oli.unit_price * oi.quantity), 0) as original_item_total,
          COALESCE(SUM(oli.unit_price * oi.quantity), 0) as original_item_subtotal,
          0 as original_item_tax_total,
          COALESCE(SUM(oli.unit_price * oi.quantity), 0) as item_total,
          COALESCE(SUM(oli.unit_price * oi.quantity), 0) as item_subtotal,
          0 as item_tax_total,

          -- Shipping totals (default to 50 for now, can be made dynamic)
          50 as original_shipping_total,
          50 as original_shipping_subtotal,
          0 as original_shipping_tax_total,
          50 as shipping_total,
          50 as shipping_subtotal,
          0 as shipping_tax_total,

          -- Discount and gift card totals
          0 as discount_total,
          0 as discount_tax_total,
          0 as gift_card_total,
          0 as gift_card_tax_total,

          -- Payment totals
          0 as paid_total,
          0 as refunded_total

        FROM order_line_item oli
        LEFT JOIN order_item oi ON oli.id = oi.item_id AND oi.deleted_at IS NULL
        WHERE oi.order_id = $1
        AND oli.deleted_at IS NULL
      `, [orderId]);
            const financials = financialResult.rows[0] || {};
            // Calculate derived totals (corrected logic to match store orders list)
            // Ensure all values are properly converted to numbers
            const originalSubtotal = Number(financials.original_item_subtotal) || 0; // Items only
            const originalTaxTotal = (Number(financials.original_item_tax_total) || 0) +
                (Number(financials.original_shipping_tax_total) || 0);
            const originalTotal = originalSubtotal +
                (Number(financials.original_shipping_subtotal) || 0) +
                originalTaxTotal -
                (Number(financials.discount_total) || 0) -
                (Number(financials.gift_card_total) || 0);
            const subtotal = Number(financials.item_subtotal) || 0; // Items only
            const taxTotal = (Number(financials.item_tax_total) || 0) + (Number(financials.shipping_tax_total) || 0);
            const total = subtotal +
                (Number(financials.shipping_subtotal) || 0) +
                taxTotal -
                (Number(financials.discount_total) || 0) -
                (Number(financials.gift_card_total) || 0);
            const pendingDifference = total - (Number(financials.paid_total) || 0);
            // Build comprehensive order object with all financial fields
            order = {
                id: orderRow.id,
                status: orderRow.status,
                currency_code: orderRow.currency_code,
                email: orderRow.email,
                display_id: orderRow.display_id,
                created_at: orderRow.created_at,
                updated_at: orderRow.updated_at,
                tenant_id: orderRow.tenant_id,
                metadata: orderRow.metadata,
                customer_id: orderRow.customer_id,
                region_id: orderRow.region_id,
                sales_channel_id: orderRow.sales_channel_id,
                shipping_address_id: orderRow.shipping_address_id,
                billing_address_id: orderRow.billing_address_id,
                is_draft_order: orderRow.is_draft_order,
                no_notification: orderRow.no_notification,
                // Comprehensive financial fields (matching store orders list format)
                original_item_total: financials.original_item_total || 0,
                original_item_subtotal: financials.original_item_subtotal || 0,
                original_item_tax_total: financials.original_item_tax_total || 0,
                item_total: financials.item_total || 0,
                item_subtotal: financials.item_subtotal || 0,
                item_tax_total: financials.item_tax_total || 0,
                original_total: originalTotal,
                original_subtotal: originalSubtotal,
                original_tax_total: originalTaxTotal,
                total: total,
                subtotal: subtotal,
                tax_total: taxTotal,
                discount_total: financials.discount_total || 0,
                discount_tax_total: financials.discount_tax_total || 0,
                gift_card_total: financials.gift_card_total || 0,
                gift_card_tax_total: financials.gift_card_tax_total || 0,
                shipping_total: financials.shipping_total || 0,
                shipping_subtotal: financials.shipping_subtotal || 0,
                shipping_tax_total: financials.shipping_tax_total || 0,
                original_shipping_total: financials.original_shipping_total || 0,
                original_shipping_subtotal: financials.original_shipping_subtotal || 0,
                original_shipping_tax_total: financials.original_shipping_tax_total || 0,
                paid_total: financials.paid_total || 0,
                refunded_total: financials.refunded_total || 0,
                pending_difference: pendingDifference,
                // Totals from order_summary (for compatibility)
                totals: orderRow.order_totals,
                // Customer information
                customer: orderRow.customer_id
                    ? {
                        id: orderRow.customer_id,
                        email: orderRow.customer_email,
                        first_name: orderRow.customer_first_name,
                        last_name: orderRow.customer_last_name,
                        phone: orderRow.customer_phone,
                    }
                    : null,
                // Shipping address
                shipping_address: orderRow.shipping_address_id
                    ? {
                        id: orderRow.shipping_address_id,
                        first_name: orderRow.shipping_first_name,
                        last_name: orderRow.shipping_last_name,
                        address_1: orderRow.shipping_address_1,
                        address_2: orderRow.shipping_address_2,
                        city: orderRow.shipping_city,
                        postal_code: orderRow.shipping_postal_code,
                        province: orderRow.shipping_province,
                        country_code: orderRow.shipping_country_code,
                        phone: orderRow.shipping_phone,
                    }
                    : null,
                // Billing address
                billing_address: orderRow.billing_address_id
                    ? {
                        id: orderRow.billing_address_id,
                        first_name: orderRow.billing_first_name,
                        last_name: orderRow.billing_last_name,
                        address_1: orderRow.billing_address_1,
                        address_2: orderRow.billing_address_2,
                        city: orderRow.billing_city,
                        postal_code: orderRow.billing_postal_code,
                        province: orderRow.billing_province,
                        country_code: orderRow.billing_country_code,
                        phone: orderRow.billing_phone,
                    }
                    : null,
                // Order items
                items: items,
            };
        }
        catch (dbError) {
            console.error('❌ [STORE-ORDER-DETAIL] Database error:', dbError);
            throw dbError;
        }
        finally {
            await client.end();
            console.log(`🔗 [STORE-ORDER-DETAIL] Database connection closed`);
        }
        // Return response in Medusa v2 format
        const response = {
            order,
        };
        console.log(`✅ [STORE-ORDER-DETAIL] Returning order ${orderId} for customer ${customerId}`);
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('❌ [STORE-ORDER-DETAIL] Error fetching order:', error);
        return res.status(500).json({
            error: 'Internal Server Error',
            message: 'Failed to fetch order details',
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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