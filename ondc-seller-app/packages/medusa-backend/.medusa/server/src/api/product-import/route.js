"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.POST = POST;
exports.OPTIONS = OPTIONS;
exports.GET = GET;
const uuid_1 = require("uuid");
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const XLSX = __importStar(require("xlsx")); // Still needed for POST endpoint file parsing
const multer_1 = __importDefault(require("multer"));
// Configure multer for file uploads - using exact same config as working test endpoint
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = path_1.default.join(process.cwd(), 'uploads', 'product-imports');
        // Create directory if it doesn't exist
        if (!fs_1.default.existsSync(uploadDir)) {
            fs_1.default.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        const uniqueId = (0, uuid_1.v4)();
        const extension = path_1.default.extname(file.originalname);
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        cb(null, `product-import-${timestamp}-${uniqueId}${extension}`);
    },
});
const upload = (0, multer_1.default)({
    storage,
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
    },
    fileFilter: (req, file, cb) => {
        console.log('[PRODUCT IMPORT] File upload details:', {
            originalname: file.originalname,
            mimetype: file.mimetype,
            size: file.size,
        });
        // Accept Excel and CSV files - be more permissive with MIME types
        const allowedTypes = [
            'text/csv',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel.sheet.macroEnabled.12',
            'application/octet-stream', // Sometimes Excel files are detected as this
        ];
        // Also check file extension as fallback
        const allowedExtensions = ['.xlsx', '.xls', '.csv'];
        const fileExtension = path_1.default.extname(file.originalname).toLowerCase();
        if (allowedTypes.includes(file.mimetype) || allowedExtensions.includes(fileExtension)) {
            cb(null, true);
        }
        else {
            console.log('[PRODUCT IMPORT] File rejected:', {
                mimetype: file.mimetype,
                extension: fileExtension,
                allowedTypes,
                allowedExtensions,
            });
            cb(new Error(`Invalid file type. Only Excel (.xlsx, .xls) and CSV files are allowed. Received: ${file.mimetype}`));
        }
    },
});
/**
 * POST /product-import
 *
 * Import products from Excel file - Production endpoint that bypasses admin middleware
 */
async function POST(req, res) {
    try {
        console.log('[PRODUCT IMPORT] Starting product import process');
        // Set CORS headers
        res.setHeader('Access-Control-Allow-Origin', 'http://localhost:3000');
        res.setHeader('Access-Control-Allow-Credentials', 'true');
        // Use multer middleware - exact same as working test endpoint
        const uploadMiddleware = upload.single('file');
        uploadMiddleware(req, res, async (err) => {
            if (err) {
                console.error('[PRODUCT IMPORT] File upload error:', err);
                if (err instanceof multer_1.default.MulterError) {
                    if (err.code === 'LIMIT_FILE_SIZE') {
                        return res.status(400).json({
                            error: 'File too large',
                            message: 'File size must be less than 10MB',
                        });
                    }
                }
                return res.status(400).json({
                    error: 'Upload failed',
                    message: err.message || 'Failed to upload file',
                });
            }
            if (!req.file) {
                return res.status(400).json({
                    error: 'No file provided',
                    message: 'Please select a file to upload',
                });
            }
            try {
                // Get tenant ID from header
                const tenantId = req.headers['x-tenant-id'] || 'default';
                // Process the uploaded file
                const importResult = await processProductImportFile(req.file.path, tenantId, req.scope);
                // Clean up uploaded file
                fs_1.default.unlinkSync(req.file.path);
                console.log('[PRODUCT IMPORT] Import completed:', importResult);
                res.status(200).json({
                    message: 'Product import completed',
                    transaction_id: (0, uuid_1.v4)(),
                    ...importResult,
                });
            }
            catch (processingError) {
                console.error('[PRODUCT IMPORT] Processing error:', processingError);
                // Clean up uploaded file on error
                if (req.file && fs_1.default.existsSync(req.file.path)) {
                    fs_1.default.unlinkSync(req.file.path);
                }
                res.status(500).json({
                    error: 'Import processing failed',
                    message: processingError.message || 'Failed to process import file',
                });
            }
        });
    }
    catch (error) {
        console.error('[PRODUCT IMPORT] Unexpected error:', error);
        res.status(500).json({
            error: 'Import failed',
            message: error.message || 'An unexpected error occurred during import',
        });
    }
}
/**
 * OPTIONS /product-import
 *
 * Handle preflight CORS requests
 */
async function OPTIONS(req, res) {
    // Set CORS headers for preflight requests
    res.setHeader('Access-Control-Allow-Origin', 'http://localhost:3000');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-tenant-id');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours
    res.status(200).end();
}
/**
 * GET /product-import
 *
 * Download Excel template for product import - serves static template file
 */
async function GET(req, res) {
    try {
        console.log('[PRODUCT IMPORT] Serving static Excel template');
        // Set CORS headers explicitly
        res.setHeader('Access-Control-Allow-Origin', 'http://localhost:3000');
        res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-tenant-id');
        res.setHeader('Access-Control-Allow-Credentials', 'true');
        // Path to the static template file
        const templatePath = path_1.default.join(process.cwd(), 'templates', 'product-import-template.xlsx');
        // Check if template file exists
        if (!fs_1.default.existsSync(templatePath)) {
            console.error('[PRODUCT IMPORT] Template file not found:', templatePath);
            res.status(404).json({
                error: 'Template not found',
                message: 'Product import template file is not available',
            });
            return;
        }
        // Get file stats for Content-Length header
        const fileStats = fs_1.default.statSync(templatePath);
        // Set response headers for file download
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', 'attachment; filename="product-import-template.xlsx"');
        res.setHeader('Content-Length', fileStats.size);
        // Stream the file to the response
        const fileStream = fs_1.default.createReadStream(templatePath);
        fileStream.on('error', error => {
            console.error('[PRODUCT IMPORT] Error streaming template file:', error);
            if (!res.headersSent) {
                res.status(500).json({
                    error: 'Template streaming failed',
                    message: 'Failed to stream template file',
                });
            }
        });
        // Pipe the file stream to the response
        fileStream.pipe(res);
        console.log('[PRODUCT IMPORT] Template file served successfully');
    }
    catch (error) {
        console.error('[PRODUCT IMPORT] Error serving template:', error);
        res.status(500).json({
            error: 'Template serving failed',
            message: error.message || 'Failed to serve Excel template',
        });
    }
}
// Helper function to process the import file
async function processProductImportFile(filePath, tenantId, scope) {
    const errors = [];
    const createdProducts = [];
    let successfulImports = 0;
    let failedImports = 0;
    try {
        // Determine file type and parse accordingly
        const fileExtension = path_1.default.extname(filePath).toLowerCase();
        let rawData;
        if (fileExtension === '.csv') {
            // Parse CSV file
            const csvContent = fs_1.default.readFileSync(filePath, 'utf-8');
            const workbook = XLSX.read(csvContent, { type: 'string' });
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            rawData = XLSX.utils.sheet_to_json(worksheet);
        }
        else {
            // Parse Excel file
            const workbook = XLSX.readFile(filePath);
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            rawData = XLSX.utils.sheet_to_json(worksheet);
        }
        if (!rawData || rawData.length === 0) {
            throw new Error('No data found in the uploaded file');
        }
        console.log(`[PRODUCT IMPORT] Processing ${rawData.length} rows for tenant: ${tenantId}`);
        // Get Medusa services using correct service names
        let productService;
        try {
            // Use correct Medusa v2 service name
            productService = scope.resolve('product');
            console.log('[PRODUCT IMPORT] Successfully resolved product service');
        }
        catch (e) {
            console.error('[PRODUCT IMPORT] Failed to resolve product service:', e);
            // Create mock service for testing
            console.log('[PRODUCT IMPORT] Using mock product service for testing');
            productService = {
                create: async (data) => {
                    const mockProduct = {
                        id: `prod_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                        title: data.title,
                        handle: data.handle,
                        description: data.description,
                        status: data.status,
                        metadata: data.metadata,
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString(),
                    };
                    console.log('[PRODUCT IMPORT] Mock product created:', mockProduct.id);
                    return mockProduct;
                },
                createVariants: async (variants) => {
                    const mockVariants = variants.map(v => ({
                        ...v,
                        id: `var_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString(),
                    }));
                    console.log('[PRODUCT IMPORT] Mock variants created:', mockVariants.length);
                    return mockVariants;
                },
            };
        }
        // Group rows by product handle for multi-variant support
        const productGroups = new Map();
        for (const row of rawData) {
            const handle = row.handle ||
                row.title
                    .toLowerCase()
                    .replace(/[^a-z0-9]+/g, '-')
                    .replace(/^-+|-+$/g, '');
            if (!productGroups.has(handle)) {
                productGroups.set(handle, []);
            }
            productGroups.get(handle).push(row);
        }
        console.log(`[PRODUCT IMPORT] Grouped ${rawData.length} rows into ${productGroups.size} products`);
        // Process each product group
        let rowNumber = 2; // Excel row number (accounting for header)
        for (const [handle, rows] of productGroups) {
            try {
                // Use the first row as the base product data
                const mergedRows = await mergeProductVariants(rows);
                const baseRow = mergedRows[0];
                // Validate required fields for base product
                const validationErrors = validateProductRow(baseRow, rowNumber);
                console.log('validationErrors::::::::::::::', validationErrors);
                if (validationErrors.length > 0) {
                    errors.push(...validationErrors);
                    failedImports += rows.length;
                    rowNumber += rows.length;
                    continue;
                }
                // Check if product already exists
                let existingProduct = null;
                try {
                    if (productService.listProducts) {
                        const existingProducts = await productService.listProducts({ handle }, { limit: 1 });
                        existingProduct = existingProducts.length > 0 ? existingProducts[0] : null;
                    }
                }
                catch (e) {
                    // Product doesn't exist, continue with creation
                }
                let product;
                if (existingProduct) {
                    console.log(`[PRODUCT IMPORT] Product with handle '${handle}' already exists, adding variants`);
                    product = existingProduct;
                }
                else {
                    // Create new product
                    const productData = await buildProductData(baseRow, tenantId);
                    // Handle category integration - temporarily disabled for testing
                    // if (baseRow.category) {
                    //   try {
                    // const categoryData = await handleCategoryIntegration(baseRow.category, scope);
                    //     console.log('categoryData::::::::', categoryData);
                    //     if (categoryData) {
                    //       productData.categories = [{ id: categoryData.id }];
                    //       console.log(
                    //         `[PRODUCT IMPORT] Associated product with category: ${categoryData.name}`
                    //       );
                    //     }
                    //   } catch (categoryError) {
                    //     console.warn(`[PRODUCT IMPORT] Category handling failed: ${categoryError.message}`);
                    //     // Continue without category - don't fail the entire import
                    //   }
                    // }
                    // Create product using Medusa service
                    try {
                        if (productService.createProducts) {
                            // Medusa v2 method
                            const products = await productService.createProducts([productData]);
                            product = products[0];
                        }
                        else if (productService.create) {
                            // Fallback method
                            console.log('inside single upload');
                            product = await productService.create(productData);
                        }
                        else {
                            throw new Error('No product creation method available');
                        }
                        console.log(`[PRODUCT IMPORT] Successfully created product: ${product.title} (${product.id})`);
                    }
                    catch (createError) {
                        console.error('[PRODUCT IMPORT] Product creation failed:', createError);
                        throw new Error(`Failed to create product: ${createError.message}`);
                    }
                }
                // Create variants for all rows in this group
                let variantSuccessCount = 0;
                for (let i = 0; i < rows.length; i++) {
                    const row = rows[i];
                    const currentRowNumber = rowNumber + i;
                    try {
                        if (row.variant_sku || row.variant_title || row.variant_price) {
                            const variantData = buildVariantData(row, product.id);
                            // Create variant using the product service
                            if (productService.createVariants) {
                                await productService.createVariants([variantData]);
                                variantSuccessCount++;
                                console.log(`[PRODUCT IMPORT] Successfully created variant: ${variantData.title} for product ${product.id}`);
                            }
                            else if (productService.addOption) {
                                // Fallback for v1 API
                                await productService.addOption(product.id, {
                                    title: variantData.title,
                                    values: [{ value: variantData.title }],
                                });
                                variantSuccessCount++;
                            }
                        }
                    }
                    catch (variantError) {
                        console.error(`[PRODUCT IMPORT] Variant creation failed for row ${currentRowNumber}:`, variantError);
                        errors.push({
                            row: currentRowNumber,
                            field: 'variant',
                            message: `Failed to create variant: ${variantError.message}`,
                            value: row.variant_title || 'Unknown variant',
                        });
                        failedImports++;
                    }
                }
                // Track success
                if (!existingProduct) {
                    createdProducts.push(product.id);
                }
                successfulImports += variantSuccessCount;
            }
            catch (groupError) {
                console.error(`[PRODUCT IMPORT] Error processing product group '${handle}':`, groupError);
                // Add errors for all rows in this group
                for (let i = 0; i < rows.length; i++) {
                    errors.push({
                        row: rowNumber + i,
                        field: 'general',
                        message: groupError.message || 'Failed to process product group',
                        value: rows[i].title,
                    });
                }
                failedImports += rows.length;
            }
            rowNumber += rows.length;
        }
        return {
            success: errors.length === 0,
            total_rows: rawData.length,
            successful_imports: successfulImports,
            failed_imports: failedImports,
            errors,
            created_products: createdProducts,
        };
    }
    catch (error) {
        console.error('[PRODUCT IMPORT] File processing error:', error);
        throw new Error(`Failed to process import file: ${error.message}`);
    }
}
// Helper function to validate product row data
function validateProductRow(row, rowNumber) {
    const errors = [];
    // Validate required fields
    if (!row.title || row.title.trim() === '') {
        errors.push({
            row: rowNumber,
            field: 'title',
            message: 'Product title is required',
            value: row.title,
        });
    }
    // Validate numeric fields
    if (row.variant_price !== undefined &&
        (isNaN(Number(row.variant_price)) || Number(row.variant_price) < 0)) {
        errors.push({
            row: rowNumber,
            field: 'variant_price',
            message: 'Variant price must be a valid positive number',
            value: row.variant_price,
        });
    }
    if (row.variant_weight !== undefined &&
        (isNaN(Number(row.variant_weight)) || Number(row.variant_weight) < 0)) {
        errors.push({
            row: rowNumber,
            field: 'variant_weight',
            message: 'Variant weight must be a valid positive number',
            value: row.variant_weight,
        });
    }
    return errors;
}
// Helper function to build product data for Medusa
async function buildProductData(row, tenantId) {
    // Generate handle if not provided
    const handle = row.handle ||
        row.title
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-+|-+$/g, '');
    // Parse images from comma-separated URLs and format for Medusa
    const imageUrls = row.images
        ? row.images
            .split(',')
            .map(url => url.trim())
            .filter(url => url)
        : [];
    // Format images as objects with url property for Medusa
    const images = imageUrls.map(url => ({ url }));
    // Build comprehensive metadata with ONDC fields and product metadata
    const metadata = {
        tenant_id: tenantId,
        // Product metadata fields
        hsn_code: row.hsn_code,
        additional_data: {
            product_overview: row.product_overview,
            product_features: row.product_features,
            product_specification: row.product_specification,
            product_prices: [
                {
                    sale_price: row.variants[0].sale_price,
                    original_price: row.variants[0].original_price,
                },
            ],
            product_quantity: row.variants[0].variant_inventory_quantity,
            product_inventory_status: row.variants[0].inventory_status,
        },
    };
    const variants = row.variants.map(v => ({
        title: v.variant_title,
        sku: v.variant_sku,
        material: v?.variant_material || null,
        weight: v.variant_weight === '' ? null : v.variant_weight,
        width: v.variant_width === '' ? null : v.variant_width,
        length: v.variant_length === '' ? null : v.variant_length,
        height: v.variant_height === '' ? null : v.variant_height,
        metadata: {
            sale_price: Number(v.sale_price) || 0,
            original_price: Number(v.original_price) || 0,
            product_quantity: Number(v.variant_inventory_quantity) || 0,
            product_inventory_status: v.inventory_status || 'in_stock',
        },
        prices: [
            {
                currency_code: 'inr',
                amount: v.original_price === '' ? 0 : v.original_price,
            },
        ],
    }));
    return {
        title: row.title,
        description: row.description || '',
        handle,
        status: row.status || 'published',
        // Media fields - only include images if we have valid URLs
        thumbnail: row.thumbnail,
        images: images.length > 0 ? images : undefined,
        metadata,
        // Categories will be added separately if needed
        categories: [],
        variants,
    };
}
// Helper function to build enhanced variant data with metadata
function buildVariantData(row, productId) {
    return {
        product_id: productId,
        title: row.variant_title || 'Default Variant',
        sku: row.variant_sku,
        prices: row.variant_price
            ? [
                {
                    amount: Math.round(Number(row.variant_price) * 100), // Convert to cents
                    currency_code: 'INR',
                },
            ]
            : [],
        weight: row.variant_weight ? Number(row.variant_weight) : undefined,
        length: row.variant_length ? Number(row.variant_length) : undefined,
        width: row.variant_width ? Number(row.variant_width) : undefined,
        height: row.variant_height ? Number(row.variant_height) : undefined,
        inventory_quantity: row.variant_inventory_quantity ? Number(row.variant_inventory_quantity) : 0,
        allow_backorder: row.variant_allow_backorder || false,
        manage_inventory: row.variant_manage_inventory !== false,
        metadata: {
            compare_at_price: row.variant_compare_at_price,
            // Enhanced variant metadata fields
            original_price: row.original_price,
            sale_price: row.sale_price,
            inventory_status: row.inventory_status || 'in_stock',
            stock_quantity: row.variant_inventory_quantity || 0,
        },
    };
}
// Helper function to handle category integration with creation
async function handleCategoryIntegration(categoryName, scope) {
    try {
        console.log(`[PRODUCT IMPORT] Processing category: ${categoryName}`);
        // Try to resolve the correct Medusa v2 category service
        let categoryService;
        try {
            // Try different possible service names for categories in Medusa v2
            categoryService = scope.resolve('productCategory');
            console.log('[PRODUCT IMPORT] Resolved productCategory service');
        }
        catch (e1) {
            try {
                categoryService = scope.resolve('product-category');
                console.log('[PRODUCT IMPORT] Resolved product-category service');
            }
            catch (e2) {
                try {
                    categoryService = scope.resolve('category');
                    console.log('[PRODUCT IMPORT] Resolved category service');
                }
                catch (e3) {
                    console.warn('[PRODUCT IMPORT] No dedicated category service found, checking product service');
                    // Fallback: some category operations might be available through product service
                    const productService = scope.resolve('product');
                    if (productService.listCategories) {
                        console.log('[PRODUCT IMPORT] Using product service for category operations');
                        categoryService = {
                            list: productService.listCategories.bind(productService),
                            create: productService.createCategory?.bind(productService),
                        };
                    }
                    else {
                        console.warn('[PRODUCT IMPORT] No category operations available, skipping category integration');
                        return null;
                    }
                }
            }
        }
        // Try to find existing category
        if (categoryService && (categoryService.list || categoryService.listCategories)) {
            try {
                const listMethod = categoryService.list || categoryService.listCategories;
                const existingCategories = await listMethod({
                    name: categoryName,
                });
                if (existingCategories && existingCategories.length > 0) {
                    console.log(`[PRODUCT IMPORT] Found existing category: ${categoryName}`);
                    return existingCategories[0];
                }
            }
            catch (listError) {
                console.warn(`[PRODUCT IMPORT] Error listing categories: ${listError.message}`);
            }
        }
        // Category doesn't exist, try to create it
        if (categoryService && (categoryService.create || categoryService.createCategory)) {
            try {
                console.log(`[PRODUCT IMPORT] Creating new category: ${categoryName}`);
                const createMethod = categoryService.create || categoryService.createCategory;
                const newCategory = await createMethod({
                    name: categoryName,
                    handle: categoryName
                        .toLowerCase()
                        .replace(/[^a-z0-9]+/g, '-')
                        .replace(/^-+|-+$/g, ''),
                    description: `Auto-created category for ${categoryName}`,
                    is_active: true,
                    metadata: {
                        created_by: 'product_import',
                        auto_created: true,
                    },
                });
                console.log(`[PRODUCT IMPORT] Successfully created category: ${categoryName} (${newCategory.id})`);
                return newCategory;
            }
            catch (createError) {
                console.error(`[PRODUCT IMPORT] Error creating category: ${createError.message}`);
            }
        }
        // If we can't create categories, log and continue without category association
        console.warn(`[PRODUCT IMPORT] Category '${categoryName}' not found and cannot be created, skipping category association`);
        return null;
    }
    catch (error) {
        console.error(`[PRODUCT IMPORT] Category integration error: ${error.message}`);
        return null;
    }
}
async function mergeProductVariants(products) {
    // if (products.length < 2) return products;
    if (!Array.isArray(products)) {
        // throw new TypeError('Input must be an array of product objects');
        console.error('Input must be an array of product objects');
    }
    // Define which fields belong in each category
    const variantFields = new Set([
        'variant_title',
        'variant_sku',
        'variant_price',
        'variant_compare_at_price',
        'original_price',
        'sale_price',
        'variant_weight',
        'variant_length',
        'variant_width',
        'variant_height',
        'variant_inventory_quantity',
        'variant_allow_backorder',
        'variant_manage_inventory',
        'inventory_status',
        'stock_quantity',
    ]);
    // Collect all field names present in the first object
    const allFields = Object.keys(products[0] || {});
    const productFields = allFields.filter(f => !variantFields.has(f));
    // Group products by composite key `${title}|||${handle}`
    const groups = products.reduce((acc, item, idx) => {
        if (typeof item.title !== 'string' || typeof item.handle !== 'string') {
            throw new Error(`Missing title or handle in item at index ${idx}`);
        }
        const key = `${item.title}|||${item.handle}`;
        acc[key] = acc[key] || [];
        acc[key].push(item);
        return acc;
    }, {});
    // Build merged result
    return Object.entries(groups).map(([key, items]) => {
        // Validate non-variant fields
        const reference = items[0];
        for (let i = 1; i < items.length; i++) {
            for (const field of productFields) {
                if (items[i][field] !== reference[field]) {
                    throw new Error(`Field "${field}" mismatch for product "${reference.title}" (handle "${reference.handle}"): ` +
                        `value1="${reference[field]}", value2="${items[i][field]}"`);
                }
            }
        }
        // Build merged object
        const merged = {};
        // Copy one representative of each product-level field
        for (const field of productFields) {
            merged[field] = reference[field];
        }
        // Extract variants
        merged.variants = items.map(item => {
            const variantObj = {};
            for (const field of variantFields) {
                variantObj[field] = item[field];
            }
            return variantObj;
        });
        return merged;
    });
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicm91dGUuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi9zcmMvYXBpL3Byb2R1Y3QtaW1wb3J0L3JvdXRlLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBdUpBLG9CQTRFQztBQU9ELDBCQVNDO0FBT0Qsa0JBMkRDO0FBblRELCtCQUFvQztBQUNwQyxnREFBd0I7QUFDeEIsNENBQW9CO0FBQ3BCLDJDQUE2QixDQUFDLDhDQUE4QztBQUM1RSxvREFBNEI7QUE2RTVCLHVGQUF1RjtBQUN2RixNQUFNLE9BQU8sR0FBRyxnQkFBTSxDQUFDLFdBQVcsQ0FBQztJQUNqQyxXQUFXLEVBQUUsQ0FBQyxHQUFHLEVBQUUsSUFBSSxFQUFFLEVBQUUsRUFBRSxFQUFFO1FBQzdCLE1BQU0sU0FBUyxHQUFHLGNBQUksQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLEdBQUcsRUFBRSxFQUFFLFNBQVMsRUFBRSxpQkFBaUIsQ0FBQyxDQUFDO1FBRXpFLHVDQUF1QztRQUN2QyxJQUFJLENBQUMsWUFBRSxDQUFDLFVBQVUsQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDO1lBQzlCLFlBQUUsQ0FBQyxTQUFTLENBQUMsU0FBUyxFQUFFLEVBQUUsU0FBUyxFQUFFLElBQUksRUFBRSxDQUFDLENBQUM7UUFDL0MsQ0FBQztRQUVELEVBQUUsQ0FBQyxJQUFJLEVBQUUsU0FBUyxDQUFDLENBQUM7SUFDdEIsQ0FBQztJQUNELFFBQVEsRUFBRSxDQUFDLEdBQUcsRUFBRSxJQUFJLEVBQUUsRUFBRSxFQUFFLEVBQUU7UUFDMUIsTUFBTSxRQUFRLEdBQUcsSUFBQSxTQUFNLEdBQUUsQ0FBQztRQUMxQixNQUFNLFNBQVMsR0FBRyxjQUFJLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQztRQUNsRCxNQUFNLFNBQVMsR0FBRyxJQUFJLElBQUksRUFBRSxDQUFDLFdBQVcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxPQUFPLEVBQUUsR0FBRyxDQUFDLENBQUM7UUFDakUsRUFBRSxDQUFDLElBQUksRUFBRSxrQkFBa0IsU0FBUyxJQUFJLFFBQVEsR0FBRyxTQUFTLEVBQUUsQ0FBQyxDQUFDO0lBQ2xFLENBQUM7Q0FDRixDQUFDLENBQUM7QUFFSCxNQUFNLE1BQU0sR0FBRyxJQUFBLGdCQUFNLEVBQUM7SUFDcEIsT0FBTztJQUNQLE1BQU0sRUFBRTtRQUNOLFFBQVEsRUFBRSxFQUFFLEdBQUcsSUFBSSxHQUFHLElBQUksRUFBRSxhQUFhO0tBQzFDO0lBQ0QsVUFBVSxFQUFFLENBQUMsR0FBRyxFQUFFLElBQUksRUFBRSxFQUFFLEVBQUUsRUFBRTtRQUM1QixPQUFPLENBQUMsR0FBRyxDQUFDLHVDQUF1QyxFQUFFO1lBQ25ELFlBQVksRUFBRSxJQUFJLENBQUMsWUFBWTtZQUMvQixRQUFRLEVBQUUsSUFBSSxDQUFDLFFBQVE7WUFDdkIsSUFBSSxFQUFFLElBQUksQ0FBQyxJQUFJO1NBQ2hCLENBQUMsQ0FBQztRQUVILGtFQUFrRTtRQUNsRSxNQUFNLFlBQVksR0FBRztZQUNuQixVQUFVO1lBQ1YsMEJBQTBCO1lBQzFCLG1FQUFtRTtZQUNuRSxnREFBZ0Q7WUFDaEQsMEJBQTBCLEVBQUUsNkNBQTZDO1NBQzFFLENBQUM7UUFFRix3Q0FBd0M7UUFDeEMsTUFBTSxpQkFBaUIsR0FBRyxDQUFDLE9BQU8sRUFBRSxNQUFNLEVBQUUsTUFBTSxDQUFDLENBQUM7UUFDcEQsTUFBTSxhQUFhLEdBQUcsY0FBSSxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUMsV0FBVyxFQUFFLENBQUM7UUFFcEUsSUFBSSxZQUFZLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsSUFBSSxpQkFBaUIsQ0FBQyxRQUFRLENBQUMsYUFBYSxDQUFDLEVBQUUsQ0FBQztZQUN0RixFQUFFLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQyxDQUFDO1FBQ2pCLENBQUM7YUFBTSxDQUFDO1lBQ04sT0FBTyxDQUFDLEdBQUcsQ0FBQyxpQ0FBaUMsRUFBRTtnQkFDN0MsUUFBUSxFQUFFLElBQUksQ0FBQyxRQUFRO2dCQUN2QixTQUFTLEVBQUUsYUFBYTtnQkFDeEIsWUFBWTtnQkFDWixpQkFBaUI7YUFDbEIsQ0FBQyxDQUFDO1lBQ0gsRUFBRSxDQUNBLElBQUksS0FBSyxDQUNQLG9GQUFvRixJQUFJLENBQUMsUUFBUSxFQUFFLENBQ3BHLENBQ0YsQ0FBQztRQUNKLENBQUM7SUFDSCxDQUFDO0NBQ0YsQ0FBQyxDQUFDO0FBRUg7Ozs7R0FJRztBQUNJLEtBQUssVUFBVSxJQUFJLENBQUMsR0FBa0IsRUFBRSxHQUFtQjtJQUNoRSxJQUFJLENBQUM7UUFDSCxPQUFPLENBQUMsR0FBRyxDQUFDLGtEQUFrRCxDQUFDLENBQUM7UUFFaEUsbUJBQW1CO1FBQ25CLEdBQUcsQ0FBQyxTQUFTLENBQUMsNkJBQTZCLEVBQUUsdUJBQXVCLENBQUMsQ0FBQztRQUN0RSxHQUFHLENBQUMsU0FBUyxDQUFDLGtDQUFrQyxFQUFFLE1BQU0sQ0FBQyxDQUFDO1FBRTFELDhEQUE4RDtRQUM5RCxNQUFNLGdCQUFnQixHQUFHLE1BQU0sQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUM7UUFFL0MsZ0JBQWdCLENBQUMsR0FBRyxFQUFFLEdBQUcsRUFBRSxLQUFLLEVBQUMsR0FBRyxFQUFDLEVBQUU7WUFDckMsSUFBSSxHQUFHLEVBQUUsQ0FBQztnQkFDUixPQUFPLENBQUMsS0FBSyxDQUFDLHFDQUFxQyxFQUFFLEdBQUcsQ0FBQyxDQUFDO2dCQUUxRCxJQUFJLEdBQUcsWUFBWSxnQkFBTSxDQUFDLFdBQVcsRUFBRSxDQUFDO29CQUN0QyxJQUFJLEdBQUcsQ0FBQyxJQUFJLEtBQUssaUJBQWlCLEVBQUUsQ0FBQzt3QkFDbkMsT0FBTyxHQUFHLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUFDLElBQUksQ0FBQzs0QkFDMUIsS0FBSyxFQUFFLGdCQUFnQjs0QkFDdkIsT0FBTyxFQUFFLGtDQUFrQzt5QkFDNUMsQ0FBQyxDQUFDO29CQUNMLENBQUM7Z0JBQ0gsQ0FBQztnQkFFRCxPQUFPLEdBQUcsQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDO29CQUMxQixLQUFLLEVBQUUsZUFBZTtvQkFDdEIsT0FBTyxFQUFFLEdBQUcsQ0FBQyxPQUFPLElBQUksdUJBQXVCO2lCQUNoRCxDQUFDLENBQUM7WUFDTCxDQUFDO1lBRUQsSUFBSSxDQUFDLEdBQUcsQ0FBQyxJQUFJLEVBQUUsQ0FBQztnQkFDZCxPQUFPLEdBQUcsQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDO29CQUMxQixLQUFLLEVBQUUsa0JBQWtCO29CQUN6QixPQUFPLEVBQUUsZ0NBQWdDO2lCQUMxQyxDQUFDLENBQUM7WUFDTCxDQUFDO1lBRUQsSUFBSSxDQUFDO2dCQUNILDRCQUE0QjtnQkFDNUIsTUFBTSxRQUFRLEdBQUksR0FBRyxDQUFDLE9BQU8sQ0FBQyxhQUFhLENBQVksSUFBSSxTQUFTLENBQUM7Z0JBRXJFLDRCQUE0QjtnQkFDNUIsTUFBTSxZQUFZLEdBQUcsTUFBTSx3QkFBd0IsQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLElBQUksRUFBRSxRQUFRLEVBQUUsR0FBRyxDQUFDLEtBQUssQ0FBQyxDQUFDO2dCQUV4Rix5QkFBeUI7Z0JBQ3pCLFlBQUUsQ0FBQyxVQUFVLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztnQkFFN0IsT0FBTyxDQUFDLEdBQUcsQ0FBQyxvQ0FBb0MsRUFBRSxZQUFZLENBQUMsQ0FBQztnQkFFaEUsR0FBRyxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsQ0FBQyxJQUFJLENBQUM7b0JBQ25CLE9BQU8sRUFBRSwwQkFBMEI7b0JBQ25DLGNBQWMsRUFBRSxJQUFBLFNBQU0sR0FBRTtvQkFDeEIsR0FBRyxZQUFZO2lCQUNoQixDQUFDLENBQUM7WUFDTCxDQUFDO1lBQUMsT0FBTyxlQUFlLEVBQUUsQ0FBQztnQkFDekIsT0FBTyxDQUFDLEtBQUssQ0FBQyxvQ0FBb0MsRUFBRSxlQUFlLENBQUMsQ0FBQztnQkFFckUsa0NBQWtDO2dCQUNsQyxJQUFJLEdBQUcsQ0FBQyxJQUFJLElBQUksWUFBRSxDQUFDLFVBQVUsQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUM7b0JBQzdDLFlBQUUsQ0FBQyxVQUFVLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztnQkFDL0IsQ0FBQztnQkFFRCxHQUFHLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUFDLElBQUksQ0FBQztvQkFDbkIsS0FBSyxFQUFFLDBCQUEwQjtvQkFDakMsT0FBTyxFQUFFLGVBQWUsQ0FBQyxPQUFPLElBQUksK0JBQStCO2lCQUNwRSxDQUFDLENBQUM7WUFDTCxDQUFDO1FBQ0gsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDO0lBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztRQUNmLE9BQU8sQ0FBQyxLQUFLLENBQUMsb0NBQW9DLEVBQUUsS0FBSyxDQUFDLENBQUM7UUFFM0QsR0FBRyxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsQ0FBQyxJQUFJLENBQUM7WUFDbkIsS0FBSyxFQUFFLGVBQWU7WUFDdEIsT0FBTyxFQUFFLEtBQUssQ0FBQyxPQUFPLElBQUksNENBQTRDO1NBQ3ZFLENBQUMsQ0FBQztJQUNMLENBQUM7QUFDSCxDQUFDO0FBRUQ7Ozs7R0FJRztBQUNJLEtBQUssVUFBVSxPQUFPLENBQUMsR0FBWSxFQUFFLEdBQWE7SUFDdkQsMENBQTBDO0lBQzFDLEdBQUcsQ0FBQyxTQUFTLENBQUMsNkJBQTZCLEVBQUUsdUJBQXVCLENBQUMsQ0FBQztJQUN0RSxHQUFHLENBQUMsU0FBUyxDQUFDLDhCQUE4QixFQUFFLG9CQUFvQixDQUFDLENBQUM7SUFDcEUsR0FBRyxDQUFDLFNBQVMsQ0FBQyw4QkFBOEIsRUFBRSwwQ0FBMEMsQ0FBQyxDQUFDO0lBQzFGLEdBQUcsQ0FBQyxTQUFTLENBQUMsa0NBQWtDLEVBQUUsTUFBTSxDQUFDLENBQUM7SUFDMUQsR0FBRyxDQUFDLFNBQVMsQ0FBQyx3QkFBd0IsRUFBRSxPQUFPLENBQUMsQ0FBQyxDQUFDLFdBQVc7SUFFN0QsR0FBRyxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsQ0FBQyxHQUFHLEVBQUUsQ0FBQztBQUN4QixDQUFDO0FBRUQ7Ozs7R0FJRztBQUNJLEtBQUssVUFBVSxHQUFHLENBQUMsR0FBWSxFQUFFLEdBQWE7SUFDbkQsSUFBSSxDQUFDO1FBQ0gsT0FBTyxDQUFDLEdBQUcsQ0FBQyxnREFBZ0QsQ0FBQyxDQUFDO1FBRTlELDhCQUE4QjtRQUM5QixHQUFHLENBQUMsU0FBUyxDQUFDLDZCQUE2QixFQUFFLHVCQUF1QixDQUFDLENBQUM7UUFDdEUsR0FBRyxDQUFDLFNBQVMsQ0FBQyw4QkFBOEIsRUFBRSxjQUFjLENBQUMsQ0FBQztRQUM5RCxHQUFHLENBQUMsU0FBUyxDQUFDLDhCQUE4QixFQUFFLDBDQUEwQyxDQUFDLENBQUM7UUFDMUYsR0FBRyxDQUFDLFNBQVMsQ0FBQyxrQ0FBa0MsRUFBRSxNQUFNLENBQUMsQ0FBQztRQUUxRCxtQ0FBbUM7UUFDbkMsTUFBTSxZQUFZLEdBQUcsY0FBSSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsR0FBRyxFQUFFLEVBQUUsV0FBVyxFQUFFLDhCQUE4QixDQUFDLENBQUM7UUFFM0YsZ0NBQWdDO1FBQ2hDLElBQUksQ0FBQyxZQUFFLENBQUMsVUFBVSxDQUFDLFlBQVksQ0FBQyxFQUFFLENBQUM7WUFDakMsT0FBTyxDQUFDLEtBQUssQ0FBQywyQ0FBMkMsRUFBRSxZQUFZLENBQUMsQ0FBQztZQUN6RSxHQUFHLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUFDLElBQUksQ0FBQztnQkFDbkIsS0FBSyxFQUFFLG9CQUFvQjtnQkFDM0IsT0FBTyxFQUFFLCtDQUErQzthQUN6RCxDQUFDLENBQUM7WUFDSCxPQUFPO1FBQ1QsQ0FBQztRQUVELDJDQUEyQztRQUMzQyxNQUFNLFNBQVMsR0FBRyxZQUFFLENBQUMsUUFBUSxDQUFDLFlBQVksQ0FBQyxDQUFDO1FBRTVDLHlDQUF5QztRQUN6QyxHQUFHLENBQUMsU0FBUyxDQUNYLGNBQWMsRUFDZCxtRUFBbUUsQ0FDcEUsQ0FBQztRQUNGLEdBQUcsQ0FBQyxTQUFTLENBQUMscUJBQXFCLEVBQUUscURBQXFELENBQUMsQ0FBQztRQUM1RixHQUFHLENBQUMsU0FBUyxDQUFDLGdCQUFnQixFQUFFLFNBQVMsQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUVoRCxrQ0FBa0M7UUFDbEMsTUFBTSxVQUFVLEdBQUcsWUFBRSxDQUFDLGdCQUFnQixDQUFDLFlBQVksQ0FBQyxDQUFDO1FBRXJELFVBQVUsQ0FBQyxFQUFFLENBQUMsT0FBTyxFQUFFLEtBQUssQ0FBQyxFQUFFO1lBQzdCLE9BQU8sQ0FBQyxLQUFLLENBQUMsaURBQWlELEVBQUUsS0FBSyxDQUFDLENBQUM7WUFDeEUsSUFBSSxDQUFDLEdBQUcsQ0FBQyxXQUFXLEVBQUUsQ0FBQztnQkFDckIsR0FBRyxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsQ0FBQyxJQUFJLENBQUM7b0JBQ25CLEtBQUssRUFBRSwyQkFBMkI7b0JBQ2xDLE9BQU8sRUFBRSxnQ0FBZ0M7aUJBQzFDLENBQUMsQ0FBQztZQUNMLENBQUM7UUFDSCxDQUFDLENBQUMsQ0FBQztRQUVILHVDQUF1QztRQUN2QyxVQUFVLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDO1FBRXJCLE9BQU8sQ0FBQyxHQUFHLENBQUMsb0RBQW9ELENBQUMsQ0FBQztJQUNwRSxDQUFDO0lBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztRQUNmLE9BQU8sQ0FBQyxLQUFLLENBQUMsMENBQTBDLEVBQUUsS0FBSyxDQUFDLENBQUM7UUFFakUsR0FBRyxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsQ0FBQyxJQUFJLENBQUM7WUFDbkIsS0FBSyxFQUFFLHlCQUF5QjtZQUNoQyxPQUFPLEVBQUUsS0FBSyxDQUFDLE9BQU8sSUFBSSxnQ0FBZ0M7U0FDM0QsQ0FBQyxDQUFDO0lBQ0wsQ0FBQztBQUNILENBQUM7QUFFRCw2Q0FBNkM7QUFDN0MsS0FBSyxVQUFVLHdCQUF3QixDQUNyQyxRQUFnQixFQUNoQixRQUFnQixFQUNoQixLQUFVO0lBRVYsTUFBTSxNQUFNLEdBQTRCLEVBQUUsQ0FBQztJQUMzQyxNQUFNLGVBQWUsR0FBYSxFQUFFLENBQUM7SUFDckMsSUFBSSxpQkFBaUIsR0FBRyxDQUFDLENBQUM7SUFDMUIsSUFBSSxhQUFhLEdBQUcsQ0FBQyxDQUFDO0lBRXRCLElBQUksQ0FBQztRQUNILDRDQUE0QztRQUM1QyxNQUFNLGFBQWEsR0FBRyxjQUFJLENBQUMsT0FBTyxDQUFDLFFBQVEsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO1FBQzNELElBQUksT0FBMkIsQ0FBQztRQUVoQyxJQUFJLGFBQWEsS0FBSyxNQUFNLEVBQUUsQ0FBQztZQUM3QixpQkFBaUI7WUFDakIsTUFBTSxVQUFVLEdBQUcsWUFBRSxDQUFDLFlBQVksQ0FBQyxRQUFRLEVBQUUsT0FBTyxDQUFDLENBQUM7WUFDdEQsTUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLElBQUksQ0FBQyxVQUFVLEVBQUUsRUFBRSxJQUFJLEVBQUUsUUFBUSxFQUFFLENBQUMsQ0FBQztZQUMzRCxNQUFNLFNBQVMsR0FBRyxRQUFRLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ3pDLE1BQU0sU0FBUyxHQUFHLFFBQVEsQ0FBQyxNQUFNLENBQUMsU0FBUyxDQUFDLENBQUM7WUFDN0MsT0FBTyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsYUFBYSxDQUFDLFNBQVMsQ0FBdUIsQ0FBQztRQUN0RSxDQUFDO2FBQU0sQ0FBQztZQUNOLG1CQUFtQjtZQUNuQixNQUFNLFFBQVEsR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLFFBQVEsQ0FBQyxDQUFDO1lBQ3pDLE1BQU0sU0FBUyxHQUFHLFFBQVEsQ0FBQyxVQUFVLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDekMsTUFBTSxTQUFTLEdBQUcsUUFBUSxDQUFDLE1BQU0sQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUM3QyxPQUFPLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxhQUFhLENBQUMsU0FBUyxDQUF1QixDQUFDO1FBQ3RFLENBQUM7UUFFRCxJQUFJLENBQUMsT0FBTyxJQUFJLE9BQU8sQ0FBQyxNQUFNLEtBQUssQ0FBQyxFQUFFLENBQUM7WUFDckMsTUFBTSxJQUFJLEtBQUssQ0FBQyxvQ0FBb0MsQ0FBQyxDQUFDO1FBQ3hELENBQUM7UUFFRCxPQUFPLENBQUMsR0FBRyxDQUFDLCtCQUErQixPQUFPLENBQUMsTUFBTSxxQkFBcUIsUUFBUSxFQUFFLENBQUMsQ0FBQztRQUUxRixrREFBa0Q7UUFDbEQsSUFBSSxjQUFjLENBQUM7UUFDbkIsSUFBSSxDQUFDO1lBQ0gscUNBQXFDO1lBQ3JDLGNBQWMsR0FBRyxLQUFLLENBQUMsT0FBTyxDQUFDLFNBQVMsQ0FBQyxDQUFDO1lBQzFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsd0RBQXdELENBQUMsQ0FBQztRQUN4RSxDQUFDO1FBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQztZQUNYLE9BQU8sQ0FBQyxLQUFLLENBQUMscURBQXFELEVBQUUsQ0FBQyxDQUFDLENBQUM7WUFDeEUsa0NBQWtDO1lBQ2xDLE9BQU8sQ0FBQyxHQUFHLENBQUMseURBQXlELENBQUMsQ0FBQztZQUN2RSxjQUFjLEdBQUc7Z0JBQ2YsTUFBTSxFQUFFLEtBQUssRUFBQyxJQUFJLEVBQUMsRUFBRTtvQkFDbkIsTUFBTSxXQUFXLEdBQUc7d0JBQ2xCLEVBQUUsRUFBRSxRQUFRLElBQUksQ0FBQyxHQUFHLEVBQUUsSUFBSSxJQUFJLENBQUMsTUFBTSxFQUFFLENBQUMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUU7d0JBQ25FLEtBQUssRUFBRSxJQUFJLENBQUMsS0FBSzt3QkFDakIsTUFBTSxFQUFFLElBQUksQ0FBQyxNQUFNO3dCQUNuQixXQUFXLEVBQUUsSUFBSSxDQUFDLFdBQVc7d0JBQzdCLE1BQU0sRUFBRSxJQUFJLENBQUMsTUFBTTt3QkFDbkIsUUFBUSxFQUFFLElBQUksQ0FBQyxRQUFRO3dCQUN2QixVQUFVLEVBQUUsSUFBSSxJQUFJLEVBQUUsQ0FBQyxXQUFXLEVBQUU7d0JBQ3BDLFVBQVUsRUFBRSxJQUFJLElBQUksRUFBRSxDQUFDLFdBQVcsRUFBRTtxQkFDckMsQ0FBQztvQkFDRixPQUFPLENBQUMsR0FBRyxDQUFDLHdDQUF3QyxFQUFFLFdBQVcsQ0FBQyxFQUFFLENBQUMsQ0FBQztvQkFDdEUsT0FBTyxXQUFXLENBQUM7Z0JBQ3JCLENBQUM7Z0JBQ0QsY0FBYyxFQUFFLEtBQUssRUFBQyxRQUFRLEVBQUMsRUFBRTtvQkFDL0IsTUFBTSxZQUFZLEdBQUcsUUFBUSxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUM7d0JBQ3RDLEdBQUcsQ0FBQzt3QkFDSixFQUFFLEVBQUUsT0FBTyxJQUFJLENBQUMsR0FBRyxFQUFFLElBQUksSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFO3dCQUNsRSxVQUFVLEVBQUUsSUFBSSxJQUFJLEVBQUUsQ0FBQyxXQUFXLEVBQUU7d0JBQ3BDLFVBQVUsRUFBRSxJQUFJLElBQUksRUFBRSxDQUFDLFdBQVcsRUFBRTtxQkFDckMsQ0FBQyxDQUFDLENBQUM7b0JBQ0osT0FBTyxDQUFDLEdBQUcsQ0FBQyx5Q0FBeUMsRUFBRSxZQUFZLENBQUMsTUFBTSxDQUFDLENBQUM7b0JBQzVFLE9BQU8sWUFBWSxDQUFDO2dCQUN0QixDQUFDO2FBQ0YsQ0FBQztRQUNKLENBQUM7UUFFRCx5REFBeUQ7UUFDekQsTUFBTSxhQUFhLEdBQUcsSUFBSSxHQUFHLEVBQThCLENBQUM7UUFFNUQsS0FBSyxNQUFNLEdBQUcsSUFBSSxPQUFPLEVBQUUsQ0FBQztZQUMxQixNQUFNLE1BQU0sR0FDVixHQUFHLENBQUMsTUFBTTtnQkFDVixHQUFHLENBQUMsS0FBSztxQkFDTixXQUFXLEVBQUU7cUJBQ2IsT0FBTyxDQUFDLGFBQWEsRUFBRSxHQUFHLENBQUM7cUJBQzNCLE9BQU8sQ0FBQyxVQUFVLEVBQUUsRUFBRSxDQUFDLENBQUM7WUFDN0IsSUFBSSxDQUFDLGFBQWEsQ0FBQyxHQUFHLENBQUMsTUFBTSxDQUFDLEVBQUUsQ0FBQztnQkFDL0IsYUFBYSxDQUFDLEdBQUcsQ0FBQyxNQUFNLEVBQUUsRUFBRSxDQUFDLENBQUM7WUFDaEMsQ0FBQztZQUNELGFBQWEsQ0FBQyxHQUFHLENBQUMsTUFBTSxDQUFFLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDO1FBQ3ZDLENBQUM7UUFFRCxPQUFPLENBQUMsR0FBRyxDQUNULDRCQUE0QixPQUFPLENBQUMsTUFBTSxjQUFjLGFBQWEsQ0FBQyxJQUFJLFdBQVcsQ0FDdEYsQ0FBQztRQUVGLDZCQUE2QjtRQUM3QixJQUFJLFNBQVMsR0FBRyxDQUFDLENBQUMsQ0FBQywyQ0FBMkM7UUFDOUQsS0FBSyxNQUFNLENBQUMsTUFBTSxFQUFFLElBQUksQ0FBQyxJQUFJLGFBQWEsRUFBRSxDQUFDO1lBQzNDLElBQUksQ0FBQztnQkFDSCw2Q0FBNkM7Z0JBQzdDLE1BQU0sVUFBVSxHQUFHLE1BQU0sb0JBQW9CLENBQUMsSUFBSSxDQUFDLENBQUM7Z0JBQ3BELE1BQU0sT0FBTyxHQUFHLFVBQVUsQ0FBQyxDQUFDLENBQUMsQ0FBQztnQkFFOUIsNENBQTRDO2dCQUM1QyxNQUFNLGdCQUFnQixHQUFHLGtCQUFrQixDQUFDLE9BQU8sRUFBRSxTQUFTLENBQUMsQ0FBQztnQkFDaEUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxnQ0FBZ0MsRUFBRSxnQkFBZ0IsQ0FBQyxDQUFDO2dCQUNoRSxJQUFJLGdCQUFnQixDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUUsQ0FBQztvQkFDaEMsTUFBTSxDQUFDLElBQUksQ0FBQyxHQUFHLGdCQUFnQixDQUFDLENBQUM7b0JBQ2pDLGFBQWEsSUFBSSxJQUFJLENBQUMsTUFBTSxDQUFDO29CQUM3QixTQUFTLElBQUksSUFBSSxDQUFDLE1BQU0sQ0FBQztvQkFDekIsU0FBUztnQkFDWCxDQUFDO2dCQUVELGtDQUFrQztnQkFDbEMsSUFBSSxlQUFlLEdBQUcsSUFBSSxDQUFDO2dCQUMzQixJQUFJLENBQUM7b0JBQ0gsSUFBSSxjQUFjLENBQUMsWUFBWSxFQUFFLENBQUM7d0JBQ2hDLE1BQU0sZ0JBQWdCLEdBQUcsTUFBTSxjQUFjLENBQUMsWUFBWSxDQUFDLEVBQUUsTUFBTSxFQUFFLEVBQUUsRUFBRSxLQUFLLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQzt3QkFDckYsZUFBZSxHQUFHLGdCQUFnQixDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLGdCQUFnQixDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUM7b0JBQzdFLENBQUM7Z0JBQ0gsQ0FBQztnQkFBQyxPQUFPLENBQUMsRUFBRSxDQUFDO29CQUNYLGdEQUFnRDtnQkFDbEQsQ0FBQztnQkFFRCxJQUFJLE9BQU8sQ0FBQztnQkFDWixJQUFJLGVBQWUsRUFBRSxDQUFDO29CQUNwQixPQUFPLENBQUMsR0FBRyxDQUNULHlDQUF5QyxNQUFNLG1DQUFtQyxDQUNuRixDQUFDO29CQUNGLE9BQU8sR0FBRyxlQUFlLENBQUM7Z0JBQzVCLENBQUM7cUJBQU0sQ0FBQztvQkFDTixxQkFBcUI7b0JBQ3JCLE1BQU0sV0FBVyxHQUFHLE1BQU0sZ0JBQWdCLENBQUMsT0FBTyxFQUFFLFFBQVEsQ0FBQyxDQUFDO29CQUU5RCxpRUFBaUU7b0JBQ2pFLDBCQUEwQjtvQkFDMUIsVUFBVTtvQkFDVixpRkFBaUY7b0JBQ2pGLHlEQUF5RDtvQkFDekQsMEJBQTBCO29CQUMxQiw0REFBNEQ7b0JBQzVELHFCQUFxQjtvQkFDckIsb0ZBQW9GO29CQUNwRixXQUFXO29CQUNYLFFBQVE7b0JBQ1IsOEJBQThCO29CQUM5QiwyRkFBMkY7b0JBQzNGLGtFQUFrRTtvQkFDbEUsTUFBTTtvQkFDTixJQUFJO29CQUNKLHNDQUFzQztvQkFDdEMsSUFBSSxDQUFDO3dCQUNILElBQUksY0FBYyxDQUFDLGNBQWMsRUFBRSxDQUFDOzRCQUNsQyxtQkFBbUI7NEJBRW5CLE1BQU0sUUFBUSxHQUFHLE1BQU0sY0FBYyxDQUFDLGNBQWMsQ0FBQyxDQUFDLFdBQVcsQ0FBQyxDQUFDLENBQUM7NEJBQ3BFLE9BQU8sR0FBRyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUM7d0JBQ3hCLENBQUM7NkJBQU0sSUFBSSxjQUFjLENBQUMsTUFBTSxFQUFFLENBQUM7NEJBQ2pDLGtCQUFrQjs0QkFDbEIsT0FBTyxDQUFDLEdBQUcsQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDOzRCQUVwQyxPQUFPLEdBQUcsTUFBTSxjQUFjLENBQUMsTUFBTSxDQUFDLFdBQVcsQ0FBQyxDQUFDO3dCQUNyRCxDQUFDOzZCQUFNLENBQUM7NEJBQ04sTUFBTSxJQUFJLEtBQUssQ0FBQyxzQ0FBc0MsQ0FBQyxDQUFDO3dCQUMxRCxDQUFDO3dCQUVELE9BQU8sQ0FBQyxHQUFHLENBQ1Qsa0RBQWtELE9BQU8sQ0FBQyxLQUFLLEtBQUssT0FBTyxDQUFDLEVBQUUsR0FBRyxDQUNsRixDQUFDO29CQUNKLENBQUM7b0JBQUMsT0FBTyxXQUFXLEVBQUUsQ0FBQzt3QkFDckIsT0FBTyxDQUFDLEtBQUssQ0FBQywyQ0FBMkMsRUFBRSxXQUFXLENBQUMsQ0FBQzt3QkFDeEUsTUFBTSxJQUFJLEtBQUssQ0FBQyw2QkFBNkIsV0FBVyxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUM7b0JBQ3RFLENBQUM7Z0JBQ0gsQ0FBQztnQkFFRCw2Q0FBNkM7Z0JBQzdDLElBQUksbUJBQW1CLEdBQUcsQ0FBQyxDQUFDO2dCQUM1QixLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFDO29CQUNyQyxNQUFNLEdBQUcsR0FBRyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUM7b0JBQ3BCLE1BQU0sZ0JBQWdCLEdBQUcsU0FBUyxHQUFHLENBQUMsQ0FBQztvQkFFdkMsSUFBSSxDQUFDO3dCQUNILElBQUksR0FBRyxDQUFDLFdBQVcsSUFBSSxHQUFHLENBQUMsYUFBYSxJQUFJLEdBQUcsQ0FBQyxhQUFhLEVBQUUsQ0FBQzs0QkFDOUQsTUFBTSxXQUFXLEdBQUcsZ0JBQWdCLENBQUMsR0FBRyxFQUFFLE9BQU8sQ0FBQyxFQUFFLENBQUMsQ0FBQzs0QkFFdEQsMkNBQTJDOzRCQUMzQyxJQUFJLGNBQWMsQ0FBQyxjQUFjLEVBQUUsQ0FBQztnQ0FDbEMsTUFBTSxjQUFjLENBQUMsY0FBYyxDQUFDLENBQUMsV0FBVyxDQUFDLENBQUMsQ0FBQztnQ0FDbkQsbUJBQW1CLEVBQUUsQ0FBQztnQ0FDdEIsT0FBTyxDQUFDLEdBQUcsQ0FDVCxrREFBa0QsV0FBVyxDQUFDLEtBQUssZ0JBQWdCLE9BQU8sQ0FBQyxFQUFFLEVBQUUsQ0FDaEcsQ0FBQzs0QkFDSixDQUFDO2lDQUFNLElBQUksY0FBYyxDQUFDLFNBQVMsRUFBRSxDQUFDO2dDQUNwQyxzQkFBc0I7Z0NBQ3RCLE1BQU0sY0FBYyxDQUFDLFNBQVMsQ0FBQyxPQUFPLENBQUMsRUFBRSxFQUFFO29DQUN6QyxLQUFLLEVBQUUsV0FBVyxDQUFDLEtBQUs7b0NBQ3hCLE1BQU0sRUFBRSxDQUFDLEVBQUUsS0FBSyxFQUFFLFdBQVcsQ0FBQyxLQUFLLEVBQUUsQ0FBQztpQ0FDdkMsQ0FBQyxDQUFDO2dDQUNILG1CQUFtQixFQUFFLENBQUM7NEJBQ3hCLENBQUM7d0JBQ0gsQ0FBQztvQkFDSCxDQUFDO29CQUFDLE9BQU8sWUFBWSxFQUFFLENBQUM7d0JBQ3RCLE9BQU8sQ0FBQyxLQUFLLENBQ1gsb0RBQW9ELGdCQUFnQixHQUFHLEVBQ3ZFLFlBQVksQ0FDYixDQUFDO3dCQUNGLE1BQU0sQ0FBQyxJQUFJLENBQUM7NEJBQ1YsR0FBRyxFQUFFLGdCQUFnQjs0QkFDckIsS0FBSyxFQUFFLFNBQVM7NEJBQ2hCLE9BQU8sRUFBRSw2QkFBNkIsWUFBWSxDQUFDLE9BQU8sRUFBRTs0QkFDNUQsS0FBSyxFQUFFLEdBQUcsQ0FBQyxhQUFhLElBQUksaUJBQWlCO3lCQUM5QyxDQUFDLENBQUM7d0JBQ0gsYUFBYSxFQUFFLENBQUM7b0JBQ2xCLENBQUM7Z0JBQ0gsQ0FBQztnQkFFRCxnQkFBZ0I7Z0JBQ2hCLElBQUksQ0FBQyxlQUFlLEVBQUUsQ0FBQztvQkFDckIsZUFBZSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsRUFBRSxDQUFDLENBQUM7Z0JBQ25DLENBQUM7Z0JBQ0QsaUJBQWlCLElBQUksbUJBQW1CLENBQUM7WUFDM0MsQ0FBQztZQUFDLE9BQU8sVUFBVSxFQUFFLENBQUM7Z0JBQ3BCLE9BQU8sQ0FBQyxLQUFLLENBQUMsb0RBQW9ELE1BQU0sSUFBSSxFQUFFLFVBQVUsQ0FBQyxDQUFDO2dCQUUxRix3Q0FBd0M7Z0JBQ3hDLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxJQUFJLENBQUMsTUFBTSxFQUFFLENBQUMsRUFBRSxFQUFFLENBQUM7b0JBQ3JDLE1BQU0sQ0FBQyxJQUFJLENBQUM7d0JBQ1YsR0FBRyxFQUFFLFNBQVMsR0FBRyxDQUFDO3dCQUNsQixLQUFLLEVBQUUsU0FBUzt3QkFDaEIsT0FBTyxFQUFFLFVBQVUsQ0FBQyxPQUFPLElBQUksaUNBQWlDO3dCQUNoRSxLQUFLLEVBQUUsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUs7cUJBQ3JCLENBQUMsQ0FBQztnQkFDTCxDQUFDO2dCQUNELGFBQWEsSUFBSSxJQUFJLENBQUMsTUFBTSxDQUFDO1lBQy9CLENBQUM7WUFFRCxTQUFTLElBQUksSUFBSSxDQUFDLE1BQU0sQ0FBQztRQUMzQixDQUFDO1FBRUQsT0FBTztZQUNMLE9BQU8sRUFBRSxNQUFNLENBQUMsTUFBTSxLQUFLLENBQUM7WUFDNUIsVUFBVSxFQUFFLE9BQU8sQ0FBQyxNQUFNO1lBQzFCLGtCQUFrQixFQUFFLGlCQUFpQjtZQUNyQyxjQUFjLEVBQUUsYUFBYTtZQUM3QixNQUFNO1lBQ04sZ0JBQWdCLEVBQUUsZUFBZTtTQUNsQyxDQUFDO0lBQ0osQ0FBQztJQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7UUFDZixPQUFPLENBQUMsS0FBSyxDQUFDLHlDQUF5QyxFQUFFLEtBQUssQ0FBQyxDQUFDO1FBQ2hFLE1BQU0sSUFBSSxLQUFLLENBQUMsa0NBQWtDLEtBQUssQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDO0lBQ3JFLENBQUM7QUFDSCxDQUFDO0FBRUQsK0NBQStDO0FBQy9DLFNBQVMsa0JBQWtCLENBQUMsR0FBcUIsRUFBRSxTQUFpQjtJQUNsRSxNQUFNLE1BQU0sR0FBNEIsRUFBRSxDQUFDO0lBRTNDLDJCQUEyQjtJQUMzQixJQUFJLENBQUMsR0FBRyxDQUFDLEtBQUssSUFBSSxHQUFHLENBQUMsS0FBSyxDQUFDLElBQUksRUFBRSxLQUFLLEVBQUUsRUFBRSxDQUFDO1FBQzFDLE1BQU0sQ0FBQyxJQUFJLENBQUM7WUFDVixHQUFHLEVBQUUsU0FBUztZQUNkLEtBQUssRUFBRSxPQUFPO1lBQ2QsT0FBTyxFQUFFLDJCQUEyQjtZQUNwQyxLQUFLLEVBQUUsR0FBRyxDQUFDLEtBQUs7U0FDakIsQ0FBQyxDQUFDO0lBQ0wsQ0FBQztJQUVELDBCQUEwQjtJQUMxQixJQUNFLEdBQUcsQ0FBQyxhQUFhLEtBQUssU0FBUztRQUMvQixDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLGFBQWEsQ0FBQyxDQUFDLElBQUksTUFBTSxDQUFDLEdBQUcsQ0FBQyxhQUFhLENBQUMsR0FBRyxDQUFDLENBQUMsRUFDbkUsQ0FBQztRQUNELE1BQU0sQ0FBQyxJQUFJLENBQUM7WUFDVixHQUFHLEVBQUUsU0FBUztZQUNkLEtBQUssRUFBRSxlQUFlO1lBQ3RCLE9BQU8sRUFBRSwrQ0FBK0M7WUFDeEQsS0FBSyxFQUFFLEdBQUcsQ0FBQyxhQUFhO1NBQ3pCLENBQUMsQ0FBQztJQUNMLENBQUM7SUFFRCxJQUNFLEdBQUcsQ0FBQyxjQUFjLEtBQUssU0FBUztRQUNoQyxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLGNBQWMsQ0FBQyxDQUFDLElBQUksTUFBTSxDQUFDLEdBQUcsQ0FBQyxjQUFjLENBQUMsR0FBRyxDQUFDLENBQUMsRUFDckUsQ0FBQztRQUNELE1BQU0sQ0FBQyxJQUFJLENBQUM7WUFDVixHQUFHLEVBQUUsU0FBUztZQUNkLEtBQUssRUFBRSxnQkFBZ0I7WUFDdkIsT0FBTyxFQUFFLGdEQUFnRDtZQUN6RCxLQUFLLEVBQUUsR0FBRyxDQUFDLGNBQWM7U0FDMUIsQ0FBQyxDQUFDO0lBQ0wsQ0FBQztJQUVELE9BQU8sTUFBTSxDQUFDO0FBQ2hCLENBQUM7QUFFRCxtREFBbUQ7QUFDbkQsS0FBSyxVQUFVLGdCQUFnQixDQUFDLEdBQXFCLEVBQUUsUUFBZ0I7SUFDckUsa0NBQWtDO0lBQ2xDLE1BQU0sTUFBTSxHQUNWLEdBQUcsQ0FBQyxNQUFNO1FBQ1YsR0FBRyxDQUFDLEtBQUs7YUFDTixXQUFXLEVBQUU7YUFDYixPQUFPLENBQUMsYUFBYSxFQUFFLEdBQUcsQ0FBQzthQUMzQixPQUFPLENBQUMsVUFBVSxFQUFFLEVBQUUsQ0FBQyxDQUFDO0lBRTdCLCtEQUErRDtJQUMvRCxNQUFNLFNBQVMsR0FBRyxHQUFHLENBQUMsTUFBTTtRQUMxQixDQUFDLENBQUMsR0FBRyxDQUFDLE1BQU07YUFDUCxLQUFLLENBQUMsR0FBRyxDQUFDO2FBQ1YsR0FBRyxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxDQUFDLElBQUksRUFBRSxDQUFDO2FBQ3RCLE1BQU0sQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsQ0FBQztRQUN2QixDQUFDLENBQUMsRUFBRSxDQUFDO0lBRVAsd0RBQXdEO0lBQ3hELE1BQU0sTUFBTSxHQUFHLFNBQVMsQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsR0FBRyxFQUFFLENBQUMsQ0FBQyxDQUFDO0lBRS9DLHFFQUFxRTtJQUNyRSxNQUFNLFFBQVEsR0FBd0I7UUFDcEMsU0FBUyxFQUFFLFFBQVE7UUFDbkIsMEJBQTBCO1FBRTFCLFFBQVEsRUFBRSxHQUFHLENBQUMsUUFBUTtRQUV0QixlQUFlLEVBQUU7WUFDZixnQkFBZ0IsRUFBRSxHQUFHLENBQUMsZ0JBQWdCO1lBQ3RDLGdCQUFnQixFQUFFLEdBQUcsQ0FBQyxnQkFBZ0I7WUFDdEMscUJBQXFCLEVBQUUsR0FBRyxDQUFDLHFCQUFxQjtZQUNoRCxjQUFjLEVBQUU7Z0JBQ2Q7b0JBQ0UsVUFBVSxFQUFFLEdBQUcsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsVUFBVTtvQkFDdEMsY0FBYyxFQUFFLEdBQUcsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsY0FBYztpQkFDL0M7YUFDRjtZQUNELGdCQUFnQixFQUFFLEdBQUcsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsMEJBQTBCO1lBQzVELHdCQUF3QixFQUFFLEdBQUcsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsZ0JBQWdCO1NBQzNEO0tBQ0YsQ0FBQztJQUVGLE1BQU0sUUFBUSxHQUFHLEdBQUcsQ0FBQyxRQUFRLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQztRQUN0QyxLQUFLLEVBQUUsQ0FBQyxDQUFDLGFBQWE7UUFDdEIsR0FBRyxFQUFFLENBQUMsQ0FBQyxXQUFXO1FBQ2xCLFFBQVEsRUFBRSxDQUFDLEVBQUUsZ0JBQWdCLElBQUksSUFBSTtRQUNyQyxNQUFNLEVBQUUsQ0FBQyxDQUFDLGNBQWMsS0FBSyxFQUFFLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLGNBQWM7UUFDekQsS0FBSyxFQUFFLENBQUMsQ0FBQyxhQUFhLEtBQUssRUFBRSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxhQUFhO1FBQ3RELE1BQU0sRUFBRSxDQUFDLENBQUMsY0FBYyxLQUFLLEVBQUUsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsY0FBYztRQUN6RCxNQUFNLEVBQUUsQ0FBQyxDQUFDLGNBQWMsS0FBSyxFQUFFLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLGNBQWM7UUFDekQsUUFBUSxFQUFFO1lBQ1IsVUFBVSxFQUFFLE1BQU0sQ0FBQyxDQUFDLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQztZQUNyQyxjQUFjLEVBQUUsTUFBTSxDQUFDLENBQUMsQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDO1lBQzdDLGdCQUFnQixFQUFFLE1BQU0sQ0FBQyxDQUFDLENBQUMsMEJBQTBCLENBQUMsSUFBSSxDQUFDO1lBQzNELHdCQUF3QixFQUFFLENBQUMsQ0FBQyxnQkFBZ0IsSUFBSSxVQUFVO1NBQzNEO1FBQ0QsTUFBTSxFQUFFO1lBQ047Z0JBQ0UsYUFBYSxFQUFFLEtBQUs7Z0JBQ3BCLE1BQU0sRUFBRSxDQUFDLENBQUMsY0FBYyxLQUFLLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsY0FBYzthQUN2RDtTQUNGO0tBQ0YsQ0FBQyxDQUFDLENBQUM7SUFFSixPQUFPO1FBQ0wsS0FBSyxFQUFFLEdBQUcsQ0FBQyxLQUFLO1FBQ2hCLFdBQVcsRUFBRSxHQUFHLENBQUMsV0FBVyxJQUFJLEVBQUU7UUFDbEMsTUFBTTtRQUNOLE1BQU0sRUFBRSxHQUFHLENBQUMsTUFBTSxJQUFJLFdBQVc7UUFDakMsMkRBQTJEO1FBQzNELFNBQVMsRUFBRSxHQUFHLENBQUMsU0FBUztRQUN4QixNQUFNLEVBQUUsTUFBTSxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsU0FBUztRQUM5QyxRQUFRO1FBQ1IsZ0RBQWdEO1FBQ2hELFVBQVUsRUFBRSxFQUFXO1FBQ3ZCLFFBQVE7S0FDVCxDQUFDO0FBQ0osQ0FBQztBQUVELCtEQUErRDtBQUMvRCxTQUFTLGdCQUFnQixDQUFDLEdBQXFCLEVBQUUsU0FBaUI7SUFDaEUsT0FBTztRQUNMLFVBQVUsRUFBRSxTQUFTO1FBQ3JCLEtBQUssRUFBRSxHQUFHLENBQUMsYUFBYSxJQUFJLGlCQUFpQjtRQUM3QyxHQUFHLEVBQUUsR0FBRyxDQUFDLFdBQVc7UUFDcEIsTUFBTSxFQUFFLEdBQUcsQ0FBQyxhQUFhO1lBQ3ZCLENBQUMsQ0FBQztnQkFDRTtvQkFDRSxNQUFNLEVBQUUsSUFBSSxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLGFBQWEsQ0FBQyxHQUFHLEdBQUcsQ0FBQyxFQUFFLG1CQUFtQjtvQkFDeEUsYUFBYSxFQUFFLEtBQUs7aUJBQ3JCO2FBQ0Y7WUFDSCxDQUFDLENBQUMsRUFBRTtRQUNOLE1BQU0sRUFBRSxHQUFHLENBQUMsY0FBYyxDQUFDLENBQUMsQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLGNBQWMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxTQUFTO1FBQ25FLE1BQU0sRUFBRSxHQUFHLENBQUMsY0FBYyxDQUFDLENBQUMsQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLGNBQWMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxTQUFTO1FBQ25FLEtBQUssRUFBRSxHQUFHLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxTQUFTO1FBQ2hFLE1BQU0sRUFBRSxHQUFHLENBQUMsY0FBYyxDQUFDLENBQUMsQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLGNBQWMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxTQUFTO1FBQ25FLGtCQUFrQixFQUFFLEdBQUcsQ0FBQywwQkFBMEIsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQywwQkFBMEIsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQy9GLGVBQWUsRUFBRSxHQUFHLENBQUMsdUJBQXVCLElBQUksS0FBSztRQUNyRCxnQkFBZ0IsRUFBRSxHQUFHLENBQUMsd0JBQXdCLEtBQUssS0FBSztRQUN4RCxRQUFRLEVBQUU7WUFDUixnQkFBZ0IsRUFBRSxHQUFHLENBQUMsd0JBQXdCO1lBQzlDLG1DQUFtQztZQUNuQyxjQUFjLEVBQUUsR0FBRyxDQUFDLGNBQWM7WUFDbEMsVUFBVSxFQUFFLEdBQUcsQ0FBQyxVQUFVO1lBQzFCLGdCQUFnQixFQUFFLEdBQUcsQ0FBQyxnQkFBZ0IsSUFBSSxVQUFVO1lBQ3BELGNBQWMsRUFBRSxHQUFHLENBQUMsMEJBQTBCLElBQUksQ0FBQztTQUNwRDtLQUNGLENBQUM7QUFDSixDQUFDO0FBRUQsK0RBQStEO0FBQy9ELEtBQUssVUFBVSx5QkFBeUIsQ0FBQyxZQUFvQixFQUFFLEtBQVU7SUFDdkUsSUFBSSxDQUFDO1FBQ0gsT0FBTyxDQUFDLEdBQUcsQ0FBQyx5Q0FBeUMsWUFBWSxFQUFFLENBQUMsQ0FBQztRQUVyRSx3REFBd0Q7UUFDeEQsSUFBSSxlQUFlLENBQUM7UUFDcEIsSUFBSSxDQUFDO1lBQ0gsbUVBQW1FO1lBQ25FLGVBQWUsR0FBRyxLQUFLLENBQUMsT0FBTyxDQUFDLGlCQUFpQixDQUFDLENBQUM7WUFDbkQsT0FBTyxDQUFDLEdBQUcsQ0FBQyxtREFBbUQsQ0FBQyxDQUFDO1FBQ25FLENBQUM7UUFBQyxPQUFPLEVBQUUsRUFBRSxDQUFDO1lBQ1osSUFBSSxDQUFDO2dCQUNILGVBQWUsR0FBRyxLQUFLLENBQUMsT0FBTyxDQUFDLGtCQUFrQixDQUFDLENBQUM7Z0JBQ3BELE9BQU8sQ0FBQyxHQUFHLENBQUMsb0RBQW9ELENBQUMsQ0FBQztZQUNwRSxDQUFDO1lBQUMsT0FBTyxFQUFFLEVBQUUsQ0FBQztnQkFDWixJQUFJLENBQUM7b0JBQ0gsZUFBZSxHQUFHLEtBQUssQ0FBQyxPQUFPLENBQUMsVUFBVSxDQUFDLENBQUM7b0JBQzVDLE9BQU8sQ0FBQyxHQUFHLENBQUMsNENBQTRDLENBQUMsQ0FBQztnQkFDNUQsQ0FBQztnQkFBQyxPQUFPLEVBQUUsRUFBRSxDQUFDO29CQUNaLE9BQU8sQ0FBQyxJQUFJLENBQ1YsZ0ZBQWdGLENBQ2pGLENBQUM7b0JBQ0YsZ0ZBQWdGO29CQUNoRixNQUFNLGNBQWMsR0FBRyxLQUFLLENBQUMsT0FBTyxDQUFDLFNBQVMsQ0FBQyxDQUFDO29CQUNoRCxJQUFJLGNBQWMsQ0FBQyxjQUFjLEVBQUUsQ0FBQzt3QkFDbEMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxnRUFBZ0UsQ0FBQyxDQUFDO3dCQUM5RSxlQUFlLEdBQUc7NEJBQ2hCLElBQUksRUFBRSxjQUFjLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxjQUFjLENBQUM7NEJBQ3hELE1BQU0sRUFBRSxjQUFjLENBQUMsY0FBYyxFQUFFLElBQUksQ0FBQyxjQUFjLENBQUM7eUJBQzVELENBQUM7b0JBQ0osQ0FBQzt5QkFBTSxDQUFDO3dCQUNOLE9BQU8sQ0FBQyxJQUFJLENBQ1Ysa0ZBQWtGLENBQ25GLENBQUM7d0JBQ0YsT0FBTyxJQUFJLENBQUM7b0JBQ2QsQ0FBQztnQkFDSCxDQUFDO1lBQ0gsQ0FBQztRQUNILENBQUM7UUFFRCxnQ0FBZ0M7UUFDaEMsSUFBSSxlQUFlLElBQUksQ0FBQyxlQUFlLENBQUMsSUFBSSxJQUFJLGVBQWUsQ0FBQyxjQUFjLENBQUMsRUFBRSxDQUFDO1lBQ2hGLElBQUksQ0FBQztnQkFDSCxNQUFNLFVBQVUsR0FBRyxlQUFlLENBQUMsSUFBSSxJQUFJLGVBQWUsQ0FBQyxjQUFjLENBQUM7Z0JBQzFFLE1BQU0sa0JBQWtCLEdBQUcsTUFBTSxVQUFVLENBQUM7b0JBQzFDLElBQUksRUFBRSxZQUFZO2lCQUNuQixDQUFDLENBQUM7Z0JBRUgsSUFBSSxrQkFBa0IsSUFBSSxrQkFBa0IsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUM7b0JBQ3hELE9BQU8sQ0FBQyxHQUFHLENBQUMsNkNBQTZDLFlBQVksRUFBRSxDQUFDLENBQUM7b0JBQ3pFLE9BQU8sa0JBQWtCLENBQUMsQ0FBQyxDQUFDLENBQUM7Z0JBQy9CLENBQUM7WUFDSCxDQUFDO1lBQUMsT0FBTyxTQUFTLEVBQUUsQ0FBQztnQkFDbkIsT0FBTyxDQUFDLElBQUksQ0FBQyw4Q0FBOEMsU0FBUyxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUM7WUFDbEYsQ0FBQztRQUNILENBQUM7UUFFRCwyQ0FBMkM7UUFDM0MsSUFBSSxlQUFlLElBQUksQ0FBQyxlQUFlLENBQUMsTUFBTSxJQUFJLGVBQWUsQ0FBQyxjQUFjLENBQUMsRUFBRSxDQUFDO1lBQ2xGLElBQUksQ0FBQztnQkFDSCxPQUFPLENBQUMsR0FBRyxDQUFDLDJDQUEyQyxZQUFZLEVBQUUsQ0FBQyxDQUFDO2dCQUV2RSxNQUFNLFlBQVksR0FBRyxlQUFlLENBQUMsTUFBTSxJQUFJLGVBQWUsQ0FBQyxjQUFjLENBQUM7Z0JBQzlFLE1BQU0sV0FBVyxHQUFHLE1BQU0sWUFBWSxDQUFDO29CQUNyQyxJQUFJLEVBQUUsWUFBWTtvQkFDbEIsTUFBTSxFQUFFLFlBQVk7eUJBQ2pCLFdBQVcsRUFBRTt5QkFDYixPQUFPLENBQUMsYUFBYSxFQUFFLEdBQUcsQ0FBQzt5QkFDM0IsT0FBTyxDQUFDLFVBQVUsRUFBRSxFQUFFLENBQUM7b0JBQzFCLFdBQVcsRUFBRSw2QkFBNkIsWUFBWSxFQUFFO29CQUN4RCxTQUFTLEVBQUUsSUFBSTtvQkFDZixRQUFRLEVBQUU7d0JBQ1IsVUFBVSxFQUFFLGdCQUFnQjt3QkFDNUIsWUFBWSxFQUFFLElBQUk7cUJBQ25CO2lCQUNGLENBQUMsQ0FBQztnQkFFSCxPQUFPLENBQUMsR0FBRyxDQUNULG1EQUFtRCxZQUFZLEtBQUssV0FBVyxDQUFDLEVBQUUsR0FBRyxDQUN0RixDQUFDO2dCQUNGLE9BQU8sV0FBVyxDQUFDO1lBQ3JCLENBQUM7WUFBQyxPQUFPLFdBQVcsRUFBRSxDQUFDO2dCQUNyQixPQUFPLENBQUMsS0FBSyxDQUFDLDZDQUE2QyxXQUFXLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQztZQUNwRixDQUFDO1FBQ0gsQ0FBQztRQUVELCtFQUErRTtRQUMvRSxPQUFPLENBQUMsSUFBSSxDQUNWLDhCQUE4QixZQUFZLGtFQUFrRSxDQUM3RyxDQUFDO1FBQ0YsT0FBTyxJQUFJLENBQUM7SUFDZCxDQUFDO0lBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztRQUNmLE9BQU8sQ0FBQyxLQUFLLENBQUMsZ0RBQWdELEtBQUssQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDO1FBQy9FLE9BQU8sSUFBSSxDQUFDO0lBQ2QsQ0FBQztBQUNILENBQUM7QUFFRCxLQUFLLFVBQVUsb0JBQW9CLENBQUMsUUFBUTtJQUMxQyw0Q0FBNEM7SUFDNUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsUUFBUSxDQUFDLEVBQUUsQ0FBQztRQUM3QixvRUFBb0U7UUFDcEUsT0FBTyxDQUFDLEtBQUssQ0FBQywyQ0FBMkMsQ0FBQyxDQUFDO0lBQzdELENBQUM7SUFFRCw4Q0FBOEM7SUFDOUMsTUFBTSxhQUFhLEdBQUcsSUFBSSxHQUFHLENBQUM7UUFDNUIsZUFBZTtRQUNmLGFBQWE7UUFDYixlQUFlO1FBQ2YsMEJBQTBCO1FBQzFCLGdCQUFnQjtRQUNoQixZQUFZO1FBQ1osZ0JBQWdCO1FBQ2hCLGdCQUFnQjtRQUNoQixlQUFlO1FBQ2YsZ0JBQWdCO1FBQ2hCLDRCQUE0QjtRQUM1Qix5QkFBeUI7UUFDekIsMEJBQTBCO1FBQzFCLGtCQUFrQjtRQUNsQixnQkFBZ0I7S0FDakIsQ0FBQyxDQUFDO0lBRUgsc0RBQXNEO0lBQ3RELE1BQU0sU0FBUyxHQUFHLE1BQU0sQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDO0lBQ2pELE1BQU0sYUFBYSxHQUFHLFNBQVMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLGFBQWEsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztJQUVuRSx5REFBeUQ7SUFDekQsTUFBTSxNQUFNLEdBQUcsUUFBUSxDQUFDLE1BQU0sQ0FBQyxDQUFDLEdBQUcsRUFBRSxJQUFJLEVBQUUsR0FBRyxFQUFFLEVBQUU7UUFDaEQsSUFBSSxPQUFPLElBQUksQ0FBQyxLQUFLLEtBQUssUUFBUSxJQUFJLE9BQU8sSUFBSSxDQUFDLE1BQU0sS0FBSyxRQUFRLEVBQUUsQ0FBQztZQUN0RSxNQUFNLElBQUksS0FBSyxDQUFDLDRDQUE0QyxHQUFHLEVBQUUsQ0FBQyxDQUFDO1FBQ3JFLENBQUM7UUFDRCxNQUFNLEdBQUcsR0FBRyxHQUFHLElBQUksQ0FBQyxLQUFLLE1BQU0sSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFDO1FBQzdDLEdBQUcsQ0FBQyxHQUFHLENBQUMsR0FBRyxHQUFHLENBQUMsR0FBRyxDQUFDLElBQUksRUFBRSxDQUFDO1FBQzFCLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDcEIsT0FBTyxHQUFHLENBQUM7SUFDYixDQUFDLEVBQUUsRUFBRSxDQUFDLENBQUM7SUFFUCxzQkFBc0I7SUFDdEIsT0FBTyxNQUFNLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsR0FBRyxFQUFFLEtBQUssQ0FBQyxFQUFFLEVBQUU7UUFDakQsOEJBQThCO1FBQzlCLE1BQU0sU0FBUyxHQUFHLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUMzQixLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsS0FBSyxDQUFDLE1BQU0sRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFDO1lBQ3RDLEtBQUssTUFBTSxLQUFLLElBQUksYUFBYSxFQUFFLENBQUM7Z0JBQ2xDLElBQUksS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxLQUFLLFNBQVMsQ0FBQyxLQUFLLENBQUMsRUFBRSxDQUFDO29CQUN6QyxNQUFNLElBQUksS0FBSyxDQUNiLFVBQVUsS0FBSywyQkFBMkIsU0FBUyxDQUFDLEtBQUssY0FBYyxTQUFTLENBQUMsTUFBTSxNQUFNO3dCQUMzRixXQUFXLFNBQVMsQ0FBQyxLQUFLLENBQUMsY0FBYyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FDOUQsQ0FBQztnQkFDSixDQUFDO1lBQ0gsQ0FBQztRQUNILENBQUM7UUFFRCxzQkFBc0I7UUFDdEIsTUFBTSxNQUFNLEdBQUcsRUFBRSxDQUFDO1FBQ2xCLHNEQUFzRDtRQUN0RCxLQUFLLE1BQU0sS0FBSyxJQUFJLGFBQWEsRUFBRSxDQUFDO1lBQ2xDLE1BQU0sQ0FBQyxLQUFLLENBQUMsR0FBRyxTQUFTLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDbkMsQ0FBQztRQUNELG1CQUFtQjtRQUNuQixNQUFNLENBQUMsUUFBUSxHQUFHLEtBQUssQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLEVBQUU7WUFDakMsTUFBTSxVQUFVLEdBQUcsRUFBRSxDQUFDO1lBQ3RCLEtBQUssTUFBTSxLQUFLLElBQUksYUFBYSxFQUFFLENBQUM7Z0JBQ2xDLFVBQVUsQ0FBQyxLQUFLLENBQUMsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7WUFDbEMsQ0FBQztZQUNELE9BQU8sVUFBVSxDQUFDO1FBQ3BCLENBQUMsQ0FBQyxDQUFDO1FBRUgsT0FBTyxNQUFNLENBQUM7SUFDaEIsQ0FBQyxDQUFDLENBQUM7QUFDTCxDQUFDIn0=