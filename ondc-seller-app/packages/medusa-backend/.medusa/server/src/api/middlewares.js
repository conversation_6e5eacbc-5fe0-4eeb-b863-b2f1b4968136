"use strict";
/**
 * Task 2.2: Integrate Middleware with All Medusa Endpoints
 *
 * This file defines middleware registration for all Medusa v2 routes
 * using the defineMiddlewares approach. It ensures tenant middleware
 * runs on all /admin/* and /store/* endpoints with proper execution order.
 */
Object.defineProperty(exports, "__esModule", { value: true });
console.log('🔧 [MIDDLEWARES] Loading middleware configuration...');
const http_1 = require("@medusajs/framework/http");
const tenant_1 = require("../middleware/tenant");
/**
 * Enhanced Tenant Middleware Wrapper
 * Wraps the tenant middleware for Medusa v2 compatibility
 */
const tenantMiddlewareWrapper = async (req, res, next) => {
    try {
        await (0, tenant_1.tenantMiddleware)(req, res, next);
    }
    catch (error) {
        console.error('❌ [MIDDLEWARE] Tenant middleware error:', error);
        await (0, tenant_1.tenantErrorHandler)(error, req, res, next);
    }
};
/**
 * Request Logging Middleware
 * Logs all requests with tenant context for debugging
 */
const requestLogger = (req, res, next) => {
    const tenantId = req.tenantId || 'unknown';
    const timestamp = new Date().toISOString();
    console.log(`🌐 [${timestamp}] ${req.method} ${req.path} | Tenant: ${tenantId}`);
    // Log request headers in development
    if (process.env.NODE_ENV === 'development') {
        const tenantHeader = req.headers['x-tenant-id'];
        if (tenantHeader) {
            console.log(`   📋 Tenant Header: ${tenantHeader}`);
        }
    }
    next();
};
/**
 * Response Headers Middleware
 * Adds tenant-related headers to responses
 */
const responseHeaders = (req, res, next) => {
    const tenantId = req.tenantId;
    if (tenantId && process.env.NODE_ENV === 'development') {
        res.setHeader('X-Tenant-Context', tenantId);
        res.setHeader('X-Multi-Tenant-Enabled', 'true');
    }
    next();
};
/**
 * Middleware Configuration
 * Defines which middleware applies to which routes
 */
exports.default = (0, http_1.defineMiddlewares)({
    routes: [
        // ============================================================================
        // ADMIN ROUTES - All /admin/* endpoints
        // ============================================================================
        {
            matcher: '/admin/**',
            middlewares: [requestLogger, tenantMiddlewareWrapper, responseHeaders],
        },
        // ============================================================================
        // STORE ROUTES - All /store/* endpoints
        // ============================================================================
        {
            matcher: '/store/**',
            middlewares: [requestLogger, tenantMiddlewareWrapper, responseHeaders],
        },
        // ============================================================================
        // SPECIFIC ADMIN ENDPOINTS - Enhanced logging for key endpoints
        // ============================================================================
        {
            matcher: '/admin/products/**',
            middlewares: [
                (req, res, next) => {
                    console.log(`🛍️  [PRODUCTS] ${req.method} ${req.path} | Tenant: ${req.tenantId}`);
                    next();
                },
            ],
        },
        {
            matcher: '/admin/customers/**',
            middlewares: [
                (req, res, next) => {
                    console.log(`👥 [CUSTOMERS] ${req.method} ${req.path} | Tenant: ${req.tenantId}`);
                    next();
                },
            ],
        },
        {
            matcher: '/admin/orders/**',
            middlewares: [
                (req, res, next) => {
                    console.log(`📦 [ORDERS] ${req.method} ${req.path} | Tenant: ${req.tenantId}`);
                    next();
                },
            ],
        },
        // ============================================================================
        // TENANT MANAGEMENT ENDPOINT - Special handling
        // ============================================================================
        {
            matcher: '/admin/tenant/**',
            middlewares: [
                (req, res, next) => {
                    console.log(`🏢 [TENANT-MGMT] ${req.method} ${req.path} | Tenant: ${req.tenantId}`);
                    next();
                },
            ],
        },
        // ============================================================================
        // STORE PRODUCT ENDPOINTS - Enhanced logging
        // ============================================================================
        {
            matcher: '/store/products/**',
            middlewares: [
                (req, res, next) => {
                    console.log(`🛒 [STORE-PRODUCTS] ${req.method} ${req.path} | Tenant: ${req.tenantId}`);
                    next();
                },
            ],
        },
        // ============================================================================
        // STORE ORDERS ENDPOINTS - Authentication + Enhanced logging
        // ============================================================================
        {
            matcher: '/store/orders/**',
            middlewares: [
                (0, http_1.authenticate)(['customer'], 'bearer'),
                (req, res, next) => {
                    console.log(`📦 [STORE-ORDERS] ${req.method} ${req.path} | Tenant: ${req.tenantId} | Customer: ${req.auth_context?.actor_id || 'None'}`);
                    next();
                },
            ],
        },
    ],
});
/**
 * Middleware Execution Order:
 *
 * 1. requestLogger - Logs incoming requests with tenant context
 * 2. tenantMiddlewareWrapper - Extracts and validates tenant from headers
 * 3. responseHeaders - Adds tenant context to response headers
 * 4. Specific endpoint loggers - Additional logging for key endpoints
 *
 * This ensures:
 * - All /admin/* and /store/* routes have tenant middleware
 * - Proper error handling for tenant validation failures
 * - Comprehensive logging for debugging and monitoring
 * - Response headers for development debugging
 */
//# sourceMappingURL=data:application/json;base64,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