"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.POST = exports.OPTIONS = void 0;
const child_process_1 = require("child_process");
const util_1 = require("util");
const execAsync = (0, util_1.promisify)(child_process_1.exec);
// CORS headers for admin signup - dynamically set based on request origin
const getAllowedOrigins = () => {
    const adminCors = process.env.ADMIN_CORS || 'http://localhost:3000,http://localhost:3001';
    return adminCors.split(',').map(origin => origin.trim());
};
const getCorsHeaders = (requestOrigin) => {
    const allowedOrigins = getAllowedOrigins();
    const origin = requestOrigin && allowedOrigins.includes(requestOrigin) ? requestOrigin : allowedOrigins[0]; // Default to first allowed origin
    return {
        'Access-Control-Allow-Origin': origin,
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Tenant-ID',
        'Access-Control-Allow-Credentials': 'true',
        'Access-Control-Max-Age': '86400',
    };
};
// Handle preflight OPTIONS requests
const OPTIONS = async (req, res) => {
    const origin = req.headers.origin;
    const corsHeaders = getCorsHeaders(origin);
    Object.entries(corsHeaders).forEach(([key, value]) => {
        res.setHeader(key, value);
    });
    return res.status(200).end();
};
exports.OPTIONS = OPTIONS;
const POST = async (req, res) => {
    // Set CORS headers for all responses
    const origin = req.headers.origin;
    const corsHeaders = getCorsHeaders(origin);
    Object.entries(corsHeaders).forEach(([key, value]) => {
        res.setHeader(key, value);
    });
    try {
        console.log('🔐 Admin signup request:', { email: req.body?.email });
        const { email, password, firstName, lastName, phone, storeName, storeHandle } = req.body;
        console.log('req.body::::::::', req.body);
        // Get tenant ID from header
        const tenantId = req.headers['x-tenant-id'] || 'default';
        console.log('🏢 Tenant ID:', tenantId);
        // Validate input
        if (!email || !password) {
            return res.status(400).json({
                message: 'Email and password are required',
            });
        }
        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return res.status(400).json({
                message: 'Invalid email format',
            });
        }
        // Validate password strength
        if (password.length < 8) {
            return res.status(400).json({
                message: 'Password must be at least 8 characters long',
            });
        }
        console.log('🎯 Using two-step process: CLI user creation + user data update...');
        // STEP 1: Create basic user using CLI command
        console.log('📝 STEP 1: Creating basic user via CLI command...');
        try {
            // Execute the CLI command from the correct directory
            const workingDirectory = process.cwd();
            const cliCommand = `npx medusa user --email "${email}" --password "${password}"`;
            console.log('🔧 Working directory:', workingDirectory);
            console.log('🔧 Executing CLI command:', cliCommand);
            const { stdout, stderr } = await execAsync(cliCommand, {
                cwd: workingDirectory,
                timeout: 30000, // 30 second timeout
            });
            console.log('🔍 CLI stdout:', stdout);
            console.log('🔍 CLI stderr:', stderr);
            // Check for user already exists error
            if (stdout.includes('Identity with email already exists') || stderr.includes('Identity with email already exists')) {
                console.log('⚠️ User already exists, returning appropriate error');
                return res.status(400).json({
                    message: 'User with this email already exists',
                });
            }
            // Check if the command was successful
            if (stderr && stderr.includes('Error')) {
                console.error('❌ CLI command failed with error:', stderr);
                return res.status(400).json({
                    message: `Failed to create user account: ${stderr}`,
                });
            }
            // Check for various success indicators in the output
            const successIndicators = [
                'User created successfully',
                'created successfully',
                'User with email',
                'successfully created'
            ];
            const hasSuccess = successIndicators.some(indicator => stdout.toLowerCase().includes(indicator.toLowerCase()) ||
                stderr.toLowerCase().includes(indicator.toLowerCase()));
            if (!hasSuccess) {
                console.error('❌ CLI command did not report success');
                console.log('🔍 Full stdout:', stdout);
                console.log('🔍 Full stderr:', stderr);
                return res.status(400).json({
                    message: 'CLI command did not complete successfully',
                });
            }
            console.log('✅ STEP 1 COMPLETED: User created successfully via CLI command');
        }
        catch (cliError) {
            console.error('❌ CLI command execution failed:', cliError);
            console.log('🔍 CLI error details:', {
                message: cliError.message,
                code: cliError.code,
                signal: cliError.signal,
                stdout: cliError.stdout,
                stderr: cliError.stderr,
            });
            return res.status(400).json({
                message: `Failed to create user account via CLI: ${cliError.message}`,
            });
        }
        // STEP 2: Update user with complete data
        console.log('🔄 STEP 2: Updating user with complete data...');
        try {
            // Get user service from container - try different service names
            let userService = null;
            const serviceNames = ['user', 'userModuleService', 'userModule'];
            for (const serviceName of serviceNames) {
                try {
                    userService = req.scope.resolve(serviceName);
                    console.log(`✅ User service resolved successfully as: ${serviceName}`);
                    break;
                }
                catch (error) {
                    console.log(`❌ Failed to resolve service: ${serviceName}`);
                }
            }
            if (!userService) {
                console.error('❌ Could not resolve user service with any known name');
                return res.status(500).json({
                    message: 'User service not available for data update',
                });
            }
            // Find the newly created user by email
            console.log(`🔍 Searching for user with email: ${email}`);
            let users = [];
            try {
                if (typeof userService.listUsers === 'function') {
                    users = await userService.listUsers({ email: email });
                }
                else if (typeof userService.list === 'function') {
                    users = await userService.list({ email: email });
                }
                else if (typeof userService.retrieveByEmail === 'function') {
                    const user = await userService.retrieveByEmail(email);
                    users = user ? [user] : [];
                }
                else {
                    console.log('❌ No suitable method found to fetch users');
                    console.log('Available methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(userService)));
                    return res.status(500).json({
                        message: 'Cannot find user after CLI creation - service method not available',
                    });
                }
            }
            catch (fetchError) {
                console.error('❌ Error fetching user:', fetchError);
                return res.status(500).json({
                    message: 'User was created but could not be found for updating',
                });
            }
            if (!users || users.length === 0) {
                console.error('❌ User not found after CLI creation');
                return res.status(500).json({
                    message: 'User was created but could not be found for updating',
                });
            }
            const createdUser = users[0];
            console.log('✅ User found:', { id: createdUser.id, email: createdUser.email });
            // STEP 2: Update user with complete data
            console.log('� Setting up authentication for user...');
            // Prepare complete user data for update
            const userData = {
                email: email,
                first_name: firstName || 'Admin',
                last_name: lastName || 'User',
                avatar_url: null,
                metadata: {
                    user_type: 'admin',
                    tenant_id: tenantId,
                    phone: phone,
                    store_name: storeName,
                    store_handle: storeHandle,
                    onboarding_status: 'pending',
                    onboarding_add_product: false,
                    onboarding_add_bulk_product: false,
                    onboarding_store_configuration: false,
                },
            };
            console.log('🔄 Updating user with complete data:', {
                email,
                first_name: userData.first_name,
                last_name: userData.last_name,
                metadata_keys: Object.keys(userData.metadata)
            });
            let updatedUser = null;
            try {
                // Try different methods to update user with correct parameters
                if (typeof userService.updateUsers === 'function') {
                    // updateUsers expects an array of updates with id and data
                    const updateResult = await userService.updateUsers([
                        {
                            id: createdUser.id,
                            ...userData,
                        },
                    ]);
                    updatedUser = Array.isArray(updateResult) ? updateResult[0] : updateResult;
                }
                else if (typeof userService.update === 'function') {
                    updatedUser = await userService.update(createdUser.id, userData);
                }
                else if (typeof userService.updateUser === 'function') {
                    updatedUser = await userService.updateUser(createdUser.id, userData);
                }
                else {
                    console.log('❌ No suitable method found to update users');
                    console.log('Available methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(userService)));
                    return res.status(500).json({
                        message: 'User created but update method not available',
                    });
                }
                if (!updatedUser) {
                    console.log('⚠️ User update returned null or undefined');
                    return res.status(500).json({
                        message: 'User created but update operation failed',
                    });
                }
                console.log('✅ STEP 2 COMPLETED: User updated successfully with complete data');
            }
            catch (updateError) {
                console.error('❌ Error updating user with complete data:', updateError);
                return res.status(500).json({
                    message: `User created but failed to update with complete data: ${updateError.message}`,
                });
            }
            // Both steps completed successfully
            console.log('✅ Admin signup completed successfully - both steps completed');
            return res.status(201).json({
                message: 'Admin account created successfully',
                user: {
                    id: updatedUser.id || createdUser.id,
                    email: updatedUser.email || createdUser.email,
                    first_name: updatedUser.first_name || userData.first_name,
                    last_name: updatedUser.last_name || userData.last_name,
                    metadata: updatedUser.metadata || userData.metadata,
                },
                success: true,
                autoLoginSuccess: false, // Frontend expects this field
                isNewUser: true, // Frontend expects this field
                steps_completed: {
                    cli_user_creation: true,
                    user_data_update: true,
                },
            });
        }
        catch (userCreationError) {
            console.error('❌ Error creating user:', userCreationError);
            return res.status(400).json({
                message: `Failed to create user account: ${userCreationError.message}`,
            });
        }
    }
    catch (error) {
        console.error('❌ Admin signup error:', error);
        return res.status(500).json({
            message: 'Internal server error during signup',
        });
    }
};
exports.POST = POST;
//# sourceMappingURL=data:application/json;base64,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