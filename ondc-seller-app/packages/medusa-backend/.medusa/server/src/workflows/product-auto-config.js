"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.applyAutoConfigToExistingProductsWorkflow = exports.importProductsWithAutoConfigWorkflow = exports.createProductsWithAutoConfigWorkflow = void 0;
const workflows_sdk_1 = require("@medusajs/workflows-sdk");
const core_flows_1 = require("@medusajs/core-flows");
/**
 * Step to apply automatic configuration to created products
 */
const applyProductAutoConfigStep = (0, workflows_sdk_1.createStep)('apply-product-auto-config', async (input, { container }) => {
    const productAutoConfigService = container.resolve('productAutoConfigService');
    const logger = container.resolve('logger');
    logger.info(`🔧 [WORKFLOW] Applying auto-configuration to ${input.productIds.length} products`);
    const results = {
        successful: 0,
        failed: 0,
        errors: [],
    };
    // Apply configuration to each product
    for (const productId of input.productIds) {
        try {
            await productAutoConfigService.applyAutoConfiguration(productId, input.tenantId);
            results.successful++;
            logger.info(`✅ [WORKFLOW] Auto-configuration applied to product: ${productId}`);
        }
        catch (error) {
            results.failed++;
            results.errors.push(`Product ${productId}: ${error.message}`);
            logger.error(`❌ [WORKFLOW] Failed to configure product ${productId}:`, error);
        }
    }
    logger.info(`✅ [WORKFLOW] Auto-configuration completed: ${results.successful} successful, ${results.failed} failed`);
    return new workflows_sdk_1.StepResponse(results, {
        productIds: input.productIds,
        results,
    });
}, async (compensationInput, { container }) => {
    const logger = container.resolve('logger');
    logger.info(`🔄 [WORKFLOW] Compensating auto-configuration for products: ${compensationInput.productIds}`);
    // Note: In a real scenario, you might want to revert the sales channel assignments
    // For now, we'll just log the compensation
});
/**
 * Enhanced product creation workflow with automatic configuration
 *
 * This workflow:
 * 1. Creates products using the standard Medusa workflow
 * 2. Automatically assigns them to the default sales channel
 * 3. Sets manage_inventory: false for all variants
 */
exports.createProductsWithAutoConfigWorkflow = (0, workflows_sdk_1.createWorkflow)('create-products-with-auto-config', function (input) {
    // Step 1: Create products using standard Medusa workflow
    const createdProducts = core_flows_1.createProductsWorkflow.runAsStep({
        input: {
            products: input.products,
        },
    });
    // Step 2: Extract product IDs for configuration
    let productIds = [];
    if (Array.isArray(createdProducts)) {
        productIds = createdProducts.map((product) => product.id).filter(Boolean);
    }
    else if (createdProducts?.result && Array.isArray(createdProducts.result)) {
        productIds = createdProducts.result.map((product) => product.id).filter(Boolean);
    }
    else if (createdProducts?.id) {
        productIds = [createdProducts.id];
    }
    else if (createdProducts) {
        // Handle case where createdProducts is a single product object
        productIds = [createdProducts].map((product) => product.id).filter(Boolean);
    }
    // Step 3: Apply automatic configuration
    const configurationResults = applyProductAutoConfigStep({
        productIds,
        tenantId: input.tenantId,
    });
    // Return both the created products and configuration results
    return new workflows_sdk_1.WorkflowResponse({
        products: createdProducts,
        configurationResults,
    });
});
/**
 * Workflow specifically for Excel import with auto-configuration
 */
exports.importProductsWithAutoConfigWorkflow = (0, workflows_sdk_1.createWorkflow)('import-products-with-auto-config', function (input) {
    // Step 1: Create products
    const createdProducts = core_flows_1.createProductsWorkflow.runAsStep({
        input: {
            products: input.products,
        },
    });
    // Step 2: Apply auto-configuration with tenant context
    let productIds = [];
    if (Array.isArray(createdProducts)) {
        productIds = createdProducts.map((product) => product.id).filter(Boolean);
    }
    else if (createdProducts?.result && Array.isArray(createdProducts.result)) {
        productIds = createdProducts.result.map((product) => product.id).filter(Boolean);
    }
    else if (createdProducts?.id) {
        productIds = [createdProducts.id];
    }
    else if (createdProducts) {
        // Handle case where createdProducts is a single product object
        productIds = [createdProducts].map((product) => product.id).filter(Boolean);
    }
    const configurationResults = applyProductAutoConfigStep({
        productIds,
        tenantId: input.tenantId,
    });
    return new workflows_sdk_1.WorkflowResponse({
        products: createdProducts,
        configurationResults,
    });
});
/**
 * Standalone workflow to apply configuration to existing products
 */
exports.applyAutoConfigToExistingProductsWorkflow = (0, workflows_sdk_1.createWorkflow)('apply-auto-config-to-existing-products', function (input) {
    const configurationResults = applyProductAutoConfigStep({
        productIds: input.productIds,
        tenantId: input.tenantId,
    });
    return new workflows_sdk_1.WorkflowResponse({
        configurationResults,
    });
});
//# sourceMappingURL=data:application/json;base64,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