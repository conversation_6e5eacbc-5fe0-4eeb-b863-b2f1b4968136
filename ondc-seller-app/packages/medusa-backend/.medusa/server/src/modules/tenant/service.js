"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TenantService = void 0;
/**
 * Tenant Service for Medusa v2
 *
 * Provides multi-tenant functionality including:
 * - Tenant validation and configuration
 * - ONDC-specific tenant settings
 * - Feature management per tenant
 * - Tenant-aware data filtering
 */
class TenantService {
    constructor(container, options = {}) {
        this.tenantCache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
        // Initialize service without extending MedusaService
    }
    /**
     * Get tenant configuration by ID
     */
    async getTenantConfig(tenantId) {
        // Check cache first
        if (this.tenantCache.has(tenantId)) {
            const cached = this.tenantCache.get(tenantId);
            const isExpired = Date.now() - cached.updatedAt.getTime() > this.cacheTimeout;
            if (!isExpired) {
                return cached;
            }
        }
        // Fetch tenant configuration
        const config = await this.fetchTenantConfig(tenantId);
        if (config) {
            this.tenantCache.set(tenantId, config);
        }
        return config;
    }
    /**
     * Validate if tenant exists and is active
     */
    async validateTenant(tenantId) {
        const config = await this.getTenantConfig(tenantId);
        return config !== null && config.status === 'active';
    }
    /**
     * Check if tenant has specific feature enabled
     */
    async hasTenantFeature(tenantId, feature) {
        const config = await this.getTenantConfig(tenantId);
        if (!config) {
            return false;
        }
        return config.settings.features.includes(feature) ||
            config.settings.features.includes('all');
    }
    /**
     * Get ONDC configuration for tenant
     */
    async getOndcConfig(tenantId) {
        const config = await this.getTenantConfig(tenantId);
        return config?.settings.ondcConfig || null;
    }
    /**
     * Get all active tenants
     */
    async getActiveTenants() {
        // In development, return mock tenants
        if (process.env.NODE_ENV === 'development') {
            return [
                await this.getTenantConfig('tenant-electronics-001'),
                await this.getTenantConfig('tenant-fashion-002'),
                await this.getTenantConfig('tenant-books-003'),
            ].filter(Boolean);
        }
        // In production, fetch from database
        const validTenants = process.env.VALID_TENANTS?.split(',') || ['default'];
        const configs = await Promise.all(validTenants.map(id => this.getTenantConfig(id)));
        return configs.filter(Boolean);
    }
    /**
     * Create tenant-aware database query filter
     */
    createTenantFilter(tenantId) {
        return {
            tenant_id: tenantId
        };
    }
    /**
     * Add tenant context to data
     */
    addTenantContext(data, tenantId) {
        return {
            ...data,
            tenant_id: tenantId
        };
    }
    /**
     * Validate tenant access to resource
     */
    async validateTenantAccess(tenantId, resourceTenantId) {
        // Basic validation - resource must belong to the same tenant
        return tenantId === resourceTenantId;
    }
    /**
     * Get tenant-specific settings
     */
    async getTenantSettings(tenantId) {
        const config = await this.getTenantConfig(tenantId);
        return config?.settings || null;
    }
    /**
     * Update tenant configuration (admin only)
     */
    async updateTenantConfig(tenantId, updates) {
        const existing = await this.getTenantConfig(tenantId);
        if (!existing) {
            throw new Error(`Tenant ${tenantId} not found`);
        }
        const updated = {
            ...existing,
            ...updates,
            id: tenantId, // Ensure ID cannot be changed
            updatedAt: new Date()
        };
        // In production, save to database
        // For now, update cache
        this.tenantCache.set(tenantId, updated);
        return updated;
    }
    /**
     * Clear tenant cache
     */
    clearTenantCache(tenantId) {
        if (tenantId) {
            this.tenantCache.delete(tenantId);
        }
        else {
            this.tenantCache.clear();
        }
    }
    /**
     * Private method to fetch tenant configuration
     */
    async fetchTenantConfig(tenantId) {
        // For development mode, return mock configuration
        if (process.env.NODE_ENV === 'development') {
            return this.createMockTenantConfig(tenantId);
        }
        // In production, this would fetch from database
        const validTenants = process.env.VALID_TENANTS?.split(',') || ['default'];
        if (!validTenants.includes(tenantId)) {
            return null;
        }
        return this.createMockTenantConfig(tenantId);
    }
    /**
     * Create mock tenant configuration for development
     */
    createMockTenantConfig(tenantId) {
        const tenantConfigs = {
            'tenant-electronics-001': {
                name: 'Electronics Store',
                domain: 'electronics.ondc-seller.com',
                settings: {
                    currency: 'INR',
                    timezone: 'Asia/Kolkata',
                    features: ['products', 'orders', 'customers', 'analytics', 'inventory'],
                    ondcConfig: {
                        participantId: 'electronics-participant-001',
                        subscriberId: 'electronics-subscriber-001',
                        bppId: 'ondc-bpp-electronics-001',
                    },
                },
            },
            'tenant-fashion-002': {
                name: 'Fashion Store',
                domain: 'fashion.ondc-seller.com',
                settings: {
                    currency: 'INR',
                    timezone: 'Asia/Kolkata',
                    features: ['products', 'orders', 'customers', 'promotions'],
                    ondcConfig: {
                        participantId: 'fashion-participant-002',
                        subscriberId: 'fashion-subscriber-002',
                        bppId: 'ondc-bpp-fashion-002',
                    },
                },
            },
            'tenant-books-003': {
                name: 'Books Store',
                domain: 'books.ondc-seller.com',
                settings: {
                    currency: 'INR',
                    timezone: 'Asia/Kolkata',
                    features: ['products', 'orders', 'customers'],
                    ondcConfig: {
                        participantId: 'books-participant-003',
                        subscriberId: 'books-subscriber-003',
                        bppId: 'ondc-bpp-books-003',
                    },
                },
            },
        };
        const baseConfig = {
            id: tenantId,
            name: `Tenant ${tenantId}`,
            domain: `${tenantId}.ondc-seller.com`,
            settings: {
                currency: 'INR',
                timezone: 'Asia/Kolkata',
                features: ['products', 'orders', 'customers'],
                ondcConfig: {
                    participantId: process.env[`ONDC_PARTICIPANT_ID_${tenantId.toUpperCase()}`] || process.env.ONDC_PARTICIPANT_ID,
                    subscriberId: process.env[`ONDC_SUBSCRIBER_ID_${tenantId.toUpperCase()}`] || process.env.ONDC_SUBSCRIBER_ID,
                    bppId: process.env[`ONDC_BPP_ID_${tenantId.toUpperCase()}`] || `${process.env.ONDC_BPP_ID || 'ondc-bpp'}-${tenantId}`,
                },
            },
            status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        const specificConfig = tenantConfigs[tenantId] || {};
        return {
            ...baseConfig,
            ...specificConfig,
            id: tenantId,
            settings: {
                ...baseConfig.settings,
                ...specificConfig.settings,
            },
        };
    }
}
exports.TenantService = TenantService;
exports.default = TenantService;
//# sourceMappingURL=data:application/json;base64,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