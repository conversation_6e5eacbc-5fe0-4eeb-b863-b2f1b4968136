#!/usr/bin/env node

/**
 * Generate Postman Collection from OpenAPI Specification
 * This script converts the OpenAPI spec to a Postman collection for easy testing
 */

const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');

// Read the OpenAPI specification (try JSON first, then YAML)
let openApiSpec;
const jsonPath = path.join(__dirname, 'openapi-spec.json');
const yamlPath = path.join(__dirname, 'openapi-updated.yaml');

try {
  if (fs.existsSync(jsonPath)) {
    openApiSpec = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));
    console.log('📖 Using JSON OpenAPI specification');
  } else if (fs.existsSync(yamlPath)) {
    openApiSpec = yaml.load(fs.readFileSync(yamlPath, 'utf8'));
    console.log('📖 Using YAML OpenAPI specification');
  } else {
    throw new Error('No OpenAPI specification found');
  }
} catch (error) {
  console.error('❌ Error reading OpenAPI specification:', error.message);
  process.exit(1);
}

// Generate Postman collection
function generatePostmanCollection(spec) {
  const collection = {
    info: {
      name: spec.info.title,
      description: spec.info.description,
      version: spec.info.version,
      schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json',
    },
    auth: {
      type: 'bearer',
      bearer: [
        {
          key: 'token',
          value: '{{admin_token}}',
          type: 'string',
        },
      ],
    },
    variable: [
      {
        key: 'base_url',
        value: 'http://localhost:9000',
        type: 'string',
      },
      {
        key: 'admin_token',
        value: '',
        type: 'string',
      },
      {
        key: 'publishable_api_key',
        value: 'pk_test_123',
        type: 'string',
      },
      {
        key: 'tenant_id',
        value: 'tenant-electronics-001',
        type: 'string',
      },
    ],
    item: [],
  };

  // Group endpoints by tags
  const folders = {};

  for (const [path, methods] of Object.entries(spec.paths)) {
    for (const [method, operation] of Object.entries(methods)) {
      if (typeof operation !== 'object' || !operation.tags) continue;

      const tag = operation.tags[0];
      if (!folders[tag]) {
        folders[tag] = {
          name: tag,
          item: [],
        };
      }

      // Create request
      const request = {
        name: operation.summary || `${method.toUpperCase()} ${path}`,
        request: {
          method: method.toUpperCase(),
          header: [],
          url: {
            raw: `{{base_url}}${path}`,
            host: ['{{base_url}}'],
            path: path.split('/').filter(p => p),
          },
        },
      };

      // Add headers based on security requirements
      if (operation.security) {
        operation.security.forEach(security => {
          if (security.BearerAuth) {
            request.request.auth = {
              type: 'bearer',
              bearer: [
                {
                  key: 'token',
                  value: '{{admin_token}}',
                  type: 'string',
                },
              ],
            };
          }
          if (security.PublishableApiKey) {
            request.request.header.push({
              key: 'x-publishable-api-key',
              value: '{{publishable_api_key}}',
              type: 'text',
            });
          }
        });
      }

      // Add tenant header for multi-tenant endpoints
      if (
        operation.parameters &&
        operation.parameters.some(p => p.$ref === '#/components/parameters/TenantId')
      ) {
        request.request.header.push({
          key: 'x-tenant-id',
          value: '{{tenant_id}}',
          type: 'text',
        });
      }

      // Add request body for POST/PUT requests
      if (operation.requestBody && ['post', 'put', 'patch'].includes(method)) {
        const content = operation.requestBody.content;
        if (content['application/json']) {
          request.request.header.push({
            key: 'Content-Type',
            value: 'application/json',
            type: 'text',
          });

          // Add example body based on schema
          const schema = content['application/json'].schema;
          request.request.body = {
            mode: 'raw',
            raw: JSON.stringify(generateExampleFromSchema(schema), null, 2),
          };
        }
      }

      folders[tag].item.push(request);
    }
  }

  // Add folders to collection
  collection.item = Object.values(folders);

  return collection;
}

// Generate example data from schema
function generateExampleFromSchema(schema) {
  if (!schema) return {};

  if (schema.example) return schema.example;

  if (schema.type === 'object' && schema.properties) {
    const example = {};
    for (const [key, prop] of Object.entries(schema.properties)) {
      if (prop.example !== undefined) {
        example[key] = prop.example;
      } else if (prop.type === 'string') {
        example[key] = prop.format === 'email' ? '<EMAIL>' : 'string';
      } else if (prop.type === 'integer') {
        example[key] = 1;
      } else if (prop.type === 'number') {
        example[key] = 1.0;
      } else if (prop.type === 'boolean') {
        example[key] = true;
      } else if (prop.type === 'array') {
        example[key] = [];
      } else if (prop.type === 'object') {
        example[key] = {};
      }
    }
    return example;
  }

  return {};
}

// Generate and save collection
try {
  const collection = generatePostmanCollection(openApiSpec);
  const outputPath = path.join(
    __dirname,
    'postman',
    'ONDC-Seller-Backend-API.postman_collection.json'
  );

  // Ensure postman directory exists
  const postmanDir = path.dirname(outputPath);
  if (!fs.existsSync(postmanDir)) {
    fs.mkdirSync(postmanDir, { recursive: true });
  }

  fs.writeFileSync(outputPath, JSON.stringify(collection, null, 2));
  console.log(`✅ Postman collection generated: ${outputPath}`);

  // Also generate environment file
  const environment = {
    name: 'ONDC Seller Backend - Development',
    values: [
      {
        key: 'base_url',
        value: 'http://localhost:9000',
        enabled: true,
      },
      {
        key: 'admin_token',
        value: '',
        enabled: true,
      },
      {
        key: 'publishable_api_key',
        value: 'pk_test_123',
        enabled: true,
      },
      {
        key: 'tenant_id',
        value: 'tenant-electronics-001',
        enabled: true,
      },
      {
        key: 'customer_email',
        value: '<EMAIL>',
        enabled: true,
      },
    ],
  };

  const envPath = path.join(
    __dirname,
    'postman',
    'ONDC-Seller-Backend-Development.postman_environment.json'
  );
  fs.writeFileSync(envPath, JSON.stringify(environment, null, 2));
  console.log(`✅ Postman environment generated: ${envPath}`);
} catch (error) {
  console.error('❌ Error generating Postman collection:', error);
  process.exit(1);
}
