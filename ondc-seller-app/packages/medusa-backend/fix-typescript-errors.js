#!/usr/bin/env node

/**
 * Automated TypeScript Error Fixer
 * This script automatically fixes common TypeScript errors in the Medusa backend
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Starting automated TypeScript error fixes...');

// 1. Install missing dependencies
function installDependencies() {
  console.log('📦 Installing missing dependencies...');

  try {
    execSync('npm install json-schema-to-zod', { stdio: 'inherit' });
    execSync('npm install --save-dev @types/node', { stdio: 'inherit' });
    console.log('✅ Dependencies installed successfully');
  } catch (error) {
    console.error('❌ Error installing dependencies:', error.message);
  }
}

// 2. Fix src/index.ts file
function fixIndexTs() {
  const indexPath = path.join(__dirname, 'src', 'index.ts');

  if (!fs.existsSync(indexPath)) {
    console.log('⚠️  src/index.ts not found, skipping fixes');
    return;
  }

  console.log('🔨 Fixing src/index.ts...');

  let content = fs.readFileSync(indexPath, 'utf8');

  // Fix imports
  const importFixes = [
    {
      search: /import axios from 'axios';/g,
      replace: "import axios, { AxiosRequestConfig, AxiosError, Method } from 'axios';",
    },
    {
      search: /import { jsonSchemaToZod } from 'json-schema-to-zod';/g,
      replace: "import { jsonSchemaToZod } from 'json-schema-to-zod';",
    },
  ];

  // Apply import fixes
  importFixes.forEach(fix => {
    if (!content.includes('AxiosRequestConfig, AxiosError, Method')) {
      content = content.replace(fix.search, fix.replace);
    }
  });

  // Add missing import if not present
  if (!content.includes('json-schema-to-zod') && !content.includes('import { jsonSchemaToZod }')) {
    const firstImport = content.indexOf('import');
    if (firstImport !== -1) {
      const importLine = "import { jsonSchemaToZod } from 'json-schema-to-zod';\n";
      content = content.slice(0, firstImport) + importLine + content.slice(firstImport);
    }
  }

  // Fix axios calls - pattern 1: axios({ config })
  content = content.replace(
    /const response = await axios\(\{([^}]+)\}\);/g,
    `const config: AxiosRequestConfig = {$1};
    const response = await axios(config);`
  );

  // Fix method type assertions
  content = content.replace(
    /method: definition\.method\.toUpperCase\(\),/g,
    'method: definition.method.toUpperCase() as Method,'
  );

  content = content.replace(
    /method: ([^,\s]+)\.toUpperCase\(\),/g,
    'method: $1.toUpperCase() as Method,'
  );

  // Fix axios error handling
  content = content.replace(
    /if \(axios\.isAxiosError\(error\)\) \{[\s\S]*?errorMessage = formatApiError\(error\);/g,
    `if (axios.isAxiosError(error)) {
        errorMessage = formatApiError(error as AxiosError);`
  );

  // Fix direct axios calls without proper config typing
  content = content.replace(
    /await axios\(config\);/g,
    'await axios(config as AxiosRequestConfig);'
  );

  // Write the fixed content back
  fs.writeFileSync(indexPath, content);
  console.log('✅ src/index.ts fixed successfully');
}

// 3. Update package.json with correct dependencies
function updatePackageJson() {
  const packagePath = path.join(__dirname, 'package.json');

  if (!fs.existsSync(packagePath)) {
    console.log('⚠️  package.json not found');
    return;
  }

  console.log('📝 Updating package.json dependencies...');

  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));

  // Ensure dependencies exist
  if (!packageJson.dependencies) packageJson.dependencies = {};
  if (!packageJson.devDependencies) packageJson.devDependencies = {};

  // Add/update dependencies
  packageJson.dependencies['json-schema-to-zod'] = '^2.1.0';
  packageJson.dependencies['axios'] = '^1.6.0';
  packageJson.devDependencies['@types/node'] = '^20.0.0';
  packageJson.devDependencies['typescript'] = '^5.0.0';

  fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
  console.log('✅ package.json updated successfully');
}

// 4. Create TypeScript configuration if missing
function ensureTsConfig() {
  const tsConfigPath = path.join(__dirname, 'tsconfig.json');

  if (fs.existsSync(tsConfigPath)) {
    console.log('✅ tsconfig.json already exists');
    return;
  }

  console.log('📝 Creating tsconfig.json...');

  const tsConfig = {
    compilerOptions: {
      target: 'ES2022',
      module: 'commonjs',
      lib: ['ES2022'],
      allowJs: true,
      outDir: './dist',
      rootDir: './src',
      strict: true,
      moduleResolution: 'node',
      esModuleInterop: true,
      skipLibCheck: true,
      forceConsistentCasingInFileNames: true,
      resolveJsonModule: true,
      experimentalDecorators: true,
      emitDecoratorMetadata: true,
    },
    include: ['src/**/*'],
    exclude: ['node_modules', 'dist'],
  };

  fs.writeFileSync(tsConfigPath, JSON.stringify(tsConfig, null, 2));
  console.log('✅ tsconfig.json created successfully');
}

// 5. Clean build artifacts
function cleanBuild() {
  console.log('🧹 Cleaning build artifacts...');

  const dirsToClean = ['dist', 'node_modules/.cache'];

  dirsToClean.forEach(dir => {
    const dirPath = path.join(__dirname, dir);
    if (fs.existsSync(dirPath)) {
      try {
        execSync(`rm -rf ${dirPath}`, { stdio: 'inherit' });
        console.log(`✅ Cleaned ${dir}`);
      } catch (error) {
        console.log(`⚠️  Could not clean ${dir}: ${error.message}`);
      }
    }
  });
}

// 6. Try to build and run
function buildAndTest() {
  console.log('🔨 Attempting to build...');

  try {
    execSync('npm run build', { stdio: 'inherit' });
    console.log('✅ Build successful!');

    console.log('🚀 Starting server...');
    execSync('node start-server.js &', { stdio: 'inherit' });
    console.log('✅ Server started successfully!');
  } catch (error) {
    console.log('⚠️  Build failed, but you can try running directly with: node start-server.js');
    console.log('Error details:', error.message);
  }
}

// Main execution
async function main() {
  try {
    console.log('🎯 Automated TypeScript Error Fixer for Medusa Backend');
    console.log('='.repeat(60));

    // Step 1: Install dependencies
    installDependencies();

    // Step 2: Update package.json
    updatePackageJson();

    // Step 3: Ensure TypeScript config
    ensureTsConfig();

    // Step 4: Fix TypeScript errors in source files
    fixIndexTs();

    // Step 5: Clean build artifacts
    cleanBuild();

    // Step 6: Try to build and run
    buildAndTest();

    console.log('\n' + '='.repeat(60));
    console.log('🎉 Automated fixes completed!');
    console.log('\nNext steps:');
    console.log('1. Run: npm run build');
    console.log('2. If build succeeds, run: node start-server.js');
    console.log('3. If build fails, you can run directly: node start-server.js');
    console.log('4. Server will be available at: http://localhost:9000');
  } catch (error) {
    console.error('💥 Automation failed:', error);
    process.exit(1);
  }
}

// Run the automation
main();
