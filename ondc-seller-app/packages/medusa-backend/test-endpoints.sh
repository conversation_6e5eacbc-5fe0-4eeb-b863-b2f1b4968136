#!/bin/bash

# Test API Endpoints Script
# This script tests the key API endpoints using curl

BASE_URL="http://localhost:9000"
TENANT_ID="tenant-electronics-001"
PUBLISHABLE_API_KEY="pk_test_123"

echo "🚀 Starting API Endpoint Tests"
echo "📍 Base URL: $BASE_URL"
echo "🏢 Tenant ID: $TENANT_ID"
echo "============================================================"

# Test 1: Store Information
echo -e "\n🧪 Testing: Get tenant-specific store information"
curl -s -X GET "$BASE_URL/store/test-info" \
  -H "x-tenant-id: $TENANT_ID" \
  -H "x-publishable-api-key: $PUBLISHABLE_API_KEY" \
  -H "Content-Type: application/json" | jq '.' || echo "❌ Failed or no JSON response"

# Test 2: Admin Tenant Information  
echo -e "\n🧪 Testing: Get admin tenant configuration"
curl -s -X GET "$BASE_URL/admin/tenant" \
  -H "x-tenant-id: $TENANT_ID" \
  -H "Content-Type: application/json" | jq '.' || echo "❌ Failed or no JSON response"

# Test 3: Orders (simple endpoint)
echo -e "\n🧪 Testing: Get orders by email"
curl -s -X GET "$BASE_URL/store/orders/simple?email=<EMAIL>" \
  -H "x-tenant-id: $TENANT_ID" \
  -H "x-publishable-api-key: $PUBLISHABLE_API_KEY" \
  -H "Content-Type: application/json" | jq '.' || echo "❌ Failed or no JSON response"

# Test 4: Authentication (admin login)
echo -e "\n🧪 Testing: Admin user login"
ADMIN_TOKEN=$(curl -s -X POST "$BASE_URL/auth/user/emailpass" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"supersecret"}' | jq -r '.token' 2>/dev/null)

if [ "$ADMIN_TOKEN" != "null" ] && [ -n "$ADMIN_TOKEN" ]; then
  echo "✅ Admin login successful, token received"
  
  # Test 5: Analytics with admin token
  echo -e "\n🧪 Testing: Get sales analytics"
  curl -s -X GET "$BASE_URL/admin/analytics/sales?from=2024-01-01&to=2024-01-31" \
    -H "Authorization: Bearer $ADMIN_TOKEN" \
    -H "x-tenant-id: $TENANT_ID" \
    -H "Content-Type: application/json" | jq '.' || echo "❌ Failed or no JSON response"
else
  echo "❌ Admin login failed, skipping analytics test"
fi

# Test 6: Check if server is running
echo -e "\n🧪 Testing: Server health check"
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/admin/custom")
if [ "$HTTP_STATUS" = "200" ]; then
  echo "✅ Server is running (HTTP $HTTP_STATUS)"
else
  echo "❌ Server may not be running (HTTP $HTTP_STATUS)"
fi

echo -e "\n============================================================"
echo "🏁 API Endpoint Tests Completed"
echo "💡 Note: Some endpoints may require proper authentication or data setup"