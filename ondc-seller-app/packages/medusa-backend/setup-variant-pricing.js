#!/usr/bin/env node

/**
 * Setup Variant Pricing for Medusa v2
 * This script creates price sets and prices for all variants that don't have them,
 * and sets manage_inventory to false for all variants.
 */

const { Client } = require('pg');
require('dotenv').config();

const client = new Client({
  connectionString:
    process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/medusa_backend',
});

// Default pricing based on product categories and SKU patterns
const getDefaultPrice = (sku, title) => {
  const skuLower = (sku || '').toLowerCase();
  const titleLower = (title || '').toLowerCase();

  // Electronics and phones
  if (
    skuLower.includes('samsung') ||
    skuLower.includes('galaxy') ||
    skuLower.includes('phone') ||
    titleLower.includes('samsung') ||
    titleLower.includes('galaxy') ||
    titleLower.includes('phone')
  ) {
    return 25000; // ₹250
  }

  // Watches
  if (skuLower.includes('watch') || titleLower.includes('watch')) {
    return 2500; // ₹25
  }

  // Food items
  if (
    skuLower.includes('aatta') ||
    skuLower.includes('salt') ||
    skuLower.includes('biscuit') ||
    titleLower.includes('flour') ||
    titleLower.includes('salt') ||
    titleLower.includes('biscuit')
  ) {
    return 150; // ₹1.50
  }

  // Clothing
  if (
    skuLower.includes('shirt') ||
    skuLower.includes('jacket') ||
    skuLower.includes('dress') ||
    titleLower.includes('shirt') ||
    titleLower.includes('jacket') ||
    titleLower.includes('dress')
  ) {
    return 1500; // ₹15
  }

  // Bags
  if (skuLower.includes('bag') || titleLower.includes('bag')) {
    return 800; // ₹8
  }

  // Default price for other items
  return 500; // ₹5
};

async function setupVariantPricing() {
  try {
    await client.connect();
    console.log('🔗 Connected to database');

    console.log('💰 Setting up variant pricing...');

    // Step 1: Update all variants to disable inventory management
    console.log('📦 Disabling inventory management for all variants...');
    const updateResult = await client.query(`
      UPDATE product_variant 
      SET manage_inventory = false 
      WHERE manage_inventory = true
    `);
    console.log(`✅ Updated ${updateResult.rowCount} variants to disable inventory management`);

    // Step 2: Get variants without price sets
    console.log('🔍 Finding variants without prices...');
    const variantsWithoutPrices = await client.query(`
      SELECT pv.id, pv.sku, pv.title
      FROM product_variant pv 
      LEFT JOIN product_variant_price_set pvps ON pv.id = pvps.variant_id
      WHERE pvps.price_set_id IS NULL
      ORDER BY pv.created_at
    `);

    const variants = variantsWithoutPrices.rows;
    console.log(`💸 Found ${variants.length} variants without prices`);

    // Step 3: Create price sets and prices for variants without them
    for (const variant of variants) {
      try {
        const priceSetId = `pset_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const priceId = `price_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const defaultPrice = getDefaultPrice(variant.sku, variant.title);

        // Create price set
        await client.query(
          `
          INSERT INTO price_set (id, created_at, updated_at)
          VALUES ($1, NOW(), NOW())
        `,
          [priceSetId]
        );

        // Create price with raw_amount
        const rawAmount = { value: defaultPrice.toString(), precision: 20 };
        await client.query(
          `
          INSERT INTO price (id, title, price_set_id, currency_code, amount, raw_amount, created_at, updated_at)
          VALUES ($1, $2, $3, 'inr', $4, $5, NOW(), NOW())
        `,
          [priceId, 'Default Price', priceSetId, defaultPrice, JSON.stringify(rawAmount)]
        );

        // Link variant to price set
        await client.query(
          `
          INSERT INTO product_variant_price_set (id, variant_id, price_set_id, created_at, updated_at)
          VALUES ($1, $2, $3, NOW(), NOW())
        `,
          [`pvps_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, variant.id, priceSetId]
        );

        console.log(
          `✅ Added price ₹${(defaultPrice / 100).toFixed(2)} for variant: ${variant.id} (SKU: ${variant.sku || 'N/A'})`
        );

        // Small delay to ensure unique timestamps
        await new Promise(resolve => setTimeout(resolve, 10));
      } catch (error) {
        console.log(`⚠️ Skipping variant ${variant.id}: ${error.message}`);
      }
    }

    // Step 4: Verify pricing setup
    console.log('🔍 Verifying pricing setup...');
    const pricingStats = await client.query(`
      SELECT 
        COUNT(*) as total_variants,
        COUNT(pvps.price_set_id) as variants_with_prices,
        COUNT(*) - COUNT(pvps.price_set_id) as variants_without_prices
      FROM product_variant pv 
      LEFT JOIN product_variant_price_set pvps ON pv.id = pvps.variant_id
    `);

    const stats = pricingStats.rows[0];
    console.log(`📊 Pricing Statistics:`);
    console.log(`   Total variants: ${stats.total_variants}`);
    console.log(`   Variants with prices: ${stats.variants_with_prices}`);
    console.log(`   Variants without prices: ${stats.variants_without_prices}`);

    console.log('🎉 Variant pricing setup completed!');
    console.log('✅ All variants now have manage_inventory = false');
    console.log('✅ All variants now have default prices assigned');
    console.log('🛒 Cart functionality should work seamlessly for all products!');
  } catch (error) {
    console.error('❌ Error setting up variant pricing:', error);
    throw error;
  } finally {
    await client.end();
  }
}

// Run the setup
setupVariantPricing()
  .then(() => {
    console.log('✅ Variant pricing setup completed successfully');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Variant pricing setup failed:', error);
    process.exit(1);
  });
