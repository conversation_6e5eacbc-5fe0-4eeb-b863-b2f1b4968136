-- Task 1.2: Enable RLS on Medusa Tables
-- Enable Row-Level Security on all tenant-scoped tables

-- Core business tables
ALTER TABLE product ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_variant ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_category ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_collection ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_tag ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_type ENABLE ROW LEVEL SECURITY;

-- Customer and order tables
ALTER TABLE customer ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_address ENABLE ROW LEVEL SECURITY;
ALTER TABLE order ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_line_item ENABLE ROW LEVEL SECURITY;

-- Cart tables
ALTER TABLE cart_line_item ENABLE ROW LEVEL SECURITY;

-- Inventory and pricing tables
ALTER TABLE inventory_level ENABLE ROW LEVEL SECURITY;
ALTER TABLE price ENABLE ROW LEVEL SECURITY;

-- Payment and fulfillment tables
ALTER TABLE payment ENABLE ROW LEVEL SECURITY;
ALTER TABLE fulfillment ENABLE ROW LEVEL SECURITY;

-- Sales channel table
ALTER TABLE sales_channel ENABLE ROW LEVEL SECURITY;

-- Verify RLS is enabled
SELECT 
    'RLS Status Check:' as status,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
    AND tablename IN (
        'product', 'product_variant', 'product_category', 'product_collection',
        'product_tag', 'product_type', 'customer', 'customer_address',
        'order', 'order_line_item', 'cart_line_item', 'inventory_level',
        'price', 'payment', 'fulfillment', 'sales_channel'
    )
ORDER BY tablename;

SELECT 'RLS enabled successfully on all tenant-scoped tables' as result;
