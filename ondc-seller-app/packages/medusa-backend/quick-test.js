#!/usr/bin/env node

const axios = require('axios');

const BASE_URL = 'http://localhost:9000';
const ADMIN_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY3Rvcl9pZCI6InVzZXJfMDFKWjRWVkdFSlg2S1RRM1JaTUI3OFk1TTEiLCJhY3Rvcl90eXBlIjoidXNlciIsImF1dGhfaWRlbnRpdHlfaWQiOiJhdXRoaWRfMDFKWjRWVkdKSlRLVzNSWUdNTUUwRVg0NDMiLCJhcHBfbWV0YWRhdGEiOnsidXNlcl9pZCI6InVzZXJfMDFKWjRWVkdFSlg2S1RRM1JaTUI3OFk1TTEifSwiaWF0IjoxNzU0NDYzNTcxLCJleHAiOjE3NTQ1NDk5NzF9.8QuHH1MajzPppiHgz0yHRamCtzsgC0GIqc9sWcmlryo';

async function quickTest() {
  const endpoints = [
    '/admin/products',
    '/admin/customers', 
    '/admin/orders',
    '/admin/product-categories',
    '/admin/inventory',
    '/admin/collections'
  ];

  const tenants = ['default', 'tenant-electronics-001', 'tenant-fashion-002'];

  console.log('🚀 Quick Tenant Filtering Test\n');

  for (const tenant of tenants) {
    console.log(`🏢 Testing tenant: ${tenant}`);
    console.log('-'.repeat(40));

    for (const endpoint of endpoints) {
      try {
        const response = await axios.get(`${BASE_URL}${endpoint}?limit=5`, {
          headers: {
            'Authorization': `Bearer ${ADMIN_TOKEN}`,
            'Content-Type': 'application/json',
            'x-tenant-id': tenant
          }
        });

        const resourceKey = Object.keys(response.data).find(key => 
          Array.isArray(response.data[key]) && key !== '_tenant'
        );
        
        const count = response.data[resourceKey]?.length || 0;
        const totalInDb = response.data._tenant?.total_in_db || 0;
        
        console.log(`  ${endpoint}: ${count} items (${totalInDb} total) ✅`);
      } catch (error) {
        console.log(`  ${endpoint}: Error - ${error.response?.status || 'Network'} ❌`);
      }
    }
    console.log('');
  }
}

quickTest().catch(console.error);