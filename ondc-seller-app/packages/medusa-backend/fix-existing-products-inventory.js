#!/usr/bin/env node

/**
 * Fix Existing Products Inventory Script
 * 
 * This script fixes inventory setup for existing products to ensure they can be added to cart.
 * It applies the same auto-configuration that new products will receive automatically.
 */

const { Client } = require('pg');
require('dotenv').config();

const DEFAULT_SALES_CHANNEL_ID = process.env.DEFAULT_SALES_CHANNEL_ID || 'sc_01K33ENQEWWEMQNT0WDXAHMCWD';
const DEFAULT_STOCK_LOCATION_ID = process.env.DEFAULT_STOCK_LOCATION_ID || 'sloc_01K33E8B4MEHSRZBWT2RM11N6A';

async function fixExistingProductsInventory() {
  console.log('🔧 Starting inventory fix for existing products...');
  console.log(`📺 Sales Channel: ${DEFAULT_SALES_CHANNEL_ID}`);
  console.log(`📦 Stock Location: ${DEFAULT_STOCK_LOCATION_ID}`);

  const client = new Client({
    connectionString: process.env.DATABASE_URL
  });

  try {
    await client.connect();
    console.log('✅ Database connected');

    // Step 1: Get all product variants that need fixing
    console.log('\n📋 Step 1: Finding variants that need inventory setup...');
    const variantsQuery = `
      SELECT pv.id, pv.sku, pv.product_id, pv.manage_inventory, p.title as product_title
      FROM product_variant pv
      JOIN product p ON pv.product_id = p.id
      WHERE pv.deleted_at IS NULL AND p.deleted_at IS NULL
      ORDER BY pv.created_at DESC;
    `;
    
    const variantsResult = await client.query(variantsQuery);
    console.log(`Found ${variantsResult.rows.length} variants to process`);

    let fixedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    // Step 2: Process each variant
    for (const variant of variantsResult.rows) {
      try {
        console.log(`\n🔍 Processing variant: ${variant.id} (${variant.sku})`);

        // Step 2a: Set manage_inventory to false
        if (variant.manage_inventory !== false) {
          await client.query('UPDATE product_variant SET manage_inventory = false WHERE id = $1;', [variant.id]);
          console.log(`  ✅ Set manage_inventory = false`);
        } else {
          console.log(`  ✓ manage_inventory already false`);
        }

        // Step 2b: Create inventory item if it doesn't exist
        const inventoryItemId = `inv_${variant.id.slice(8)}`;
        const existingItemResult = await client.query('SELECT id FROM inventory_item WHERE id = $1;', [inventoryItemId]);
        
        if (existingItemResult.rows.length === 0) {
          await client.query(`
            INSERT INTO inventory_item (id, sku, created_at, updated_at)
            VALUES ($1, $2, NOW(), NOW());
          `, [inventoryItemId, variant.sku || variant.id]);
          console.log(`  ✅ Created inventory item: ${inventoryItemId}`);
        } else {
          console.log(`  ✓ Inventory item already exists`);
        }

        // Step 2c: Link inventory item to variant if not linked
        const existingLinkResult = await client.query('SELECT id FROM product_variant_inventory_item WHERE variant_id = $1 AND inventory_item_id = $2;', [variant.id, inventoryItemId]);
        
        if (existingLinkResult.rows.length === 0) {
          const linkId = `pvii_${variant.id.slice(8)}_${inventoryItemId.slice(4)}`;
          await client.query(`
            INSERT INTO product_variant_inventory_item (id, variant_id, inventory_item_id, required_quantity, created_at, updated_at)
            VALUES ($1, $2, $3, 1, NOW(), NOW());
          `, [linkId, variant.id, inventoryItemId]);
          console.log(`  ✅ Linked variant to inventory item`);
        } else {
          console.log(`  ✓ Variant already linked to inventory item`);
        }

        // Step 2d: Create inventory level for the correct stock location
        const existingLevelResult = await client.query('SELECT id FROM inventory_level WHERE inventory_item_id = $1 AND location_id = $2;', [inventoryItemId, DEFAULT_STOCK_LOCATION_ID]);
        
        if (existingLevelResult.rows.length === 0) {
          const levelId = `invlvl_${inventoryItemId.slice(4)}_${DEFAULT_STOCK_LOCATION_ID.slice(5)}`;
          await client.query(`
            INSERT INTO inventory_level (id, inventory_item_id, location_id, stocked_quantity, reserved_quantity, incoming_quantity, created_at, updated_at)
            VALUES ($1, $2, $3, 1000, 0, 0, NOW(), NOW());
          `, [levelId, inventoryItemId, DEFAULT_STOCK_LOCATION_ID]);
          console.log(`  ✅ Created inventory level with 1000 stock`);
        } else {
          // Update existing level to ensure sufficient stock
          await client.query('UPDATE inventory_level SET stocked_quantity = 1000, reserved_quantity = 0 WHERE inventory_item_id = $1 AND location_id = $2;', [inventoryItemId, DEFAULT_STOCK_LOCATION_ID]);
          console.log(`  ✅ Updated inventory level to 1000 stock`);
        }

        fixedCount++;
        console.log(`  ✅ Variant ${variant.id} processed successfully`);

      } catch (error) {
        console.error(`  ❌ Error processing variant ${variant.id}:`, error.message);
        errorCount++;
      }
    }

    // Step 3: Verify sales channel stock location association
    console.log('\n📺 Step 3: Verifying sales channel stock location association...');
    const scslResult = await client.query('SELECT * FROM sales_channel_stock_location WHERE sales_channel_id = $1 AND stock_location_id = $2 AND deleted_at IS NULL;', [DEFAULT_SALES_CHANNEL_ID, DEFAULT_STOCK_LOCATION_ID]);
    
    if (scslResult.rows.length === 0) {
      console.log('⚠️  Sales channel not associated with stock location, creating association...');
      const scslId = `scsl_fix_${Date.now()}`;
      await client.query(`
        INSERT INTO sales_channel_stock_location (id, sales_channel_id, stock_location_id, created_at, updated_at)
        VALUES ($1, $2, $3, NOW(), NOW());
      `, [scslId, DEFAULT_SALES_CHANNEL_ID, DEFAULT_STOCK_LOCATION_ID]);
      console.log('✅ Sales channel stock location association created');
    } else {
      console.log('✅ Sales channel stock location association already exists');
    }

    await client.end();

    // Summary
    console.log('\n🎉 Inventory fix completed!');
    console.log(`📊 Summary:`);
    console.log(`  - Variants processed: ${variantsResult.rows.length}`);
    console.log(`  - Successfully fixed: ${fixedCount}`);
    console.log(`  - Skipped: ${skippedCount}`);
    console.log(`  - Errors: ${errorCount}`);
    console.log(`\n✅ All existing products should now be able to be added to cart!`);

  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  fixExistingProductsInventory()
    .then(() => {
      console.log('\n✅ Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Script failed:', error);
      process.exit(1);
    });
}

module.exports = { fixExistingProductsInventory };
