# Product Auto-Configuration Implementation

This document describes the implementation of automatic sales channel assignment and inventory management configuration for products in the Medusa v2 backend.

## Overview

The system automatically applies the following configurations to all products created through any method:

1. **Sales Channel Assignment**: Assigns products to the default sales channel (`sc_01K33ENQEWWEMQNT0WDXAHMCWD`)
2. **Inventory Management**: Sets `manage_inventory: false` for all product variants

## Implementation Components

### 1. Environment Configuration

The default sales channel ID is configured in `.env`:

```env
# Sales Channel Configuration
DEFAULT_SALES_CHANNEL_ID=sc_01K33ENQEWWEMQNT0WDXAHMCWD
```

### 2. ProductAutoConfigService

**File**: `src/services/product-auto-config.ts`

A dedicated service that handles:
- Sales channel assignment
- Inventory management configuration
- Bulk operations for Excel imports
- Validation of sales channel existence

**Key Methods**:
- `applyAutoConfiguration(productId, tenantId)` - Apply config to single product
- `applyAutoConfigurationBulk(productIds, tenantId)` - Apply config to multiple products
- `validateDefaultSalesChannel()` - Check if default sales channel exists

### 3. Workflows

**File**: `src/workflows/product-auto-config.ts`

Three workflows for different scenarios:

#### createProductsWithAutoConfigWorkflow
- Creates products using standard Medusa workflow
- Automatically applies configuration
- Used for API product creation

#### importProductsWithAutoConfigWorkflow
- Specialized for Excel import operations
- Handles bulk product creation with auto-config

#### applyAutoConfigToExistingProductsWorkflow
- Applies configuration to existing products
- Used for retroactive configuration

### 4. API Integration

#### Tenant Products API
**File**: `src/api/admin/tenant-products/route.ts`

- POST endpoint now uses `createProductsWithAutoConfigWorkflow`
- Returns auto-configuration results in response
- Includes sales channel assignment status

#### Main Products API
**File**: `src/api/admin/products/route.ts`

- POST endpoint applies auto-configuration after product creation
- Direct database operations include `manage_inventory: false` for variants
- Response includes auto-configuration status

#### Excel Import API
**File**: `src/api/admin/product-import/route.ts`

- Applies auto-configuration to all imported products
- Sets `manage_inventory: false` during variant creation
- Returns auto-configuration results in import summary

## Usage Examples

### 1. Creating a Product via API

```bash
curl -X POST "http://localhost:9000/admin/tenant-products" \
  -H "Content-Type: application/json" \
  -H "x-tenant-id: tenant-electronics-001" \
  -d '{
    "title": "Test Product",
    "description": "A test product",
    "variants": [
      {
        "title": "Default Variant",
        "sku": "TEST-001",
        "prices": [{"currency_code": "inr", "amount": 1000}]
      }
    ]
  }'
```

**Response includes**:
```json
{
  "success": true,
  "data": {
    "product": {...},
    "autoConfiguration": {
      "salesChannelAssigned": true,
      "inventoryManagementDisabled": true,
      "salesChannelId": "sc_01K33ENQEWWEMQNT0WDXAHMCWD",
      "results": {
        "successful": 1,
        "failed": 0,
        "errors": []
      }
    }
  }
}
```

### 2. Excel Import

Products imported via Excel automatically receive:
- Sales channel assignment to `DEFAULT_SALES_CHANNEL_ID`
- `manage_inventory: false` for all variants

Import response includes auto-configuration results:
```json
{
  "message": "Product import completed",
  "successful_imports": 5,
  "created_products": ["prod_123", "prod_124", ...],
  "auto_configuration": {
    "successful": 5,
    "failed": 0,
    "errors": []
  }
}
```

## Testing

### Manual Testing

Run the test script to verify implementation:

```bash
cd ondc-seller-app/packages/backend-new/medusa-backend
npx ts-node src/scripts/test-product-auto-config.ts
```

### API Testing

1. **Create Product via Tenant API**:
   ```bash
   POST /admin/tenant-products
   ```

2. **Create Product via Main API**:
   ```bash
   POST /admin/products
   ```

3. **Import Products via Excel**:
   ```bash
   POST /admin/product-import
   ```

### Verification Steps

For each created product, verify:

1. **Sales Channel Assignment**:
   ```sql
   SELECT * FROM product_sales_channel 
   WHERE product_id = 'your_product_id' 
   AND sales_channel_id = 'sc_01K33ENQEWWEMQNT0WDXAHMCWD';
   ```

2. **Inventory Management**:
   ```sql
   SELECT id, manage_inventory FROM product_variant 
   WHERE product_id = 'your_product_id';
   ```

## Multi-Tenant Compatibility

The implementation is fully compatible with the existing multi-tenant architecture:

- Tenant ID is passed through all workflows
- Auto-configuration respects tenant isolation
- Works with all tenant-aware services

## Error Handling

The system includes comprehensive error handling:

- Failed auto-configuration doesn't prevent product creation
- Detailed error reporting in API responses
- Graceful degradation if sales channel doesn't exist
- Logging for debugging and monitoring

## Configuration

### Environment Variables

```env
# Required: Default sales channel ID
DEFAULT_SALES_CHANNEL_ID=sc_01K33ENQEWWEMQNT0WDXAHMCWD

# Optional: Enable/disable auto-configuration (future enhancement)
ENABLE_PRODUCT_AUTO_CONFIG=true
```

### Service Registration

The `ProductAutoConfigService` is automatically registered with Medusa's dependency injection container and available as `productAutoConfigService`.

## Future Enhancements

1. **Configurable Sales Channels**: Support different sales channels per tenant
2. **Conditional Auto-Config**: Enable/disable based on product type or category
3. **Batch Processing**: Optimize bulk operations for large imports
4. **Audit Trail**: Track all auto-configuration changes
5. **Admin UI**: Interface to manage auto-configuration settings
