/**
 * Test RLS Session Variables
 * This script tests session variable persistence in PostgreSQL
 */

const { Client } = require('pg');

async function testRLSSession() {
  console.log('🧪 Testing RLS Session Variables...\n');

  const client = new Client({
    connectionString: 'postgresql://medusa_app:medusa_app_password@localhost:5432/medusa_backend'
  });

  try {
    await client.connect();
    console.log('✅ Connected to database');

    // Test session variable persistence in single connection
    console.log('\n📋 Testing session variable persistence:');
    
    // Set tenant context
    await client.query('SELECT set_tenant_context($1)', ['default']);
    console.log('   ✓ Set tenant context to "default"');
    
    // Check context immediately
    const context1 = await client.query('SELECT get_current_tenant_context() as tenant');
    console.log(`   ✓ Current context: "${context1.rows[0].tenant}"`);
    
    // Query products in same connection
    const products1 = await client.query('SELECT COUNT(*) as count FROM product');
    console.log(`   ✓ Products count: ${products1.rows[0].count}`);
    
    // Check context again
    const context2 = await client.query('SELECT get_current_tenant_context() as tenant');
    console.log(`   ✓ Context after query: "${context2.rows[0].tenant}"`);

    // Test with different tenant
    console.log('\n📋 Testing tenant switch:');
    await client.query('SELECT set_tenant_context($1)', ['tenant-electronics-001']);
    console.log('   ✓ Set tenant context to "tenant-electronics-001"');
    
    const context3 = await client.query('SELECT get_current_tenant_context() as tenant');
    console.log(`   ✓ Current context: "${context3.rows[0].tenant}"`);
    
    const products2 = await client.query('SELECT COUNT(*) as count FROM product');
    console.log(`   ✓ Products count: ${products2.rows[0].count}`);

    // Test the validate_tenant_access function directly
    console.log('\n📋 Testing validate_tenant_access function:');
    const validation1 = await client.query('SELECT validate_tenant_access($1) as valid', ['tenant-electronics-001']);
    console.log(`   ✓ Validate same tenant: ${validation1.rows[0].valid}`);
    
    const validation2 = await client.query('SELECT validate_tenant_access($1) as valid', ['different-tenant']);
    console.log(`   ✓ Validate different tenant: ${validation2.rows[0].valid}`);

    // Test manual query with WHERE clause
    console.log('\n📋 Testing manual tenant filtering:');
    const manualQuery = await client.query(`
      SELECT COUNT(*) as count 
      FROM product 
      WHERE tenant_id = $1
    `, ['tenant-electronics-001']);
    console.log(`   ✓ Manual filter for electronics: ${manualQuery.rows[0].count}`);

    const manualQuery2 = await client.query(`
      SELECT COUNT(*) as count 
      FROM product 
      WHERE tenant_id = $1
    `, ['default']);
    console.log(`   ✓ Manual filter for default: ${manualQuery2.rows[0].count}`);

    console.log('\n🎉 RLS Session Test Completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    await client.end();
  }
}

testRLSSession().catch(console.error);
