#!/usr/bin/env node

const { Client } = require('pg');

async function debugTables() {
  const client = new Client({
    connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
  });

  try {
    await client.connect();
    console.log('🔍 Connected to database, checking table structure...\n');

    // Check what tables exist
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name LIKE '%product%' 
      OR table_name LIKE '%variant%' 
      OR table_name LIKE '%image%' 
      OR table_name LIKE '%option%'
      ORDER BY table_name
    `);
    
    console.log('📋 Product-related tables:');
    tablesResult.rows.forEach(row => {
      console.log(`  - ${row.table_name}`);
    });

    // Check specific product for debugging
    const productId = 'prod_1754468789812_0qsgtyy5i'; // kirana product 3
    
    console.log(`\n🔍 Debugging product: ${productId}`);
    console.log('='.repeat(50));

    // Check if product exists
    const productResult = await client.query(`
      SELECT id, title, tenant_id 
      FROM product 
      WHERE id = $1
    `, [productId]);
    
    if (productResult.rows.length > 0) {
      console.log('✅ Product found:', productResult.rows[0]);
    } else {
      console.log('❌ Product not found');
      return;
    }

    // Check for variants in different possible table names
    const variantTables = ['product_variant', 'variant', 'product_variants'];
    for (const tableName of variantTables) {
      try {
        const variantResult = await client.query(`
          SELECT COUNT(*) as count 
          FROM ${tableName} 
          WHERE product_id = $1
        `, [productId]);
        console.log(`📦 Variants in ${tableName}: ${variantResult.rows[0].count}`);
      } catch (e) {
        console.log(`❌ Table ${tableName} does not exist or error: ${e.message}`);
      }
    }

    // Check for images in different possible table names
    const imageTables = ['image', 'product_image', 'images'];
    for (const tableName of imageTables) {
      try {
        const imageResult = await client.query(`
          SELECT COUNT(*) as count 
          FROM ${tableName} 
          WHERE product_id = $1
        `, [productId]);
        console.log(`🖼️  Images in ${tableName}: ${imageResult.rows[0].count}`);
      } catch (e) {
        console.log(`❌ Table ${tableName} does not exist or error: ${e.message}`);
      }
    }

    // Check for options in different possible table names
    const optionTables = ['product_option', 'option', 'product_options'];
    for (const tableName of optionTables) {
      try {
        const optionResult = await client.query(`
          SELECT COUNT(*) as count 
          FROM ${tableName} 
          WHERE product_id = $1
        `, [productId]);
        console.log(`⚙️  Options in ${tableName}: ${optionResult.rows[0].count}`);
      } catch (e) {
        console.log(`❌ Table ${tableName} does not exist or error: ${e.message}`);
      }
    }

    // Check what columns exist in the product table
    console.log('\n📋 Product table columns:');
    const columnsResult = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'product' 
      ORDER BY ordinal_position
    `);
    
    columnsResult.rows.forEach(row => {
      console.log(`  - ${row.column_name}: ${row.data_type}`);
    });

  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await client.end();
  }
}

debugTables().catch(console.error);