-- Task 1.3: Create RLS Policies for Medusa Tables
-- Create comprehensive RLS policies using current_setting('app.current_tenant_id')

-- ============================================================================
-- PRODUCT TABLES POLICIES
-- ============================================================================

-- Product table policies
CREATE POLICY tenant_isolation_policy ON product
    FOR ALL
    TO PUBLIC
    USING (validate_tenant_access(tenant_id));

-- Product variant policies
CREATE POLICY tenant_isolation_policy ON product_variant
    FOR ALL
    TO PUBLIC
    USING (validate_tenant_access(tenant_id));

-- Product category policies
CREATE POLICY tenant_isolation_policy ON product_category
    FOR ALL
    TO PUBLIC
    USING (validate_tenant_access(tenant_id));

-- Product collection policies
CREATE POLICY tenant_isolation_policy ON product_collection
    FOR ALL
    TO PUBLIC
    USING (validate_tenant_access(tenant_id));

-- Product tag policies
CREATE POLICY tenant_isolation_policy ON product_tag
    FOR ALL
    TO PUBLIC
    USING (validate_tenant_access(tenant_id));

-- Product type policies
CREATE POLICY tenant_isolation_policy ON product_type
    FOR ALL
    TO PUBLIC
    USING (validate_tenant_access(tenant_id));

-- ============================================================================
-- CUSTOMER TABLES POLICIES
-- ============================================================================

-- Customer policies
CREATE POLICY tenant_isolation_policy ON customer
    FOR ALL
    TO PUBLIC
    USING (validate_tenant_access(tenant_id));

-- Customer address policies
CREATE POLICY tenant_isolation_policy ON customer_address
    FOR ALL
    TO PUBLIC
    USING (validate_tenant_access(tenant_id));

-- ============================================================================
-- ORDER TABLES POLICIES
-- ============================================================================

-- Order policies (using quoted table name due to reserved keyword)
CREATE POLICY tenant_isolation_policy ON "order"
    FOR ALL
    TO PUBLIC
    USING (validate_tenant_access(tenant_id));

-- Order line item policies
CREATE POLICY tenant_isolation_policy ON order_line_item
    FOR ALL
    TO PUBLIC
    USING (validate_tenant_access(tenant_id));

-- ============================================================================
-- CART TABLES POLICIES
-- ============================================================================

-- Cart line item policies
CREATE POLICY tenant_isolation_policy ON cart_line_item
    FOR ALL
    TO PUBLIC
    USING (validate_tenant_access(tenant_id));

-- ============================================================================
-- INVENTORY AND PRICING POLICIES
-- ============================================================================

-- Inventory level policies
CREATE POLICY tenant_isolation_policy ON inventory_level
    FOR ALL
    TO PUBLIC
    USING (validate_tenant_access(tenant_id));

-- Price policies
CREATE POLICY tenant_isolation_policy ON price
    FOR ALL
    TO PUBLIC
    USING (validate_tenant_access(tenant_id));

-- ============================================================================
-- PAYMENT AND FULFILLMENT POLICIES
-- ============================================================================

-- Payment policies
CREATE POLICY tenant_isolation_policy ON payment
    FOR ALL
    TO PUBLIC
    USING (validate_tenant_access(tenant_id));

-- Fulfillment policies
CREATE POLICY tenant_isolation_policy ON fulfillment
    FOR ALL
    TO PUBLIC
    USING (validate_tenant_access(tenant_id));

-- ============================================================================
-- SALES CHANNEL POLICIES
-- ============================================================================

-- Sales channel policies
CREATE POLICY tenant_isolation_policy ON sales_channel
    FOR ALL
    TO PUBLIC
    USING (validate_tenant_access(tenant_id));

-- ============================================================================
-- VERIFICATION
-- ============================================================================

-- List all created policies
SELECT 
    'RLS Policies Created:' as status,
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE schemaname = 'public'
    AND policyname = 'tenant_isolation_policy'
ORDER BY tablename;

SELECT 'RLS Policies created successfully for all tenant-scoped tables' as result;
