#!/usr/bin/env node

/**
 * Simple Connection Pool Test
 * 
 * Tests the API endpoints with multiple consecutive calls to verify
 * that the connection pool exhaustion issue has been resolved.
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:9001';
const TENANT_ID = 'tenant-electronics-001';

// Test configuration
const TEST_CONFIG = {
  consecutiveCalls: 25, // Number of consecutive calls to make
  delayBetweenCalls: 200, // Delay between calls in ms
};

// Simple endpoints to test
const ENDPOINTS = [
  { name: 'Store Products', url: '/store/products', method: 'GET' },
  { name: 'Admin Products', url: '/admin/products', method: 'GET' },
  { name: 'Admin Customers', url: '/admin/customers', method: 'GET' },
];

// Test results tracking
let totalRequests = 0;
let successfulRequests = 0;
let failedRequests = 0;
let errors = [];

/**
 * Make a single API request
 */
async function makeRequest(endpoint, requestNumber) {
  const startTime = Date.now();
  
  try {
    const response = await axios({
      method: endpoint.method,
      url: `${BASE_URL}${endpoint.url}`,
      headers: {
        'x-tenant-id': TENANT_ID,
        'Content-Type': 'application/json',
      },
      timeout: 30000, // 30 second timeout
    });

    const responseTime = Date.now() - startTime;
    successfulRequests++;

    console.log(`✅ [${requestNumber}] ${endpoint.name}: ${response.status} (${responseTime}ms)`);
    
    return {
      success: true,
      status: response.status,
      responseTime,
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    failedRequests++;
    
    const errorInfo = {
      endpoint: endpoint.name,
      requestNumber,
      error: error.message,
      status: error.response?.status,
      responseTime,
    };
    
    errors.push(errorInfo);
    
    console.error(`❌ [${requestNumber}] ${endpoint.name}: ${error.message} (${responseTime}ms)`);
    
    return {
      success: false,
      error: error.message,
      status: error.response?.status,
      responseTime,
    };
  }
}

/**
 * Test consecutive calls to a single endpoint
 */
async function testConsecutiveCalls(endpoint) {
  console.log(`\n🚀 Testing ${TEST_CONFIG.consecutiveCalls} consecutive calls to ${endpoint.name}`);
  console.log('='.repeat(60));
  
  for (let i = 1; i <= TEST_CONFIG.consecutiveCalls; i++) {
    totalRequests++;
    await makeRequest(endpoint, i);
    
    // Small delay between requests
    if (TEST_CONFIG.delayBetweenCalls > 0) {
      await new Promise(resolve => setTimeout(resolve, TEST_CONFIG.delayBetweenCalls));
    }
  }
}

/**
 * Display test results
 */
function displayTestResults() {
  console.log('\n📊 TEST RESULTS SUMMARY');
  console.log('='.repeat(60));
  console.log(`Total Requests: ${totalRequests}`);
  console.log(`Successful Requests: ${successfulRequests}`);
  console.log(`Failed Requests: ${failedRequests}`);
  console.log(`Success Rate: ${((successfulRequests / totalRequests) * 100).toFixed(2)}%`);
  
  if (errors.length > 0) {
    console.log('\n❌ ERRORS ENCOUNTERED:');
    console.log('='.repeat(60));
    errors.forEach((error, index) => {
      console.log(`${index + 1}. [${error.requestNumber}] ${error.endpoint}: ${error.error}`);
    });
  }
  
  // Determine if the test passed
  const successRate = (successfulRequests / totalRequests) * 100;
  const testPassed = successRate >= 95; // 95% success rate required
  
  console.log('\n🎯 TEST VERDICT');
  console.log('='.repeat(60));
  if (testPassed) {
    console.log('✅ CONNECTION POOL FIX SUCCESSFUL!');
    console.log('   All endpoints are handling high-frequency requests properly.');
  } else {
    console.log('❌ CONNECTION POOL ISSUES STILL EXIST!');
    console.log('   Some endpoints are still failing under load.');
  }
  
  return testPassed;
}

/**
 * Main test execution
 */
async function runTests() {
  console.log('🧪 SIMPLE CONNECTION POOL TEST');
  console.log('='.repeat(60));
  console.log(`Base URL: ${BASE_URL}`);
  console.log(`Tenant ID: ${TENANT_ID}`);
  console.log(`Consecutive Calls: ${TEST_CONFIG.consecutiveCalls}`);
  console.log('='.repeat(60));
  
  try {
    // Test each endpoint with consecutive calls
    for (const endpoint of ENDPOINTS) {
      await testConsecutiveCalls(endpoint);
    }
    
    // Display results
    const testPassed = displayTestResults();
    
    process.exit(testPassed ? 0 : 1);
    
  } catch (error) {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n⚠️ Test interrupted by user');
  displayTestResults();
  process.exit(1);
});

// Run the tests
if (require.main === module) {
  runTests();
}
