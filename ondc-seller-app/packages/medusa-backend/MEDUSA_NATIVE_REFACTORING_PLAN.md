# Medusa Native Multi-Tenancy Refactoring Plan

## Overview

This document outlines the complete refactoring plan to replace custom multi-tenancy implementations with Medusa's native Sales Channels and Publishable API Keys approach.

## Phase 1: Sales Channel Setup (Week 1)

### 1.1 Create Tenant Sales Channels

Replace custom tenant configurations with Medusa's native Sales Channels:

```typescript
// src/scripts/setup-tenant-sales-channels.ts
import { createSalesChannelsWorkflow } from '@medusajs/core-flows';
import { createApiKeysWorkflow, linkSalesChannelsToApiKeyWorkflow } from '@medusajs/core-flows';

export async function setupTenantSalesChannels(container: any) {
  const tenantConfigs = [
    {
      id: 'tenant-electronics-001',
      name: 'Electronics Store',
      description: 'Electronics and gadgets marketplace',
      metadata: {
        ondcConfig: {
          participantId: 'electronics-participant-001',
          subscriberId: 'electronics-subscriber-001',
          bppId: 'ondc-bpp-electronics-001',
        },
        currency: 'INR',
        timezone: 'Asia/Kolkata',
        domain: 'electronics.ondc-seller.com',
      },
    },
    {
      id: 'tenant-fashion-002',
      name: 'Fashion Store',
      description: 'Fashion and clothing marketplace',
      metadata: {
        ondcConfig: {
          participantId: 'fashion-participant-002',
          subscriberId: 'fashion-subscriber-002',
          bppId: 'ondc-bpp-fashion-002',
        },
        currency: 'INR',
        timezone: 'Asia/Kolkata',
        domain: 'fashion.ondc-seller.com',
      },
    },
  ];

  // Create sales channels for each tenant
  const { result: salesChannels } = await createSalesChannelsWorkflow(container).run({
    input: {
      salesChannelsData: tenantConfigs.map(config => ({
        name: config.name,
        description: config.description,
        metadata: config.metadata,
      })),
    },
  });

  // Create publishable API keys for each sales channel
  for (let i = 0; i < salesChannels.length; i++) {
    const salesChannel = salesChannels[i];
    const config = tenantConfigs[i];

    const { result: apiKeys } = await createApiKeysWorkflow(container).run({
      input: {
        api_keys: [
          {
            title: `${config.name} API Key`,
            type: 'publishable',
            created_by: 'system',
          },
        ],
      },
    });

    // Link API key to sales channel
    await linkSalesChannelsToApiKeyWorkflow(container).run({
      input: {
        id: apiKeys[0].id,
        add: [salesChannel.id],
      },
    });

    console.log(`Created tenant: ${config.id}`);
    console.log(`Sales Channel ID: ${salesChannel.id}`);
    console.log(`API Key: ${apiKeys[0].token}`);
  }
}
```

### 1.2 Update Medusa Configuration

```typescript
// medusa-config.ts - Remove custom tenant middleware
export default defineConfig({
  projectConfig: {
    databaseUrl: process.env.DATABASE_URL,
    http: {
      storeCors: process.env.STORE_CORS!,
      adminCors: process.env.ADMIN_CORS!,
      authCors: process.env.AUTH_CORS!,
      // Remove custom tenant headers - use standard Medusa headers
      cors: {
        origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002'],
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
        allowedHeaders: [
          'Content-Type',
          'Authorization',
          'x-medusa-access-token',
          'x-publishable-api-key', // Standard Medusa header
          'Accept',
          'Origin',
        ],
        credentials: true,
      },
    },
  },
  modules: {
    // Use standard Medusa modules
    cart: { resolve: '@medusajs/cart' },
    order: { resolve: '@medusajs/order' },
    customer: { resolve: '@medusajs/customer' },
    payment: { resolve: '@medusajs/payment' },
    inventory: { resolve: '@medusajs/inventory' },
    stockLocation: { resolve: '@medusajs/stock-location' },
    salesChannel: { resolve: '@medusajs/sales-channel' },
  },
});
```

## Phase 2: Remove Custom API Endpoints (Week 1-2)

### 2.1 Remove Custom Admin Endpoints

**Files to Delete:**

- `src/api/admin/tenant/route.ts`
- `src/api/admin/test-multi-tenant/route.ts`
- `src/api/admin/products/route.ts` (custom implementation)
- `src/api/admin/customers/route.ts` (custom implementation)
- `src/api/admin/orders/route.ts` (custom implementation)

**Replace with Standard Medusa Admin APIs:**

```typescript
// Use Medusa's built-in admin endpoints:
// GET /admin/sales-channels - List all tenant sales channels
// GET /admin/sales-channels/{id} - Get specific tenant configuration
// PUT /admin/sales-channels/{id} - Update tenant configuration
// GET /admin/products?sales_channel_id={id} - Get tenant products
// GET /admin/customers?sales_channel_id={id} - Get tenant customers
// GET /admin/orders?sales_channel_id={id} - Get tenant orders
```

### 2.2 Remove Custom Store Endpoints

**Files to Delete:**

- `src/api/store/test-info/route.ts`
- `src/api/store/test-products/route.ts`
- `src/api/store/products/route.ts` (custom implementation)
- `src/api/store/orders/create/route.ts` (custom implementation)

**Replace with Standard Medusa Store APIs:**

```typescript
// Use Medusa's built-in store endpoints:
// GET /store/products - Automatically filtered by publishable API key
// GET /store/products/{id} - Get specific product
// POST /store/carts - Create cart (automatically scoped to sales channel)
// POST /store/carts/{id}/line-items - Add items to cart
// POST /store/orders - Create order from cart
```

### 2.3 Remove Custom Middleware

**Files to Delete:**

- `src/middleware/tenant.ts`
- `src/middleware/tenant-query-filter.ts`
- `src/api/middlewares.ts`

**Replace with Medusa's Native Context:**
Medusa automatically provides sales channel context through publishable API keys.

## Phase 3: Update Frontend Integration (Week 2)

### 3.1 Refactor Frontend API Client

```typescript
// lib/medusa-native-api.ts
export class MedusaNativeAPI {
  private baseURL: string;
  private publishableKey: string;
  private adminToken?: string;

  constructor(tenantId: string) {
    this.baseURL = process.env.NEXT_PUBLIC_MEDUSA_API_URL || 'http://localhost:9000';

    // Map tenant ID to publishable API key
    this.publishableKey = this.getTenantPublishableKey(tenantId);
  }

  private getTenantPublishableKey(tenantId: string): string {
    const keyMap = {
      'tenant-electronics-001': process.env.NEXT_PUBLIC_ELECTRONICS_API_KEY!,
      'tenant-fashion-002': process.env.NEXT_PUBLIC_FASHION_API_KEY!,
      default: process.env.NEXT_PUBLIC_DEFAULT_API_KEY!,
    };

    return keyMap[tenantId] || keyMap['default'];
  }

  // Store API Methods (use standard Medusa endpoints)
  async getProducts(params?: ProductFilters): Promise<ProductsResponse> {
    const searchParams = new URLSearchParams(params as any);

    return await this.request<ProductsResponse>(`/store/products?${searchParams}`, {
      headers: {
        'x-publishable-api-key': this.publishableKey,
      },
    });
  }

  async getProduct(id: string): Promise<ProductResponse> {
    return await this.request<ProductResponse>(`/store/products/${id}`, {
      headers: {
        'x-publishable-api-key': this.publishableKey,
      },
    });
  }

  async createCart(data?: CreateCartData): Promise<CartResponse> {
    return await this.request<CartResponse>('/store/carts', {
      method: 'POST',
      headers: {
        'x-publishable-api-key': this.publishableKey,
      },
      body: JSON.stringify(data || {}),
    });
  }

  async addToCart(cartId: string, item: LineItem): Promise<CartResponse> {
    return await this.request<CartResponse>(`/store/carts/${cartId}/line-items`, {
      method: 'POST',
      headers: {
        'x-publishable-api-key': this.publishableKey,
      },
      body: JSON.stringify(item),
    });
  }

  async createOrder(cartId: string): Promise<OrderResponse> {
    return await this.request<OrderResponse>('/store/orders', {
      method: 'POST',
      headers: {
        'x-publishable-api-key': this.publishableKey,
      },
      body: JSON.stringify({ cart_id: cartId }),
    });
  }

  // Admin API Methods (use standard Medusa endpoints)
  setAdminToken(token: string): void {
    this.adminToken = token;
  }

  async getSalesChannels(): Promise<SalesChannelsResponse> {
    return await this.request<SalesChannelsResponse>('/admin/sales-channels', {
      headers: {
        Authorization: `Bearer ${this.adminToken}`,
      },
    });
  }

  async getSalesChannel(id: string): Promise<SalesChannelResponse> {
    return await this.request<SalesChannelResponse>(`/admin/sales-channels/${id}`, {
      headers: {
        Authorization: `Bearer ${this.adminToken}`,
      },
    });
  }

  async getAdminProducts(salesChannelId?: string): Promise<ProductsResponse> {
    const params = salesChannelId ? `?sales_channel_id=${salesChannelId}` : '';

    return await this.request<ProductsResponse>(`/admin/products${params}`, {
      headers: {
        Authorization: `Bearer ${this.adminToken}`,
      },
    });
  }

  async getAdminCustomers(salesChannelId?: string): Promise<CustomersResponse> {
    const params = salesChannelId ? `?sales_channel_id=${salesChannelId}` : '';

    return await this.request<CustomersResponse>(`/admin/customers${params}`, {
      headers: {
        Authorization: `Bearer ${this.adminToken}`,
      },
    });
  }

  async getAdminOrders(salesChannelId?: string): Promise<OrdersResponse> {
    const params = salesChannelId ? `?sales_channel_id=${salesChannelId}` : '';

    return await this.request<OrdersResponse>(`/admin/orders${params}`, {
      headers: {
        Authorization: `Bearer ${this.adminToken}`,
      },
    });
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}`);
    }

    return await response.json();
  }
}
```

### 3.2 Update Environment Variables

```bash
# .env.local - Replace tenant-specific configurations
NEXT_PUBLIC_MEDUSA_API_URL=http://localhost:9000

# Tenant-specific publishable API keys (generated from Phase 1)
NEXT_PUBLIC_ELECTRONICS_API_KEY=pk_electronics_generated_key
NEXT_PUBLIC_FASHION_API_KEY=pk_fashion_generated_key
NEXT_PUBLIC_DEFAULT_API_KEY=pk_default_generated_key

# Remove custom tenant configurations
# NEXT_PUBLIC_TENANT_ID=tenant-electronics-001 (no longer needed)
```

## Phase 4: Database Migration (Week 2)

### 4.1 Migrate Tenant Data to Sales Channels

```typescript
// src/scripts/migrate-tenant-data.ts
export async function migrateTenantDataToSalesChannels(container: any) {
  const query = container.resolve('query');

  // Get all products with tenant_id
  const { data: products } = await query.graph({
    entity: 'product',
    fields: ['id', 'title', 'metadata'],
    filters: {},
  });

  // Get sales channels
  const { data: salesChannels } = await query.graph({
    entity: 'sales_channel',
    fields: ['id', 'name', 'metadata'],
    filters: {},
  });

  // Create tenant to sales channel mapping
  const tenantToSalesChannel = new Map();
  salesChannels.forEach(channel => {
    if (channel.metadata?.tenantId) {
      tenantToSalesChannel.set(channel.metadata.tenantId, channel.id);
    }
  });

  // Link products to appropriate sales channels
  for (const product of products) {
    const tenantId = product.metadata?.tenant_id;
    if (tenantId && tenantToSalesChannel.has(tenantId)) {
      const salesChannelId = tenantToSalesChannel.get(tenantId);

      // Link product to sales channel
      await linkProductsToSalesChannelWorkflow(container).run({
        input: {
          id: salesChannelId,
          add: [product.id],
        },
      });
    }
  }

  console.log(`Migrated ${products.length} products to sales channels`);
}
```

### 4.2 Remove Tenant Columns (Optional - After Migration Verification)

```sql
-- After verifying sales channel migration works correctly
-- Remove tenant_id columns (optional - can keep for backup)

-- ALTER TABLE product DROP COLUMN IF EXISTS tenant_id;
-- ALTER TABLE customer DROP COLUMN IF EXISTS tenant_id;
-- ALTER TABLE "order" DROP COLUMN IF EXISTS tenant_id;
-- ... (other tables)
```

## Phase 5: ONDC Integration Preservation (Week 3)

### 5.1 Store ONDC Configuration in Sales Channel Metadata

```typescript
// src/services/ondc-config.ts
export class ONDCConfigService {
  private salesChannelService: any;

  constructor(container: any) {
    this.salesChannelService = container.resolve('salesChannelService');
  }

  async getONDCConfig(salesChannelId: string): Promise<ONDCConfig> {
    const salesChannel = await this.salesChannelService.retrieve(salesChannelId);

    return {
      participantId: salesChannel.metadata?.ondcConfig?.participantId,
      subscriberId: salesChannel.metadata?.ondcConfig?.subscriberId,
      bppId: salesChannel.metadata?.ondcConfig?.bppId,
      domain: salesChannel.metadata?.domain,
      currency: salesChannel.metadata?.currency || 'INR',
      timezone: salesChannel.metadata?.timezone || 'Asia/Kolkata',
    };
  }

  async updateONDCConfig(salesChannelId: string, config: Partial<ONDCConfig>): Promise<void> {
    await this.salesChannelService.update(salesChannelId, {
      metadata: {
        ondcConfig: config,
      },
    });
  }
}
```

### 5.2 Create ONDC-Specific Workflows

```typescript
// src/workflows/ondc-order-workflow.ts
import { createWorkflow, WorkflowResponse } from '@medusajs/workflows-sdk';
import { createOrderWorkflow } from '@medusajs/core-flows';

export const ondcOrderWorkflow = createWorkflow(
  'ondc-order-workflow',
  function (input: { cartId: string; salesChannelId: string }) {
    // Get ONDC configuration for the sales channel
    const ondcConfig = getONDCConfigStep(input.salesChannelId);

    // Create standard Medusa order
    const order = createOrderWorkflow.runAsStep({
      input: { cart_id: input.cartId },
    });

    // Add ONDC-specific processing
    const ondcOrder = processONDCOrderStep({
      order,
      ondcConfig,
    });

    return new WorkflowResponse(ondcOrder);
  }
);
```

## Migration Timeline & Benefits

### **Timeline: 3 Weeks Total**

- **Week 1:** Sales channel setup + Remove custom admin endpoints
- **Week 2:** Remove custom store endpoints + Frontend refactoring + Data migration
- **Week 3:** ONDC integration preservation + Testing + Documentation

### **Benefits After Refactoring:**

1. **✅ Reduced Codebase:** Remove ~2000+ lines of custom code
2. **✅ Better Maintainability:** Use Medusa's tested and maintained APIs
3. **✅ Improved Performance:** Leverage Medusa's optimized query patterns
4. **✅ Enhanced Security:** Use Medusa's built-in security mechanisms
5. **✅ Future-Proof:** Automatic compatibility with Medusa updates
6. **✅ Standard Patterns:** Follow Medusa's recommended architecture
7. **✅ Better Documentation:** Use Medusa's comprehensive API documentation

### **Preserved Functionality:**

- ✅ Complete tenant isolation through sales channels
- ✅ ONDC-specific configurations stored in metadata
- ✅ All existing API functionality maintained
- ✅ Frontend integration patterns preserved
- ✅ Database-level data separation maintained

This refactoring transforms the custom multi-tenancy implementation into a standard, maintainable, and scalable solution using Medusa's native capabilities while preserving all existing functionality and ONDC-specific requirements.

## Phase 6: ONDC Workflow Integration (Week 3)

### 6.1 ONDC Configuration Service

```typescript
// src/services/ondc-config.ts
import { AbstractModuleService } from '@medusajs/framework/utils';

export class ONDCConfigService extends AbstractModuleService {
  async getONDCConfig(salesChannelId: string): Promise<ONDCConfig> {
    const salesChannel = await this.salesChannelService_.retrieve(salesChannelId);

    if (!salesChannel.metadata?.ondcConfig) {
      throw new Error(`ONDC configuration not found for sales channel: ${salesChannelId}`);
    }

    return {
      participantId: salesChannel.metadata.ondcConfig.participantId,
      subscriberId: salesChannel.metadata.ondcConfig.subscriberId,
      bppId: salesChannel.metadata.ondcConfig.bppId,
      domain: salesChannel.metadata.ondcConfig.domain,
      region: salesChannel.metadata.ondcConfig.region,
      currency: salesChannel.metadata.currency || 'INR',
      timezone: salesChannel.metadata.timezone || 'Asia/Kolkata',
    };
  }

  async updateONDCConfig(salesChannelId: string, config: Partial<ONDCConfig>): Promise<void> {
    await this.salesChannelService_.update(salesChannelId, {
      metadata: {
        ondcConfig: {
          ...config,
        },
      },
    });
  }

  async validateONDCConfig(config: ONDCConfig): Promise<boolean> {
    // Validate ONDC configuration
    const required = ['participantId', 'subscriberId', 'bppId', 'domain', 'region'];

    for (const field of required) {
      if (!config[field]) {
        throw new Error(`ONDC configuration missing required field: ${field}`);
      }
    }

    return true;
  }
}
```

### 6.2 ONDC Order Workflow

```typescript
// src/workflows/ondc-order-workflow.ts
import { createWorkflow, WorkflowResponse } from '@medusajs/workflows-sdk';
import { createOrderWorkflow } from '@medusajs/core-flows';

export const ondcOrderWorkflow = createWorkflow(
  'ondc-order-workflow',
  function (input: { cartId: string; salesChannelId: string }) {
    // Step 1: Get ONDC configuration for the sales channel
    const ondcConfig = getONDCConfigStep(input.salesChannelId);

    // Step 2: Validate ONDC compliance
    const validationResult = validateONDCComplianceStep({
      cartId: input.cartId,
      ondcConfig,
    });

    // Step 3: Create standard Medusa order
    const order = createOrderWorkflow.runAsStep({
      input: { cart_id: input.cartId },
    });

    // Step 4: Add ONDC-specific processing
    const ondcOrder = processONDCOrderStep({
      order,
      ondcConfig,
      validationResult,
    });

    // Step 5: Send ONDC notifications
    const notificationResult = sendONDCNotificationsStep({
      order: ondcOrder,
      ondcConfig,
    });

    return new WorkflowResponse({
      order: ondcOrder,
      ondcConfig,
      notifications: notificationResult,
    });
  }
);
```

## Implementation Commands

### Step 1: Setup Sales Channels

```bash
# Run the setup script
cd ondc-seller-app/packages/backend-new/medusa-backend
npm run setup:tenants

# Or manually:
npx ts-node src/scripts/setup-tenant-sales-channels.ts
```

### Step 2: Migrate Existing Data

```bash
# Run the migration script
npm run migrate:tenants

# Or manually:
npx ts-node src/scripts/migrate-tenant-data.ts
```

### Step 3: Remove Custom Endpoints

```bash
# Remove custom API files
rm -rf src/api/admin/tenant
rm -rf src/api/admin/test-multi-tenant
rm -rf src/api/store/test-info
rm -rf src/api/store/test-products
rm src/api/middlewares.ts
rm src/middleware/tenant.ts
rm src/middleware/tenant-query-filter.ts
```

### Step 4: Update Package.json Scripts

```json
{
  "scripts": {
    "setup:tenants": "npx ts-node src/scripts/setup-tenant-sales-channels.ts",
    "migrate:tenants": "npx ts-node src/scripts/migrate-tenant-data.ts",
    "verify:migration": "npx ts-node src/scripts/migrate-tenant-data.ts --verify"
  }
}
```

## Testing the Refactored Implementation

### Test Store API with Publishable Keys

```bash
# Electronics tenant products
curl -H "x-publishable-api-key: pk_electronics_generated_key" \
     http://localhost:9000/store/products

# Fashion tenant products
curl -H "x-publishable-api-key: pk_fashion_generated_key" \
     http://localhost:9000/store/products
```

### Test Admin API with Sales Channel Filtering

```bash
# Get all sales channels (tenants)
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     http://localhost:9000/admin/sales-channels

# Get products for specific sales channel
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     "http://localhost:9000/admin/products?sales_channel_id=sc_electronics"
```

## Benefits Summary

### ✅ **Immediate Benefits**

- **Reduced Codebase:** Remove ~2000+ lines of custom code
- **Better Performance:** Use Medusa's optimized query patterns
- **Enhanced Security:** Leverage Medusa's built-in security mechanisms
- **Standard Patterns:** Follow Medusa's recommended architecture

### ✅ **Long-term Benefits**

- **Future-Proof:** Automatic compatibility with Medusa updates
- **Better Maintainability:** Use Medusa's tested and maintained APIs
- **Improved Documentation:** Leverage Medusa's comprehensive API docs
- **Community Support:** Access to Medusa's community and ecosystem

### ✅ **Preserved Functionality**

- **Complete Tenant Isolation:** Through sales channels and publishable keys
- **ONDC Configurations:** Stored in sales channel metadata
- **All API Functionality:** Maintained through native Medusa endpoints
- **Database Separation:** Maintained through sales channel associations
- **Frontend Integration:** Preserved with updated API client

This refactoring transforms the custom multi-tenancy implementation into a standard, maintainable, and scalable solution using Medusa's native capabilities while preserving all existing functionality and ONDC-specific requirements.
