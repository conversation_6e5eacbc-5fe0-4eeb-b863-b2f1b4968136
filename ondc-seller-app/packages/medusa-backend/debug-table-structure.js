#!/usr/bin/env node

const { Client } = require('pg');

async function debugTableStructure() {
  const client = new Client({
    connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend'
  });

  try {
    await client.connect();
    console.log('🔍 Checking table structures for Medusa v2...\n');

    const tables = ['product_variant', 'image', 'product_option', 'product_option_value', 'money_amount'];

    for (const tableName of tables) {
      console.log(`📋 ${tableName.toUpperCase()} table structure:`);
      console.log('='.repeat(50));
      
      try {
        const columnsResult = await client.query(`
          SELECT column_name, data_type, is_nullable, column_default
          FROM information_schema.columns 
          WHERE table_name = $1 
          ORDER BY ordinal_position
        `, [tableName]);
        
        if (columnsResult.rows.length === 0) {
          console.log(`❌ Table ${tableName} not found or has no columns`);
        } else {
          columnsResult.rows.forEach(row => {
            console.log(`  - ${row.column_name}: ${row.data_type} ${row.is_nullable === 'NO' ? '(NOT NULL)' : '(nullable)'}`);
          });
        }
      } catch (e) {
        console.log(`❌ Error checking ${tableName}: ${e.message}`);
      }
      console.log('');
    }

    // Check if there are any existing variants, images, options in the database
    console.log('📊 Existing data counts:');
    console.log('='.repeat(30));
    
    try {
      const variantCount = await client.query('SELECT COUNT(*) as count FROM product_variant');
      console.log(`📦 Total variants: ${variantCount.rows[0].count}`);
      
      const imageCount = await client.query('SELECT COUNT(*) as count FROM image');
      console.log(`🖼️  Total images: ${imageCount.rows[0].count}`);
      
      const optionCount = await client.query('SELECT COUNT(*) as count FROM product_option');
      console.log(`⚙️  Total options: ${optionCount.rows[0].count}`);

      // Check if any have tenant_id
      const variantTenantCount = await client.query('SELECT COUNT(*) as count FROM product_variant WHERE tenant_id IS NOT NULL');
      console.log(`📦 Variants with tenant_id: ${variantTenantCount.rows[0].count}`);
      
    } catch (e) {
      console.log(`❌ Error checking counts: ${e.message}`);
    }

  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await client.end();
  }
}

debugTableStructure().catch(console.error);