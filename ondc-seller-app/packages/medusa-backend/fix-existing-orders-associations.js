#!/usr/bin/env node

/**
 * Fix Existing Orders Customer/Tenant Associations
 *
 * This script fixes existing orders that were created without proper customer and tenant associations.
 * It attempts to infer the correct associations based on cart data and metadata.
 */

const { Client } = require('pg');
require('dotenv').config();

async function fixExistingOrdersAssociations() {
  console.log('🔧 Starting fix for existing orders with missing customer/tenant associations...');

  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    await client.connect();
    console.log('✅ Database connected');

    // Step 1: Find orders with missing customer_id or tenant_id
    console.log('\n📋 Step 1: Finding orders with missing associations...');
    const problematicOrdersQuery = `
      SELECT o.id, o.customer_id, o.tenant_id, o.email, o.metadata, o.created_at
      FROM "order" o
      WHERE (o.customer_id IS NULL OR o.tenant_id IS NULL)
        AND o.deleted_at IS NULL
      ORDER BY o.created_at DESC;
    `;

    const problematicOrdersResult = await client.query(problematicOrdersQuery);
    console.log(`Found ${problematicOrdersResult.rows.length} orders with missing associations`);

    if (problematicOrdersResult.rows.length === 0) {
      console.log('✅ All orders already have proper associations!');
      await client.end();
      return;
    }

    let fixedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    // Step 2: Process each problematic order
    for (const order of problematicOrdersResult.rows) {
      try {
        console.log(`\n🔍 Processing order: ${order.id}`);
        console.log(
          `  Current state: customer_id=${order.customer_id}, tenant_id=${order.tenant_id}`
        );

        let inferredCustomerId = order.customer_id;
        let inferredTenantId = order.tenant_id;

        // Skip cart inference since cart_id is not available in order table

        // Try to infer from metadata
        if (order.metadata && (!inferredCustomerId || !inferredTenantId)) {
          console.log(`  📋 Checking metadata...`);

          if (!inferredCustomerId && order.metadata.customer_id) {
            inferredCustomerId = order.metadata.customer_id;
            console.log(`  ✅ Inferred customer_id from metadata: ${inferredCustomerId}`);
          }
          if (!inferredTenantId && order.metadata.tenant_id) {
            inferredTenantId = order.metadata.tenant_id;
            console.log(`  ✅ Inferred tenant_id from metadata: ${inferredTenantId}`);
          }
        }

        // Try to infer tenant from email domain or other patterns
        if (!inferredTenantId && order.email) {
          console.log(`  📧 Attempting to infer tenant from email: ${order.email}`);

          // Simple heuristic: if email contains certain patterns, assign to specific tenant
          if (order.email.includes('kirana') || order.email.includes('grocery')) {
            inferredTenantId = 'my-kirana-store';
            console.log(`  ✅ Inferred tenant_id from email pattern: ${inferredTenantId}`);
          } else if (order.email.includes('fashion') || order.email.includes('clothing')) {
            inferredTenantId = 'fashion-store';
            console.log(`  ✅ Inferred tenant_id from email pattern: ${inferredTenantId}`);
          } else {
            // Default to most common tenant
            inferredTenantId = 'my-kirana-store';
            console.log(`  ⚠️  Using default tenant_id: ${inferredTenantId}`);
          }
        }

        // If we still don't have customer_id, try to find customer by email
        if (!inferredCustomerId && order.email) {
          console.log(`  👤 Looking for customer by email: ${order.email}`);

          const customerResult = await client.query(
            'SELECT id FROM customer WHERE email = $1 AND deleted_at IS NULL LIMIT 1;',
            [order.email]
          );

          if (customerResult.rows.length > 0) {
            inferredCustomerId = customerResult.rows[0].id;
            console.log(`  ✅ Found customer by email: ${inferredCustomerId}`);
          }
        }

        // Update the order if we have inferred values
        if (inferredCustomerId || inferredTenantId) {
          const updateFields = [];
          const updateValues = [];
          let paramIndex = 1;

          if (inferredCustomerId && inferredCustomerId !== order.customer_id) {
            updateFields.push(`customer_id = $${paramIndex++}`);
            updateValues.push(inferredCustomerId);
          }

          if (inferredTenantId && inferredTenantId !== order.tenant_id) {
            updateFields.push(`tenant_id = $${paramIndex++}`);
            updateValues.push(inferredTenantId);
          }

          // Update metadata to include fix information
          const updatedMetadata = {
            ...order.metadata,
            fixed_associations: true,
            fix_timestamp: new Date().toISOString(),
            original_customer_id: order.customer_id,
            original_tenant_id: order.tenant_id,
          };

          updateFields.push(`metadata = $${paramIndex++}`);
          updateValues.push(JSON.stringify(updatedMetadata));

          updateValues.push(order.id); // WHERE clause parameter

          if (updateFields.length > 1) {
            // More than just metadata
            const updateQuery = `
              UPDATE "order" 
              SET ${updateFields.join(', ')}, updated_at = NOW()
              WHERE id = $${paramIndex}
              RETURNING id, customer_id, tenant_id;
            `;

            const updateResult = await client.query(updateQuery, updateValues);

            if (updateResult.rows.length > 0) {
              const updatedOrder = updateResult.rows[0];
              console.log(`  ✅ Order updated successfully:`, {
                id: updatedOrder.id,
                customer_id: updatedOrder.customer_id,
                tenant_id: updatedOrder.tenant_id,
              });
              fixedCount++;
            } else {
              console.log(`  ❌ Update failed for order ${order.id}`);
              errorCount++;
            }
          } else {
            console.log(`  ⚠️  No changes needed for order ${order.id}`);
            skippedCount++;
          }
        } else {
          console.log(`  ⚠️  Could not infer customer_id or tenant_id for order ${order.id}`);
          skippedCount++;
        }
      } catch (error) {
        console.error(`  ❌ Error processing order ${order.id}:`, error.message);
        errorCount++;
      }
    }

    await client.end();

    // Summary
    console.log('\n🎉 Order associations fix completed!');
    console.log(`📊 Summary:`);
    console.log(`  - Orders processed: ${problematicOrdersResult.rows.length}`);
    console.log(`  - Successfully fixed: ${fixedCount}`);
    console.log(`  - Skipped: ${skippedCount}`);
    console.log(`  - Errors: ${errorCount}`);

    if (fixedCount > 0) {
      console.log(`\n✅ ${fixedCount} orders now have proper customer/tenant associations!`);
      console.log('🔍 Customers should now be able to retrieve their orders via the store API.');
    }
  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  }
}

// Test function to verify a specific order can be retrieved
async function testOrderRetrieval(orderId, customerId, tenantId) {
  console.log(`\n🧪 Testing order retrieval for: ${orderId}`);

  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    await client.connect();

    // Check if order exists and has proper associations
    const orderResult = await client.query(
      `
      SELECT id, customer_id, tenant_id, email, status
      FROM "order" 
      WHERE id = $1 AND customer_id = $2 AND tenant_id = $3 AND deleted_at IS NULL;
    `,
      [orderId, customerId, tenantId]
    );

    if (orderResult.rows.length > 0) {
      const order = orderResult.rows[0];
      console.log(`✅ Order can be retrieved:`, {
        id: order.id,
        customer_id: order.customer_id,
        tenant_id: order.tenant_id,
        email: order.email,
        status: order.status,
      });
      console.log(`✅ Store API should now return this order for the authenticated customer!`);
    } else {
      console.log(`❌ Order cannot be retrieved - missing associations or doesn't exist`);
    }

    await client.end();
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the script
if (require.main === module) {
  const args = process.argv.slice(2);

  if (args[0] === 'test' && args[1] && args[2] && args[3]) {
    // Test mode for specific order
    testOrderRetrieval(args[1], args[2], args[3])
      .then(() => {
        console.log('\n✅ Test completed');
        process.exit(0);
      })
      .catch(error => {
        console.error('\n❌ Test failed:', error);
        process.exit(1);
      });
  } else {
    // Normal fix mode
    fixExistingOrdersAssociations()
      .then(() => {
        console.log('\n✅ Script completed successfully');
        process.exit(0);
      })
      .catch(error => {
        console.error('\n❌ Script failed:', error);
        process.exit(1);
      });
  }
}

module.exports = { fixExistingOrdersAssociations, testOrderRetrieval };
