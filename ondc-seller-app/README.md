# ONDC Seller Platform

A comprehensive monorepo for the ONDC (Open Network for Digital Commerce) Seller Platform, built with Turborepo, Next.js, Medusa Commerce, and Prisma.

## Overview

This platform provides sellers on the ONDC network with tools to manage their products, orders, and customers. It includes a modern web dashboard built with Next.js, a powerful e-commerce backend powered by Medusa Commerce, and a shared database layer using Prisma.

## What's inside?

This monorepo uses [Turborepo](https://turbo.build/repo) and contains the following packages:

### Production Packages

- `frontend-new`: Enhanced [Next.js](https://nextjs.org/) app with cart system, multi-tenancy, and modern UI components
- `medusa-backend`: Enhanced [Medusa Commerce](https://medusajs.com/) v2 server with multi-tenancy, comprehensive API endpoints, and ONDC integration
- `cms-strapi`: [Strapi](https://strapi.io/) CMS for content management and dynamic content delivery
- `prisma`: Shared Prisma schema and client for database access

### Key Features

The platform provides:

- **Multi-tenant Architecture** → Complete tenant isolation across frontend and backend
- **Modern E-commerce Stack** → Next.js 14 + Medusa v2 + Strapi CMS
- **Production-Ready** → Optimized build process and deployment configuration
- **Comprehensive APIs** → Full REST API coverage for all e-commerce operations

### Utilities

This Turborepo has some additional tools already set up for you:

- [TypeScript](https://www.typescriptlang.org/) for static type checking
- [ESLint](https://eslint.org/) for code linting
- [Jest](https://jestjs.io) for testing
- [Prettier](https://prettier.io) for code formatting
- [Docker](https://www.docker.com/) for containerization
- [Keycloak](https://www.keycloak.org/) for authentication (oneSSO)

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- npm (v9 or later)
- Docker and Docker Compose (for local development with PostgreSQL, Redis, and Keycloak)

### Quick Start

For a quick start with the frontend only, you can run:

```bash
cd packages/frontend-new
npm install
npm run dev
```

This will start the Next.js frontend on http://localhost:3000.

For a complete development environment with all services:

```bash
# Install all dependencies
npm install

# Start all services in development mode
npm run dev

# Or start individual services:

# Start Strapi CMS
npm run dev:cms-strapi

# Start Medusa backend
npm run dev:medusa-backend

# Start frontend
cd packages/frontend-new
npm install
npm run dev
```

### Installation

1. Clone the repository:

```bash
git clone https://github.com/your-username/ondc-seller.git
cd ondc-seller
```

2. Run the setup script:

```bash
./scripts/setup-dev.sh
```

This script will:

- Install all dependencies
- Generate the Prisma client
- Set up the development environment

3. Set up environment variables:

Copy the example environment files and update them with your configuration:

```bash
cp .env.example .env
cp packages/prisma/.env.example packages/prisma/.env
cp packages/backend/.env.example packages/backend/.env
cp packages/frontend/.env.example packages/frontend/.env
```

4. Start the development services:

```bash
docker-compose -f docker-compose.dev.yml up -d
```

This will start:

- PostgreSQL database
- Redis cache
- Keycloak authentication server

5. Initialize the database:

```bash
cd packages/prisma
npx prisma migrate dev --name init
npx prisma generate
```

This will:

- Create the database tables based on the Prisma schema
- Generate the Prisma client for type-safe database access

6. Start the development server:

```bash
npm run dev
```

## Development

### Development and Production Modes

The application supports two modes:

#### Development Mode

- Uses hardcoded authentication credentials (username: `demo`, password: `demo`)
- Provides detailed logging and debugging information
- Hot reloading for faster development

#### Production Mode

- Integrates with oneSSO (Keycloak) for authentication
- Optimized for performance
- Proper error handling and security measures

### Build

To build all apps and packages, run the following command:

```bash
npm run build
```

### Develop

To develop all apps and packages, run the following command:

```bash
npm run dev
```

### Testing

To run tests for all apps and packages, run the following command:

```bash
./scripts/run-tests.sh
```

### Database Management

#### Migrations

When you make changes to the Prisma schema, you need to create and apply migrations:

```bash
cd packages/prisma
npx prisma migrate dev --name <migration-name>
```

#### Generating Prisma Client

After making changes to the schema or applying migrations, regenerate the Prisma client:

```bash
cd packages/prisma
npx prisma generate
```

#### Database Reset

To reset the database during development:

```bash
cd packages/prisma
npx prisma migrate reset
```

This will drop the database, recreate it, and run all migrations.

### API Documentation

The backend API is documented using OpenAPI (Swagger). To generate and view the API documentation:

```bash
cd packages/backend
npm run api:docs
npm run api:serve-docs
```

Then open your browser at http://localhost:8080 to view the API documentation.

## Deployment

### Using Docker Compose

The easiest way to deploy the entire stack is using Docker Compose:

```bash
docker-compose up -d
```

This will start:

- Frontend container
- Backend container
- PostgreSQL database
- Redis cache
- Keycloak authentication server

### Manual Deployment

#### Frontend

The frontend can be deployed to Vercel or any other Next.js-compatible hosting service:

```bash
cd packages/frontend
npm run build
npm run start
```

#### Backend

The backend can be deployed to any Node.js hosting service that supports PostgreSQL and Redis:

```bash
cd packages/backend
npm run build
npm run start
```

## Architecture

### API Architecture

The ONDC Seller Platform uses a layered API architecture to provide a flexible and scalable solution:

```
┌─────────────┐                     ┌─────────────────┐
│             │                     │                 │
│  Frontend   │                     │ Official Medusa │
│  (Next.js)  │                     │ Commerce API    │
│             │                     │                 │
└─────────────┘                     └─────────────────┘
       │                                      ▲
       │                                      │
       │                                      │
       ▼                                      │
┌─────────────┐                     ┌─────────────────┐
│             │                     │                 │
│ Medusa-MCP  │────────────────────▶│ Multi-tenancy   │
│  Service    │                     │    Plugin       │
│             │                     │                 │
└─────────────┘                     └─────────────────┘
```

1. **Frontend Layer (Next.js)**
   - Provides the user interface for sellers
   - Communicates with the Medusa-MCP service for data access
   - Implements multi-tenancy support via the `x-tenant-id` header

2. **Medusa-MCP Service Layer**
   - Acts as an intermediary between the frontend and backend
   - Provides JSON-RPC API for standardized communication
   - Handles authentication and token management
   - Implements caching and request optimization

3. **Medusa Commerce API Layer**
   - Provides the core e-commerce functionality
   - Implements multi-tenancy through a custom plugin
   - Manages product, order, and customer data
   - Integrates with external services like payment gateways

4. **Multi-tenancy Plugin**
   - Ensures proper tenant isolation in the database
   - Adds tenant context to all database operations
   - Manages tenant-specific configuration and settings

### Frontend

- Next.js 14 with App Router
- TypeScript for type safety
- Tailwind CSS for styling
- Medusa JS client for data fetching
- Jest and React Testing Library for testing
- Medusa-MCP integration for AI-powered automation

#### API Integration

The frontend integrates with the Medusa backend through the Medusa JS client and Medusa-MCP service:

1. **Medusa JS Client**
   - Located in `packages/frontend/src/lib/medusa-client.ts`
   - Provides a type-safe interface to the Medusa API
   - Includes multi-tenancy support via the `x-tenant-id` header
   - Implements token-based authentication with refresh capability
   - Provides retry logic for network failures

2. **Enhanced Cart API**
   - Located in `packages/frontend/src/lib/medusa/cart-api.ts`
   - Provides advanced cart functionality including:
     - Persistent carts using local storage
     - Cart merging for guest and authenticated users
     - Comprehensive cart operations (add, update, remove items)
     - Shipping and billing address management
     - Payment session handling
   - Includes React hooks for easy integration in components
   - Maintains multi-tenant functionality with tenant ID in all requests
   - Implements proper error handling and fallbacks

3. **Medusa-MCP Integration**
   - Located in `packages/frontend/src/lib/mcp/useMcpTool.ts`
   - Provides React hooks for interacting with the Medusa-MCP service
   - Implements JSON-RPC communication protocol
   - Enables AI-powered automation and workflow orchestration

4. **Multi-tenancy Support**
   - Every API request includes the tenant ID in the headers
   - Tenant context is managed through the `tenant.ts` utility
   - Allows the application to work with multiple sellers on the ONDC platform

5. **Error Handling**
   - Standardized error handling across all API calls
   - Typed error responses for better debugging
   - Automatic retry for network-related failures

### Backend

- Medusa Commerce for e-commerce functionality
- Custom multi-tenancy plugin for tenant isolation
- TypeScript for type safety
- Jest for testing
- OpenAPI for API documentation

### Database

- PostgreSQL for data storage
- Prisma for database access and migrations
- Shared schema across packages

#### Data Models

The following data models are defined in the Prisma schema:

1. **User**
   - Basic user authentication and profile information
   - Fields: id, email, name, password, role, timestamps
   - Relationships: one-to-one with SellerProfile, one-to-many with Order

2. **SellerProfile**
   - Seller-specific information including KYC and ONDC credentials
   - KYC Fields: businessName, legalEntityType, pan, gstin, address, city, state, country, pincode
   - ONDC Fields: ondcSubscriberId, ondcSubscriberUrl, signingPrivateKey, encryptionPrivateKey, encryptionPublicKey
   - Verification Fields: isVerified, verificationStatus
   - Relationships: one-to-one with User, one-to-many with Product

3. **Product**
   - Product catalog information
   - Fields: id, name, description, images, price, category, timestamps
   - Relationships: many-to-one with SellerProfile, one-to-one with InventoryItem, one-to-many with OrderItem, many-to-many with Category

4. **InventoryItem**
   - Inventory tracking for products
   - Fields: id, quantity, sku, timestamps
   - Relationships: one-to-one with Product

5. **Order**
   - Order information
   - Fields: id, status, total, shippingAddress, timestamps
   - Relationships: many-to-one with User, one-to-many with OrderItem, one-to-one with Payment, one-to-one with Delivery

6. **OrderItem**
   - Individual items within an order
   - Fields: id, quantity, unitPrice, timestamps
   - Relationships: many-to-one with Order, many-to-one with Product

7. **Payment**
   - Payment information for orders
   - Fields: id, amount, paymentMethod, transactionId, status, timestamps
   - Relationships: one-to-one with Order

8. **Delivery**
   - Delivery information for orders
   - Fields: id, trackingNumber, carrier, status, estimatedDeliveryDate, timestamps
   - Relationships: one-to-one with Order

9. **Category**
   - Category information for products
   - Fields: id, name, timestamps
   - Relationships: many-to-many with Product

### Authentication

- oneSSO (Keycloak) for authentication in production
- Hardcoded credentials in development mode

### Notifications

- Supabase Realtime for real-time notifications

## Bootstrapping Medusa Backend with Multi-Tenancy Support

### Prerequisites

- Ensure you have Node.js (v18+) and npm installed
- Make sure you're in the project root directory

### Steps to Initialize Medusa Backend

1. Navigate to the backend package directory:

   ```bash
   cd packages/backend/
   ```

2. Initialize a new Medusa project in the current directory with sample data:

   ```bash
   npx @medusajs/medusa-cli new . --seed
   ```

   This command will:
   - Install all required Medusa dependencies
   - Set up the basic Medusa structure
   - Seed the database with initial product and store data

3. Open the `medusa-config.js` file in your code editor and update the plugins array to include the multi-tenancy plugin:

   ```javascript
   const plugins = [
     // Existing plugins will be here
     `medusa-fulfillment-manual`,
     `medusa-payment-manual`,
     // Add the multi-tenancy plugin
     {
       resolve: '@medusajs/medusa/dist/plugins/multi-tenancy',
       options: {
         // Configure tenant-specific metadata fields for ONDC
         tenant_metadata_fields: ['ondcId', 'sellerCategory', 'region'],
       },
     },
   ];
   ```

4. Save the file and restart your Medusa server to apply the changes:

   ```bash
   npm run dev
   ```

5. Verify the installation by accessing the Medusa admin panel at http://localhost:9000/admin

This will set up your Medusa backend with multi-tenancy support, allowing you to manage multiple sellers on the ONDC platform.

## ONDC Seller App Integration

This project includes integration with the official ONDC seller application to provide a complete ONDC-compliant seller platform.

### Cloning & Configuring ONDC Seller Frontend

1. The official ONDC seller application has been integrated into our monorepo structure in the `packages/ondc-seller-app` directory.

2. The integration includes:
   - The seller frontend application
   - The seller API for ONDC protocol integration
   - A notifications service for real-time updates

3. Configuration is handled through environment variables:

   ```
   # ONDC credentials for authentication
   ONDC_PARTICIPANT_ID=your-participant-id
   ONDC_SUBSCRIBER_ID=your-subscriber-id
   ONDC_SUBSCRIBER_URL=your-subscriber-url
   ONDC_REGISTRY_URL=your-registry-url
   ONDC_AUTH_SIGNING_KEY=your-auth-signing-key
   ONDC_ENCRYPTION_PUBLIC_KEY=your-encryption-public-key
   ONDC_ENCRYPTION_PRIVATE_KEY=your-encryption-private-key
   ```

4. Our Next.js frontend communicates with both the Medusa backend and the ONDC seller app API, providing a unified interface for sellers.

5. Product data is synchronized between Medusa and the ONDC network, ensuring consistent product information across all channels.

For more details, see the [ONDC Seller App README](packages/ondc-seller-app/README.md).

## Strapi CMS Integration

This project includes integration with Strapi CMS, a headless content management system that provides a flexible and customizable way to manage content for the ONDC Seller Platform.

### Features

- **Content Management**: Manage banners, pages, product categories, and other content through a user-friendly admin panel
- **API-First**: Access content through a RESTful API
- **Multi-Tenant Support**: Content can be organized by tenant/seller
- **Media Library**: Upload and manage images and other media files
- **Role-Based Access Control**: Control who can access and modify content

### Content Types

The CMS includes the following content types:

- **Seller**: Seller profiles with ONDC credentials
- **Product Category**: Categories for organizing products
- **Product**: Product information
- **Order**: Order information
- **Customer**: Customer information
- **Banner**: Promotional banners for the storefront
- **Page**: Static pages like About Us, Terms of Service, etc.

### Getting Started with Strapi

1. Navigate to the cms-strapi package:

   ```bash
   cd packages/cms-strapi
   ```

2. Start the Strapi server:

   ```bash
   npm run develop
   ```

3. Access the admin panel at http://localhost:1337/admin

4. Create content types and add content through the admin panel

5. Access content from the frontend using the Strapi API client:

   ```tsx
   import { getProducts } from '@/lib/strapi';

   // In a React component
   const products = await getProducts();
   ```

For more details, see the [Strapi CMS README](packages/cms-strapi/README.md).

## Medusa-MCP Integration

This project includes integration with Medusa-MCP, a Model Context Protocol (MCP) server designed for the Medusa JavaScript SDK. This integration enables:

### What is Medusa-MCP?

Medusa-MCP is a bridge between AI/automation tools and your Medusa e-commerce backend, providing:

1. **AI-Powered Automation**: Connect AI assistants or LLMs to your Medusa store
2. **Workflow Orchestration**: Automate inventory management, pricing adjustments, and order processing
3. **Standardized Communication**: Use JSON-RPC for reliable communication between services
4. **Extensible Architecture**: Add custom functionality through plugins

### Setting Up Medusa-MCP

1. Configure environment variables in `packages/medusa-mcp/.env`:

   ```
   MEDUSA_BACKEND_URL=http://localhost:9000
   PUBLISHABLE_KEY=your_publishable_key_here
   MEDUSA_USERNAME=<EMAIL>
   MEDUSA_PASSWORD=admin_password
   ```

2. Build and start the MCP server:

   ```bash
   ./scripts/start-mcp.sh
   ```

3. Use MCP tools in your frontend components:

   ```tsx
   import { useGetProducts } from '../lib/mcp/useMcpTool';

   export default function ProductList() {
     const { execute, data, isLoading, error } = useGetProducts();

     useEffect(() => {
       execute({ limit: 10 });
     }, [execute]);

     // Render products...
   }
   ```

### Benefits for ONDC Seller Platform

For our multi-tenant ONDC Seller Platform, Medusa-MCP enables:

- Automated seller onboarding and management
- AI-powered insights and recommendations for sellers
- Intelligent automation of routine tasks
- Seamless integration with external services and APIs

## Developer Guides

- [Frontend Developer Guide](docs/frontend-developer-guide.md)
- [Backend Developer Guide](docs/backend-developer-guide.md)

## Troubleshooting

### Common Issues and Solutions

#### Frontend Issues

1. **Banner images not displaying properly**
   - Check that Strapi CMS is running on port 1339
   - Verify that the banner content type has an `image` field with proper population
   - Ensure the image URLs are properly formatted in the frontend code
   - Check browser console for any 404 errors related to image URLs

2. **API connection issues**
   - Verify that all services are running (Strapi CMS, Medusa backend, Medusa MCP)
   - Check that the API tokens and keys are correctly set in the `.env.local` file
   - Look for CORS errors in the browser console
   - Try the test API page at `/test-api` to diagnose specific API issues

3. **Multi-tenancy issues**
   - Ensure the `x-tenant-id` header is being properly set in API requests
   - Verify that the tenant exists in the Strapi CMS
   - Check that the tenant has the required ONDC credentials

#### Backend Issues

1. **Medusa API authentication issues**
   - Verify that the publishable API key is correctly set in the `.env.local` file
   - Check that the Medusa backend is running on the correct port (default: 9001)
   - Try accessing the Medusa API directly using curl to isolate the issue

2. **Strapi CMS issues**
   - Ensure PostgreSQL is running and accessible
   - Check that the Strapi API token is correctly set in the `.env.local` file
   - Verify that the content types are properly configured

3. **Database connection issues**
   - Check database credentials in the `.env` files
   - Verify that PostgreSQL is running and accessible
   - Try connecting to the database directly using a tool like psql or pgAdmin

### Logs and Monitoring

The application maintains several types of logs:

1. **Change logs**: Track all changes to the codebase in `CHANGELOG.md`
2. **Error logs**: Runtime errors are logged to the console and can be found in the terminal output
3. **Navigation logs**: User navigation is tracked in the browser console

To access these logs:

- Change logs: View the `CHANGELOG.md` file in the project root
- Error logs: Check the terminal output when running the application
- Navigation logs: Open the browser console (F12 or Ctrl+Shift+I) and look for navigation-related messages

## License

This project is licensed under the MIT License - see the LICENSE file for details.
