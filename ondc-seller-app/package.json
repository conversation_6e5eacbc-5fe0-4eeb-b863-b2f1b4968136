{"name": "ondc-seller", "version": "0.0.1", "private": true, "workspaces": ["packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "test": "turbo run test", "test:unit": "turbo run test:unit", "test:integration": "turbo run test:integration", "test:e2e": "turbo run test:e2e", "test:coverage": "turbo run test --coverage", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "prepare": "echo 'Skipping husky install for now'", "clean": "turbo run clean", "api:docs": "turbo run api:docs", "docker:build": "turbo run docker:build", "docker:push": "turbo run docker:push", "flutter:build": "turbo run flutter:build", "flutter:test": "turbo run flutter:test", "dev:ondc-adapter": "turbo run dev --filter=@ondc-seller/ondc-adapter", "dev:mobile": "cd packages/mobile-app && flutter run", "install:mobile": "cd packages/mobile-app && flutter pub get", "setup": "npm install && npm run install:mobile"}, "devDependencies": {"eslint": "^8.56.0", "husky": "^8.0.0", "prettier": "^3.1.1", "turbo": "^2.0.0"}, "engines": {"node": ">=18.0.0"}, "packageManager": "npm@9.2.0", "dependencies": {"@mikro-orm/core": "^6.4.3", "@types/lodash": "^4.17.16", "@vercel/mcp-adapter": "^1.0.0", "keycloak-js": "^26.2.0", "lodash": "^4.17.21", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.30.1", "styled-components": "^6.1.19", "zod": "^3.22.4"}}